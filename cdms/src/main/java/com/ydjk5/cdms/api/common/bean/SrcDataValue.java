package com.ydjk5.cdms.api.common.bean;

import com.ydjk5.cdms.api.common.consts.ApiConsts;
import com.ydjk5.cdms.api.common.consts.ApiConsts.DataTypeEnum;
import com.ydjk5.cdms.api.common.utils.DateUtil;

import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;


/***************************************************************************
 * <PRE>
 * 
 *  Project Name    : cdms-api
 *  
 *  Description     : 血糖设备数据采集数据报文对象
 *  
 *  Author          : huke.zhang
 * 
 * </PRE>
 ***************************************************************************/
public class SrcDataValue implements Serializable {
	
	public static final long serialVersionUID = -1203493109929647895L;
	
	private String srcData;
	private String start;
	private String version;
	private String user;
	private String customer;
	private String modelCode;
	private String deviceType;
	private String serialNumber;
	private String data1;
	private String data2;
	private String data3;
	private String year;
	private String month;
	private String day;
	private String hour;
	private String minute;
	private String checkCode;
	private String separator43;
	private String imsi;
	private String separator59;
	private String latiLongitude;
	private String crcCode;
	//以下2.6版本开始增加
	private String imei;//2.6版本开始增加 
	private String extend; //2.6版本开始增加  保留字符
	private String mealtime; //餐段 2.7版本血糖开始增加 1 餐前 2餐后 0 无选择
	private String time; //秒 2.7版本血糖开始增加
	
	public SrcDataValue(String srcData){
		this.srcData = srcData;
		parseData();
	}

	protected  void parseData() {
		
		this.start = this.srcData.substring(0, 2);
		this.version = this.srcData.substring(2, 4);
		this.user = this.srcData.substring(4, 5);
		this.customer = this.srcData.substring(5, 7);
		this.modelCode = this.srcData.substring(7, 9);
		this.deviceType = this.srcData.substring(9, 11); // 机种码
		this.serialNumber = this.srcData.substring(11, 20);
		this.data1 = this.srcData.substring(20, 23);
		this.data2 = this.srcData.substring(23, 26);
		// 血糖仪是以mg/dL为单位传送，如果要显示为mmol/dL的单位，则要除以18，并四舍五入才能与血糖仪端显示的数据一致
		this.data3 = this.srcData.substring(26, 29);
		if(ApiConsts.AgreementVersion.V_AGREEMENT_27.equals(this.version) && 
				DataTypeEnum.BLGLUCOSE.getCode().equals(this.deviceType) 
		  ){
			this.time = this.srcData.substring(23, 25);
			this.mealtime= this.srcData.substring(25, 26);
		}
		this.year = this.srcData.substring(29, 31);
		this.month = this.srcData.substring(31, 33);
		this.day = this.srcData.substring(33, 35);
		this.hour = this.srcData.substring(35, 37);
		this.minute = this.srcData.substring(37, 39);
		this.checkCode = this.srcData.substring(39, 43);
		this.separator43 = this.srcData.substring(43, 44);
		this.imsi = this.srcData.substring(44, 59);
		this.separator59 = this.srcData.substring(59, 60);
		if(this.version.equals(ApiConsts.AgreementVersion.V_AGREEMENT_25)){
			this.latiLongitude = this.srcData.substring(60, 85);
		}else{
			this.imei = this.srcData.substring(60, 75);
			this.extend=this.srcData.substring(75, 85);
		}
		
		if (this.srcData.length() > 85) {
			this.crcCode = this.srcData.substring(85);
		}
	}

	public String getSrcData() {
		return this.srcData;
	}

	public String getStart() {
		return this.start;
	}

	public void setStart(String start) {
		this.start = start;
	}

	public String getVersion() {
		return this.version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getUser() {
		return this.user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public String getCustomer() {
		return this.customer;
	}

	public void setCustomer(String customer) {
		this.customer = customer;
	}

	public String getModelCode() {
		return this.modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public String getDeviceType() {
		return this.deviceType;
	}

	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}

	public String getSerialNumber() {
		return this.serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getData1() {
		return this.data1;
	}

	public void setData1(String data1) {
		this.data1 = data1;
	}

	public String getData2() {
		return this.data2;
	}

	public void setData2(String data2) {
		this.data2 = data2;
	}

	public String getData3() {
		return this.data3;
	}

	public void setData3(String data3) {
		this.data3 = data3;
	}

	public String getYear() {
		return this.year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getMonth() {
		return this.month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getDay() {
		return this.day;
	}

	public void setDay(String day) {
		this.day = day;
	}

	public String getHour() {
		return this.hour;
	}

	public void setHour(String hour) {
		this.hour = hour;
	}

	public String getMinute() {
		return this.minute;
	}

	public void setMinute(String minute) {
		this.minute = minute;
	}

	public String getCheckCode() {
		return this.checkCode;
	}

	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}

	public String getSeparator43() {
		return this.separator43;
	}

	public void setSeparator43(String separator43) {
		this.separator43 = separator43;
	}

	public String getImsi() {
		return this.imsi;
	}

	public void setImsi(String imsi) {
		this.imsi = imsi;
	}

	public String getSeparator59() {
		return this.separator59;
	}

	public void setSeparator59(String separator59) {
		this.separator59 = separator59;
	}

	public String getLatiLongitude() {
		return this.latiLongitude;
	}

	public void setLatiLongitude(String latiLongitude) {
		this.latiLongitude = latiLongitude;
	}

	public String getCrcCode() {
		return this.crcCode;
	}

	public void setCrcCode(String crcCode) {
		this.crcCode = crcCode;
	}
	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}
	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}
	
	public String getMealtime() {
		return mealtime;
	}

	public void setMealtime(String mealtime) {
		this.mealtime = mealtime;
	}

	public void setSrcData(String srcData) {
		this.srcData = srcData;
	}
	public String getTime() {
		return time;
	}

	public void setTime(String time) {
		this.time = time;
	}

	public Date getCheckTime() {
		StringBuffer datesb = new StringBuffer(this.year).append("-");
		datesb.append(this.month).append("-").append(this.day).append(" ");
		datesb.append(this.hour).append(":").append(this.minute).append(":00");

		try {
			return DateUtil.parseToDate(datesb.toString(), "yy-MM-dd HH:mm:ss");
		} catch (ParseException e) {
			return null;
		}
	}

	public String toString() {
		return "SrcDataValue [srcData=" + this.srcData + ", start="
				+ this.start + ", version=" + this.version + ", member="
				+ this.user + ", customer=" + this.customer + ", modelCode="
				+ this.modelCode + ", deviceType=" + this.deviceType
				+ ", serialNumber=" + this.serialNumber + ", data1="
				+ this.data1 + ", data2=" + this.data2 + ", data3="
				+ this.data3 + ", year=" + this.year + ", month=" + this.month
				+ ", day=" + this.day + ", hour=" + this.hour + ", minute="
				+ this.minute + ", checkCode=" + this.checkCode
				+ ", separator43=" + this.separator43 + ", imsi=" + this.imsi
				+ ", separator59=" + this.separator59 + ", latiLongitude="
				+ this.latiLongitude + ", crcCode=" + this.crcCode + ", imei="+this.imei+", extend="+this.extend+", mealtime="+this.mealtime+", time="+this.time+"]";
	}
}
