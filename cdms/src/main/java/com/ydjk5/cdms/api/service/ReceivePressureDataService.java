package com.ydjk5.cdms.api.service;

import com.ydjk5.cdms.api.common.bean.SrcDataValue;


/***************************************************************************
 * <PRE>
 * 
 *  Project Name    : cdms-api
 *  
 *  Description     : 血压设备数据采集
 *  
 *  Author          : huke.zhang
 * 
 * </PRE>
 ***************************************************************************/
public interface ReceivePressureDataService {

	/**
	 * 处理上报的数据
	 * 
	 * @param srcDatavalue
	 * @return
	 */
	public boolean handleReceivedData(SrcDataValue srcDatavalue);
	
}
