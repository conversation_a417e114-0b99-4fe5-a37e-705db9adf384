package com.ydjk5.cdms.api.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.ydjk5.cdms.api.common.bean.SrcDataValue;

import com.ydjk5.cdms.api.common.utils.HdwDataCheckUtils;
import com.ydjk5.cdms.api.service.ReceiveDataService;


/***************************************************************************
 * <PRE>
 * 
 *  Project Name    : cdms-api
 *  
 *  Description     : 血糖设备数据采集service
 *  
 *  Author          : huke.zhang
 * 
 * </PRE>
 ***************************************************************************/
@Service
public class ReceiveDataServiceImpl implements ReceiveDataService {

	private static Logger logger = LoggerFactory.getLogger(ReceiveDataServiceImpl.class);

	/**
	 * 处理上报的数据
	 * 
	 * @param srcDatavalue
	 * @return
	 */
	@Override
	public boolean handleReceivedData(SrcDataValue srcDatavalue) {
		System.out.println();
		return true;
	}
	

	


	
}
	
	


