package com.ydjk5.cdms.api.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ydjk5.cdms.api.common.bean.SrcDataValue;
import com.ydjk5.cdms.api.service.ReceivePressureDataService;



/***************************************************************************
 * <PRE>
 * 
 *  Project Name    : cdms-api
 *  
 *  Description     : 血压设备数据采集service
 *  
 *  Author          : huke.zhang
 * 
 * </PRE>
 ***************************************************************************/
@Service
public class ReceivePressureDataServiceImpl implements ReceivePressureDataService {

	private static Logger logger = LoggerFactory.getLogger(ReceivePressureDataServiceImpl.class);


	
	
	/**
	 * 处理上报的数据
	 * 
	 * @param srcDatavalue
	 * @return
	 */
	@Override
	public boolean handleReceivedData(SrcDataValue srcDatavalue) {
		//血压
		String patientId = null;
		String devSN = srcDatavalue.getSerialNumber();
		try
		{






		}
		catch (Exception e)
		{
			logger.error("GPRS血压设备血压上传异常", e);

		}

		return true;
	}
	









}
