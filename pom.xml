<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.wingcloud</groupId>
    <artifactId>thasd</artifactId>
    <version>lc.0.1</version>
    <modules>
        <module>thasd-gateway</module>
        <module>thasd-common</module>
        <module>thasd-sys</module>
        <module>thasd-sys-api</module>
        <module>thasd-web</module>
        <module>tool-test</module>
        <module>thasd-patients</module>
        <module>thasd-model</module>
        <module>thasd-plan</module>
        <module>thasd-referral</module>
        <module>thasd-hisapi</module>
        <module>thasd-rmisp</module>
        <module>thasd-mtask</module>
        <module>thasd-synhis</module>
        <module>thasd-analyze</module>
        <module>thasd-import</module>
        <module>thasd-xcx</module>
        <module>thasd-lot</module>
    </modules>
    <packaging>pom</packaging>
    <name>thasd</name>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <jfinal.version>4.9.08</jfinal.version>
        <jfinal.undertow.version>2.4</jfinal.undertow.version>
        <io.undertow.websockets.version>2.0.33.Final</io.undertow.websockets.version>
        <hutool.version>5.7.22</hutool.version>
        <beetl.version>3.3.2.RELEASE</beetl.version>
        <beetlsql.version>3.13.0-RELEASE</beetlsql.version>
        <druid.version>1.2.4</druid.version>
        <mssql-jdbc.version>8.2.2.jre8</mssql-jdbc.version>
        <fastjson.version>1.2.75</fastjson.version>
        <cglib-nodep.version>3.2.5</cglib-nodep.version>
        <jsonpath.version>2.5.0</jsonpath.version>
        <nutz.version>1.b.52</nutz.version>
        <log4j.version>1.2.17</log4j.version>
        <slf4j.version>1.7.7</slf4j.version>
        <jedis.version>2.9.0</jedis.version>
        <fst.version>2.57</fst.version>
        <lombok.version>1.18.12</lombok.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-codec.version>1.9</commons-codec.version>
        <sqllite.jdbc>3.34.0</sqllite.jdbc>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--MAVEN打包选择运行环境-->
    <!-- 1:local(默认) 本地 2:dev:开发环境 3:test 4:uat 用户验收测试 5.pro:生产环境-->
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <activeProject>dev</activeProject>
                <productMode>false</productMode>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <activeProject>prod</activeProject>
                <productMode>true</productMode>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <activeProject>local</activeProject>
                <productMode>true</productMode>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

</project>