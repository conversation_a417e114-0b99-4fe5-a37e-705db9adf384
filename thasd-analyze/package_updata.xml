<?xml version="1.0" encoding="UTF-8"?>
<assembly>
	<!-- 
		assembly 打包配置更多配置可参考官方文档：
			http://maven.apache.org/plugins/maven-assembly-plugin/assembly.html
	 -->
	
	<id>updata</id>
	<!--
		设置打包格式，可同时设置多种格式，常用格式有：dir、zip、tar、tar.gz
		dir 格式便于在本地测试打包结果
		zip 格式便于 windows 系统下解压运行
		tar、tar.gz 格式便于 linux 系统下解压运行
	 -->
	<formats>
<!--		<format>tar.gz</format>-->
<!--		<format>dir</format>-->
		<format>zip</format>
	</formats>
	
	<!-- 打 zip 设置为 true 时，会在 zip 包中生成一个根目录，打 dir 时设置为 false 少层目录 -->
	<includeBaseDirectory>true</includeBaseDirectory>
	
	<fileSets>
		<!-- src/main/resources 全部 copy 到 config 目录下 -->
		<fileSet>
			<directory>${basedir}/target/classes</directory>
			<outputDirectory>config</outputDirectory>
			<includes>
				<include>/sql/*</include>
				<include>/static/*</include>
				<include>auth.config.txt</include>
				<include>config-${activeProject}.txt</include>
				<include>undertow-${activeProject}.txt</include>
				<include>*.pfx</include>
				<include>*.properties</include>
			</includes>
		</fileSet>
		
		<!-- src/main/webapp 全部 copy 到 webapp 目录下 -->
		<fileSet>
			<directory>${basedir}/src/main/webapp</directory>
			<outputDirectory>webapp</outputDirectory>
		</fileSet>
		<!-- 将第三方依赖打包到lib目录中 -->
		<fileSet>
			<directory>${basedir}/target/lib</directory>
			<outputDirectory>lib</outputDirectory>
			<includes>
				<include>thasd-common-${project.version}.jar</include>
				<include>thasd-model-${project.version}.jar</include>
			</includes>
			<fileMode>0755</fileMode>
		</fileSet>
		<!-- 将项目启动jar打包到boot目录中 -->
		<fileSet>
			<directory>${basedir}/target</directory>
			<outputDirectory>lib</outputDirectory>
			<fileMode>0755</fileMode>
			<includes>
				<include>${project.build.finalName}.jar</include>
			</includes>
		</fileSet>
		<!-- 项目根下面的脚本文件 copy 到根目录下 脚本文件在 linux 下的权限设为 755，无需 chmod 可直接运行 -->
		<!--
		<fileSet>
			<directory>${basedir}/bin</directory>
			<outputDirectory>bin</outputDirectory>
			<fileMode>755</fileMode>
			<lineEnding>unix</lineEnding>
			<includes>
				<include>*.sh</include>
			</includes>
		</fileSet>
		-->
		<fileSet>
			<directory>${basedir}/bin</directory>
			<outputDirectory></outputDirectory>
			<fileMode>755</fileMode>
			<lineEnding>windows</lineEnding>
			<includes>
				<include>*.bat</include>
			</includes>
		</fileSet>
	</fileSets>

</assembly>



