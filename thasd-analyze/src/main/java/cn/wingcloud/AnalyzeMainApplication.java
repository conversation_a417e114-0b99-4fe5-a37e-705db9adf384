package cn.wingcloud;

import cn.wingcloud.beetlsql.JFinalBeetlSqlTo;
import cn.wingcloud.task.MainTask;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import org.nutz.log.Log;
import org.nutz.log.Logs;

public class AnalyzeMainApplication {

    private static final Log log = Logs.get();

    private static Prop profile;
    private static String activeProject = "prod";
    private static Prop p;
    public static boolean DEBUG = false;

    public static void main(String[] args) {

        if (profile == null) {
            profile = PropKit.useFirstFound( "env.properties");
            activeProject = profile.get("activeProject","prod");
            p = PropKit.use("config-"+activeProject+".txt");
            DEBUG = p.getBoolean("devMode",false);
            log.infof("profile: @%s",activeProject);

            JFinalBeetlSqlTo.init(p);

            MainTask mainTask = new MainTask();
            mainTask.sync();

        }
    }



}
