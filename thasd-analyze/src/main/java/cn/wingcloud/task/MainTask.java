package cn.wingcloud.task;

import cn.wingcloud.task.service.AnalyzeAbiService;
import cn.wingcloud.task.service.AnalyzeFeiGongNengService;
import cn.wingcloud.task.service.AnalyzeJcjyService;
import cn.wingcloud.task.service.AnalyzeYandiBBService;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static cn.wingcloud.task.TaskConstant.*;

public class MainTask {

    private static final Log log = Logs.get();
    private static ScheduledExecutorService service = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    public void sync(){

        Runnable analyzeJcjyRun = new Runnable() {
            public void run() {
                try {
                    new AnalyzeYandiBBService().execute();
                    new AnalyzeYandiBBService().execute();
                    new AnalyzeJcjyService().execute();
//                    new AnalyzeFeiGongNengService().execute();
//                    new AnalyzeAbiService().execute();

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("MainTask.runnable1时出错：",e.fillInStackTrace());
                }
            }
        };

        service.scheduleWithFixedDelay(analyzeJcjyRun,INIT_WTIME,AFTER_WTIME_LONG,UNIT);

    }
}
