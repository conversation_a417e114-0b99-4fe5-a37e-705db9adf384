package cn.wingcloud.task.service;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.wingcloud.AnalyzeMainApplication;
import cn.wingcloud.beetlsql.JFinalBeetlSqlTo;
import cn.wingcloud.dto.PlanSfjlDto;
import cn.wingcloud.pojo.PlanSfjhDetails;
import cn.wingcloud.util.DateConvert;
import org.beetl.sql.core.DSTransactionManager;
import org.beetl.sql.core.SQLManager;
import org.beetl.sql.core.SqlId;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class AnalyzeAbiService {

    private static final Log log = Logs.get();

    public void execute(){

        if (!AnalyzeMainApplication.DEBUG) {
            try {
                if(!DateConvert.isRequireTwoTime("22:40","23:59","00:00","05:50")){
                    log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeAbiService.isRequireTwoTime >>  %s ", "不在运行时间段内{\"22:40\"——\"00:00\" || \"00:00\"——\"05:50\"}");
                    return;
                }
            } catch (ParseException e) {
                log.errorf(">>>>>>>>>>>>>>>>>>>AnalyzeAbiService.isRequireTwoTime >> ERROR —— %s ", e.getMessage(),e);
                return;
            }
        }

        long stime = SystemClock.now();
        log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeAbiService.execute >> 开始执行 - %s <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<", stime);

        try {

            SQLManager toSqlManger = JFinalBeetlSqlTo.dao();

            String nowDate = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN);
            String startDate = nowDate +" 00:00:00";
            String endDate =   nowDate +" 23:59:59";

            MapBuilder param = MapUtil.builder();
            param.put("startDate",startDate).put("endDate",endDate);



            List<PlanSfjlDto> itemList = toSqlManger.select(SqlId.of("Abi","analyzeList"),PlanSfjlDto.class,MapUtil.builder(param.map()).build());
            long totalCount = itemList.size();//总记录

            log.infof("totalCount：%s",totalCount);

            if(totalCount > 0){

                List<PlanSfjlDto> itemInsertList = new ArrayList<>();
                List<PlanSfjhDetails> itemJhUpdateList = new ArrayList<>();
                itemList.stream().forEach(sfjl -> {
                    if(null != sfjl.getResult() || null != sfjl.getFwsj()){
                        itemInsertList.add(sfjl);

                        PlanSfjhDetails upDetail  = new PlanSfjhDetails();
                        upDetail.setId(sfjl.getDetailid());
                        upDetail.setRecordid(sfjl.getId());
                        upDetail.setIscomplete(1L);
                        itemJhUpdateList.add(upDetail);
                    }

                });
                if(itemInsertList.size() > 0){

                    DSTransactionManager.start();

                    toSqlManger.insertBatch(PlanSfjlDto.class,itemInsertList);
                    toSqlManger.updateBatchTemplateById(PlanSfjhDetails.class,itemJhUpdateList);

                    DSTransactionManager.commit();
                }
            }
        } catch (Exception e) {
            log.error(e,e.getCause());
            try {
                DSTransactionManager.rollback();
            } catch (SQLException e1) {
                e1.printStackTrace();
            }
        } finally {
            DSTransactionManager.clear();
        }
        log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeAbiService.execute >> -数据执行结束 - %s 耗时：%s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<",SystemClock.now(), DateUtil.formatBetween(SystemClock.now()-stime, BetweenFormatter.Level.MILLISECOND));

    }
}
