package cn.wingcloud.task.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.wingcloud.AnalyzeMainApplication;
import cn.wingcloud.beetlsql.JFinalBeetlSqlTo;
import cn.wingcloud.dto.LabDto;
import cn.wingcloud.dto.PacsDto;
import cn.wingcloud.dto.PlanSfjlDto;
import cn.wingcloud.pojo.PlanSfjhDetails;
import cn.wingcloud.util.DateConvert;
import com.alibaba.fastjson.JSONObject;
import org.beetl.sql.core.DSTransactionManager;
import org.beetl.sql.core.SQLManager;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.page.DefaultPageRequest;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.core.page.PageResult;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

public class AnalyzeJcjyService {

    private static final Log log = Logs.get();

    public void execute(){

        if (!AnalyzeMainApplication.DEBUG) {
            try {
                if(!DateConvert.isRequireTwoTime("22:40","23:59","00:00","05:50")){
                    log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeJcjyService.isRequireTwoTime >>  %s ", "不在运行时间段内{\"22:40\"——\"00:00\" || \"00:00\"——\"05:50\"}");
                    return;
                }
            } catch (ParseException e) {
                log.errorf(">>>>>>>>>>>>>>>>>>>AnalyzeJcjyService.isRequireTwoTime >> ERROR —— %s ", e.getMessage(),e);
                return;
            }
        }

        long stime = SystemClock.now();
        log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeJcjyService.execute >> 开始执行 - %s <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<", stime);

        try {

            SQLManager toSqlManger = JFinalBeetlSqlTo.dao();

            String nowDate = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN);
            String startDate = nowDate +" 00:00:00";
            String endDate =   nowDate +" 23:59:59";

            MapBuilder param = MapUtil.builder();
            param.put("startDate",startDate).put("endDate",endDate);

            int pageSize = 5000;
            long page = 1;

            PageRequest pageRequest = DefaultPageRequest.of(page,pageSize,true,false);
            PageResult<PlanSfjlDto> pageResult = toSqlManger.pageQuery(SqlId.of("Analyze","analyzeList"),PlanSfjlDto.class,MapUtil.builder(param.map()).build(),pageRequest);

            long totalCount = pageResult.getTotalRow();//总记录
            long totalPage = pageResult.getTotalPage();//总页数

            log.infof("分页：%s",totalPage + " / " + totalCount);

            if(totalPage >= 1){

                List<PlanSfjlDto> itemInsertList = new ArrayList<>();
                List<PlanSfjhDetails> itemJhUpdateList = new ArrayList<>();

                for (long i = page; i <= totalPage; i++) {

                    PageRequest itemPageRequest = DefaultPageRequest.of(i,pageSize,false,true);
                    PageResult<PlanSfjlDto> itemPageResult = toSqlManger.pageQuery(SqlId.of("Analyze","analyzeList"),PlanSfjlDto.class,MapUtil.builder(param.map()).build(),itemPageRequest);
                    List<PlanSfjlDto> itemList = itemPageResult.getList();
                    itemList.stream().forEach(sfjl -> {

                        if(null != sfjl.getPacsid() || null != sfjl.getLabid()){

                            JSONObject gson = new JSONObject();
                            LabDto lab = BeanUtil.copyProperties(sfjl,LabDto.class);
                            PacsDto pacs = BeanUtil.copyProperties(sfjl,PacsDto.class);
                            if(null != lab && null != lab.getLabid()){
                                gson.put("lab",lab);
                            }
                            if(null != pacs && null != pacs.getPacsid()){
                                gson.put("pacs",pacs);
                            }
                            if(!gson.isEmpty()){
                                sfjl.setGson(gson.toJSONString());
                            }

                            itemInsertList.add(sfjl);

                            PlanSfjhDetails upDetail  = new PlanSfjhDetails();
                            upDetail.setId(sfjl.getDetailid());
                            upDetail.setRecordid(sfjl.getId());
                            upDetail.setIscomplete(1L);

                            itemJhUpdateList.add(upDetail);
                        }

                    });

                }
                if(itemInsertList.size() > 0){

                    DSTransactionManager.start();

                    toSqlManger.insertBatch(PlanSfjlDto.class,itemInsertList);
                    toSqlManger.updateBatchTemplateById(PlanSfjhDetails.class,itemJhUpdateList);

                    DSTransactionManager.commit();
                }
            }
        } catch (Exception e) {
            log.error(e,e.getCause());
            try {
                DSTransactionManager.rollback();
            } catch (SQLException e1) {
                e1.printStackTrace();
            }
        } finally {
            DSTransactionManager.clear();
        }
        log.infof(">>>>>>>>>>>>>>>>>>>AnalyzeJcjyService.execute >> -数据执行结束 - %s 耗时：%s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<",SystemClock.now(), DateUtil.formatBetween(SystemClock.now()-stime, BetweenFormatter.Level.MILLISECOND));

    }
}
