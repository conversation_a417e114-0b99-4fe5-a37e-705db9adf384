
# ä¸è½½å°åï¼
# https://github.com/undertow-io/undertow/blob/master/examples/src/main/resources/logging.properties

#
# JBoss, Home of Professional Open Source.
# Copyright 2012 Red Hat, Inc., and individual contributors
# as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Additional logger names to configure (root logger is always configured)
loggers=org.xnio.listener,org.xnio.ssl,org.apache,io.undertow.util.TestHttpClient

# Root logger configuration
logger.level=${test.level:ERROR}
logger.handlers=CONSOLE

# Console handler configuration
handler.CONSOLE=org.jboss.logmanager.handlers.ConsoleHandler
handler.CONSOLE.properties=autoFlush,target
handler.CONSOLE.target=SYSTEM_ERR
handler.CONSOLE.level=ALL
handler.CONSOLE.autoFlush=true
handler.CONSOLE.formatter=PATTERN

# The log format pattern
formatter.PATTERN=org.jboss.logmanager.formatters.PatternFormatter
formatter.PATTERN.properties=pattern
formatter.PATTERN.pattern=%d{HH:mm:ss,SSS} %-5p (%t) [%c] <%F:%L> %m%n

logger.org.xnio.listener.level=DEBUG

logger.org.xnio.ssl.level=DEBUG

logger.org.apache.level=WARN
logger.org.apache.useParentHandlers=false
logger.io.undertow.util.TestHttpClient.level=WARN