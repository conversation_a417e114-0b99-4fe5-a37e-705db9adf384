analyzeCols
===
* 获取最新时间
```sql
    cast(REPLACE(NEWID(), '-', '') as varchar(32)) AS id,s.planid,s.id as detailid,s.parentid,p.id as patientid,p.name, p.idcard, s.proid, s.proname, s.executetime, s.endtime,
    '特检科' AS hisdoc,
    'bc6c1cf649464aea902d2cc153a77396' AS orgid,
    '106000' AS orgcode,
    '满庄镇卫生院' AS orgname,
    '超级管理员' AS createname,
    '000000000000000000000000000000000000' AS createid,
    CONVERT(VARCHAR,GETDATE(),20) as createtime,
    TJK.*
```

analyzeList
===
* 获取最新时间
```sql

    SELECT

    -- @pageTag(){
    #{use("analyzeCols")}
    -- @}
        
    from thasd_plan_sfjh_details s WITH(NOLOCK) INNER JOIN thasd_patients p WITH(NOLOCK)  ON s.patientid = p.id
    -- @pageIgnoreTag(){
    OUTER APPLY(
        SELECT top 1 JSON_VALUE(CONVERT(varchar(max),hl.content), '$.data.results.disease') as result,CONVERT ( VARCHAR, hl.jcrq, 23 ) AS fwsj FROM BPHS.dbo.app_tjks_check hl WITH(NOLOCK)
        WHERE hl.category = 1 and JSON_VALUE(CONVERT(varchar(max),hl.content), '$.data.patient.id_number') collate Chinese_PRC_90_CI_AI = p.idcard and hl.jcrq collate Chinese_PRC_90_CI_AI >= s.executetime and hl.jcrq collate Chinese_PRC_90_CI_AI <= s.endtime
        ORDER BY hl.jcrq desc
    ) AS [TJK]
    -- @}
    WHERE s.isdel = 0 AND s.proid = '628465d95b755ad9d641dcae' AND s.executetime <= #{startDate} AND s.endtime >= #{endDate}  AND s.iscomplete IS NULL and TJK.fwsj is not null
    -- @pageIgnoreTag(){
    order by s.executetime asc
    -- @}

```


	
	