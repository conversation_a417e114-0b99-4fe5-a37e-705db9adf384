package cn.wingcloud.annotation;

import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE,ElementType.METHOD})
public @interface MethodMenu {

    MenuExEnum menu();

    long visible0() default 1;//某菜单普通用户是否可见:0-不可见，1-可见
    long visible2() default 1;//某菜单二级管理员用户是否可见:0-不可见，1-可见

    String url();
    String subf() default "";

    MenuEnum parent() default MenuEnum.ROOT;

}
