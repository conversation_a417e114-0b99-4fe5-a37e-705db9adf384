@echo off

set APPLICATION=thasd-gateway

set APPLICATION_JAR=%APPLICATION%.jar

title  %APPLICATION%

set BASE_PATH=%~dp0

set CONFIG=%BASE_PATH%config/;%BASE_PATH%/lib/*
set CONFIG_DIR=%BASE_PATH%config/

set LOG_DIR=%BASE_PATH%logs
set LOG_FILE=%APPLICATION%.log
set LOG_PATH=%LOG_DIR%\%LOG_FILE%
if not exist %LOG_DIR% (
    md %LOG_DIR%
)
echo %BASE_PATH%
echo %LOG_PATH%

set JAVA_OPT=-server -Xms512m -Xmx512m -Xmn1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:SoftRefLRUPolicyMSPerMB=512
set JAVA_OPT=%JAVA_OPT% -XX:-OmitStackTraceInFastThrow

cd %BASE_PATH%

%1 mshta vbscript:CreateObject("WScript.Shell").Run("%~s0 ::",0,FALSE)(window.close)&&exit

java %JAVA_OPT% -Dfile.encoding=utf-8 -jar %BASE_PATH%%APPLICATION_JAR% --spring.config.location=%CONFIG_DIR% > %LOG_PATH% 2>&1 &

exit