<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>thasd</artifactId>
        <groupId>cn.wingcloud</groupId>
        <version>lc.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>thasd-gateway</artifactId>
    <groupId>cn.wingcloud</groupId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <spring.boot.version>2.4.4</spring.boot.version>
        <spring.cloud.version>2020.0.2</spring.cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <hutool.version>5.3.5</hutool.version>
        <logstash.version>6.1</logstash.version>
        <jwt.version>0.9.1</jwt.version>
        <bouncycastle.version>1.65.01</bouncycastle.version>
        <xnio-nio.version>3.8.0.Final</xnio-nio.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>2.4.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--logstash日志记录客户端-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash.version}</version>
        </dependency>
        <!-- fast json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.netflix.servo</groupId>-->
<!--            <artifactId>servo-core</artifactId>-->
<!--            <version>0.12.7</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.netflix.archaius</groupId>-->
<!--            <artifactId>archaius-core</artifactId>-->
<!--            <version>0.7.6</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.google.guava</groupId>-->
<!--                    <artifactId>guava</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
    </dependencies>

<!--    <build>-->
<!--        <resources>-->
<!--            <resource>-->
<!--                <directory>src/main/resources</directory>-->
<!--                &lt;!&ndash;开启过滤，用指定的参数替换directory下的文件中的参数&ndash;&gt;-->
<!--                <filtering>true</filtering>-->
<!--            </resource>-->
<!--        </resources>-->
<!--        <plugins>-->
<!--            &lt;!&ndash; 在这里添加springloader plugin&ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <version>${spring.boot.version}</version>-->
<!--                <configuration>-->
<!--                    <layout>ZIP</layout>-->
<!--                    <fork>true</fork>-->
<!--                    <addResources>true</addResources>-->
<!--                    <profiles>${activeProject}</profiles>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>repackage</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--            &lt;!&ndash;-->
<!--                使用 mvn clean package 打包-->
<!--                更多配置可参考官司方文档：http://maven.apache.org/plugins/maven-assembly-plugin/single-mojo.html-->
<!--            &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-assembly-plugin</artifactId>-->
<!--                <version>3.1.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>make-assembly</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>single</goal>-->
<!--                        </goals>-->

<!--                        <configuration>-->
<!--                            &lt;!&ndash; 打包生成的文件名 &ndash;&gt;-->
<!--                            &lt;!&ndash; jar 等压缩文件在被打包进入 zip、tar.gz 时是否压缩，设置为 false 可加快打包速度 &ndash;&gt;-->
<!--                            <recompressZippedFiles>false</recompressZippedFiles>-->
<!--                            &lt;!&ndash; 打包生成的文件是否要追加 package.xml 中定义的 id 值 &ndash;&gt;-->
<!--                            <appendAssemblyId>true</appendAssemblyId>-->
<!--                            &lt;!&ndash; 指向打包描述文件 package.xml &ndash;&gt;-->
<!--                            <descriptors>-->
<!--                                <descriptor>package.xml</descriptor>-->
<!--                            </descriptors>-->
<!--                            &lt;!&ndash; 打包结果输出的基础目录 &ndash;&gt;-->
<!--                            <outputDirectory>${project.build.directory}/</outputDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->

    <build>
        <!-- 打包后的启动jar名称 -->
        <finalName>thasd-gateway</finalName>
        <resources>
            <!-- 资源文件配置 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-${activeProject}.yml</include>
                    <include>bootstrap.yml</include>
                    <include>*.xml</include>
                    <include>**/*.xml</include>
                    <include>*.properties</include>
                    <include>**/*.properties</include>
                    <include>*.txt</include>
                </includes>
            </resource>
            <!--解决切换配置文件不生效问题-->
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**/*.yml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <!-- 在这里添加springloader plugin-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <layout>ZIP</layout>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                    <includes>
                        <!-- 项目启动jar包中排除依赖包 -->
                        <include>
                            <groupId>non-exists</groupId>
                            <artifactId>non-exists</artifactId>
                        </include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <!-- 项目启动类 -->
                            <mainClass>cn.wingcloud.GatewayApplication</mainClass>
                            <!-- 依赖的jar的目录前缀 -->
                            <classpathPrefix>./lib</classpathPrefix>
                            <addClasspath>true</addClasspath>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./config/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <includes>
                        <!-- 只打包指定目录的文件 -->
                        <include>cn/wingcloud/**</include>
                        <!--解决日志文件找不到问题-->
                        <include>*.xml</include>
                        <include>**/*.xml</include>
                        <include>*.properties</include>
                    </includes>
                </configuration>
            </plugin>
            <!-- 该插件的作用是用于复制指定的文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/config/</outputDirectory>
                            <useDefaultDelimiters>false</useDefaultDelimiters>
                            <delimiters>
                                <delimiter>@</delimiter>
                            </delimiters>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>**/*.yml</include>
                                        <include>**/*.xml</include>
                                        <include>**/*.properties</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <!--会使用默认的占位符，增加此配置项后就没问题了 '@profileActive@'-->
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                    <resources>
                        <resource>
                            <directory>src/main/resources/</directory>
                            <filtering>true</filtering>
                        </resource>
                    </resources>
                </configuration>
            </plugin>
            <!-- 该插件的作用是用于复制依赖的jar包到指定的文件夹里 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <includeScope>compile</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
<!--            &lt;!&ndash; 打包时跳过测试 &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <skipTests>true</skipTests>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <!--
                使用 mvn clean package 打包
                更多配置可参考官司方文档：http://maven.apache.org/plugins/maven-assembly-plugin/single-mojo.html
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>

                        <configuration>
                            <!-- 打包生成的文件名 -->
                            <!-- jar 等压缩文件在被打包进入 zip、tar.gz 时是否压缩，设置为 false 可加快打包速度 -->
                            <recompressZippedFiles>false</recompressZippedFiles>
                            <!-- 打包生成的文件是否要追加 package.xml 中定义的 id 值 -->
                            <appendAssemblyId>true</appendAssemblyId>
                            <!-- 指向打包描述文件 package.xml -->
                            <descriptors>
                                <descriptor>package.xml</descriptor>
                                <descriptor>package_updata.xml</descriptor>
                            </descriptors>
                            <!-- 打包结果输出的基础目录 -->
                            <outputDirectory>${project.build.directory}/</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>