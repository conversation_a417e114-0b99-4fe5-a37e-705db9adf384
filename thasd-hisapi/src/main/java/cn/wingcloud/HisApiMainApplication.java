package cn.wingcloud;

import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.util.IpUtil;
import cn.wingcloud.util.SocketUtils;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.server.undertow.UndertowServer;
import org.nutz.log.Log;
import org.nutz.log.Logs;

public class HisApiMainApplication {

    private static final Log log = Logs.get();

    private static Prop profile;
    static String activeProject = "prod";

    /**
     * 启动入口，运行此 main 方法可以启动项目
     */
    public static void main(String[] args) {
        int port = SocketUtils.findAvailableTcpPort(9151,9160);
        String ipAddress = IpUtil.getIpAddress();

        System.setProperty("undertow.port",String.valueOf(port));
        System.setProperty("upload.ip",ipAddress);

        if (profile == null) {
            profile = PropKit.useFirstFound( "env.properties");
            activeProject = profile.get("activeProject");
            log.infof("profile: @%s",activeProject);
        }
        String undertowConfig = "undertow-"+activeProject+".txt";
        UndertowServer
                .createByJFinalFilter("Thasd HisApi",SysConfig.class,undertowConfig)
                .configWeb(webBuilder -> {
                    webBuilder.add404ErrorPage("/error/404.html");
                    webBuilder.addErrorPage(403,"/error/403.html");
                    webBuilder.addErrorPage(500,"/error/500.html");
                }).setPort(port).start();

    }


}
