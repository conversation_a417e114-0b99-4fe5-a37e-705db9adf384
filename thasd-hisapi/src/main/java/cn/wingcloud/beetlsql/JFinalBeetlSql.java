package cn.wingcloud.beetlsql;

import com.jfinal.kit.Prop;
import com.jfinal.plugin.druid.DruidPlugin;
import org.beetl.sql.clazz.NameConversion;
import org.beetl.sql.core.Interceptor;
import org.beetl.sql.core.SQLManager;
import org.beetl.sql.core.SQLManagerBuilder;
import org.beetl.sql.core.db.DBStyle;
import org.beetl.sql.core.db.SqlServer2012Style;
import org.beetl.sql.core.loader.MarkdownClasspathLoader;

import javax.sql.DataSource;

/**
 *
 * <AUTHOR>
 */
public class JFinalBeetlSql {

    static SQLManager sqlManager = null;
    static String nc = null;
    static String sqlRoot = null;
    static String dbStyle = null;
    static String[] ins = null;
    static JFinalConnectonSource ds = null;

    public static void init( Prop properties) {
        DruidPlugin druidPlugin = new DruidPlugin(properties.get("jdbcUrl"), properties.get("user"), properties.get("password").trim());
        setPlugin(druidPlugin);
        druidPlugin.start();
        ds = new JFinalConnectonSource(druidPlugin.getDataSource(), null);
        initProp(properties);
        initSQLManager();


    }

    public static void init(DataSource master, DataSource[] slaves,Prop properties) {
        ds = new JFinalConnectonSource(master, slaves);
        initProp(properties);
        initSQLManager();

    }



    private static void initProp(Prop properties) {
        nc = properties.get("sql.nc", org.beetl.sql.core.DefaultNameConversion.class.getName());
        sqlRoot = properties.get("sql.root", "sql");
        String interceptors = properties.get("sql.interceptor");
        System.out.println("================="+interceptors);
        ins = null;
        if (interceptors != null) {
            ins = interceptors.split(",");
        }
        dbStyle = properties.get("sql.dbStyle", SqlServer2012Style.class.getName());
    }

    private static void initSQLManager() {

        DBStyle dbStyleIns = (DBStyle) instance(dbStyle);
        MarkdownClasspathLoader sqlLoader = new MarkdownClasspathLoader(sqlRoot);
        NameConversion ncIns = (NameConversion) instance(nc);
        Interceptor[] inters = null;
        if (ins != null) {
            inters = new Interceptor[ins.length];
            //add suxj 2015/08/25
            for (int i = 0; i < inters.length; i++) {
                inters[i] = (Interceptor) instance(ins[i]);
            }
        } else {
            inters = new Interceptor[0];
        }

        SQLManagerBuilder builder = new SQLManagerBuilder(ds);
        builder.setNc(ncIns);
        builder.setInters(inters);
        builder.setDbStyle(dbStyleIns);
        builder.setSqlLoader(sqlLoader);
        sqlManager = builder.build();

    }

    private static Object instance(String clsName) {
        Object c;
        try {
            c = Class.forName(clsName).newInstance();
        } catch (Exception e) {
            throw new RuntimeException("初始化类错误" + clsName, e);
        }
        return c;
    }


    public static SQLManager dao() {
        return sqlManager;
    }

    protected static void setPlugin(DruidPlugin druidPlugin){

        druidPlugin.setInitialSize(20);
        druidPlugin.setMinIdle(32);
        druidPlugin.setMaxActive(150);

        druidPlugin.setTimeBetweenConnectErrorMillis(30*1000);
        //配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位毫秒
        druidPlugin.setTimeBetweenEvictionRunsMillis(60000);
        druidPlugin.setValidationQuery("select 1");
        //此项配置为true即可，不影响性能，并且保证安全性。意义为：申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
        druidPlugin.setTestWhileIdle(true);
        //这里建议配置为TRUE，防止取到的连接不可用。获取链接的时候，不校验是否可用，开启会有损性能-
        druidPlugin.setTestOnBorrow(false);
        // 归还链接到连接池的时候校验链接是否可用
        druidPlugin.setTestOnReturn(false);
        //链接使用超过时间限制是否回收
        druidPlugin.setRemoveAbandoned(false);
        druidPlugin.setRemoveAbandonedTimeoutMillis(300*1000);

    }
}