package cn.wingcloud.jfinal.action;

import cn.wingcloud.common.response.ErrorResponseData;
import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.service.PatientsService;
import cn.wingcloud.service.impl.PatientsServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.core.Controller;
import org.nutz.log.Log;
import org.nutz.log.Logs;

/**
 * 三高接口
 * 
 * <AUTHOR>
 * @Date 2022-04-11
 */
public class HisController extends Controller {

    private static final Log log = Logs.get();

    @Inject(PatientsServiceImpl.class)
    private PatientsService patientsService;

    public void index() {
        renderError(403);
    }

    /**
     * 获取当前患者信息
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void get() {
        ResponseData responseData = patientsService.get();
        renderJson(responseData);
    }

    /**
     * 获取当前患者信息
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void getPatientId() {
        ResponseData responseData = patientsService.getPatientId();
        renderJson(responseData);
    }

    /**
     * 获取患者信息
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void getAll() {
        ResponseData responseData = patientsService.getAll();
        renderJson(responseData);
    }

    public void getStatus() {
        ResponseData responseData = patientsService.getStatus();
        renderJson(responseData);
    }

    public void getPlan() {
        ResponseData responseData = patientsService.getPlan();
        renderJson(responseData);
    }

    public void viewPlan() {
        String url = patientsService.viewPlan();
        if(url.startsWith("http")){
            redirect(url);
        }else {
            renderJson(new ErrorResponseData(url));
        }

    }


    /**
     * 获取患者信息
     * 测试 370911194712126032 郭有生
     * http://localhost:9000/loginForHisByXyy?patientid=002ecf61b7cc4fdb8338b52f261c520f&username=hisx000001
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void getPatientApply() {
        ResponseData responseData = patientsService.getPatientApply();
        renderJson(responseData);
    }

}
