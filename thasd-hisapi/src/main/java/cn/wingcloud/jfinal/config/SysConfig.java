package cn.wingcloud.jfinal.config;

import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.common.CustomRenderFactory;
import cn.wingcloud.jfinal.plugin.NacosPlugin;
import cn.wingcloud.jfinal.routes.DefaultRoutes;
import cn.wingcloud.jfinal.routes.BusRoutes;
import com.jfinal.config.*;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.template.Engine;
import org.nutz.log.Log;
import org.nutz.log.Logs;

/**
 * JFINAL API引导式配置
 */
public class SysConfig extends JFinalConfig {
	
	private static final Log log = Logs.get();

	static Prop p;

	/**
	 * PropKit.useFirstFound(...) 使用参数中从左到右最先被找到的配置文件
	 * 从左到右依次去找配置，找到则立即加载并立即返回，后续配置将被忽略
	 */
	static void loadConfig() {
		if (p == null) {
			Prop profile = PropKit.useFirstFound( "env.properties");
			p = PropKit.use("config-"+profile.get("activeProject","prod")+".txt");
		}
	}

	/**
	 * 配置常量
	 */
	@Override
	public void configConstant(Constants me) {
		loadConfig();
		me.setDevMode(p.getBoolean("devMode", false));
		me.setRenderFactory(new CustomRenderFactory());
		// 配置 aop 代理使用 cglib，否则将使用 jfinal 默认的动态编译代理方案
		me.setToCglibProxyFactory();
		// 配置依赖注入
		me.setInjectDependency(true);
		// 配置依赖注入时，是否对被注入类的超类进行注入
		me.setInjectSuperClass(true);
	}
	
	/**
	 * 配置路由
	 */
	@Override
	public void configRoute(Routes me) {
		me.add(new DefaultRoutes());
		me.add(new BusRoutes());
	}
	
	/**
	 * 配置插件
	 */
	@Override
	public void configPlugin(Plugins me) {
		NacosPlugin nacosPlugin = new NacosPlugin(p.get("nacos.serverAddr",""));
		nacosPlugin.start();
		JFinalBeetlSql.init(p);
	}

	/**
	 * 配置全局拦截器
	 */
	@Override
	public void configInterceptor(Interceptors me) {
	}

	/**
	 * 配置处理器
	 */
	@Override
	public void configHandler(Handlers me) {
	}

	@Override
	public void configEngine(Engine me) {
		
	}
	
}
