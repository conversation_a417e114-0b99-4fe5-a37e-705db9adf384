package cn.wingcloud.jfinal.interceptor;

import cn.wingcloud.common.response.ErrorResponseData;
import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.exception.ApiServiceException;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;

public class ExceptionInterceptor implements Interceptor {

    @Override
    public void intercept(Invocation invocation) {

        Controller controller = invocation.getController();

        try {

            invocation.invoke();

        } catch (ApiServiceException e){
            e.printStackTrace();
            controller.renderJson(new ErrorResponseData(e.getCode(),e.getErrorMessage()));
        } catch (Exception e) {
            e.printStackTrace();
            controller.renderJson(new ErrorResponseData(ResponseData.DEFAULT_ERROR_CODE,new StringBuilder(ResponseData.DEFAULT_ERROR_MESSAGE).append("-").append(e.getMessage()).toString()));
        } finally {

        }
    }

}
