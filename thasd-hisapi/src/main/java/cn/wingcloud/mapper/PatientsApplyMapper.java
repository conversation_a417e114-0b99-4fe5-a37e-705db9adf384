package cn.wingcloud.mapper;

import cn.wingcloud.pojo.PatientsApply;
import org.beetl.sql.core.page.DefaultPageResult;
import org.beetl.sql.core.page.PageRequest;
import org.beetl.sql.mapper.BaseMapper;

import java.util.Map;

/**
 * 三高患者
 * 
 * <AUTHOR>
 * @Date 2022-03-28
 */
public interface PatientsApplyMapper extends BaseMapper<PatientsApply> {
    DefaultPageResult<PatientsApply> pageLst(Map<String, Object> paras, PageRequest<PatientsApply> pageQuery);
}
