package cn.wingcloud.service;

import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.pojo.Patients;

public interface PatientsService extends BaseService{

    ResponseData get();
    ResponseData getPatientId();
    ResponseData getAll();
    ResponseData getStatus();
    ResponseData getPlan();
    String viewPlan();
    /**
     * 第一人民医院专用
     * @return
     */
    ResponseData getPatientApply();

}
