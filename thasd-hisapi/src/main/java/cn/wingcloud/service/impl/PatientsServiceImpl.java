package cn.wingcloud.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.beetlsql.Trans;
import cn.wingcloud.common.response.ErrorResponseData;
import cn.wingcloud.common.response.RdSuccessResponseData;
import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.common.response.SuccessResponseData;
import cn.wingcloud.dto.ServeItemHisDto;
import cn.wingcloud.exception.ApiServiceException;
import cn.wingcloud.jfinal.interceptor.ReqBodyHolder;
import cn.wingcloud.mapper.PatientsApplyMapper;
import cn.wingcloud.mapper.PatientsMapper;
import cn.wingcloud.pojo.*;
import cn.wingcloud.service.PatientsService;
import cn.wingcloud.util.DateConvert;
import cn.wingcloud.util.ParamsUtil;
import com.jfinal.aop.Before;
import org.beetl.sql.core.SQLReady;
import org.beetl.sql.core.SqlId;
import org.beetl.sql.core.engine.template.SQLTemplateEngine;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.*;

/**
 * 三高患者
 * 
 * <AUTHOR>
 * @Date 2022-03-28
 */
public class PatientsServiceImpl extends BaseServiceImpl implements PatientsService {

    private static final Log log = Logs.get();

    @Override
    @Before(Trans.class)
    public ResponseData get() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String id = requestParams.getPara("id");
            Patients patients = getMapper().unique(id);
            return new RdSuccessResponseData(patients);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者信息时出错：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    @Before(Trans.class)
    public ResponseData getAll() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String idcard = requestParams.getPara("idcard",null);
            String username = requestParams.getPara("username",null);
            String nowTime = DateConvert.getDateString(DateConvert.YMD);

            Config config = getMapper().getSQLManager().unique(Config.class,"patientUrl");
            Patients patients = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getAll"),MapUtil.builder().put("idcard",idcard).build(),Patients.class);

            if(null == patients){
                Map<String,Object> result = new HashMap<>();
                result.put("noticeList",null);
                result.put("noticeInfo", "");
                result.put("patientUrl","");
                result.put("patientInfo","未建档");
                return new SuccessResponseData(result);
            }

            String patientUrl = config.getParam() + "?patientid=" +patients.getId() + "&username=" + username;
            List<Notice> notice = getMapper().getSQLManager().select(SqlId.of("Patients","getNotice"),Notice.class,MapUtil.builder().put("idcard",idcard).put("nowTime",nowTime).build());
            String jwsjb = Optional.ofNullable(patients.getJwsjb()).orElseGet(() -> "");

            String[] jwsjbArr = jwsjb.split(",");
            boolean isgxy = (null != patients && patients.getIsgxy() == 1) ? true : false;
            boolean istnb = (null != patients && patients.getIstnb() == 1) ? true : false;
            boolean isgxz = (null != patients && patients.getIsgxz() == 1) ? true : false;
            for (String key : jwsjbArr) {
                if(!key.equals("")){
                    if(key.equals("2")){
                        isgxy = true;
                    }
                    if(key.equals("3")){
                        istnb = true;
                    }
                    if(key.equals("31")){
                        isgxz = true;
                    }
                }
            }
            Map<String,Object> result = new HashMap<>();
            result.put("noticeList",notice);
            result.put("noticeInfo", "复诊计划: " + (notice.size() > 0 ? "到期" : "无"));
            result.put("patientUrl",patientUrl);
            result.put("patientInfo",getHzlx(patients,isgxy,istnb,isgxz));
            return new SuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者信息时出错-HisApi：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    @Before(Trans.class)
    public ResponseData getStatus() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String idcard = requestParams.getPara("idcard",null);

            Map<String,Object> patients = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getStatus"),MapUtil.builder().put("idcard",idcard).build(),Map.class);

            if(null == patients){
                Map<String,Object> result = new HashMap<>();
                result.put("pmtype","未建档");
                result.put("pmtypecode", "-1");
                return new SuccessResponseData(result);
            }else{
                if(patients.get("pmtypecode").equals("0")){
                    patients.put("pmtype","已纳入三高共管");
                }else{
                    patients.put("pmtype","未纳入三高共管");
                }
            }
            return new SuccessResponseData(patients);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者状态时出错-HisApi：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    @Before(Trans.class)
    public ResponseData getPlan() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String idcard = requestParams.getPara("idcard",null);
            Plan plan = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getPlan"),MapUtil.builder().put("idcard",idcard).build(),Plan.class);
            if(null == plan){
                Map<String,Object> result = new HashMap<>();
                result.put("fzjhList",new ArrayList<>());
                result.put("jkzd",new ArrayList<>());
                result.put("yyzd",new ArrayList<>());
                return new SuccessResponseData(result);
            }else{
                Map<String,Object> result = new HashMap<>();
                List<ServeItemHisDto> jkzdList = new ArrayList<>();
                List<ServeItemHisDto> itemList = getMapper().getSQLManager().execute("select name as itemname,val as itemcode from biz_basic_dic_data where diccode = 'plan_item' and val in ('01','02','03','05','8888') order by sortcode asc", ServeItemHisDto.class,null);
                itemList.forEach(serveItemDto -> {
                    List<Map> itemTplList = getMapper().getSQLManager().execute("select content from thasd_plan_jkzd where isdel = 0 and planid = '"+plan.getId()+"' and itemcode = '"+serveItemDto.getItemcode()+"' order by sortcode asc", Map.class,null);
                    serveItemDto.setList(itemTplList);
                    jkzdList.add(serveItemDto);
                });
                ServeItemHisDto yyzd = new  ServeItemHisDto();
                yyzd.setItemcode("yyzd");
                yyzd.setItemname("用药指导");
                List<Map> yyzdTplList = getMapper().getSQLManager().execute("select ywmc,yypl,yyjl,yyff,yytype from thasd_plan_yyzd where isdel = 0 and planid = '"+plan.getId()+"' order by sortcode asc", Map.class,null);
                yyzd.setList(yyzdTplList);
                jkzdList.add(yyzd);
                List<Map> sfjhDetailList = getMapper().getSQLManager().execute("select id,proname,prorate,executetime,endtime  from thasd_plan_sfjh_details p where isdel = 0 and planid = '"+plan.getId()+"' and iscomplete is null order by executetime asc,sortcode asc", Map.class,null);
                result.put("fzjhList",sfjhDetailList);
                result.put("jkzd",jkzdList);
                return new SuccessResponseData(result);
            }
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者状态时出错-HisApi：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    @Before(Trans.class)
    public String viewPlan() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String idcard = requestParams.getPara("idcard",null);
            Plan plan = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getPlan"),MapUtil.builder().put("idcard",idcard).build(),Plan.class);
            Config config = getMapper().getSQLManager().unique(Config.class,"planUrl");
            if(null == plan){
                return "获取三高患者管理方案时出错-HisApi：该患者管理档案不存在";
            }else{
                String patientUrl = config.getParam() + "?id=" +plan.getId();
                return patientUrl;
            }
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者管理方案时出错-HisApi：").append(e.getMessage()).toString();
            return errorMessage;
        }
    }

    @Override
    @Before(Trans.class)
    public ResponseData getPatientId() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String idcard = requestParams.getPara("idcard",null);
            Patients patients = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getAll"),MapUtil.builder().put("idcard",idcard).build(),Patients.class);
            Map<String,Object> result = new HashMap<>();
            result.put("patientid", patients.getId());
            result.put("stateMsg",null == patients ? "未建档" : "");
            return new SuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者信息时出错：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    @Before(Trans.class)
    public ResponseData getPatientApply() {
        try {

            ParamsUtil requestParams = ReqBodyHolder.get();

            String idcard = requestParams.getPara("idcard",null);
            String username = requestParams.getPara("username",null);
            String dig = requestParams.getPara("dig",null);//2 高血压,3 糖尿病,31 高血脂
            String name = requestParams.getPara("name",null);//姓名

            Users authUser = null;
            try {
                authUser = getMapper().getSQLManager().executeQueryOne(new SQLReady("select * from basic_user where isdel = 0 and hisusername = ?",new Object[]{username}),Users.class);
                if(authUser == null){
                    return new ErrorResponseData(ResponseData.DEFAULT_ERROR_CODE, "传入医师账号错误或未绑定到三高平台");
                }
            } catch (Exception e) {
                return new ErrorResponseData(ResponseData.DEFAULT_ERROR_CODE, "传入医师账号错误或未绑定到三高平台");
            }

            Config config = getMapper().getSQLManager().unique(Config.class,"patientUrlByXyy");
            Config fastAddPatientCfg = getMapper().getSQLManager().unique(Config.class,"fastAddPatient");
            Patients patients = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","getAll"),MapUtil.builder().put("idcard",idcard).build(),Patients.class);

            String addPatientUrl =  fastAddPatientCfg.getParam() == null ? "" :fastAddPatientCfg.getParam() + "?username=" + username + "&idcard=" + idcard+ "&name=" + name;

            if(null == patients){
                Patients checkPatients = checkPatient(idcard);
                if(null == checkPatients){
                    Map<String,Object> result = new HashMap<>();
                    result.put("state", "-1");
                    result.put("stateMsg","未建档");
                    result.put("patientUrl",addPatientUrl);
                    result.put("patientInfo", "未建档");
                    return new SuccessResponseData(ResponseData.DEFAULT_SUCCESS_CODE, "无该患者档案",result);
                }
                patients = checkPatients;
            }

            String patientUrl = config.getParam() + "?patientid=" +patients.getId() + "&username=" + username;
            String jwsjb = Optional.ofNullable(patients.getJwsjb()).orElseGet(() -> "");
            String jwsjbDig =  Optional.ofNullable(dig).orElseGet(() -> "");

            String[] jwsjbArr = jwsjb.split(",");
            boolean isgxy = (null != patients && patients.getIsgxy() == 1) ? true : false;
            boolean istnb = (null != patients && patients.getIstnb() == 1) ? true : false;
            boolean isgxz = (null != patients && patients.getIsgxz() == 1) ? true : false;
            for (String key : jwsjbArr) {
                if(!key.equals("")){
                    if(key.equals("2")){
                        isgxy = true;
                    }
                    if(key.equals("3")){
                        istnb = true;
                    }
                    if(key.equals("31")){
                        isgxz = true;
                    }
                }
            }

            /**
             * 纳入申请
             */
            Map<String,Object> result = new HashMap<>();
            result.put("state", patients.getPmtypecode()); //-1 未建档 0 管理中 1 失访 2 待管理
            result.put("stateMsg",getHzstate(patients.getPmtypecode()));
            result.put("patientUrl",patientUrl);
            result.put("patientInfo",getHzlx(patients,isgxy,istnb,isgxz));

            Patients checkPatient = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","checkPatientOrg"),MapUtil.builder().put("idcard",idcard).put("orgid",authUser.getOrgid()).build(),Patients.class);
            if(null != checkPatient){
                return new SuccessResponseData(ResponseData.DEFAULT_SUCCESS_CODE, "该患者已经纳入本单位管理",result);
            }else{
                int applySize = getMapper().getSQLManager().intValue(SqlId.of("Patients","checkPatientApply"),MapUtil.builder().put("idcard",idcard).put("orgid",authUser.getOrgid()).build());
                if(applySize > 0){
                    return new SuccessResponseData(ResponseData.DEFAULT_SUCCESS_CODE, "该患者已提交申请纳入管理，请工作人员与该患者所属三高基地管理人员进行沟通协调",result);
                }
            }

            updatePatient(patients,isgxy,istnb,isgxz,jwsjbDig);
            apply(patients.getId(),authUser);

            return new SuccessResponseData(ResponseData.DEFAULT_SUCCESS_CODE, "该患者已提交申请纳入管理，请工作人员与该患者所属三高基地管理人员进行沟通协调",result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取三高患者信息时出错-HisApi-getPatient：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }


    private void apply(String patientid,Users authUser) {
        Patients patients = getMapper().getSQLManager().unique(Patients.class,patientid);
        String orgid = patients.getOrgid();
        String orgname = patients.getOrgname();
        String orgidl2 = patients.getOrgidl2();
        String orgnamel2 = patients.getOrgnamel2();
        String orgidl1 = patients.getOrgidl1();
        String orgnamel1 = patients.getOrgnamel1();

        String authUserOrgId = authUser.getOrgid();
        String authUserOrgname = authUser.getOrgname();
        String authUserId = authUser.getId();
        String authUserName = authUser.getName();
        String authUserLoginName = authUser.getUsername();

        PatientsApply patientsApply = BeanUtil.copyProperties(patients, PatientsApply.class);
        patientsApply.init();
        patientsApply.setCreateid(authUserId);
        patientsApply.setCreatename(authUserName);
        patientsApply.setPatientid(patients.getId());

        patientsApply.setOrgidl1(orgidl1);
        patientsApply.setOrgnamel1(orgnamel1);
        patientsApply.setOrgidl2(authUserOrgId);
        patientsApply.setOrgnamel2(authUserOrgname);
        patientsApply.setOrgid(authUserOrgId);
        patientsApply.setOrgname(authUserOrgname);


        patientsApply.setJyuser(authUserLoginName);
        patientsApply.setJyuserid(authUserId);
        patientsApply.setJyusername(authUserName);

        patientsApply.setPorgid(orgid);
        patientsApply.setPorgname(orgname);

        patientsApply.setApplyorgid(orgidl2);
        patientsApply.setApplyorgname(orgnamel2);

        patientsApply.setApplystatue(0);

        PatientsApplyMapper applyMapper = JFinalBeetlSql.dao().getMapper(PatientsApplyMapper.class);
        applyMapper.insert(patientsApply);

    }


    private  String getHzlx(Patients patient,boolean isgxy,boolean istnb,boolean isgxz){
        try {
            if(null == patient){
                return "未建档";
            }
            String tips = "";
            if(isgxy)tips+="高血压,";
            if(istnb)tips+="糖尿病,";
            if(isgxz)tips+="高血脂,";
            if(!tips.equals(""))tips = "(" + tips.replaceFirst(".$", "") +")";
            if(patient.getPmtypecode().equals("1")){
                return "失访患者" + tips;
            }

            if(patient.getPmtypecode().equals("2")){
                return "待管理患者" + tips;
            }

            //0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"
            String bpCode =  patient.getBpgradecode();
            String dbCode =  patient.getDbgradecode();
            String lpCode =  patient.getLpgradecode();

            List<String> names = new ArrayList<>();
            String bpStr = "";
            if(isgxy){
                bpStr = "高血压"+getHzlxStr("",bpCode);
            }else{
                bpStr = getHzlxStr("高血压",bpCode);
            }
            if(null != bpStr && !bpStr.equals(""))names.add(bpStr);


            String dbStr = "";
            if(istnb){
                dbStr = "糖尿病"+getHzlxStr("",dbCode);
            }else{
                dbStr = getHzlxStr("糖尿病",dbCode);
            }
            if(null != dbStr && !dbStr.equals(""))names.add(dbStr);

            String lpStr = "";
            if(isgxz){
                lpStr = "高血脂"+getHzlxStr("",lpCode);
            }else{
                lpStr = getHzlxStr("高血脂",lpCode);
            }
            if(null != lpStr && !lpStr.equals(""))names.add(lpStr);
            return String.join(",",names);
        } catch (Exception e) {
            return "";
        }
    }

    private String getHzlxStr(String name,String code){
        String lp = "";
        String rp = "";
        if(name.equals("")){
            lp = "(";
            rp = ")";
        }
        switch (code){
            case "1":
                return lp+name+"低危" + rp;
            case "2":
                return lp+name+"中危" + rp;
            case "3":
                return lp+name+"高危" + rp;
            case "6":
                return lp+name+"易患" + rp;
            default: return "";
        }
    }

    private String getHzstate(String code){
        //0 管理中 1 失访 2 待管理
        switch (code){
            case "0":
                return "管理中";
            case "1":
                return "失访";
            case "2":
                return "待管理";
            default: return "";
        }
    }


    private boolean getZd(String dig,String caseStr){
        String[] digArr = dig.split(",");
        for (int i = 0; i < digArr.length; i++) {
            String str = digArr[i];
            if(str.equals(caseStr)){
                return true;
            }
        }
        return false;
    }

    private boolean getJwsjb(String[] jwsjbArr,String caseStr){
        for (int i = 0; i < jwsjbArr.length; i++) {
            String str = jwsjbArr[i];
            if(str.equals(caseStr)){
                return true;
            }
        }
        return false;
    }


    private Long getSglevel(String jwsjb){
        boolean isgxy = getZd(jwsjb,"2");
        boolean istnb = getZd(jwsjb,"3");
        boolean isgxz = getZd(jwsjb,"31");
        long cglevel = 0;//三高级别 1高2高3高
        if(isgxy)if(cglevel < 3)cglevel+=1;
        if(istnb)if(cglevel < 3)cglevel+=1;
        if(isgxz)if(cglevel < 3)cglevel+=1;
        return cglevel;
    }


    private void updatePatient(Patients patient,boolean isgxy,boolean istnb,boolean isgxz,String jwsjbDig){

        try {

            Patients updatePatients = new Patients();
            updatePatients.setId(patient.getId());

            boolean isgxyZd = getZd(jwsjbDig,"2");
            boolean istnbZd = getZd(jwsjbDig,"3");
            boolean isgxzZd = getZd(jwsjbDig,"31");

            String jwsjb = Optional.ofNullable(patient.getJwsjb()).orElseGet(() -> "");

            String[] jwsjbArr = jwsjb.split(",");

            List<String> jwsList = new ArrayList<>();
            if(!isgxy && isgxyZd){
                updatePatients.setIsgxy(1L);
                if(!getJwsjb(jwsjbArr,"2")){
                    jwsList.add("2");
                }
            }
            if(!istnb && istnbZd){
                updatePatients.setIstnb(1L);
                if(!getJwsjb(jwsjbArr,"3")){
                    jwsList.add("3");
                }
            }
            if(!isgxz && isgxzZd){
                updatePatients.setIsgxz(1L);
                if(!getJwsjb(jwsjbArr,"31")){
                    jwsList.add("31");
                }
            }
            /**
             * 检查原既往史疾病
             */
            if(getJwsjb(jwsjbArr,"2")){
                updatePatients.setIsgxy(1L);
            }
            if(getJwsjb(jwsjbArr,"3")){
                updatePatients.setIstnb(1L);
            }
            if(getJwsjb(jwsjbArr,"31")){
                updatePatients.setIsgxz(1L);
            }
            String jwsjbCheck = jwsjb;
            if(jwsList.size() > 0 ){
                if(jwsjb.equals("1")){
                    jwsjb = "";
                }
                String newJwsjb = "";
                String sb = listToString(jwsList,",");
                if("".equals(jwsjb)){
                    newJwsjb = sb;
                }else{
                    newJwsjb = jwsjb+ "," +sb;
                }
                updatePatients.setJwsjb(newJwsjb);
                jwsjbCheck = newJwsjb;
            }
            updatePatients.setSglevel(getSglevel(jwsjbCheck));
            getMapper().updateTemplateById(updatePatients);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("更新三高患者信息时出错-HisApi：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
        }
    }

    private Patients checkPatient(String idcard) {
        try {
            Patients patients = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","checkPatient"),MapUtil.builder().put("idcard",idcard).build(),Patients.class);
            if(null == patients){
                EhrGrjbxx ehrGrjbxx = getMapper().getSQLManager().selectSingle(SqlId.of("Patients","checkPatient2"),MapUtil.builder().put("idcard",idcard).build(),EhrGrjbxx.class);
                if(null != ehrGrjbxx){
                    Patients genPatients = genPatients(ehrGrjbxx);
                    genPatients.initSynPatients();
                    genPatients.setSyntime(DateConvert.dateToString(ehrGrjbxx.getAddTime(),DateConvert.YMDHMS));
                    //BasicUser user = getMapper().getSQLManager().unique(BasicUser.class,genPatients.getJyuserid());

                    BasicOrganization organization = getMapper().getSQLManager().unique(BasicOrganization.class,genPatients.getOrgid());
                    String orgParentId = organization.getParentid() == null ? "" : organization.getParentid();
                    String grade = organization.getGrade();

                    if(grade.equals("1") && orgParentId.equals("000000000000000000000000000000000000")){//县级卫生机构

                        genPatients.setOrgidl1(organization.getParentid());
                        genPatients.setOrgnamel1(organization.getParentname());
                        genPatients.setOrgidl2(organization.getId());
                        genPatients.setOrgnamel2(organization.getName());

                        genPatients.setGlorgidl1(organization.getId());
                        genPatients.setGlorgnamel1(organization.getName());
                        genPatients.setGlorgidl2(organization.getId());
                        genPatients.setGlorgnamel2(organization.getName());

                    }
                    if(grade.equals("2")){//乡、镇、社区卫生机构

                        genPatients.setOrgidl1(organization.getParentid());
                        genPatients.setOrgnamel1(organization.getParentname());
                        genPatients.setOrgidl2(organization.getId());
                        genPatients.setOrgnamel2(organization.getName());

                        genPatients.setGlorgidl1(organization.getParentid());
                        genPatients.setGlorgnamel1(organization.getParentname());
                        genPatients.setGlorgidl2(organization.getId());
                        genPatients.setGlorgnamel2(organization.getName());

                    }

                    if(grade.equals("3")){//村、街道卫生机构

                        BasicOrganization orgL2 = getMapper().getSQLManager().unique(BasicOrganization.class,organization.getParentid());
                        genPatients.setOrgidl1(orgL2.getParentid());
                        genPatients.setOrgnamel1(orgL2.getParentname());
                        genPatients.setOrgidl2(orgL2.getId());
                        genPatients.setOrgnamel2(orgL2.getName());

                        genPatients.setGlorgidl1(orgL2.getParentid());
                        genPatients.setGlorgnamel1(orgL2.getParentname());
                        genPatients.setGlorgidl2(orgL2.getId());
                        genPatients.setGlorgnamel2(orgL2.getName());
                    }

                    genPatients.setOrgcode(organization.getCode());
                    genPatients.setOrgname(organization.getName());
                    genPatients.setGlorgname(organization.getName());
                    genPatients.setGlorgid(organization.getId());

                    getMapper().upsertByTemplate(genPatients);

                    SQLTemplateEngine sqlTemplateEngine =  getMapper().getSQLManager().getSqlTemplateEngine();
                    getMapper().getSQLManager().executeUpdate(new SQLReady(sqlTemplateEngine.getSqlTemplate(SqlId.of("Patients","updateGender")).render()));
                    getMapper().getSQLManager().executeUpdate(new SQLReady(sqlTemplateEngine.getSqlTemplate(SqlId.of("Patients","updateMinzu")).render()));
                    getMapper().getSQLManager().executeUpdate(new SQLReady(sqlTemplateEngine.getSqlTemplate(SqlId.of("Patients","updateArea")).render()));
                    getMapper().getSQLManager().executeUpdate(new SQLReady(sqlTemplateEngine.getSqlTemplate(SqlId.of("Patients","updateUsername")).render()));

                    return genPatients;
                }else{
                    return null;
                }
            }else{
                return patients;
            }
        } catch (Exception e) {
            String errorMessage = new StringBuilder("检查患者是否存在-checkPatient：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    private Patients genPatients(EhrGrjbxx grjbxx){
        Patients patients = new Patients();
        String jwsjb = Optional.ofNullable(grjbxx.getJwsjb()).orElseGet(() -> "");
        String[] jwsjbArr = jwsjb.split(",");
        boolean isgxy = false;
        boolean istnb = false;
        boolean isgxz = false;
        for (String key : jwsjbArr) {
            if(!key.equals("")){
                if(key.equals("2")){
                    patients.setIsgxy(new Long(1));
                    isgxy = true;
                }
                if(key.equals("3")){
                    patients.setIstnb(new Long(1));
                    istnb = true;
                }
                if(key.equals("31")){
                    patients.setIsgxz(new Long(1));
                    isgxz = true;
                }
            }
        }
        long cglevel = 0;//三高级别 1高2高3高
        if(isgxy)if(cglevel < 3)cglevel+=1;
        if(istnb)if(cglevel < 3)cglevel+=1;
        if(isgxz)if(cglevel < 3)cglevel+=1;

        patients.initSynPatients();
        patients.setId(grjbxx.getGuid());
        patients.setName(grjbxx.getName());
        patients.setNamepy(grjbxx.getNamePy());
        patients.setIdcard(grjbxx.getIdCard());
        patients.setGendercode(grjbxx.getGender());//编码
        patients.setBirthday(DateConvert.dateToString(grjbxx.getBirthday(),DateConvert.YMD));
        patients.setAge((long) DateUtil.ageOfNow(grjbxx.getBirthday()));
        patients.setMinzucode(grjbxx.getMinzu());//编码
        patients.setJtzz(grjbxx.getCzdz());
        patients.setLxdh(grjbxx.getLxdh());
        patients.setAreaid(grjbxx.getAreaId());//ID
        patients.setOrgid(grjbxx.getCreateOrgId());//ID
        patients.setGuanli(grjbxx.getGuanli());
        patients.setPmtype("待管理");
        patients.setPmtypecode("2");
        patients.setJwsjb(grjbxx.getJwsjb());
        patients.setJwsjbexzl(grjbxx.getExzlmc());
        patients.setJwsjbzyb(grjbxx.getZybmc());
        patients.setYwgms(grjbxx.getYwgms());
        patients.setYwgmsqt(grjbxx.getYwgmsqt());
        patients.setCjqk(grjbxx.getCjqk());
        patients.setJzsfq(grjbxx.getJzsfq());
        patients.setJzsmq(grjbxx.getJzsmq());
        patients.setJzsxdjm(grjbxx.getJzsxdjm());
        patients.setJzszn(grjbxx.getJzszn());
        patients.setSglevel(cglevel);
        patients.setSyntime(DateConvert.dateToString(grjbxx.getAddTime(),DateConvert.YMDHMS));

        patients.setJyuserid(grjbxx.getAddUserId());
        patients.setJyusername(grjbxx.getAddUserName());
        patients.setGljyuserid(grjbxx.getAddUserId());
        patients.setGljyusername(grjbxx.getAddUserName());

        return patients;
    }


    @Override
    public PatientsMapper getMapper(){
        return JFinalBeetlSql.dao().getMapper(PatientsMapper.class);
    }

}
