getAll
===
* 获取患者
```sql
    select u.id,u.name,u.isgxy,u.istnb,u.isgxz,u.pmtype,u.pmtypecode,u.jwsjb
         ,(select top 1 bpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as bpgradecode
         ,(select top 1 dbgradecode from thasd_deteval where patientid = u.id order by createtime desc) as dbgradecode
         ,(select top 1 lpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as lpgradecode
         ,(select top 1 createtime from thasd_deteval where patientid = u.id order by createtime desc) as prepgtime
    from thasd_patients u where isdel = 0 and idcard =  #{idcard}
```

getStatus
===
* 获取患者
```sql
    select u.id,u.pmtype,u.pmtypecode from thasd_patients u where isdel = 0 and idcard =  #{idcard}
```

getPlan
===
* 获取方案
```sql
    select top 1 u.* from thasd_plan u where isdel = 0 and patientid = (select top 1 id from thasd_patients where idcard = #{idcard} and isdel = 0 ORDER BY createtime desc)  order by createtime desc
```

getNotice
===
* 获取患者
```sql
    select datediff(day,'#{text(nowTime)}',endtime)+1 as days,p.name,d.proname,p.idcard,d.executetime,d.endtime,d.prorate,d.proratecode,(select ratelevel from biz_basic_dic_data  where val = d.proratecode) as ratelevel from thasd_plan_sfjh_details d left join thasd_patients p on d.patientid = p.id where d.isdel = 0 and p.isdel = 0 and p.idcard = #{idcard} and executetime <=  #{nowTime} and endtime >= #{nowTime} order by days asc,ratelevel asc
```


checkPatientOrg
===
* 检查患者管理单位是否已经变更为本单位
```sql
    select id,name,idcard from thasd_patients where isdel = 0 and idcard =  #{idcard} and  glorgid =  #{orgid}
```

checkPatientApply
===
* 检查患者是否已经申请
```sql
    select count(id) from thasd_patients_apply where orgid = #{orgid} and idcard = #{idcard} and applystatue = 0
```

checkPatient
===
* 查询
```sql
select * from thasd_patients where isdel = 0 and idcard = #{idcard}
```

checkPatient2
===
* 查询
```sql
select top 1 * from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and IdCard = #{idcard} order by AddTime desc
```

getEhrPatient
===
* 查询
```sql
select top 1 * from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and IdCard = #{idcard} order by AddTime desc
```
updateGender
===
* 更新患者冗余字段-性别
```sql
    update thasd_patients set gender = d.name from biz_basic_dic_data d where d.val = gendercode and d.diccode = 'sex' and gender is null
```

updateMinzu
===
* 更新患者冗余字段-民族
```sql
    update thasd_patients set minzu = d.name from biz_basic_dic_data d where d.val = minzucode and d.diccode = 'minzu' and minzu is null
```

updateArea
===
* 更新患者冗余字段-区域
```sql
    update thasd_patients set areaname = d.name from basic_area d where d.id = areaid and areaname is null
```

updateOrgCode
===
* 更新患者冗余字段-所属单位编码
```sql
    update thasd_patients set orgcode = d.code from basic_organization d where d.id = orgid and orgcode is null
```

updateOrgName
===
* 更新患者冗余字段-所属单位名称
```sql
    update thasd_patients set orgname = d.name from basic_organization d where d.id = orgid and orgname is null
```

updateUsername
===
* 更新患者冗余字段-所属单位编码
```sql
update thasd_patients set jyuser = s.LoginName collate Chinese_PRC_CI_AS from BPHS.dbo.Sys_Admin s where s.Guid collate Chinese_PRC_CI_AS = jyuserid;
update thasd_patients set gljyuser = s.LoginName collate Chinese_PRC_CI_AS from BPHS.dbo.Sys_Admin s where s.Guid collate Chinese_PRC_CI_AS = gljyuserid;
```

