package cn.wingcloud.pojo;

import lombok.Data;
import java.io.Serializable;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

/**
 * null
 *
 * <AUTHOR>
 * @Date 2024-01-19
 */
@Data
@Table ( name ="basic_user_role" )
public class BasicUserRole extends BasePojo implements Serializable {


    @AssignID
	private String id;

	private String userid;

	private String roleid;

	/**
	 * 排序码
	 */
	private Double sortcode;

	/**
	 * 是否有效（0-无效，1-有效）
	 */
	private long isvalid;

	/**
	 * 创建时间
	 */
	private String createtime;

	/**
	 * 创建人ID
	 */
	private String createid;

	/**
	 * 创建人姓名
	 */
	private String createname;

	/**
	 * 修改时间
	 */
	private String updatetime;

	/**
	 * 修改人ID
	 */
	private String updateid;

	/**
	 * 修改人姓名
	 */
	private String updatename;

	/**
	 * 删除标记
	 */
	private Long isdel;

}
