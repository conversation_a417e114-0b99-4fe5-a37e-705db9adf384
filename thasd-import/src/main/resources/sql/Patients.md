pageCondition
===
* 分页查询条件
```sql
    -- @if(!isBlank(paras.name)){
    and name like #{'%'+paras.name+'%'}
    -- @}
    -- @if(!isBlank(paras.idcard)){
    and idcard like #{'%'+paras.idcard+'%'}
    -- @}
    -- @if(!isBlank(paras.pmtypecode)){
    and pmtypecode = #{paras.pmtypecode}
    -- @}
    -- @if(!isBlank(paras.areaid)){
    and areaid = #{paras.areaid}
    -- @}
    -- @if(!isBlank(paras.orgid)){
        -- @if(!isBlank(paras.islower) && paras.islower == "1"){
        and (orgid = #{paras.orgid} or orgidl2= #{paras.orgid})
        -- @} else{
        and orgid = #{paras.orgid}
        -- @}
    -- @}
    -- @if(!isBlank(paras.glorgid)){
        -- @if(!isBlank(paras.islower) && paras.islower == "1"){
        and (glorgid = #{paras.glorgid} or glorgidl2= #{paras.glorgid})
        -- @} else{
        and glorgid = #{paras.glorgid}
        -- @}
    -- @}
```
pageYGLCondition
===
* 分页查询条件
```sql
    -- @if(!isBlank(paras.bpgradecode)){
    and bpgradecode = #{paras.bpgradecode}
    -- @}
    -- @if(!isBlank(paras.dbgradecode)){
    and dbgradecode = #{paras.dbgradecode}
    -- @}
    -- @if(!isBlank(paras.lpgradecode)){
    and lpgradecode = #{paras.lpgradecode}
    -- @}
    -- @if(!isBlank(paras.jyuser)){
    and jyuser like #{'%'+paras.jyuser+'%'}
    -- @}
    -- @if(!isBlank(paras.jyusername)){
    and jyusername like #{'%'+paras.jyusername+'%'}
    -- @}
    -- @if(!isBlank(paras.gljyuser)){
    and gljyuser like #{'%'+paras.gljyuser+'%'}
    -- @}
    -- @if(!isBlank(paras.gljyusername)){
    and gljyusername like #{'%'+paras.gljyusername+'%'}
    -- @}
    -- @if(!isBlank(paras.isbfz)){
    and bfz is not null and bfz != '0'
    -- @}
    -- @if(!isBlank(paras.bfz)){
    and bfz like #{'%'+paras.bfz+'%'}
    -- @}
    -- @if(!isBlank(type) && type== "gxy"){
    and isgxy = 1
    -- @}
    -- @if(!isBlank(type) && type== "tnb"){
    and istnb = 1
    -- @}
    -- @if(!isBlank(type) && type== "gxz"){
    and isgxz = 1
    -- @}
    -- @if(!isBlank(orgids)){
    and glorgid in (#{text(orgids)})
    -- @}
```

pageLst
===
* 分页查询
```sql
    select #{page('tb.*')} from
    -- @if(!isBlank(paras.model) && paras.model == "ygl"){
    (
        
            SELECT  * FROM thasd_patients u
            OUTER APPLY( SELECT top 1 id as evalid,bpgradecode,dbgradecode,lpgradecode,adgradecode,bfz,bfzname,createtime as prepgtime FROM thasd_deteval d  WHERE d.patientid = u.id and d.isdel = 0 order by d.createtime desc ) AS [det]
            OUTER APPLY( SELECT top 1 id as planid FROM thasd_plan p  WHERE p.patientid = u.id  and p.isdel = 0 order by p.createtime desc ) AS [plan]
            where isdel = 0 ${use("pageCondition")}
        
    ) as tb where 1=1 ${use("pageYGLCondition")}
    -- @}else{
    thasd_patients tb where isdel = 0 ${use("pageCondition")}
    -- @}
    -- @pageIgnoreTag(){
    order by createtime desc
    -- @}
```


pageLstByNot
===
* 分页查询
```sql

    select #{page('p.*,( SELECT TOP 1 id FROM thasd_deteval WHERE patientid = p.id ORDER BY createtime DESC ) AS evalid,(select top 1 bpgradecode from thasd_deteval where patientid = p.id order by createtime desc) as bpgradecode,(select top 1 dbgradecode from thasd_deteval where patientid = p.id order by createtime desc) as dbgradecode ,(select top 1 lpgradecode from thasd_deteval where patientid = p.id order by createtime desc) as lpgradecode ,(select top 1 adgradecode from thasd_deteval where patientid = p.id order by createtime desc ) as adgradecode,(select top 1 bfz from thasd_deteval where patientid = p.id order by createtime desc) as bfz,(select top 1 bfzname from thasd_deteval where patientid = p.id order by createtime desc) as bfzname,(select top 1 createtime from thasd_deteval where patientid = p.id order by createtime desc) as prepgtime,( SELECT TOP 1 id FROM thasd_plan WHERE patientid = p.id ORDER BY createtime DESC ) AS planid')} 
    from thasd_patients p where  not exists (select Guid as id from BPHS.dbo.Ehr_Grjbxx g where  p.idcard = g.IdCard collate Chinese_PRC_CI_AS)
    and isdel = 0 ${use("pageCondition")} ${use("pageYGLCondition")}
    -- @pageIgnoreTag(){
    order by createtime desc
    -- @}
```

checkPatient
===
* 查询
```sql
select * from thasd_patients where isdel = 0 and idcard = #{idcard}
```

checkPatient2
===
* 查询
```sql
select top 1 * from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and IdCard = #{idcard} order by AddTime desc
```

getEhrPatient
===
* 查询
```sql
select top 1 * from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and IdCard = #{idcard} order by AddTime desc
```

getEhrPatient2
===
* 查询
```sql
select top 1 * from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and Guid = #{guid} order by AddTime desc
```

getEhrGxzsf
===
* 查询
```sql
select (select Name from Ehr_Grjbxx where Guid = s.RyGuid) as name,* from Ehr_Gxzsf s where  Guid = #{id}
```

list
===
* 查询
```sql
    select row_number() over(order by tb.id) as idx,tb.* from
    (
        select ( select parentname + name from basic_area where id = (SELECT parentid FROM basic_area WHERE id = u.areaid)  ) AS areafname,
               ( SELECT grade FROM basic_organization WHERE id = u.glorgid ) AS glgrade,
               ( SELECT grade FROM basic_organization WHERE id = u.orgid ) AS grade,
               u.*,
               (select top 1 bpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as bpgradecode,
               (select top 1 dbgradecode from thasd_deteval where patientid = u.id order by createtime desc) as dbgradecode,
               (select top 1 lpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as lpgradecode,
               (select top 1 adgradecode from thasd_deteval where patientid = u.id order by createtime desc ) as adgradecode,
               (select top 1 bfz from thasd_deteval where patientid = u.id order by createtime desc) as bfz,
               (select top 1 bfzname from thasd_deteval where patientid = u.id order by createtime desc) as bfzname,
               (select top 1 createtime from thasd_deteval where patientid = u.id order by createtime asc) as prepgtime
        from thasd_patients u where isdel = 0 ${use("pageCondition")}
    ) as tb where 1=1 ${use("pageYGLCondition")} order by createtime asc
```

getAll
===
* 获取患者
```sql
    select u.*,( SELECT TOP 1 id FROM thasd_deteval WHERE patientid = u.id ORDER BY createtime DESC ) AS evalid,(select top 1 bpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as bpgradecode,(select top 1 dbgradecode from thasd_deteval where patientid = u.id order by createtime desc) as dbgradecode ,(select top 1 lpgradecode from thasd_deteval where patientid = u.id order by createtime desc) as lpgradecode ,(select top 1 adgradecode from thasd_deteval where patientid = u.id order by createtime desc ) as adgradecode,(select top 1 createtime from thasd_deteval where patientid = u.id order by createtime desc) as prepgtime,( SELECT TOP 1 id FROM thasd_plan WHERE patientid = u.id ORDER BY createtime DESC ) AS planid
    from thasd_patients u where id =  #{id}
```

getJktjb
===
* 获取患者
```sql

select top 1  Sfrq as sfrq,Zssy as ssy , Zszy as szy,Shengao as height,Tizhong as weight,Yaowei as waistline,Bmi as bmi,Fzkfxt as kfxt ,Fzzdgc as zdgc,Fzgysz as gysz,Fzxqdmd as dmdzdb ,Xyqk as xyzk,Rxyl as rxyl,Ksxynl as ksxynl,Jynl as jynl
from BPHS.dbo.Ehr_Jktjb j  WITH(NOLOCK) left join BPHS.dbo.Ehr_Grjbxx  g  WITH(NOLOCK) on j.RyGuid = g.Guid
where j.RyGuid = #{patientid}  or g.IdCard = #{idcard} ORDER BY Sfrq desc

```

getBpResult
===
* 获取患者
```sql

    select top 1 ssy,szy,ghdate from rmisp.dbo.emr_mz_mjzbl  m WITH(NOLOCK)
    OUTER APPLY( SELECT p.idcardno FROM rmisp.dbo.emr_patient_info p WITH(NOLOCK) WHERE p.id = m.patientid  ) AS [p]
    WHERE  m.ghdate > '2022-03-01 00:00:00' and p.idcardno = #{idcard} and ssy != '' and szy != ''
    order by ghdate desc

```

getLabResult
===
* 查询患者检查检验数据
```sql

SELECT * FROM (

	SELECT TOP 1
	[TIME],IDCARD,ITEMCODE
	FROM rmisp.dbo.HIS_LABRESULT  WITH(NOLOCK)  WHERE ITEMCODE = 'TC' and [TIME] > '2022-03-01 00:00:00' and IDCARD = #{idcard}
	GROUP BY [TIME],IDCARD,ITEMCODE ORDER BY [TIME] DESC
	
) as tc
OUTER APPLY( select TOP 1 ITEMNAME,RESULT,UNIT from rmisp.dbo.HIS_LABRESULT d WITH(NOLOCK) where d.[TIME] = tc.[TIME] and d.[IDCARD] = tc.[IDCARD] and d.[ITEMCODE] = tc.[ITEMCODE] ) AS [detail]

UNION ALL

SELECT * FROM (

	SELECT TOP 1
	[TIME],IDCARD,ITEMCODE
	FROM rmisp.dbo.HIS_LABRESULT  WITH(NOLOCK)  WHERE ITEMCODE = 'HDL' and [TIME] > '2022-03-01 00:00:00' and IDCARD = #{idcard}
	GROUP BY [TIME],IDCARD,ITEMCODE ORDER BY [TIME] DESC
	
) as tc
OUTER APPLY( select TOP 1 ITEMNAME,RESULT,UNIT from rmisp.dbo.HIS_LABRESULT d WITH(NOLOCK) where d.[TIME] = tc.[TIME] and d.[IDCARD] = tc.[IDCARD] and d.[ITEMCODE] = tc.[ITEMCODE] ) AS [detail]

UNION ALL

SELECT * FROM (

	SELECT TOP 1
	[TIME],IDCARD,ITEMCODE
	FROM rmisp.dbo.HIS_LABRESULT  WITH(NOLOCK)  WHERE ITEMCODE = 'LDL' and [TIME] > '2022-03-01 00:00:00' and IDCARD = #{idcard}
	GROUP BY [TIME],IDCARD,ITEMCODE ORDER BY [TIME] DESC
	
) as tc
OUTER APPLY( select TOP 1 ITEMNAME,RESULT,UNIT from rmisp.dbo.HIS_LABRESULT d WITH(NOLOCK) where d.[TIME] = tc.[TIME] and d.[IDCARD] = tc.[IDCARD] and d.[ITEMCODE] = tc.[ITEMCODE] ) AS [detail]

UNION ALL

SELECT * FROM (

	SELECT TOP 1
	[TIME],IDCARD,ITEMCODE
	FROM rmisp.dbo.HIS_LABRESULT  WITH(NOLOCK)  WHERE ITEMCODE = 'TG' and [TIME] > '2022-03-01 00:00:00' and IDCARD = #{idcard}
	GROUP BY [TIME],IDCARD,ITEMCODE ORDER BY [TIME] DESC
	
) as tc
OUTER APPLY( select TOP 1 ITEMNAME,RESULT,UNIT from rmisp.dbo.HIS_LABRESULT d WITH(NOLOCK) where d.[TIME] = tc.[TIME] and d.[IDCARD] = tc.[IDCARD] and d.[ITEMCODE] = tc.[ITEMCODE] ) AS [detail]

UNION ALL

SELECT * FROM (

	SELECT TOP 1
	[TIME],IDCARD,ITEMCODE
	FROM rmisp.dbo.HIS_LABRESULT  WITH(NOLOCK)  WHERE ITEMCODE = 'GLU' and [TIME] > '2022-03-01 00:00:00' and IDCARD = #{idcard}
	GROUP BY [TIME],IDCARD,ITEMCODE ORDER BY [TIME] DESC
	
) as tc
OUTER APPLY( select TOP 1 ITEMNAME,RESULT,UNIT from rmisp.dbo.HIS_LABRESULT d WITH(NOLOCK) where d.[TIME] = tc.[TIME] and d.[IDCARD] = tc.[IDCARD] and d.[ITEMCODE] = tc.[ITEMCODE] ) AS [detail]


```

getNextAddTime
===
* 查询三高人群数
```sql
    select ISNULL(MAX(syntime),'') from thasd_patients where isdel = 0
```

countGrjbxx
===
* 查询公卫三高人群
```sql
    select count(Guid) from BPHS.dbo.Ehr_Grjbxx where Swrq is null and Status = 1 and IsDelete = 0 and  (Jwsjb like '%2%' or Jwsjb like '%3%' )
    
    and Guid not in (
                    '3a10f56c231b25426c55b2eb688440d2','3a12b1ef7fef3ee9447d10020bafc1e8','42850d2436604cc5b35c27d56cee747d','47bfad375f1e4496b805f2a0609c135b','9d14a60f81a14b39a8419624c0cb7ab8','a3271a7d98bd4258bc559f89ba41e546','3de0cd0f4ce940f09cc59ae47b253f0b','d09253a5ea39453fad2998530e1cc3c7','2a4f40931bf84906b5999e2ec9b95c91','f7f62fc34d4f4116af15adcae5cfaaa3','c2788466736f4b218442daf9f53603ea','f1aa3d5d3f7848debc73a84d13141a78','58f575fb1a3a4e928b075811ede1637e','f873da398d29402785a65819e27de22f'
                    )
    
    and CreateOrgId in (select Guid from BPHS.dbo.Sys_Organize WHERE Guid in ('3a0ff8ded93443689e2ed9ed8db28163')  or ParentGuid in ('3a0ff8ded93443689e2ed9ed8db28163'))
```

pageGrjbxx
===
* 分页公卫三高人群
```sql
    select #{page('*,(select Code from BPHS.dbo.Sys_Organize where Guid = n.CreateOrgId) as orgcode ,(select Name from BPHS.dbo.Sys_Organize where Guid = n.CreateOrgId) as orgname ,(select Name from BPHS.dbo.Sys_Area where Guid = n.AreaId) as areaname')} from BPHS.dbo.Ehr_Grjbxx n where  Swrq is null and  Status = 1 and IsDelete = 0 and  (Jwsjb like '%2%' or Jwsjb like '%3%' )
    
    and Guid not in (
                     '3a10f56c231b25426c55b2eb688440d2','3a12b1ef7fef3ee9447d10020bafc1e8','42850d2436604cc5b35c27d56cee747d','47bfad375f1e4496b805f2a0609c135b','9d14a60f81a14b39a8419624c0cb7ab8','a3271a7d98bd4258bc559f89ba41e546','3de0cd0f4ce940f09cc59ae47b253f0b','d09253a5ea39453fad2998530e1cc3c7','2a4f40931bf84906b5999e2ec9b95c91','f7f62fc34d4f4116af15adcae5cfaaa3','c2788466736f4b218442daf9f53603ea','f1aa3d5d3f7848debc73a84d13141a78','58f575fb1a3a4e928b075811ede1637e','f873da398d29402785a65819e27de22f'
                    )
    
    and CreateOrgId in (select Guid from BPHS.dbo.Sys_Organize WHERE Guid in ('3a0ff8ded93443689e2ed9ed8db28163')  or ParentGuid in ('3a0ff8ded93443689e2ed9ed8db28163'))
```

pageGrjbxx2
===
* 分页公卫三高人群-用于定时同步或已有公卫数据
```sql
    select #{page('*,(select Code from BPHS.dbo.Sys_Organize where Guid = n.CreateOrgId) as orgcode ,(select Name from BPHS.dbo.Sys_Organize where Guid = n.CreateOrgId) as orgname ,(select Name from BPHS.dbo.Sys_Area where Guid = n.AreaId) as areaname')} from BPHS.dbo.Ehr_Grjbxx n where  Swrq is null and  Status = 1 and IsDelete = 0 and  (Jwsjb like '%2%' or Jwsjb like '%3%' )
    and CreateOrgId in (select Guid from BPHS.dbo.Sys_Organize WHERE Guid in ('3a0ff8ded93443689e2ed9ed8db28163')  or ParentGuid in ('3a0ff8ded93443689e2ed9ed8db28163'))
    -- @if(isNotEmpty(addtime)){
    and AddTime > #{addtime}
    -- @}
```
grjbxxNot
===
* 不存在三高人群
```sql
    select * from BPHS.dbo.Ehr_Grjbxx where  Swrq is null and  Status = 1 and IsDelete = 0 and  Guid collate Chinese_PRC_CI_AI  not in (select id from thasd_patients where isdel = 0 ) and (Jwsjb like '%2%' or Jwsjb like '%3%' )
```

updateGender
===
* 更新患者冗余字段-性别
```sql
    update thasd_patients set gender = d.name from biz_basic_dic_data d where d.val = gendercode and d.diccode = 'sex' and gender is null
```

updateMinzu
===
* 更新患者冗余字段-民族
```sql
    update thasd_patients set minzu = d.name from biz_basic_dic_data d where d.val = minzucode and d.diccode = 'minzu' and minzu is null
```

updateArea
===
* 更新患者冗余字段-区域
```sql
    update thasd_patients set areaname = d.name from basic_area d where d.id = areaid and areaname is null
```

updateOrgCode
===
* 更新患者冗余字段-所属单位编码
```sql
    update thasd_patients set orgcode = d.code from basic_organization d where d.id = orgid and orgcode is null
```

updateOrgName
===
* 更新患者冗余字段-所属单位名称
```sql
    update thasd_patients set orgname = d.name from basic_organization d where d.id = orgid and orgname is null
```

updateUsername
===
* 更新患者冗余字段-所属单位编码
```sql
update thasd_patients set jyuser = s.LoginName collate Chinese_PRC_CI_AS from BPHS.dbo.Sys_Admin s where s.Guid collate Chinese_PRC_CI_AS = jyuserid and jyuser is null;
update thasd_patients set gljyuser = s.LoginName collate Chinese_PRC_CI_AS from BPHS.dbo.Sys_Admin s where s.Guid collate Chinese_PRC_CI_AS = gljyuserid and gljyuser is null;
```

countOrgCondition
===
* 统计-组织查询条件
```sql
    -- @if(!isBlank(category) && category != "1" && !isBlank(orgid) ){
        -- @if(!isBlank(grade) && grade == "2" ){
            and (orgid = #{orgid} or  glorgidl2 =  #{orgid})
        -- @}
        -- @if(!isBlank(grade) && grade == "3" ){
            and (orgid = #{orgid} or  glorgid =  #{orgid})
        -- @}
    -- @}
```

countGlOrgCondition
===
* 统计-组织查询条件
```sql
    -- @if(!isBlank(category) && category != "1" && !isBlank(orgid) ){
        -- @if(!isBlank(grade) && grade == "2" ){
        and glorgidl2 = #{orgid}
        -- @}
        -- @if(!isBlank(grade) && grade == "3" ){
        and glorgid = #{orgid}
        -- @}
    -- @}
```

countOrgsCondition
===
* 统计-组织查询条件
```sql
    -- @if(!isBlank(category) && category != "1" && !isBlank(orgids) ){
    and orgid in (#{text(orgids)})
    -- @}
```


countGlOrgsCondition
===
* 统计-组织查询条件
```sql
    -- @if(!isBlank(category) && category != "1" && !isBlank(orgids) ){
    and glorgid in (#{text(orgids)})
    -- @}
```

countPatients
===
* 统计所有患者
```sql
    select count(id) from thasd_patients where isdel = 0  ${use("countOrgCondition")} 
```

countYglPatients
===
* 统计管理中患者
```sql
    select count(id) from thasd_patients where  isdel = 0 and pmtypecode = 0 ${use("countGlOrgCondition")}
```

countSfPatients
===
* 统计失访患者
```sql
    select count(id) from thasd_patients where  isdel = 0  and pmtypecode = 1  ${use("countOrgCondition")}
```

countSgLevel
===
* 统计三高级别人群
```sql
    select count(id) as rs,sglevel,CASE sglevel WHEN '1' THEN '1高' WHEN '2' THEN '2高' WHEN '3' THEN '3高' ELSE '正常' END  as levelname from thasd_patients where  isdel = 0 ${use("countOrgCondition")} GROUP BY sglevel
```

countTypePatients
===
* 统计管理中高血压、糖尿病、高血脂患者人数
```sql
    select count(id) from thasd_patients where isdel = 0  and pmtypecode = 0  ${use("countGlOrgCondition")}
    -- @if(!isBlank(type) && type== "gxy"){
    and isgxy = 1
    -- @}
    -- @if(!isBlank(type) && type== "tnb"){
    and istnb = 1
    -- @}
    -- @if(!isBlank(type) && type== "gxz"){
    and isgxz = 1
    -- @}
```

countGroupCondition
===
* 统计各单位管理中高血压、糖尿病、高血脂患者人数-查询条件
```sql
    -- @if(!isBlank(type) && type== "gxy"){
    and isgxy = 1
    -- @}
    -- @if(!isBlank(type) && type== "tnb"){
    and istnb = 1
    -- @}
    -- @if(!isBlank(type) && type== "gxz"){
    and isgxz = 1
    -- @}
    -- @if(!isBlank(type) && type== "l2"){
    and ((isgxy = 1 and istnb = 1) or (isgxy = 1 and isgxz = 1) or  (isgxz = 1 and istnb = 1) )
    -- @}
    -- @if(!isBlank(type) && type== "l3"){
    and isgxy = 1 and  isgxz = 1 and istnb = 1
    -- @}
```

countGroup1Patients
===
* 统计各单位管理中高血压、糖尿病、高血脂患者人数
```sql
    select (select count(distinct id) from thasd_patients where isdel = 0 and glorgidl2 = p.glorgidl2 and pmtypecode = 0 ${use("countGroupCondition")} ) as total,p.glorgnamel2 as orgname,(select code from basic_organization where id = p.glorgidl2) as sortcode from basic_organization org left join thasd_patients p on p.glorgidl2 = org.id  where org.parentcode = '000000' and org.isdel = 0  and p.isdel = 0 
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
    GROUP BY p.glorgnamel2,p.glorgidl2 ORDER BY sortcode asc
```

countGroup2Patients
===
* 统计各单位管理中高血压、糖尿病、高血脂患者人数
```sql
    select name as orgname,code as sortcode,(select count(distinct id) from thasd_patients where isdel = 0 and glorgid = org.id and pmtypecode = 0 ${use("countGroupCondition")} ) as total from basic_organization org where org.parentid is not null and org.parentid <> '000000000000000000000000000000000000'  and org.isdel = 0
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)}) 
    -- @}
    GROUP BY name,id ,code ORDER BY code asc
```

countBfz1Patients
===
* 统计各单位管理中并发症患者人数
```sql
    SELECT
        name AS orgname,
        code AS sortcode,
        (
            SELECT COUNT ( * ) FROM
            (
                SELECT
                    id,
                    glorgidl2,
                    glorgnamel2,
                    ( SELECT TOP 1 bfz FROM thasd_deteval WHERE patientid = u.id ORDER BY createtime DESC ) AS bfz
                FROM
                    thasd_patients u
                WHERE
                    isdel = 0
                  AND pmtypecode = 0
                  AND glorgidl2 = org.id
            ) AS tb
            WHERE CHARINDEX('#{text(bfz)}', bfz ) > 0
        ) AS total
    FROM
        basic_organization org
    WHERE org.parentcode = '000000' AND org.isdel = 0 and CAST(org.code AS int) > 10
   -- @if(!isBlank(orgids)){
   and org.id in (#{text(orgids)})
   -- @}
  GROUP BY name,id,code ORDER BY code ASC
```

countBfz2Patients
===
* 统计各单位管理中并发症患者人数
```sql
    SELECT
        name AS orgname,
        code AS sortcode,
        (
            SELECT COUNT ( * ) FROM
            (
                SELECT
                    id,
                    glorgid,
                    glorgname,
                    ( SELECT TOP 1 bfz FROM thasd_deteval WHERE patientid = u.id ORDER BY createtime DESC ) AS bfz
                FROM
                    thasd_patients u
                WHERE
                    isdel = 0
                  AND pmtypecode = 0
                  AND glorgid = org.id
            ) AS tb
            WHERE CHARINDEX('#{text(bfz)}', bfz ) > 0
        ) AS total
    FROM
        basic_organization org
    WHERE org.parentid IS NOT NULL AND org.parentid <> '000000000000000000000000000000000000' AND org.isdel = 0
   -- @if(!isBlank(orgids)){
   and org.id in (#{text(orgids)})
   -- @}
  GROUP BY name,id,code ORDER BY code ASC
```


ascvdTimeCondition
===
* 查询条件
```sql
    -- @if(!isBlank( sttime)){
    and createtime >= #{sttime}
    -- @}
    -- @if(!isBlank( ettime)){
    and createtime <= #{ettime}
    -- @}
```

countAscvd1Patients
===
* 统计各单位管理中高血压、糖尿病、高血脂患者人数
```sql
    select (select count(distinct id) from ( select id,(select top 1 #{text(colname)} from thasd_deteval where patientid = u.id ${use("ascvdTimeCondition")}  order by createtime desc) as gradecode from thasd_patients u  where isdel = 0  and glorgidl2 = p.glorgidl2  and pmtypecode = 0   ) as cttb where gradecode = #{gradecode}) as total,p.glorgnamel2 as orgname,(select code from basic_organization where id = p.glorgidl2) as sortcode from basic_organization org left join thasd_patients p on p.glorgidl2 = org.id  where org.parentcode = '000000' and org.isdel = 0  and p.isdel = 0 
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
    GROUP BY p.glorgnamel2,p.glorgidl2 ORDER BY sortcode asc
```

countAscvd2Patients
===
* 统计各单位管理中高血压、糖尿病、高血脂患者人数
```sql
            
    select name as orgname,code as sortcode,(select count(distinct id) from ( select id,(select top 1 #{text(colname)} from thasd_deteval where patientid = u.id ${use("ascvdTimeCondition")} order by createtime desc) as gradecode from thasd_patients u  where isdel = 0  and glorgid = org.id  and pmtypecode = 0   ) as cttb where gradecode = #{gradecode}) as total from basic_organization org  where org.parentid is not null and org.parentid <> '000000000000000000000000000000000000'  and org.isdel = 0
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)}) 
    -- @}

    GROUP BY name,id ,code ORDER BY code asc
```

countDetevalTimeCondition
===
* 查询条件
```sql
    -- @if(!isBlank( sttime)){
    and createtime >= #{sttime}
    -- @}
    -- @if(!isBlank( ettime)){
    and createtime <= #{ettime}
    -- @}
```
countDetevalPatients
===
* 统计管理中评估人数
```sql
            SELECT SUM ( CASE WHEN pgtime IS NOT NULL THEN 1 ELSE 0 END ) evalnum FROM
            (
                SELECT  ( SELECT TOP 1 createtime FROM thasd_deteval WHERE patientid = p.id AND isdel = 0 ${use("countDetevalTimeCondition")}) AS pgtime FROM thasd_patients p WHERE isdel = 0 AND pmtypecode = '0'
                  -- @if(!isBlank(orgids)){
                  and glorgid in (#{text(orgids)})
                  -- @}
            ) AS tb
```

countSgPatients1
===
* 统计管理中高血压、糖尿病、高血脂患者人数
```sql
    select count(p.id) as [value],'高血压' as name from basic_organization org left join thasd_patients p on p.glorgidl2 = org.id where org.parentcode = '000000' and org.isdel = 0  and p.isdel = 0 and p.isgxy = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
    
    union all

    select count(p.id) as [value],'糖尿病' as name from basic_organization org left join thasd_patients p on p.glorgidl2 = org.id where org.parentcode = '000000' and org.isdel = 0  and p.isdel = 0 and p.istnb = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
      and org.id in (#{text(orgids)})
    -- @}
    
    union all
    
    select count(p.id) as [value],'高血脂' as name from basic_organization org left join thasd_patients p on p.glorgidl2 = org.id where org.parentcode = '000000' and org.isdel = 0  and p.isdel = 0 and p.isgxz = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
```

countSgPatients2
===
* 统计管理中高血压、糖尿病、高血脂患者人数
```sql
    select count(p.id) as [value],'高血压' as name from basic_organization org left join thasd_patients p on p.glorgid = org.id where org.parentid is not null and org.parentid <> '000000000000000000000000000000000000' and org.isdel = 0  and p.isdel = 0 and p.isgxy = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
    
    union all
    
    select count(p.id) as [value],'糖尿病' as name from basic_organization org left join thasd_patients p on p.glorgid = org.id where org.parentid is not null and org.parentid <> '000000000000000000000000000000000000' and org.isdel = 0  and p.isdel = 0 and p.istnb = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
    
    union all
    
    select count(p.id) as [value],'高血脂' as name from basic_organization org left join thasd_patients p on p.glorgid = org.id where org.parentid is not null and org.parentid <> '000000000000000000000000000000000000' and org.isdel = 0  and p.isdel = 0 and p.isgxz = 1  and p.pmtypecode = '0'
    -- @if(!isBlank(orgids)){
    and org.id in (#{text(orgids)})
    -- @}
```

getNotice
===
* 获取患者
```sql
    select datediff(day,'#{text(nowTime)}',endtime)+1 as days,p.name,d.id,d.proname,p.idcard,p.gljyuser,p.gljyuserid,p.gljyusername,d.executetime,d.endtime,d.prorate,d.proratecode,(select ratelevel from biz_basic_dic_data  where val = d.proratecode) as ratelevel from thasd_plan_sfjh_details d left join thasd_patients p on d.patientid = p.id where d.isdel = 0 and p.isdel = 0 and p.idcard = #{idcard} and executetime <=  #{nowTime} and endtime >= #{nowTime} and d.iscomplete is null order by days asc,ratelevel asc
```

countGetNotice
===
* 获取患者
```sql
    select count(1) from thasd_plan_sfjh_details d left join thasd_patients p on d.patientid = p.id where d.isdel = 0 and p.isdel = 0 and p.idcard = #{idcard} and executetime <=  #{nowTime} and endtime >= #{nowTime} and iscomplete is null
```

