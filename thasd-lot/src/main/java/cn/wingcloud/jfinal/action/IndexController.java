package cn.wingcloud.jfinal.action;

import cn.wingcloud.common.response.SuccessResponseData;
import com.jfinal.core.Controller;
import org.nutz.log.Log;
import org.nutz.log.Logs;

public class IndexController extends Controller {

    private static final Log log = Logs.get();

    public void index() {
        renderError(403);
    }

    public void testCors(){
        renderJson(new SuccessResponseData());
    }

}
