package cn.wingcloud.service.impl;

import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.beetlsql.Trans;
import cn.wingcloud.cdms.common.bean.SrcDataValue;
import cn.wingcloud.common.UniqueIdUtils;
import cn.wingcloud.mapper.LotBpMapper;
import cn.wingcloud.pojo.LotBpresult;
import cn.wingcloud.pojo.LotDbresult;
import cn.wingcloud.service.ReceivePressureDataService;
import com.alibaba.fastjson.JSON;
import com.jfinal.aop.Before;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 血糖
 * 
 * <AUTHOR>
 * @Date 2025-04-28
 */
public class ReceivePressureDataServiceImpl extends BaseServiceImpl implements ReceivePressureDataService {

    private static final Log log = Logs.get();

    @Override
    public LotBpMapper getMapper(){
        return JFinalBeetlSql.dao().getMapper(LotBpMapper.class);
    }

    /**
     * 处理上报的数据
     *
     * @param srcDatavalue
     * @return
     */
    @Override
    @Before(Trans.class)
    public boolean handleReceivedData(SrcDataValue srcDatavalue) {
        LotBpresult bpresult = new LotBpresult();
        bpresult.setSrcdata(JSON.toJSONString(srcDatavalue));
        bpresult.setId(UniqueIdUtils.getUniqueId());
        bpresult.setSerialnumber(srcDatavalue.getSerialNumber());
        bpresult.setSsy(Long.parseLong(srcDatavalue.getData1()));
        bpresult.setSzy(Long.parseLong(srcDatavalue.getData2()));
        bpresult.setXl(Long.parseLong(srcDatavalue.getData3()));
        // 获取当前世纪数，如 20（对应 20xx）
        int currentYear = java.time.Year.now().getValue();
        // 获取并补全年份
        int shortYear = Integer.parseInt(srcDatavalue.getYear());
        int fullYear = (currentYear / 100) * 100 + shortYear;
        // 构造标准格式的时间字符串
        LocalDateTime ldt = LocalDateTime.of(
                fullYear,
                Integer.parseInt(srcDatavalue.getMonth()),
                Integer.parseInt(srcDatavalue.getDay()),
                Integer.parseInt(srcDatavalue.getHour()),
                Integer.parseInt(srcDatavalue.getMinute())
        );
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        bpresult.setJcsj(ldt.format(formatter));
        getMapper().insert(bpresult);
        getMapper().executeUpdate("update lot_bpresult set idcard = (select idcard from lot_pdv where serialnumber = '"+bpresult.getSerialnumber()+"' and isvalid = 0)  where id = '"+bpresult.getId()+"'",null);
        return true;
    }
}
