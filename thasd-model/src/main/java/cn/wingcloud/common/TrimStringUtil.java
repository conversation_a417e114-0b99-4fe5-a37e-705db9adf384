package cn.wingcloud.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.*;

/**
 * <AUTHOR>
 * @Package com.develop
 * @Description: 处理参数内前后空格
 * @date 2021/11/27 10:00
 */
public class TrimStringUtil {

    public static String replaceAll(String value){
        String dealString = value.trim().replaceAll("\r\n", "").replaceAll("\\s+", "");
        return dealString;
    }

    /**
     * 去除所有的空格和换行  , 默认全部处理
     * <p>Map<String, Object> map = new HashMap<>();</p>
     * <p>map.put("name", "    123456    ");</p>
     * <p>map.put("age", "    123");</p>
     * <p>map.put("address", "    北京    ");</p>
     * <p>Student student = TrimStringUtil.stringAllTrim(map, new TypeReference&lt;Student&gt;(){});</p>
     * @param obj               需要处理的参数
     * @param cls               返回类型
     * @return T
     * @throws
     * <AUTHOR>
     * @date 2021/11/27 10:18
     */
    public static <T> T stringAllTrim(Object obj, Class<T> cls) {
        return stringAllTrim(obj, cls, false, "");
    }

    /**
     * 去除所有的空格和换行  , 默认全部处理
     * <p>Map<String, Object> map = new HashMap<>();</p>
     * <p>map.put("name", "    123456    ");</p>
     * <p>map.put("age", "    123");</p>
     * <p>map.put("address", "    北京    ");</p>
     * <p>Student student = TrimStringUtil.stringAllTrim(map, new TypeReference&lt;Student&gt;(){}, true, "name", "age");</p>
     * @param obj               需要处理的参数
     * @param cls               返回类型
     * @param isInclude         是否包含keys中的字段
     * @param keys              不定长字段
     * @return T
     * @throws
     * <AUTHOR>
     * @date 2021/11/27 10:18
     */
    public static <T> T stringAllTrim(Object obj, Class<T> cls,
                                      boolean isInclude, String... keys) {
        return stringAllTrim(obj, cls, isInclude, Arrays.asList(keys));
    }

    /**
     * 去除所有的空格和换行 ，根据isInclude判断需要处理的字段值
     * <p>Map<String, Object> map = new HashMap<>();</p>
     * <p>map.put("name", "    123456    ");</p>
     * <p>map.put("age", "    123");</p>
     * <p>map.put("address", "    北京    ");</p>
     * <p>Student student = TrimStringUtil.stringAllTrim(map, HasHMap.class, false, null);</p>
     * @param obj               需要处理的参数
     * @param cls               转换类型
     * @param isInclude         是否包含keys中的字段
     * @param keyList           字段枚举
     * @return T
     * @throws
     * <AUTHOR>
     * @date 2021/11/27 10:15
     */
    public static  <T> T stringAllTrim(Object obj, Class<T> cls, boolean isInclude, List<String> keyList){
        if (keyList == null) {
            keyList = new ArrayList<String>(){{
                this.add("");
            }};
        }

        Map<String, Object> hashMap = JSONObject.parseObject(JSON.toJSONString(obj), HashMap.class);
        Set<Map.Entry<String, Object>> entries = hashMap.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            if (entry.getValue() != null){
                String key = entry.getKey();
                Object paramValue = entry.getValue();
                if (paramValue instanceof String){
                    String value = (String)paramValue;
                    if ((isInclude && keyList.contains(key)) || (!isInclude && !keyList.contains(key))) {
                        String dealString = value.trim().replaceAll("\r\n", "").replaceAll("\\s+", "");
                        entry.setValue(dealString);
                    }
                }
            }
        }
        return JSON.parseObject(JSONObject.toJSONString(hashMap), cls);
    }

    /**
     * 去除前后空格，所有字段
     * <p>Map<String, Object> map = new HashMap<>();</p>
     * <p>map.put("name", "    123456    ");</p>
     * <p>map.put("age", "    123");</p>
     * <p>map.put("address", "    北京    ");</p>
     * <p>Student student = TrimStringUtil.stringTrimBackForth(map, HasHMap.class, false, null);</p>
     * @param obj               需要处理的参数
     * @param cls               转换类型
     * @return T
     * @throws
     * <AUTHOR>
     * @date 2021/11/27 10:15
     */
    public static  <T> T stringTrimBackForth(Object obj, Class<T> cls){
        Map<String, Object> hashMap = JSONObject.parseObject(JSON.toJSONString(obj), HashMap.class);
        Set<Map.Entry<String, Object>> entries = hashMap.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            if (entry.getValue() != null){
                String key = entry.getKey();
                Object paramValue = entry.getValue();
                if (paramValue instanceof String){
                    String value = (String)paramValue;
                    String dealString = value.trim();
                    entry.setValue(dealString);
                }
            }
        }
        return JSON.parseObject(JSONObject.toJSONString(hashMap), cls);
    }
}

