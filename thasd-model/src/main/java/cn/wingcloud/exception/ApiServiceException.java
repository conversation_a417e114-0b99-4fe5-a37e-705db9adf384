package cn.wingcloud.exception;

import cn.wingcloud.common.response.enums.AbstractBaseExceptionEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 接口调用出现的业务异常
 *
 * <AUTHOR>
 * @date 2021/03/15
 */
@Getter
@Setter
public class ApiServiceException extends RuntimeException {

    /**
     * 错误编码
     */
    private Integer code;

    /**
     * 错误的提示信息
     */
    private String errorMessage;

    public ApiServiceException(Integer code, String errorMessage) {
        super(errorMessage);
        this.code = code;
        this.errorMessage = errorMessage;
    }

    public ApiServiceException(AbstractBaseExceptionEnum exception) {
        super(exception.getMessage());
        this.code = exception.getCode();
        this.errorMessage = exception.getMessage();
    }

    public ApiServiceException(AbstractBaseExceptionEnum exception, String errorMessage) {
        super(errorMessage);
        this.code = exception.getCode();
        this.errorMessage = errorMessage;
    }

}