package cn.wingcloud.pojo;

import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import java.io.Serializable;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;
import java.util.Date;

/**
 * 公卫患者人员表
 *
 * <AUTHOR>
 * @Date 2022-03-28
 */
@Data
@Table ( name ="Ehr_Grjbxx" )
public class EhrGrjbxx  implements Serializable {

	/**
	 * 人员标识
	 */
	@NotNull
	private String guid;

	/**
	 * 健康档案号
	 */
	@NotNull
	private String ehrId;

	/**
	 * 姓名
	 */
	@NotNull
	private String name;

	/**
	 * 姓名拼音
	 */
	@NotNull
	private String namePy;

	/**
	 * 身份证号
	 */
	@NotNull
	private String idCard;

	/**
	 * 性别
	 */
	@NotNull
	private String gender;

	/**
	 * 出生日期
	 */
	@NotNull
	private Date birthday;

	/**
	 * 照片
	 */
	@NotNull
	private String photo;

	/**
	 * 末次月经日期
	 */
	@NotNull
	private Date mcyj;

	/**
	 * 工作单位
	 */
	@NotNull
	private String gzdw;

	/**
	 * 常住类型
	 */
	@NotNull
	private String czlx;

	/**
	 * 常住地址
	 */
	@NotNull
	private String czdz;

	/**
	 * 户籍地址
	 */
	@NotNull
	private String hjdz;

	/**
	 * 联系电话
	 */
	@NotNull
	private String lxdh;

	/**
	 * 联系人姓名
	 */
	@NotNull
	private String lxrxm;

	/**
	 * 联系人电话
	 */
	@NotNull
	private String lxrdh;

	/**
	 * 民族
	 */
	@NotNull
	private String minzu;

	/**
	 * 血型
	 */
	@NotNull
	private String xuexing;

	/**
	 * RH阴性
	 */
	@NotNull
	private String xuexingrh;

	/**
	 * 文化程度
	 */
	@NotNull
	private String whcd;

	/**
	 * 职业
	 */
	@NotNull
	private String zhiye;

	/**
	 * 婚姻情况
	 */
	@NotNull
	private String hyqk;

	/**
	 * 医疗支付方式
	 */
	@NotNull
	private String ylfyzffs;

	/**
	 * 职工卡号
	 */
	@NotNull
	private String zgkh;

	/**
	 * 居民卡号
	 */
	@NotNull
	private String jmkh;

	/**
	 * 贫困救助卡号
	 */
	@NotNull
	private String pkjzkh;

	/**
	 * 医疗支付方式其他
	 */
	@NotNull
	private String ylfyzffsqt;

	/**
	 * 药物过敏史
	 */
	@NotNull
	private String ywgms;

	/**
	 * 药物过敏史其他
	 */
	@NotNull
	private String ywgmsqt;

	/**
	 * 暴露史
	 */
	@NotNull
	private String bls;

	/**
	 * 既往史疾病
	 */
	@NotNull
	private String jwsjb;

	/**
	 * 高血压确诊日期
	 */
	@NotNull
	private Date gxyqzrq;

	/**
	 * 糖尿病确诊日期
	 */
	@NotNull
	private Date tnbqzrq;

	/**
	 * 冠心病确诊日期
	 */
	@NotNull
	private Date gxbqzrq;

	/**
	 * 慢性阻塞性肺疾病确诊日期
	 */
	@NotNull
	private Date mxzsxfjbqzrq;

	/**
	 * 恶性肿瘤确诊日期
	 */
	@NotNull
	private Date exzlqzrq;

	/**
	 * 恶性肿瘤名称
	 */
	@NotNull
	private String exzlmc;

	/**
	 * 脑卒中确诊日期
	 */
	@NotNull
	private Date nczqzrq;

	/**
	 * 严重精神障碍确诊日期
	 */
	@NotNull
	private Date yzjszaqzrq;

	/**
	 * 结核病确诊日期
	 */
	@NotNull
	private Date jhbqzrq;

	/**
	 * 肝炎确诊日期
	 */
	@NotNull
	private Date gyqzrq;

	/**
	 * 法定传染病确诊日期
	 */
	@NotNull
	private Date crbqzrq;

	/**
	 * 职业病确诊日期
	 */
	@NotNull
	private Date zybqzrq;

	/**
	 * 职业病名称
	 */
	@NotNull
	private String zybmc;

	/**
	 * 其他既往史疾病
	 */
	@NotNull
	private String jwsjbqt;

	/**
	 * 既往史手术
	 */
	@NotNull
	private String jwsss;

	/**
	 * 手术名称1
	 */
	@NotNull
	private String jwsssmc1;

	/**
	 * 手术日期1
	 */
	@NotNull
	private Date jwsssrq1;

	/**
	 * 手术名称2
	 */
	@NotNull
	private String jwsssmc2;

	/**
	 * 手术日期2
	 */
	@NotNull
	private Date jwsssrq2;

	/**
	 * 既往史外伤
	 */
	@NotNull
	private String jwsws;

	/**
	 * 外伤名称1
	 */
	@NotNull
	private String jwswsmc1;

	/**
	 * 外伤日期1
	 */
	@NotNull
	private Date jwswsrq1;

	/**
	 * 外伤名称2
	 */
	@NotNull
	private String jwswsmc2;

	/**
	 * 外伤日期2
	 */
	@NotNull
	private Date jwswsrq2;

	/**
	 * 既往史输血
	 */
	@NotNull
	private String jwssx;

	/**
	 * 输血原因1
	 */
	@NotNull
	private String jwssxyy1;

	/**
	 * 输血日期1
	 */
	@NotNull
	private Date jwssxrq1;

	/**
	 * 输血原因2
	 */
	@NotNull
	private String jwssxyy2;

	/**
	 * 输血日期2
	 */
	@NotNull
	private Date jwssxrq2;

	/**
	 * 家族史父亲
	 */
	@NotNull
	private String jzsfq;

	/**
	 * 家族史母亲
	 */
	@NotNull
	private String jzsmq;

	/**
	 * 家族史兄弟姐妹
	 */
	@NotNull
	private String jzsxdjm;

	/**
	 * 家族史子女
	 */
	@NotNull
	private String jzszn;

	/**
	 * 遗传病史
	 */
	@NotNull
	private String ycbs;

	/**
	 * 遗传病史名称
	 */
	@NotNull
	private String ycbsmc;

	/**
	 * 残疾情况
	 */
	@NotNull
	private String cjqk;

	/**
	 * 厨房排风设施
	 */
	@NotNull
	private String cfpfss;

	/**
	 * 燃料类型
	 */
	@NotNull
	private String rllx;

	/**
	 * 饮水
	 */
	@NotNull
	private String yinshui;

	/**
	 * 厕所
	 */
	@NotNull
	private String cesuo;

	/**
	 * 禽畜栏
	 */
	@NotNull
	private String qcl;

	@NotNull
	private String jtqkhz;

	/**
	 * 家庭人口数
	 */
	@NotNull
	private Integer jtqkrks;

	/**
	 * 家庭结构
	 */
	@NotNull
	private String jtqkjtjg;

	/**
	 * 居住情况
	 */
	@NotNull
	private String jtqkjzqk;

	/**
	 * 签字人
	 */
	@NotNull
	private String qzr;

	/**
	 * 签名
	 */
	@NotNull
	private String jmqm;

	/**
	 * 签名时间
	 */
	@NotNull
	private Date jmqmrq;

	/**
	 * 已管理
	 */
	@NotNull
	private String guanli;

	/**
	 * 活动状态
	 */
	@NotNull
	private Boolean status;

	/**
	 * 死亡日期
	 */
	@NotNull
	private Date swrq;

	/**
	 * 行政区划
	 */
	@NotNull
	private String areaId;

	/**
	 * 上级机构
	 */
	@NotNull
	private String createOrgPId;

	/**
	 * 建档机构
	 */
	@NotNull
	private String createOrgId;

	/**
	 * 是否删除
	 */
	@NotNull
	private Boolean isDelete;

	/**
	 * 添加时间
	 */
	@NotNull
	private Date addTime;

	/**
	 * 添加人编码
	 */
	@NotNull
	private String addUserId;

	/**
	 * 添加人姓名
	 */
	@NotNull
	private String addUserName;

	/**
	 * 修改时间
	 */
	@NotNull
	private Date editTime;

	/**
	 * 修改人编码
	 */
	@NotNull
	private String editUserId;

	/**
	 * 修改人姓名
	 */
	@NotNull
	private String editUserName;

	/**
	 * 是否审核
	 */
	@NotNull
	private Boolean isAudit;

	/**
	 * 审核时间
	 */
	@NotNull
	private Date auditTime;

	/**
	 * 审核人编码
	 */
	@NotNull
	private String auditUserId;

	/**
	 * 审核人名称
	 */
	@NotNull
	private String auditUserName;

	/**
	 * 是否完整
	 */
	@NotNull
	private Boolean isComplete;

	/**
	 * 检查空项数
	 */
	@NotNull
	private Double checkNullTerm;

	/**
	 * 空项数
	 */
	@NotNull
	private Double nullTerm;

	/**
	 * 数据完整度
	 */
	@NotNull
	private Double completeness;

	@NotNull
	private String areaPId;

	private String orgcode;
	private String orgname;
	private String areaname;
}
