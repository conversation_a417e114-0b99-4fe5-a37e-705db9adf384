package cn.wingcloud.pojo;

import lombok.Data;
import java.io.Serializable;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

/**
 * 高血脂随访-药品
 *
 * <AUTHOR>
 * @Date 2023-04-14
 */
@Data
@Table ( name ="Ehr_Gxzsf_Drugs" )
public class EhrGxzsfDrugs extends BaseBPHSPojo implements Serializable {

	/**
	 * 记录id
	 */
	@AssignID
	private String guid;

	/**
	 * 随访id
	 */
	private String sfGuid;

	/**
	 * 药品名称
	 */
	private String ywmc;

	private String ywly;

	/**
	 * 药品用法(次/日)
	 */
	private String ywyf;

	/**
	 * 药品用量
	 */
	private Double ywyl;

	/**
	 * 药品单位
	 */
	private String ywdw;

	/**
	 * 排序
	 */
	private long sort;

	/**
	 * 是否调整
	 */
	private Boolean istz;

	private String ywpl;

}
