package cn.wingcloud.service.impl;

import cn.wingcloud.pojo.BasePojo;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Record;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.List;

/**
 * 服务层基类
 * 
 * <AUTHOR>
 * @Date 2022-03-28
 */
public class BaseServiceImpl {
    
    private final Log log = Logs.get();
    
    public JSONObject getAuthUser(String alibabakey){
        try {
            return BasePojo.getAuthUser(alibabakey);
        } catch (Exception e) {
            return null;
        }
    }

    /***
     * 用于将存储多个id的List转成以分隔符隔开的字符串
     * @param list
     * @param separator
     * @return
     */
    public String listToString(List<?> list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append(list.get(i));
            if (i < list.size() - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    /***
     * 用于将存储多个id的List转成以分隔符隔开的字符串
     * @param list
     * @param separator
     * @return
     */
    public String listToStringHave(List<?> list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append("'"+list.get(i)+"'");
            if (i < list.size() - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }


    /***
     * 用于将存储多个Record的List中的某个属性列转成以分隔符隔开的字符串
     * @param list
     * @param key
     * @param separator
     * @return
     */
    public String valFromlistToString(List<Record> list, String key, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            Record record = list.get(i);
            sb.append(record.getStr(key));
            if (i < list.size() - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    /***
     * 用于将存储多个Record的List中的某个属性列转成以分隔符隔开的字符串
     * @param list
     * @param key
     * @param separator
     * @return
     */
    public String valFromlistToStringHave(List<Record> list, String key, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            Record record = list.get(i);
            sb.append("'"+record.getStr(key)+"'");
            if (i < list.size() - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }


    public String valFromlistToStringHave(String idStr, String separator) {
        StringBuilder sb = new StringBuilder();
        String[] list = idStr.split(separator);
        for (int i = 0; i < list.length; i++) {
            String str = list[i];
            sb.append("'"+str+"'");
            if (i < list.length - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    public String valFromArrToStringHave(String[] list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.length; i++) {
            String str = list[i];
            sb.append("'"+str+"'");
            if (i < list.length - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }

    public String[] strToArray(String idStr, String separator) {
        String[] list = idStr.split(separator);
        return list;
    }

    /***
     * 用于将存储多个id的List转成以分隔符隔开的字符串
     * @param list
     * @param separator
     * @return
     */
    public static String listToStringHaveWith(List<?> list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append("select '"+list.get(i)+"' ");
            if (i < list.size() - 1) {
                sb.append(separator);
            }
        }
        return sb.toString();
    }
}
