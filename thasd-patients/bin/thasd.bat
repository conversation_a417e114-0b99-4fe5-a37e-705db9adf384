@echo off

rem -------------------------------------------------------------------------
rem
rem 使用说明：
rem
rem 1: 该脚本用于别的项目时只需要修改 MAIN_CLASS 即可运行
rem
rem 2: JAVA_OPTS 可通过 -D 传入 undertow.port 与 undertow.host 这类参数覆盖
rem    配置文件中的相同值此外还有 undertow.resourcePath, undertow.ioThreads
rem    undertow.workerThreads 共五个参数可通过 -D 进行传入
rem
rem 3: JAVA_OPTS 可传入标准的 java 命令行参数,例如 -Xms256m -Xmx1024m 这类常用参数
rem
rem
rem -------------------------------------------------------------------------

setlocal & pushd

rem 项目名称
set APPLICATION=thasd-patients

rem 项目启动jar包名称
set APPLICATION_JAR=%APPLICATION%.jar

rem Java 命令行参数,根据需要开启下面的配置,改成自己需要的,注意等号前后不能有空格
rem set "JAVA_OPTS=-Xms256m -Xmx1024m -Dundertow.port=80 -Dundertow.host=0.0.0.0"
rem set "JAVA_OPTS=-Dundertow.port=80 -Dundertow.host=0.0.0.0"
set JAVA_OPT=-server -Xms2048m -Xmx2048m -Xmn512m -XX:MetaspaceSize=256m  -XX:SoftRefLRUPolicyMSPerMB=1000
set JAVA_OPT=%JAVA_OPT% -XX:-OmitStackTraceInFastThrow

if "%1"=="start" goto normal
if "%1"=="stop" goto normal
if "%1"=="restart" goto normal

goto error


:error
echo Usage: thasd.bat start | stop | restart
goto :eof


:normal
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
goto :eof


:start
set APP_BASE_PATH=%~dp0
set BASE_PATH=%~dp0
set CP=%APP_BASE_PATH%config;%APP_BASE_PATH%lib\*
set CONFIG_DIR=%BASE_PATH%config;%BASE_PATH%lib\*

echo starting server
rem java -Xverify:none %JAVA_OPTS% -cp %CP% %MAIN_CLASS%
java %JAVA_OPT% -cp %CONFIG_DIR% -Dfile.encoding=utf-8 -jar %BASE_PATH%lib\%APPLICATION_JAR%
goto :eof


:stop
set "PATH=%JAVA_HOME%\bin;%PATH%"
echo stopping server
for /f "tokens=1" %%i in ('jps -l ^| find "%APPLICATION_JAR%"') do ( taskkill /F /PID %%i )
goto :eof

:restart
call :stop
call :start
goto :eof

endlocal & popd

pause