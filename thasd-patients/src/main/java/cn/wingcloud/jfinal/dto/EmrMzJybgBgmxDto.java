package cn.wingcloud.jfinal.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 门诊检验报告明细
 *
 * <AUTHOR>
 * @Date 2021-03-23
 */
@Data
public class EmrMzJybgBgmxDto implements Serializable {
	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	
	private String originalid;

	/**
	 * 主表-检验报告ID（jybg.id）
	 */
	
	private String jybgid;

	/**
	 * Lis中此条检验报告的唯一ID
	 */
	
	private String liscode;

	/**
	 * Lis中检验项目代号
	 */
	
	private String lisxmcode;

	/**
	 * lis中项目名称
	 */
	
	private String lisxm;

	/**
	 * 检验结果
	 */
	
	private String jyjg;

	/**
	 * 结果单位
	 */
	
	private String jydw;

	/**
	 * 检验结果标志：H高L低M正常
	 */
	
	private String jyjgbz;

	/**
	 * 项目参考值
	 */
	
	private String jyckz;

	/**
	 * 排序序号
	 */
	
	private Long jyxh;

	/**
	 * 备注
	 */
	
	private String bz;

	/**
	 * 部门(科室)ID
	 */
	
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	
	private String deptname;

	/**
	 * 创建时间
	 */
	
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	
	private String createusername;

	/**
	 * 修改时间
	 */
	
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	private String lastuploadtime;

}
