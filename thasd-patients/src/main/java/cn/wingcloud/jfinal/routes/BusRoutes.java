package cn.wingcloud.jfinal.routes;

import cn.wingcloud.beetlsql.Trans;
import cn.wingcloud.jfinal.action.*;
import cn.wingcloud.jfinal.interceptor.ExceptionInterceptor;
import cn.wingcloud.jfinal.interceptor.RequestInterceptor;
import com.jfinal.config.Routes;

/**
 *  路由配置
 */
public class BusRoutes extends Routes {
	
	@Override
	public void config() {

		addInterceptor(new ExceptionInterceptor());
		addInterceptor(new RequestInterceptor());
		addInterceptor(new Trans());

		add("/api/patients", PatientsController.class, "/");
		add("/api/patients/count", PatientsCountController.class, "/");
		add("/api/patients/apply", PatientsApplyController.class, "/");
		add("/api/patients/eval", DetevalController.class, "/");
		add("/api/patients/sf", SfController.class, "/");
		add("/api/patients/jzjl", JzjlController.class, "/");
		add("/api/patients/imp", ImpJktjController.class, "/");
		add("/api/patients/gxzsf", GxzSfController.class, "/");
		add("/api/patients/plan/sfjl", PlanSfjlController.class, "/");
		add("/api/patients/bscas", BscasCountController.class, "/");


	}
}
