package cn.wingcloud.service.impl;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.common.response.RdSuccessResponseData;
import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.dto.*;
import cn.wingcloud.exception.ApiServiceException;
import cn.wingcloud.jfinal.interceptor.ReqBodyHolder;
import cn.wingcloud.mapper.PatientsMapper;
import cn.wingcloud.pojo.Patients;
import cn.wingcloud.service.TjfxService;
import cn.wingcloud.util.DateConvert;
import cn.wingcloud.util.ParamsUtil;
import com.alibaba.fastjson.JSONObject;
import org.beetl.sql.core.SqlId;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 三高患者
 * 
 * <AUTHOR>
 * @Date 2022-03-28
 */
public class TjfxServiceImpl extends BaseServiceImpl implements TjfxService {

    private static final Log log = Logs.get();

    @Override
    public ResponseData countDj() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountGzl> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countDj"),CountGzl.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
            .put("list",countGzlList)
            .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countDj：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countPg() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }
            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountGzl> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countPg"),CountGzl.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countDj：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countPgbfz() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");
            String bfz = requestParams.getPara("bfz",null);


            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }
            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountBfz> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countPgbfz"),CountBfz.class,MapUtil.builder().put("bfz",bfz).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取并发症患者时出错-countPgbfz：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countWpgry() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String glorgid = requestParams.getPara("glorgid","");
            String countType = requestParams.getPara("countType","");

            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            MapBuilder mapBuilder = MapUtil.builder().put("countType",countType);
            if(!glorgid.equals("")){
                mapBuilder.put("glorgid",glorgid);
            }else{
                mapBuilder.put("orgids",valFromlistToStringHave(orgid,","));
            }
            List<CountPg> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countWpgry"), CountPg.class,mapBuilder.build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countWpgry：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countFwjlmx() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String createid = requestParams.getPara("createid","");
            String countType = requestParams.getPara("countType","");

            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            MapBuilder mapBuilder = MapUtil.builder().put("countType",countType);
            if(!createid.equals("")){
                mapBuilder.put("createid",createid);
            }
            if(!orgid.equals("")){
                mapBuilder.put("orgids",valFromlistToStringHave(orgid,","));
            }

            mapBuilder.put("sttime",sttime).put("ettime",ettime);
            List<CountPlanSfjl> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countFwjlmx"), CountPlanSfjl.class,mapBuilder.build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countFwjlmx：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData monthpatientList() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");

            String yue = requestParams.getPara("yue");

            if(null == yue || "".equals(yue)) {
                yue = DateConvert.getNowMonth();
            }


            MapBuilder mapBuilder = MapUtil.builder().put("yue",yue).put("orgid",orgid);
            List<Map> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","2monthPatient"), Map.class,mapBuilder.build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-monthpatientList：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countGlrs() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }
            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountGlrs> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countGlrs"),CountGlrs.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countGlrs：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countFa() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }
            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountFa> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countFa"), CountFa.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countFa：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countFabwzmc() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String glorgid = requestParams.getPara("glorgid","");
            String fatype = requestParams.getPara("fatype","0");

            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");

            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }
            List<CountFa> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx","countFabwzmc"), CountFa.class,MapUtil.builder().put("fatype",fatype).put("glorgid",glorgid).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countFabwzmc：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countNrgl() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");
            String selectType = requestParams.getPara("selectType","dept");
            String sqlId = "countNrgl1";
            if(selectType.equals("person")){
                sqlId = "countNrgl2";
            }
            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountNrgl> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx",sqlId), CountNrgl.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countNrgl：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countFwjl() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");
            String selectType = requestParams.getPara("selectType","dept");
            String sqlId = "countFwjl1";
            if(selectType.equals("person")){
                sqlId = "countFwjl2";
            }
            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<CountFwjl> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx",sqlId), CountFwjl.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取工作量统计时出错-countFwjl：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countFwjlWcl() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();
            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String orgid = requestParams.getPara("orgid","");
            String sttime = requestParams.getPara("sttime");
            String ettime = requestParams.getPara("ettime");
            String wcl = requestParams.getPara("wcl");
            String selectType = requestParams.getPara("selectType","dept");
            String sqlId = "countFwjlWcl";
            if(selectType.equals("person")){
                sqlId = "countFwjlWcl";
            }
            if(null == sttime || "".equals(sttime)) {
                sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            //JSONObject authUser = getAuthUser(_alibabakey);
            //String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            //String orgid = authUser.getString("orgid");
            //String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            List<Map> countGzlList = getMapper().getSQLManager().select(SqlId.of("Tjfx",sqlId), Map.class,MapUtil.builder().put("wcl",wcl).put("orgids",valFromlistToStringHave(orgid,",")).build());
            Map<Object, Object> result = MapUtil.builder()
                    .put("list",countGzlList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取一体化项目清单完整度统计时出错-countFwjl：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }

    @Override
    public ResponseData countXxgJbfc() {
        try {
            ParamsUtil requestParams = ReqBodyHolder.get();

            String _alibabakey = requestParams.getPara("ALIBABAKEY", null);
            String tjfxType = requestParams.getPara("tjfxType", null);
            String sttime = requestParams.getPara("sttime","");
            String ettime = requestParams.getPara("ettime","");
            JSONObject authUser = getAuthUser(_alibabakey);
            String roleOrgIds = valFromlistToStringHave(authUser.getString("roleorgids"),",");
            String orgid = authUser.getString("orgid");
            String grade = authUser.getString("grade") == null ? "" : authUser.getString("grade");
            String cgpSqlId = "xxgjbHmc1";
            if(grade.equals("2") || grade.equals("3")) {//乡镇
                cgpSqlId = "xxgjbHmc";
            }

            if(null == sttime || "".equals(sttime)) {
                //sttime = DateConvert.getNowYear() +"-01-01 00:00:00";
            }else{
                sttime += " 00:00:00";
            }
            if(null == ettime || "".equals(ettime)) {
                //ettime = DateConvert.getNowYear() +"-12-31 23:59:59";
            }else {
                ettime += " 23:59:59";
            }

            List<Patients> yhList = getMapper().getSQLManager().select(SqlId.of("Tjfx",cgpSqlId),Patients.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",roleOrgIds).put("colname",tjfxType).put("gradeTemplet",tjfxType + " = 6 ").build());
            List<Patients> dwList = getMapper().getSQLManager().select(SqlId.of("Tjfx",cgpSqlId),Patients.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",roleOrgIds).put("colname",tjfxType).put("gradeTemplet",tjfxType + " = 1 ").build());
            List<Patients> zwList = getMapper().getSQLManager().select(SqlId.of("Tjfx",cgpSqlId),Patients.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",roleOrgIds).put("colname",tjfxType).put("gradeTemplet",tjfxType + " = 2 ").build());
            List<Patients> gwList = getMapper().getSQLManager().select(SqlId.of("Tjfx",cgpSqlId),Patients.class,MapUtil.builder().put("sttime",sttime).put("ettime",ettime).put("orgids",roleOrgIds).put("colname",tjfxType).put("gradeTemplet",tjfxType + " = 3 ").build());


            Map<Object, Object> result = MapUtil.builder()
                    .put("yhList", yhList)
                    .put("dwList", dwList)
                    .put("zwList", zwList)
                    .put("gwList", gwList)
                    .build();
            return new RdSuccessResponseData(result);
        } catch (Exception e) {
            String errorMessage = new StringBuilder("获取患者分层信息时出错-countXxgJbfc：").append(e.getMessage()).toString();
            log.error(errorMessage,e);
            throw new ApiServiceException(ResponseData.DEFAULT_ERROR_CODE,errorMessage);
        }
    }



    @Override
    public PatientsMapper getMapper(){
        return JFinalBeetlSql.dao().getMapper(PatientsMapper.class);
    }

}
