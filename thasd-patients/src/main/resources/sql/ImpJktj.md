getExitsPatients
===
* 获取患者
```sql

with cardTable(idcard) as (
    #{text(withIdcards)}
)
select p.idcard from thasd_patients p,cardTable c where p.isdel = 0 and p.pmtypecode #{text(opt)} '0' and p.idcard = c.idcard

```

grjbxxList
===
* 不存在三高人群
```sql

with  cardTable(idcard) as ( #{text(withIdcards)} )
select * from BPHS.dbo.Ehr_Grjbxx g,cardTable c  where  g.Swrq is null and  g.Status = 1 and g.IsDelete = 0 and g.IdCard collate Chinese_PRC_CI_AI = c.idcard

```