@echo off


set APPLICATION=thasd-plan

set APPLICATION_JAR=%APPLICATION%.jar

set BASE_PATH=%~dp0

set CONFIG_DIR=%BASE_PATH%\config;%BASE_PATH%\lib\*

set LOG_DIR=%BASE_PATH%logs
set LOG_FILE=%APPLICATION%-%random%-%%Y-%%m-%%d.log
set LOG_PATH=%LOG_DIR%\%LOG_FILE%
if not exist %LOG_DIR% (
    md %LOG_DIR%
)
echo %BASE_PATH%
echo %LOG_PATH%

rem #==========================================================================================
rem #JAVA_OPT=-server -Xms256m -Xmx256m -Xmn512m -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=256m
set JAVA_OPT=-server -Xms512m -Xmx512m -Xmn1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:SoftRefLRUPolicyMSPerMB=512
set JAVA_OPT=%JAVA_OPT% -XX:-OmitStackTraceInFastThrow

%1 mshta vbscript:CreateObject("WScript.Shell").Run("%~s0 ::",0,FALSE)(window.close)&&exit

java %JAVA_OPT% -cp %CONFIG_DIR% -Dfile.encoding=utf-8 -jar %BASE_PATH%\lib\%APPLICATION_JAR% | D:\thasd\cronolog %LOG_PATH% 2>&1 &

exit