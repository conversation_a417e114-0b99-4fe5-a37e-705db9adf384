package cn.wingcloud.jfinal.action;

import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.pojo.*;
import cn.wingcloud.service.PlanService;
import cn.wingcloud.service.impl.PlanServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.core.Controller;
import org.nutz.log.Log;
import org.nutz.log.Logs;

/**
 * 个性化管理方案
 * 
 * <AUTHOR>
 * @Date 2022-04-11
 */
public class PlanController extends Controller {

    private static final Log log = Logs.get();

    @Inject(PlanServiceImpl.class)
    private PlanService planService;

    public void index() {
        renderError(403);
    }

    /**
     * 患者列表[方案查询]-分页
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void pagelst() {
        ResponseData responseData = planService.pagelst();
        renderJson(responseData);
    }

    public void list() {
        ResponseData responseData = planService.list();
        renderJson(responseData);
    }

    /**
     * 患者列表[方案查询]-分页
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void pageLstSingle() {
        ResponseData responseData = planService.pageLstSingle();
        renderJson(responseData);
    }

    /**
     * 查看方案
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void viewPlan() {
        ResponseData responseData = planService.viewPlan();
        renderJson(responseData);
    }

    /**
     * 新增个性化管理方案
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void save() {
        ResponseData responseData = planService.save();
        renderJson(responseData);
    }

    /**
     * 新增个性化管理方案内容
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void savePlanJkzd() {
        PlanJkzd planJkzd = getBean(PlanJkzd.class, "" , true);
        ResponseData responseData = planService.savePlanJkzd(planJkzd);
        renderJson(responseData);
    }

    /**
     * 删除个性化管理方案-健康指导
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void delPlanJkzd() {
        ResponseData responseData = planService.delPlanJkzd();
        renderJson(responseData);
    }

    /**
     * 新增个性化管理方案内容
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void savePlanYyzd() {
        PlanYyzd planYyzd = getBean(PlanYyzd.class, "" , true);
        ResponseData responseData = planService.savePlanYyzd(planYyzd);
        renderJson(responseData);
    }

    /**
     * 删除个性化管理方案-用药指导
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void delPlanYyzd() {
        ResponseData responseData = planService.delPlanYyzd();
        renderJson(responseData);
    }

    public void savePlanTzyy() {
        String requestBody = getRawData();
        ResponseData responseData = planService.savePlanTzyy(requestBody);
        renderJson(responseData);
    }

    /**
     * 新增个性化管理方案内容
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void savePlanSfjh() {
        PlanSfjh planSfjh = getBean(PlanSfjh.class, "" , true);
        ResponseData responseData = planService.savePlanSfjh(planSfjh);
        renderJson(responseData);
    }

    /**
     * 删除个性化管理方案-随访计划
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void delPlanSfjh() {
        ResponseData responseData = planService.delPlanSfjh();
        renderJson(responseData);
    }

    /**
     * 获取患者健康指导方案
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void jkzdList() {
        ResponseData responseData = planService.jkzdList();
        renderJson(responseData);
    }

    /**
     * 获取患者用药指导方案
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void yyzdList() {
        ResponseData responseData = planService.yyzdList();
        renderJson(responseData);
    }

    /**
     * 获取患者复诊计划方案
     *
     * <AUTHOR>
     * @Date   2022/03/24 17:37
     **/
    public void fzjhList() {
        ResponseData responseData = planService.fzjhList();
        renderJson(responseData);
    }

    public void fzjhDetailList(){
        ResponseData responseData = planService.fzjhDetailList();
        renderJson(responseData);
    }
    public void fzjhHjList(){
        ResponseData responseData = planService.fzjhHjList();
        renderJson(responseData);
    }
    public void fzjhAll(){
        ResponseData responseData = planService.fzjhAll();
        renderJson(responseData);
    }

}
