package cn.wingcloud.jfinal.interceptor;

import cn.hutool.core.util.StrUtil;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CorsInterceptor implements Interceptor {

    @Override
    public void intercept(Invocation inv) {

        Controller controller = inv.getController();
        HttpServletRequest request = controller == null ? null : controller.getRequest();
        if (request == null) {
            return;
        }
        HttpServletResponse response = controller.getResponse();

        String origin = request.getHeader("origin");
        String corsHeaders = request.getHeader("access-control-request-headers");
        String corsMethod = request.getHeader("access-control-request-method");

        response.setHeader("Access-Control-Allow-Origin", StrUtil.isEmpty(origin) ? "*" : origin);
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Headers", StrUtil.isEmpty(corsHeaders) ? "*" : corsHeaders);
        response.setHeader("Access-Control-Allow-Methods", StrUtil.isEmpty(corsMethod) ? "*" : corsMethod);
        response.setHeader("Access-Control-Max-Age", "86400");

        if("OPTIONS".equals(request.getMethod().toUpperCase())){
            controller.renderJson("{}");
            return;
        }

        inv.invoke();

    }

}
