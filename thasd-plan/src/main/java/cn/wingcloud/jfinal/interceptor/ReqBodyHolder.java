package cn.wingcloud.jfinal.interceptor;

import cn.wingcloud.util.ParamsUtil;

/***
 * <pre>
 * 当前请求信息体的临时保存容器
 *
 *  说明：
 *     当OPEN_UP_FLAG标识在ThreadLocal里为true
 * </pre>
 *
 * <AUTHOR>
 * @Date   2021/03/12 08:26
 ***/
public class ReqBodyHolder {

    private static final ThreadLocal<Boolean> OPEN_UP_FLAG = new ThreadLocal<>();
    private static final ThreadLocal<ParamsUtil> REQEST_BODY_HOLDER = new ThreadLocal<>();

    /**
     * 初始化
     *
     * @return void
     * <AUTHOR>
     * @Date   2021/03/12 08:26
     **/
    public static void init() {
        OPEN_UP_FLAG.set(true);
    }

    /**
     * 这个方法如果OPEN_UP_FLAG标识没开启
     *
     * @param requestParams
     * @return void
     * <AUTHOR>
     * @Date   2020/07/23 08:27
     **/
    public static void set(ParamsUtil requestParams) {
        Boolean openUpFlag = OPEN_UP_FLAG.get();
        if (openUpFlag == null || openUpFlag.equals(false)) {
            return;
        } else {
            REQEST_BODY_HOLDER.set(requestParams);
        }
    }

    /**
     * 这个方法如果OPEN_UP_FLAG标识没开启，则会get值为null
     *
     * @return java.lang.String
     * <AUTHOR>
     * @Date   2020/07/23 08:28
     **/
    public static ParamsUtil get() {
        Boolean openUpFlag = OPEN_UP_FLAG.get();
        if (openUpFlag == null || openUpFlag.equals(false)) {
            return null;
        } else {
            return REQEST_BODY_HOLDER.get();
        }
    }

    /**
     * 删除保存的请求信息体
     *
     * @return void
     * <AUTHOR>
     * @Date   2020/07/23 08:28
     **/
    public static void remove() {
        OPEN_UP_FLAG.remove();
        REQEST_BODY_HOLDER.remove();
    }
}
