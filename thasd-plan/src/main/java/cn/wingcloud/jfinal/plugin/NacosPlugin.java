package cn.wingcloud.jfinal.plugin;

import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.jfinal.plugin.IPlugin;

import java.util.Properties;

public class NacosPlugin implements IPlugin {

    protected volatile boolean isStarted = false;

    protected String serverAddr = "localhost:8848";
    protected String ipAddress;
    protected int port;

    public NacosPlugin(String serverAddr) {
        this.serverAddr = serverAddr;
        this.port = Integer.valueOf(System.getProperty("undertow.port"));
        this.ipAddress = System.getProperty("upload.ip");
        System.out.println("===========d-" + ipAddress + ":" + port);
    }

    @Override
    public boolean start() {
        if (isStarted) return true;

        try {

            Properties properties = new Properties();
            properties.setProperty("serverAddr", serverAddr);

            NamingService naming = NamingFactory.createNamingService(serverAddr);
            naming.registerInstance("thasd-plan", ipAddress, port);

        } catch (NacosException e) {
            e.printStackTrace();
            throw new RuntimeException("Nacos发布服务时出错： " + e.getErrMsg());
        }

        isStarted =  true;

        return isStarted;
    }

    @Override
    public boolean stop() {
        try {
            Properties properties = new Properties();
            properties.setProperty("serverAddr", serverAddr);
            NamingService naming = NamingFactory.createNamingService(serverAddr);
            naming.deregisterInstance("thasd-plan", ipAddress, port);
        } catch (NacosException e) {
            e.printStackTrace();
        }
        isStarted = false;
        return true;
    }
}
