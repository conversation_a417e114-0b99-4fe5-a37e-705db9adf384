package cn.wingcloud.jfinal.routes;

import cn.wingcloud.beetlsql.Trans;
import cn.wingcloud.jfinal.action.*;
import cn.wingcloud.jfinal.interceptor.ExceptionInterceptor;
import cn.wingcloud.jfinal.interceptor.RequestInterceptor;
import com.jfinal.config.Routes;

/**
 * 路由配置
 */
public class BusRoutes extends Routes {
	
	@Override
	public void config() {

		addInterceptor(new ExceptionInterceptor());
		addInterceptor(new RequestInterceptor());
		addInterceptor(new Trans());

		add("/api/plan", PlanController.class, "/");
		add("/api/plan/tpl", PlanTplController.class, "/");
	}
}
