package cn.wingcloud.service;

import cn.wingcloud.common.response.ResponseData;
import cn.wingcloud.pojo.PlanJkzd;
import cn.wingcloud.pojo.PlanSfjh;
import cn.wingcloud.pojo.PlanYyzd;

public interface PlanService extends BaseService{
    ResponseData savePlanSfjhMatch(String detevalid,String planId,String patientid);
    ResponseData save(String patientid);
    ResponseData save(String patientid,String detevalid);
    ResponseData save();

    ResponseData savePlanJkzd(PlanJkzd planJkzd);
    ResponseData savePlanYyzd(PlanYyzd planYyzd);
    ResponseData savePlanSfjh(PlanSfjh planSfjh);
    ResponseData delPlanJkzd();
    ResponseData delPlanYyzd();
    ResponseData delPlanSfjh();
    ResponseData pagelst();
    ResponseData list();
    ResponseData pageLstSingle();

    ResponseData jkzdList();
    ResponseData yyzdList();
    ResponseData fzjhList();
    ResponseData fzjhDetailList();
    ResponseData fzjhHjList();
    ResponseData fzjhAll();
    ResponseData viewPlan();

    ResponseData savePlanTzyy(String requestBody);


    ResponseData reGenSfjhDetail();

}
