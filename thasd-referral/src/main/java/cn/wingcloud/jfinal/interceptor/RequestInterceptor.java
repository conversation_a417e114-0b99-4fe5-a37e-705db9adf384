package cn.wingcloud.jfinal.interceptor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.wingcloud.util.ParamsUtil;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import org.nutz.log.Log;
import org.nutz.log.Logs;

public class RequestInterceptor implements Interceptor {

    private static final Log log = Logs.get();

    private static String JSON_TYPE =  "application/json";
    private static String METHOD_TYPE =  "POST";


    @Override
    public void intercept(Invocation invocation) {

        long begin = SystemClock.now();

        ReqBodyHolder.init();

        Controller controller = invocation.getController();
        String method = controller.getRequest().getMethod();
        String contentType = controller.getRequest().getContentType();

        /**
         * 暂时取消限制
         */
//        if(!METHOD_TYPE.equalsIgnoreCase(method)){
//            controller.renderJson(new ErrorResponseData(ResponseData.DEFAULT_ERROR_CODE,"请求方法不正确"));
//            return;
//        }
//        if(!JSON_TYPE.equalsIgnoreCase(contentType)){
//            controller.renderJson(new ErrorResponseData(ResponseData.DEFAULT_ERROR_CODE,"请求头Content-Type类型不正确"));
//            return;
//        }

        ParamsUtil requestParams = new ParamsUtil(invocation.getController().getRequest());

        //log.errorf("requestBody: \n%s",requestBody);

        ReqBodyHolder.set(requestParams);

        invocation.invoke();

        ReqBodyHolder.remove();

        if (log.isDebugEnabled()) {
            log.debugf("EmrRequest完成 耗时：%s" , DateUtil.formatBetween(SystemClock.now()-begin));
        }

    }

}
