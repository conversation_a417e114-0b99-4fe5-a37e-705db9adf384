package cn.wingcloud.bean;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;

/**
 * 科室
 *
 * <AUTHOR>
 * @Date 2021-03-28
 */
@Data
@Table ( name ="basic_department" )
public class BasicDepartment  implements Serializable {

	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 部门编码
	 */
	@NotNull
	private String code;

	/**
	 * 部门名称
	 */
	@NotNull
	private String name;

	/**
	 * 上级部门ID（格式：字段orgcode+字段 上级部门.originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
	private String parentid;

	/**
	 * 上级部门代码
	 */
	@NotNull
	private String parentcode;

	/**
	 * 上级部门名称
	 */
	@NotNull
	private String parentname;

	/**
	 * 组织（单位）ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织（单位）编码
	 */
	@NotNull
	private String orgcode;

	/**
	 * 组织（单位）名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 是否有效（0-无效，1-有效）
	 */
	@Ignore
	private Long isvalid = 1L;

	/**
	 * 排序码
	 */
	@Ignore
	private Double sortcode = 99.00;

	/**
	 * 创建时间
	 */
	@Ignore
	private String createtime;

	/**
	 * 创建人ID
	 */
	@Ignore
	private String createid;

	/**
	 * 创建人姓名
	 */
	@Ignore
	private String createname;

	/**
	 * 修改时间
	 */
	@Ignore
	private String updatetime;

	/**
	 * 修改人ID
	 */
	@Ignore
	private String updateid;

	/**
	 * 修改人姓名
	 */
	@Ignore
	private String updatename;

	/**
	 * 删除标记
	 */
	@Ignore
	private Long isdel = 0L;

}
