package cn.wingcloud.bean;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;

/**
 * 门诊检验报告明细
 *
 * <AUTHOR>
 * @Date 2021-03-23
 */
@Data
@Table ( name ="emr_mz_jybg_bgmx" )
public class EmrMzJybgBgmx  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 主表-检验报告ID（jybg.id）
	 */
	@NotNull
	private String jybgid;

	/**
	 * Lis中此条检验报告的唯一ID
	 */
	@NotNull
	private String liscode;

	/**
	 * Lis中检验项目代号
	 */
	@NotNull
	private String lisxmcode;

	/**
	 * lis中项目名称
	 */
	@NotNull
	private String lisxm;

	/**
	 * 检验结果
	 */
	@NotNull
	private String jyjg;

	/**
	 * 结果单位
	 */
	@NotNull
	private String jydw;

	/**
	 * 检验结果标志：H高L低M正常
	 */
	@NotNull
	private String jyjgbz;

	/**
	 * 项目参考值
	 */
	@NotNull
	private String jyckz;

	/**
	 * 排序序号
	 */
	@NotNull
	private Long jyxh;

	/**
	 * 备注
	 */
	@NotNull
	private String bz;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;

}
