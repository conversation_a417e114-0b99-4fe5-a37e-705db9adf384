package cn.wingcloud.bean;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;

/**
 * 住院-日常病程记录
 *
 * <AUTHOR>
 * @Date 2021-03-22
 */
@Data
@Table ( name ="emr_zy_bcjl_rc" )
public class EmrZyBcjlRc  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 人员ID（上传数据person.id）
	 */
	@NotNull
	private String patientid;

	/**
	 * 年龄
	 */
	@NotNull
	private String age;

	/**
	 * 患者年龄单位：{1：岁、2：月、3：天}
	 */
	@NotNull
	private String ageunit;

	/**
	 * 住院号
	 */
	@NotNull
	private String zyh;

	/**
	 * 病案号
	 */
	@NotNull
	private String bah;

	/**
	 * 记录时间
	 */
	@NotNull
	private String jlsj;

	/**
	 * 记录内容
	 */
	@NotNull
	private String jinr;

	/**
	 * 医师签名(医师名称)
	 */
	@NotNull
	private String ysqm;

	/**
	 * 医师ID
	 */
	@NotNull
	private String ysqmid;

	/**
	 * 组织ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 组织code
	 */
	@NotNull
	private String orgcode;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;

}
