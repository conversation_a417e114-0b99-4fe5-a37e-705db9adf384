package cn.wingcloud.bean;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;

/**
 * 住院-长期医嘱单附表
 *
 * <AUTHOR>
 * @Date 2021-03-29
 */
@Data
@Table ( name ="emr_zy_yzd_cqyz" )
public class EmrZyYzdCqyz  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 主表-医嘱单ID（yzd.id）
	 */
	@NotNull
	private String parentid;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 医嘱分类：是否需要对照平台编码
	 */
	@NotNull
	private String yzfl;

	/**
	 * 医嘱分类code(参考字典：yzfl)
	 */
	@NotNull
	private String yzflcode;

	/**
	 * 序号
	 */
	@NotNull
	private Long xh;

	/**
	 * 成组号
	 */
	@NotNull
	private Long czh;

	/**
	 * 起始日期
	 */
	@NotNull
	private String qsrq;

	/**
	 * 药品字典编码、项目字典编码（此处根据平台提供药品字典、项目字典，HIS进行对照）
	 */
	@NotNull
	private String yzpubcode;

	/**
	 * 医嘱编码（涉及到项目字典与药品字典必传）
	 */
	@NotNull
	private String yzcode;

	/**
	 * 医嘱内容
	 */
	@NotNull
	private String yznr;

	/**
	 * 起始医师名称
	 */
	@NotNull
	private String qsysname;

	/**
	 * 起始医师id
	 */
	@NotNull
	private String qsysid;

	/**
	 * 起始护士名称
	 */
	@NotNull
	private String qshsname;

	/**
	 * 起始护士id
	 */
	@NotNull
	private String qshsid;

	/**
	 * 停止日期
	 */
	@NotNull
	private String tzrq;

	/**
	 * 停止医师名称
	 */
	@NotNull
	private String tzysname;

	/**
	 * 停止医师id
	 */
	@NotNull
	private String tzysid;

	/**
	 * 停止护士名称
	 */
	@NotNull
	private String tzhsname;

	/**
	 * 停止护士id
	 */
	@NotNull
	private String tzhsid;

	/**
	 * 组织ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 组织code
	 */
	@NotNull
	private String orgcode;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;

}
