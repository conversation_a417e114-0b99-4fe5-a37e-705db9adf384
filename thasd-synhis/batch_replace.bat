@echo off
echo Starting batch replacement for service classes...

REM Replace EmrMzXycfYymxService
powershell -Command "(Get-Content 'src\main\java\cn\wingcloud\task\service\EmrMzXycfYymxService.java') -replace 'EmrMzMjzblService', 'EmrMzXycfYymxService' -replace 'EmrMzMjzbl', 'EmrMzXycfYymx' | Set-Content 'src\main\java\cn\wingcloud\task\service\EmrMzXycfYymxService.java'"

REM Replace EmrMzZycfService
powershell -Command "(Get-Content 'src\main\java\cn\wingcloud\task\service\EmrMzZycfService.java') -replace 'EmrMzMjzblService', 'EmrMzZycfService' -replace 'EmrMzMjzbl', 'EmrMzZycf' -replace '门诊病历主表数据同步服务', '门诊中药处方数据同步服务' | Set-Content 'src\main\java\cn\wingcloud\task\service\EmrMzZycfService.java'"

REM Replace EmrMzZycfYymxService
powershell -Command "(Get-Content 'src\main\java\cn\wingcloud\task\service\EmrMzZycfYymxService.java') -replace 'EmrMzMjzblService', 'EmrMzZycfYymxService' -replace 'EmrMzMjzbl', 'EmrMzZycfYymx' -replace '门诊病历主表数据同步服务', '门诊中药处方用药明细数据同步服务' | Set-Content 'src\main\java\cn\wingcloud\task\service\EmrMzZycfYymxService.java'"

echo Batch replacement completed!
pause
