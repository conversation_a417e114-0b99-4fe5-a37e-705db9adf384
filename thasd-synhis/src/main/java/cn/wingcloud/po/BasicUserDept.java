package cn.wingcloud.po;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.Auto;
import org.beetl.sql.annotation.entity.InsertIgnore;
import org.beetl.sql.annotation.entity.Table;
import org.beetl.sql.annotation.entity.UpdateIgnore;

import java.io.Serializable;
import java.util.Date;

/**
 * 科室人员-多科室表
 *
 * <AUTHOR>
 * @Date 2021-03-31
 */
@Data
@Table ( name ="basic_user_dept" )
public class BasicUserDept  implements Serializable {
	/**
	 * 主键
	 */
	@Ignore
	@Auto
	@InsertIgnore
	@UpdateIgnore
	private Long id;

	/**
	 * 科室人员ID
	 */
	@NotNull
	private String userid;

	/**
	 * 科室ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;
	/**
	 * 唯一标识
	 */
	private String uniqueid;

	/**
	 * 时间戳（秒）
	 */
	private Long timestampsec;

	/**
	 * 业务时间
	 */
	private Date bustime;
}
