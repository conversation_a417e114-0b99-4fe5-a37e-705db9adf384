package cn.wingcloud.po;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 门诊中药处方
 *
 * <AUTHOR>
 * @Date 2021-03-23
 */
@Data
@Table ( name ="emr_mz_zycf" )
public class EmrMzZycf  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 人员ID（上传数据person.id）
	 */
	@NotNull
	private String patientid;

	/**
	 * 年龄
	 */
	@NotNull
	private String age;

	/**
	 * 患者年龄单位：{1：岁、2：月、3：天}
	 */
	@NotNull
	private String ageunit;

	/**
	 * 门诊号
	 */
	@NotNull
	private String mzh;

	/**
	 * 处方分类：普通处方、急诊处方、儿科处方、毒性药品、精神I类、精神Ⅱ类等(参考字典：mz_cflx)
	 */
	@NotNull
	private String cflx;

	/**
	 * 处方分类编码(参考字典：mz_cflx)
	 */
	@NotNull
	private String cflxcode;

	/**
	 * 处方日期
	 */
	@NotNull
	private String cfrq;

	/**
	 * HIS内处方发票号
	 */
	@NotNull
	private String cfsjh;

	/**
	 * HIS发药药房
	 */
	@NotNull
	private String fyyf;

	/**
	 * 处方审核医师
	 */
	@NotNull
	private String cfshyjs;

	/**
	 * 处方审核医师代码
	 */
	@NotNull
	private String cfshyjscode;

	/**
	 * 处方配药医师
	 */
	@NotNull
	private String cftpyjs;

	/**
	 * 处方配药医师代码
	 */
	@NotNull
	private String cftpyjscode;

	/**
	 * 处方核对医师
	 */
	@NotNull
	private String cfhdyjs;

	/**
	 * 处方核对医师代码
	 */
	@NotNull
	private String cfhdyjscode;

	/**
	 * 处方发药医师
	 */
	@NotNull
	private String cffyyjs;

	/**
	 * 处方发药医师代码
	 */
	@NotNull
	private String cffyyjscode;

	/**
	 * 诊断名称（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zd;

	/**
	 * 诊断名称编码（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zdcode;

	/**
	 * 剂数（付数）
	 */
	@NotNull
	private Long jishu;

	/**
	 * 中药剪煮方法（用法）
	 */
	@NotNull
	private String zyjzf;

	/**
	 * 中药剪煮方法编码（用法）(参考字典：cf_zy_yyfs、卫联)
	 */
	@NotNull
	private String zyjzfcode;

	/**
	 * 中药用药方法（频率）
	 */
	@NotNull
	private String zyyyff;

	/**
	 * 中药用药方法编码（频率）(参考字典：cf_zy_yypl、卫联)
	 */
	@NotNull
	private String zyyyffcode;

	/**
	 * 处方金额
	 */
	@NotNull
	private Double cash;

	/**
	 * 处方备注
	 */
	@NotNull
	private String remark;

	/**
	 * 组织ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 组织code
	 */
	@NotNull
	private String orgcode;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;

	private List<EmrMzZycfYymx> yymxList;

	private Integer issq;

	/**
	 * 唯一标识
	 */
	private String uniqueid;

	/**
	 * 时间戳（秒）
	 */
	private Long timestampsec;

	/**
	 * 业务时间
	 */
	private Date bustime;
}
