package cn.wingcloud.po;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;
import java.util.Date;

/**
 * 住院费用清单
 *
 * <AUTHOR>
 * @Date 2021-03-22
 */
@Data
@Table ( name ="emr_zy_fy" )
public class EmrZyFy  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 住院号
	 */
	@NotNull
	private String zyh;

	/**
	 * 费用流水号
	 */
	@NotNull
	private String lsh;

	/**
	 * 总金额
	 */
	@NotNull
	private String zje;

	/**
	 * 医保报销金额
	 */
	@NotNull
	private String ybbxje;

	/**
	 * 个人自付金额
	 */
	@NotNull
	private String grzfje;

	/**
	 * 组织ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 组织code
	 */
	@NotNull
	private String orgcode;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;
	/**
	 * 唯一标识
	 */
	private String uniqueid;

	/**
	 * 时间戳（秒）
	 */
	private Long timestampsec;

	/**
	 * 业务时间
	 */
	private Date bustime;
}
