package cn.wingcloud.po;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 住院-检验报告
 *
 * <AUTHOR>
 * @Date 2021-03-22
 */
@Data
@Table ( name ="emr_zy_jybg" )
public class EmrZyJybg  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 人员ID（上传数据person.id）
	 */
	@NotNull
	private String patientid;
	/**
	 * 患者姓名
	 */
	@NotNull
	private String name;
	/**
	 * 患者姓名
	 */
	@NotNull
	private String sex;
	/**
	 * 患者姓名
	 */
	@NotNull
	private String minzu;
	/**
	 * 年龄
	 */
	@NotNull
	private String age;

	/**
	 * 患者年龄单位：{1：岁、2：月、3：天}
	 */
	@NotNull
	private String ageunit;

	/**
	 * 住院号
	 */
	@NotNull
	private String zyh;

	/**
	 * 病案号
	 */
	@NotNull
	private String bah;

	/**
	 * 病房号
	 */
	@NotNull
	private String bfh;

	/**
	 * 病床号
	 */
	@NotNull
	private String bch;

	/**
	 * 检验报告编号
	 */
	@NotNull
	private String jybgbh;

	/**
	 * 诊断名称（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zd;

	/**
	 * 诊断名称编码（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zdcode;

	/**
	 * 标本类型
	 */
	@NotNull
	private String bblx;

	/**
	 * 标本类型code(参考字典：jy_bblx、卫联)
	 */
	@NotNull
	private String bblxcode;

	/**
	 * 检验类型名称
	 */
	@NotNull
	private String jymc;

	/**
	 * 检验报告日期
	 */
	@NotNull
	private Date jybgrq;

	/**
	 * 检验报告医师
	 */
	@NotNull
	private String jybgys;

	/**
	 * 检验报告科室代码
	 */
	@NotNull
	private String jybgks;

	/**
	 * 检验报告科室名称
	 */
	@NotNull
	private String jybgksmc;

	/**
	 * 审核医师
	 */
	@NotNull
	private String shys;

	/**
	 * 组织ID
	 */
	@NotNull
	private String orgid;

	/**
	 * 组织名称
	 */
	@NotNull
	private String orgname;

	/**
	 * 组织code
	 */
	@NotNull
	private String orgcode;

	/**
	 * 部门(科室)ID
	 */
	@NotNull
	private String deptid;

	/**
	 * 部门(科室)名称
	 */
	@NotNull
	private String deptname;

	/**
	 * 创建时间
	 */
	@NotNull
	private String createtime;

	/**
	 * 创建科室人员ID
	 */
	@NotNull
	private String createuserid;

	/**
	 * 创建科室人员姓名
	 */
	@NotNull
	private String createusername;

	/**
	 * 修改时间
	 */
	@NotNull
	private String updatetime;

	/**
	 * 修改科室人员ID
	 */
	@NotNull
	private String updateuserid;

	/**
	 * 修改科室人员姓名
	 */
	@NotNull
	private String updateusername;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;

	private List<EmrZyJybgBgmx> mxList;
	/**
	 * 唯一标识
	 */
	private String uniqueid;

	/**
	 * 时间戳（秒）
	 */
	private Long timestampsec;

	/**
	 * 业务时间
	 */
	private Date bustime;
}
