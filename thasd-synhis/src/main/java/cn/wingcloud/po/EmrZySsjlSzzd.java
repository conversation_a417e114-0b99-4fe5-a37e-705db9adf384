package cn.wingcloud.po;

import io.github.yedaxia.apidocs.Ignore;
import io.github.yedaxia.apidocs.NotNull;
import lombok.Data;
import org.beetl.sql.annotation.entity.AssignID;
import org.beetl.sql.annotation.entity.Table;

import java.io.Serializable;
import java.util.Date;

/**
 * 手术记录-术中诊断
 *
 * <AUTHOR>
 * @Date 2021-03-22
 */
@Data
@Table ( name ="emr_zy_ssjl_szzd" )
public class EmrZySsjlSzzd  implements Serializable {


	/**
	 * 主键（格式：字段orgcode+字段originalid 举例{0201-1、0201-D0C3D72F936300B3C90F53BC}）
	 */
	@NotNull
    @AssignID
	private String id;

	/**
	 * 原HIS系统记录唯一ID
	 */
	@NotNull
	private String originalid;

	/**
	 * 主表-手术记录ID（ssjl.id）
	 */
	@NotNull
	private String parentid;

	/**
	 * 诊断日期
	 */
	@NotNull
	private String zdrq;

	/**
	 * 诊断类型：西医病名，中医病名，中医证候
	 */
	@NotNull
	private String zdlx;

	/**
	 * 诊断类型code(参考字典：mz_zdlx)
	 */
	@NotNull
	private String zdlxcode;

	/**
	 * 诊断分类：初步诊断，修正诊断，确诊诊断，补充诊断(参考字典：mz_zdfl)
	 */
	@NotNull
	private String zdfl;

	/**
	 * 诊断分类code(参考字典：mz_zdfl)
	 */
	@NotNull
	private String zdflcode;

	/**
	 * 诊断名称（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zdmc;

	/**
	 * 诊断名称code（HIS系统内嘉和ICD）
	 */
	@NotNull
	private String zdcode;

	/**
	 * 最后上传时间
	 */
	@NotNull
	@Ignore
	private String lastuploadtime;
	/**
	 * 唯一标识
	 */
	private String uniqueid;

	/**
	 * 时间戳（秒）
	 */
	private Long timestampsec;

	/**
	 * 业务时间
	 */
	private Date bustime;
}
