package cn.wingcloud.task;

import cn.wingcloud.po.*;
import cn.wingcloud.task.service.EmrCommonService;
import cn.wingcloud.task.service.EmrMzJybgBgmxService;
import cn.wingcloud.task.service.EmrPatientService;
import cn.wingcloud.task.service.EmrZyJybgBgmxService;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import static cn.wingcloud.task.TaskConstant.*;

public class MainTask {

    private static final Log log = Logs.get();
    private static ScheduledExecutorService service = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    public void sync(){

        Runnable liaochengRun = new Runnable() {
            public void run() {
                try {
                    /*
                    * 患者信息
                     */
//                    new EmrPatientService().execute();

                    /*
                     * 门诊部分
                     */
                    //门急诊病历
//                    EmrCommonService mjzblService = new EmrCommonService("EmrMzMjzbl", EmrMzMjzbl.class);
//                    mjzblService.execute();
                    //门急诊病历诊断
//                    EmrCommonService mjzblZdService = new EmrCommonService("EmrMzMjzblZd", EmrMzMjzblZd.class);
//                    mjzblZdService.execute();
                    //西药处方
                    EmrCommonService xycfService = new EmrCommonService("EmrMzXycf", EmrMzXycf.class);
                    xycfService.execute();
                    EmrCommonService xycfMxService = new EmrCommonService("EmrMzXycfYymx", EmrMzXycfYymx.class);
                    xycfMxService.execute();
                    //中药处方
                    EmrCommonService zycfService = new EmrCommonService("EmrMzZycf", EmrMzZycf.class);
                    zycfService.execute();
                    EmrCommonService zycfMxService = new EmrCommonService("EmrMzZycfYymx", EmrMzZycfYymx.class);
                    zycfMxService.execute();
                    //检验报告
//                    EmrCommonService mzJybgService = new EmrCommonService("EmrMzJybg", EmrMzJybg.class);
//                    mzJybgService.execute();
//                    new EmrMzJybgBgmxService().execute();
                    //检查报告
                    EmrCommonService mzPacsService = new EmrCommonService("EmrMzJcbg", EmrMzJcbg.class);
                    mzPacsService.execute();

                    /**
                     * 住院部分
                     */
                    //检验报告
                    EmrCommonService zyJybgService = new EmrCommonService("EmrZyJybg", EmrZyJybg.class);
                    zyJybgService.execute();
                    new EmrZyJybgBgmxService().execute();
                    //检查报告
                    EmrCommonService zyPacsService = new EmrCommonService("EmrZyJcbg", EmrZyJcbg.class);
                    zyPacsService.execute();

                    /**
                     * 检查检验项目
                     */

//                    new LabapplyService().execute();
//                    new LabresultService().execute();
//                    new PacsapplyService().execute();
//                    new PacsResultService().execute();
//
//                    new EmrZyBasyService().execute();
//                    new EmrZyBasySsService().execute();
//                    new EmrZyBasyZzjhService().execute();//很多医院没有表

                } catch (Exception e) {
                    log.error("MainTask.liaochengRun-long时出错：",e.fillInStackTrace());
                }
            }
        };

        service.scheduleWithFixedDelay(liaochengRun,INIT_WTIME,AFTER_WTIME_LONG,UNIT);
    }
}
