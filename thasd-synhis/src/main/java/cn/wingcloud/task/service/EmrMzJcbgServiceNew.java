package cn.wingcloud.task.service;

import cn.wingcloud.po.EmrMzJcbg;

/**
 * 门诊检查报告数据同步服务 - 使用公共服务类的新实现
 */
public class EmrMzJcbgServiceNew {

    private final EmrCommonService<EmrMzJcbg> commonService;

    public EmrMzJcbgServiceNew() {
        // 初始化公共服务，传入NAMESPACE和实体类
        this.commonService = new EmrCommonService<>("EmrMzJcbg", EmrMzJcbg.class);
    }

    /**
     * 执行同步
     */
    public void execute() {
        commonService.execute();
    }
}
