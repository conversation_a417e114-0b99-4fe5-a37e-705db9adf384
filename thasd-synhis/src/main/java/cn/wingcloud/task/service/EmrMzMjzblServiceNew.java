package cn.wingcloud.task.service;

import cn.wingcloud.po.EmrMzMjzbl;

/**
 * 门诊病历主表数据同步服务 - 使用公共服务类的新实现
 */
public class EmrMzMjzblServiceNew {

    private final EmrCommonService<EmrMzMjzbl> commonService;

    public EmrMzMjzblServiceNew() {
        // 初始化公共服务，传入NAMESPACE和实体类
        this.commonService = new EmrCommonService<>("EmrMzMjzbl", EmrMzMjzbl.class);
    }

    /**
     * 执行同步
     */
    public void execute() {
        commonService.execute();
    }
}
