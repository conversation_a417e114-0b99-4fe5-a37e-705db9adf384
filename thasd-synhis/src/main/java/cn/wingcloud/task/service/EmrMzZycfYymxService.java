package cn.wingcloud.task.service;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.wingcloud.SyncHisMainApplication;
import cn.wingcloud.beetlsql.JFinalBeetlSqlFromOracle;
import cn.wingcloud.beetlsql.JFinalBeetlSqlTo;
import cn.wingcloud.po.EmrMzXycfYymx;
import cn.wingcloud.util.DateConvert;
import org.beetl.sql.core.DSTransactionManager;
import org.beetl.sql.core.SQLManager;
import org.beetl.sql.core.SqlId;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static cn.wingcloud.LoadFromConfig.*;

/**
 * 门诊病历中药处方明细数据同步服务
 */
public class EmrMzZycfYymxService {

    private static final Log log = Logs.get();
    private static final String NAMESPACE = "EmrMzXycfYymx";

    public void execute(){

        if (!SyncHisMainApplication.DEBUG) {
            try {
                if(!DateConvert.isRequireTwoTime("22:40","23:59","00:00","05:50")){
                    log.infof(">>>>>>>>>>>>>>>>>>>EmrMzZycfYymxService.isRequireTwoTime >>  %s ", "不在运行时间段内{\"22:40\"——\"00:00\" || \"00:00\"——\"05:50\"}");
                    return;
                }
            } catch (ParseException e) {
                log.errorf(">>>>>>>>>>>>>>>>>>>EmrMzZycfYymxService.isRequireTwoTime >> ERROR —— %s ", e.getMessage(),e);
                return;
            }
        }

        long stime = SystemClock.now();
        log.infof(">>>>>>>>>>>>>>>>>>>EmrMzZycfYymxService.execute >> 开始执行 - %s <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<", stime);

        try {
            SQLManager fromSqlManger = JFinalBeetlSqlFromOracle.dao();
            SQLManager toSqlManger = JFinalBeetlSqlTo.dao();

            MapBuilder params = MapUtil.builder().put("orgcode",ORGCODE).put("orgname",ORGNAME).put("orgid",ORGID);
            
            // 1. 查询目标库最大idn
            Long maxIdn = toSqlManger.selectSingle(SqlId.of(NAMESPACE,"maxIdn"), params.build(), Long.class);
            maxIdn = (maxIdn == null ? 0 : maxIdn);
            
            log.infof(">>>>>>>>>>>>>>>>>>>目标库最大idn = %s", maxIdn);
            
            if (maxIdn == 0) {
                // 初次同步：按年度区间同步
                executeInitialSync(fromSqlManger, toSqlManger, params);
            } else {
                // 增量同步：根据最大记录的信息进行增量同步
                executeIncrementalSync(fromSqlManger, toSqlManger, params);
            }

        } catch (Exception e) {
            log.errorf("ORGID=%s——Exception:%s",ORGNAME,e.getMessage());
        } finally {
            DSTransactionManager.clear();
        }
        log.infof(">>>>>>>>>>>>>>>>>>>EmrMzZycfYymxService.execute >> -数据执行结束 - %s 耗时：%s<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<",SystemClock.now(), DateUtil.formatBetween(SystemClock.now()-stime, BetweenFormatter.Level.MILLISECOND));
    }

    /**
     * 初次同步：只同步2025-01-01往后的数据
     */
    private void executeInitialSync(SQLManager fromSqlManger, SQLManager toSqlManger, MapBuilder params) {
        try {
            // 固定同步起始时间：2025-01-01 00:00:00
            String fixedStartTime = "2025-01-01 00:00:00";
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);

            log.infof(">>>>>>>>>>>>>>>>>>>初次同步：从 %s 开始同步数据", fixedStartTime);

            // 从2025年开始，按年度区间同步到当前年
            for (int year = 2025; year <= currentYear; year++) {
                String startTime, endTime;

                if (year == 2025) {
                    // 2025年：从1月1日开始
                    startTime = fixedStartTime;
                } else {
                    // 其他年份：从1月1日开始
                    startTime = year + "-01-01 00:00:00";
                }

                // 计算结束时间
                if (year == currentYear) {
                    // 当前年：取当前日期减去2天 + 23:59:59
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.DAY_OF_MONTH, -2);
                    endTime = DateUtil.format(cal.getTime(), "yyyy-MM-dd") + " 23:59:59";
                } else {
                    // 非当前年：取12月31日
                    endTime = year + "-12-31 23:59:59";
                }

                log.infof(">>>>>>>>>>>>>>>>>>>同步年度区间：%s 到 %s", startTime, endTime);

                try {
                    // 将年度区间按月细分，避免大数据量造成GC问题
                    syncDataByMonthRange(fromSqlManger, toSqlManger, params, startTime, endTime);
                } catch (Exception e) {
                    log.errorf("年度区间 %s 到 %s 同步失败，中断后续同步：%s", startTime, endTime, e.getMessage(), e);
                    throw e; // 重新抛出异常，中断后续年度区间的同步
                }
            }

        } catch (Exception e) {
            log.errorf("初次同步异常：%s", e.getMessage(), e);
        }
    }

    /**
     * 增量同步：根据最大记录信息进行增量同步
     */
    private void executeIncrementalSync(SQLManager fromSqlManger, SQLManager toSqlManger, MapBuilder params) {
        try {
            // 先获取最大idn
            Long maxIdn = toSqlManger.selectSingle(SqlId.of(NAMESPACE,"maxIdn"), params.build(), Long.class);
            if (maxIdn == null || maxIdn == 0) {
                log.infof(">>>>>>>>>>>>>>>>>>>无最大idn记录，跳过增量同步");
                return;
            }
            
            // 获取最大idn对应的详细信息
            Map<String, Object> queryParams = MapUtil.builder(params.build()).put("idn", maxIdn).build();
            Map<String, Object> maxIdnInfo = toSqlManger.selectSingle(SqlId.of(NAMESPACE,"maxIdnInfo"), queryParams, Map.class);
            
            if (maxIdnInfo == null) {
                log.infof(">>>>>>>>>>>>>>>>>>>无法获取最大记录信息，跳过增量同步");
                return;
            }
            
            String uniqueid = (String) maxIdnInfo.get("uniqueid");
            Long timestampsec = (Long) maxIdnInfo.get("timestampsec");
            Date bustime = (Date) maxIdnInfo.get("bustime");
            
            log.infof(">>>>>>>>>>>>>>>>>>>增量同步起点：uniqueid=%s, timestampsec=%s, bustime=%s", 
                     uniqueid, timestampsec, DateUtil.formatDateTime(bustime));
            
            // 设置增量查询的时间区间（确保不早于2025-01-01）
            String fixedMinTime = "2025-01-01 00:00:00";
            String lastSyncTime = DateUtil.format(bustime, "yyyy-MM-dd") + " 00:00:00";

            // 取最后同步时间和2025-01-01中的较大值作为起始时间
            String startTime = lastSyncTime.compareTo(fixedMinTime) >= 0 ? lastSyncTime : fixedMinTime;

            // 判断startTime是否是当前年
            Date startDate = DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss");
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(startDate);
            int startYear = startCal.get(Calendar.YEAR);
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);

            String endTime;
            if (startYear == currentYear) {
                // 当前年：使用当前日期减去2天
                Calendar cal = Calendar.getInstance();
                cal.add(Calendar.DAY_OF_MONTH, -2);
                endTime = DateUtil.format(cal.getTime(), "yyyy-MM-dd") + " 23:59:59";
            } else {
                // 非当前年：使用startTime所在年份的12月31日
                endTime = startYear + "-12-31 23:59:59";
            }

            log.infof(">>>>>>>>>>>>>>>>>>>增量同步时间区间：%s 到 %s (起始年份:%d, 当前年份:%d)",
                     startTime, endTime, startYear, currentYear);

            // 判断是否需要按月细分
            Date endDate = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");

            Calendar endCal = Calendar.getInstance();
            endCal.setTime(endDate);
            Calendar currentCal = Calendar.getInstance();

            // 如果结束时间是当前月，直接执行增量查询，不进行月度细分
            boolean isCurrentMonth = (endCal.get(Calendar.YEAR) == currentCal.get(Calendar.YEAR) &&
                                    endCal.get(Calendar.MONTH) == currentCal.get(Calendar.MONTH));

            if (isCurrentMonth) {
                log.infof(">>>>>>>>>>>>>>>>>>>当前月增量同步，直接执行查询");
                executeIncrementalQuery(fromSqlManger, toSqlManger, params, uniqueid, timestampsec, startTime, endTime);
            } else {
                log.infof(">>>>>>>>>>>>>>>>>>>跨月增量同步，按月度区间细分");
                executeIncrementalSyncByMonth(fromSqlManger, toSqlManger, params, uniqueid, timestampsec, startTime, endTime);
            }
            
        } catch (Exception e) {
            log.errorf("增量同步异常：%s", e.getMessage(), e);
        }
    }

    /**
     * 按时间区间同步数据
     */
    private void syncDataByTimeRange(SQLManager fromSqlManger, SQLManager toSqlManger,
                                   MapBuilder params, String startTime, String endTime) throws Exception {
        Map<String, Object> queryParams = MapUtil.builder(params.build())
                .put("startTime", startTime)
                .put("endTime", endTime)
                .build();

        List<EmrMzXycfYymx> itemList = fromSqlManger.select(SqlId.of(NAMESPACE,"findList"),EmrMzXycfYymx.class, queryParams);

        if (!itemList.isEmpty()) {
            log.infof(">>>>>>>>>>>>>>>>>>>时间区间 %s 到 %s 获取到 %d 条数据",startTime, endTime, itemList.size());

            // 检查内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double) usedMemory / maxMemory * 100;

            log.infof(">>>>>>>>>>>>>>>>>>>内存使用情况：%.2f%% (%d MB / %d MB)",
                     memoryUsage, usedMemory / 1024 / 1024, maxMemory / 1024 / 1024);

            // 如果内存使用超过70%，执行垃圾回收
            if (memoryUsage > 70) {
                log.infof(">>>>>>>>>>>>>>>>>>>内存使用率较高，执行垃圾回收");
                System.gc();
                // 垃圾回收后稍作等待
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 如果数据量超过5万条，进一步细分处理
            if (itemList.size() > 50000) {
                log.infof(">>>>>>>>>>>>>>>>>>>数据量过大(%d条)，分段处理避免GC压力", itemList.size());
                processBigDataInChunks(toSqlManger, itemList);
            } else {
                insertBatchData(toSqlManger, itemList);
            }

            // 插入完成后清理引用
            itemList.clear();
            itemList = null;

            // 强制垃圾回收
            System.gc();
        } else {
            log.infof(">>>>>>>>>>>>>>>>>>>时间区间 %s 到 %s 无数据", startTime, endTime);
        }
    }

    /**
     * 批量插入数据 - 分批处理避免内存溢出
     */
    private void insertBatchData(SQLManager toSqlManger, List<EmrMzXycfYymx> itemList) throws Exception {
        if (itemList == null || itemList.isEmpty()) {
            return;
        }

        int batchSize = 5000; // 每批处理100条数据
        int totalSize = itemList.size();

        log.infof(">>>>>>>>>>>>>>>>>>>开始分批插入数据，总计 %d 条，每批 %d 条", totalSize, batchSize);

        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<EmrMzXycfYymx> batchList = itemList.subList(i, endIndex);

            try {
                DSTransactionManager.start();
                toSqlManger.insertBatch(EmrMzXycfYymx.class, batchList);
                DSTransactionManager.commit();
                log.infof(">>>>>>>>>>>>>>>>>>>成功插入第 %d-%d 条数据", i + 1, endIndex);
            } catch (Exception e) {
                log.errorf("ORGID=%s——批量插入数据失败（第 %d-%d 条），中断同步操作。异常信息：%s",
                          ORGNAME, i + 1, endIndex, e.getMessage(), e);
                try {
                    DSTransactionManager.rollback();
                    log.infof(">>>>>>>>>>>>>>>>>>>事务已回滚");
                } catch (SQLException e1) {
                    log.errorf("回滚异常：%s", e1.getMessage(), e1);
                }
                throw new RuntimeException("批量插入数据失败，中断所有后续操作", e);
            }
        }

        log.infof(">>>>>>>>>>>>>>>>>>>所有数据插入完成，总计 %d 条", totalSize);
    }

    /**
     * 大数据分段处理 - 避免大数据量造成GC问题
     */
    private void processBigDataInChunks(SQLManager toSqlManger, List<EmrMzXycfYymx> itemList) throws Exception {
        int totalSize = itemList.size();
        int chunkSize = 10000; // 每段处理1万条数据

        log.infof(">>>>>>>>>>>>>>>>>>>开始分段处理大数据，总计 %d 条，每段 %d 条", totalSize, chunkSize);

        for (int i = 0; i < totalSize; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, totalSize);
            List<EmrMzXycfYymx> chunkList = itemList.subList(i, endIndex);

            log.infof(">>>>>>>>>>>>>>>>>>>处理第 %d-%d 条数据", i + 1, endIndex);

            // 处理当前段数据
            insertBatchData(toSqlManger, chunkList);

            // 每段处理完后检查内存并清理
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            long maxMemory = runtime.maxMemory();
            double memoryUsage = (double) usedMemory / maxMemory * 100;

            log.infof(">>>>>>>>>>>>>>>>>>>段处理完成，当前内存使用：%.2f%%", memoryUsage);

            // 如果内存使用超过60%，执行垃圾回收并等待
            if (memoryUsage > 60) {
                log.infof(">>>>>>>>>>>>>>>>>>>执行垃圾回收并等待");
                System.gc();
                try {
                    Thread.sleep(2000); // 等待2秒让GC完成
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        log.infof(">>>>>>>>>>>>>>>>>>>大数据分段处理完成，总计 %d 条", totalSize);
    }

    /**
     * 执行增量查询 - 当前月直接查询
     */
    private void executeIncrementalQuery(SQLManager fromSqlManger, SQLManager toSqlManger, MapBuilder params,
                                       String uniqueid, Long timestampsec, String startTime, String endTime) throws Exception {
        Map<String, Object> incrementalParams = MapUtil.builder(params.build())
                .put("uniqueid", uniqueid)
                .put("timestampsec", timestampsec)
                .put("startTime", startTime)
                .put("endTime", endTime)
                .build();

        List<EmrMzXycfYymx> itemList = fromSqlManger.select(SqlId.of(NAMESPACE,"findListByBus"),
                                                        EmrMzXycfYymx.class, incrementalParams);

        if (!itemList.isEmpty()) {
            log.infof(">>>>>>>>>>>>>>>>>>>增量同步获取到 %d 条数据", itemList.size());

            // 如果数据量超过5万条，进行分段处理
            if (itemList.size() > 50000) {
                log.infof(">>>>>>>>>>>>>>>>>>>增量数据量过大(%d条)，分段处理避免GC压力", itemList.size());
                processBigDataInChunks(toSqlManger, itemList);
            } else {
                insertBatchData(toSqlManger, itemList);
            }
        } else {
            log.infof(">>>>>>>>>>>>>>>>>>>增量同步无新数据");
        }
    }

    /**
     * 按月度区间执行增量同步 - 避免大数据量GC问题
     */
    private void executeIncrementalSyncByMonth(SQLManager fromSqlManger, SQLManager toSqlManger, MapBuilder params,
                                             String uniqueid, Long timestampsec, String startTime, String endTime) throws Exception {

        Date startDate = DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        // 按月遍历增量同步
        while (startCal.before(endCal) || startCal.equals(endCal)) {
            // 当月开始时间
            String monthStartTime;
            if (startCal.getTime().equals(startDate)) {
                // 第一个月：使用原始开始时间
                monthStartTime = startTime;
            } else {
                // 其他月份：从1号开始
                monthStartTime = String.format("%d-%02d-01 00:00:00",
                                             startCal.get(Calendar.YEAR),
                                             startCal.get(Calendar.MONTH) + 1);
            }

            // 当月结束时间
            Calendar monthEndCal = (Calendar) startCal.clone();
            monthEndCal.set(Calendar.DAY_OF_MONTH, monthEndCal.getActualMaximum(Calendar.DAY_OF_MONTH));
            monthEndCal.set(Calendar.HOUR_OF_DAY, 23);
            monthEndCal.set(Calendar.MINUTE, 59);
            monthEndCal.set(Calendar.SECOND, 59);

            String monthEndTime;
            if (monthEndCal.after(endCal)) {
                // 最后一个月：使用原始结束时间
                monthEndTime = endTime;
            } else {
                monthEndTime = DateUtil.format(monthEndCal.getTime(), "yyyy-MM-dd HH:mm:ss");
            }

            log.infof(">>>>>>>>>>>>>>>>>>>增量同步月度区间：%s 到 %s", monthStartTime, monthEndTime);

            // 执行当月增量查询
            executeIncrementalQuery(fromSqlManger, toSqlManger, params, uniqueid, timestampsec, monthStartTime, monthEndTime);

            // 移动到下一个月
            startCal.add(Calendar.MONTH, 1);
            startCal.set(Calendar.DAY_OF_MONTH, 1);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);

            // 检查是否已经超过结束时间
            if (startCal.after(endCal)) {
                break;
            }
        }
    }

    /**
     * 按月细分同步数据 - 避免大数据量造成GC问题
     */
    private void syncDataByMonthRange(SQLManager fromSqlManger, SQLManager toSqlManger,
                                    MapBuilder params, String yearStartTime, String yearEndTime) throws Exception {

        // 解析年度区间的开始和结束时间
        Date startDate = DateUtil.parse(yearStartTime, "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(yearEndTime, "yyyy-MM-dd HH:mm:ss");

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        // 按月遍历
        while (startCal.before(endCal) || startCal.equals(endCal)) {
            // 当月开始时间
            String monthStartTime;
            if (startCal.getTime().equals(startDate)) {
                // 第一个月：使用原始开始时间
                monthStartTime = yearStartTime;
            } else {
                // 其他月份：从1号开始
                monthStartTime = String.format("%d-%02d-01 00:00:00",
                                             startCal.get(Calendar.YEAR),
                                             startCal.get(Calendar.MONTH) + 1);
            }

            // 当月结束时间
            Calendar monthEndCal = (Calendar) startCal.clone();
            monthEndCal.set(Calendar.DAY_OF_MONTH, monthEndCal.getActualMaximum(Calendar.DAY_OF_MONTH));
            monthEndCal.set(Calendar.HOUR_OF_DAY, 23);
            monthEndCal.set(Calendar.MINUTE, 59);
            monthEndCal.set(Calendar.SECOND, 59);

            String monthEndTime;
            if (monthEndCal.after(endCal)) {
                // 最后一个月：使用原始结束时间
                monthEndTime = yearEndTime;
            } else {
                monthEndTime = DateUtil.format(monthEndCal.getTime(), "yyyy-MM-dd HH:mm:ss");
            }

            log.infof(">>>>>>>>>>>>>>>>>>>同步月度区间：%s 到 %s", monthStartTime, monthEndTime);

            // 同步当月数据
            syncDataByTimeRange(fromSqlManger, toSqlManger, params, monthStartTime, monthEndTime);

            // 移动到下一个月
            startCal.add(Calendar.MONTH, 1);
            startCal.set(Calendar.DAY_OF_MONTH, 1);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);

            // 检查是否已经超过结束时间
            if (startCal.after(endCal)) {
                break;
            }
        }
    }
}
