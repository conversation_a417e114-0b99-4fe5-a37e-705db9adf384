package cn.wingcloud.task.service;

import cn.wingcloud.po.EmrMzMjzbl;
import cn.wingcloud.po.EmrMzJcbg;

/**
 * EMR服务使用示例
 * 展示如何使用 EmrCommonService 公共服务类
 */
public class EmrServiceExample {

    /**
     * 门诊病历主表服务示例
     * 注意：EmrMzMjzbl 类必须实现 HasId 接口
     */
    public static class EmrMzMjzblServiceNew {
        private final EmrCommonService<EmrMzMjzbl> commonService;

        public EmrMzMjzblServiceNew() {
            // 初始化公共服务，传入NAMESPACE和实体类
            // EmrMzMjzbl 必须实现 HasId 接口
            this.commonService = new EmrCommonService<>("EmrMzMjzbl", EmrMzMjzbl.class);
        }

        public void execute() {
            commonService.execute();
        }
    }

    /**
     * 门诊检查报告服务示例
     * 注意：EmrMzJcbg 类必须实现 HasId 接口
     */
    public static class EmrMzJcbgServiceNew {
        private final EmrCommonService<EmrMzJcbg> commonService;

        public EmrMzJcbgServiceNew() {
            // 初始化公共服务，传入NAMESPACE和实体类
            // EmrMzJcbg 必须实现 HasId 接口
            this.commonService = new EmrCommonService<>("EmrMzJcbg", EmrMzJcbg.class);
        }

        public void execute() {
            commonService.execute();
        }
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 使用门诊病历主表服务
        EmrMzMjzblServiceNew mjzblService = new EmrMzMjzblServiceNew();
        mjzblService.execute();

        // 使用门诊检查报告服务
        EmrMzJcbgServiceNew jcbgService = new EmrMzJcbgServiceNew();
        jcbgService.execute();
    }
}
