findList
===
* 按时间区间查询列表
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || uniqueid as id,
    PATIENTID as patientid,
    AG<PERSON> as age,
    AGEUN<PERSON> as ageunit,
    <PERSON>Z<PERSON> as mzh,
    YX<PERSON> as yxh,
    <PERSON><PERSON><PERSON> as jcsj,
    Z<PERSON> as zd,
    ZDCODE as zdcode,
    JC<PERSON><PERSON> as jcbw,
    J<PERSON><PERSON> as jcmd,
    J<PERSON>Y<PERSON> as jcyq,
    JCFFHJS as jcffhjs,
    YXXBX as yxxbx,
    YXXZD as yxxzd,
    BGYS as bgys,
    JYB<PERSON><PERSON> as jybgks,
    JY<PERSON><PERSON><PERSON>MC as jybgksmc,
    SHY<PERSON> as shys,
    B<PERSON><PERSON> as bgsj,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_PACS
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || uniqueid as id,
    PATIENTID as patientid,
    AGE as age,
    AGEUNIT as ageunit,
    MZH as mzh,
    YXH as yxh,
    JCSJ as jcsj,
    ZD as zd,
    ZDCODE as zdcode,
    JCBW as jcbw,
    JCMD as jcmd,
    JCYQ as jcyq,
    JCFFHJS as jcffhjs,
    YXXBX as yxxbx,
    YXXZD as yxxzd,
    BGYS as bgys,
    JYBGKS as jybgks,
    JYBGKSMC as jybgksmc,
    SHYS as shys,
    BGSJ as bgsj,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_PACS
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
      AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
