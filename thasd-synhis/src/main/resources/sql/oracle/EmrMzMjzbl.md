findList
===
* 按时间区间查询列表
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || PATIENTID as patientid,
    IDCARDNO as idcardno,
    AGE as age,
    AGEUNIT as ageunit,
    MZH as mzh,
    MZTYPE as mztype,
    ISCZ as iscz,
    ISGM as isgm,
    GHDATE as ghdate,
    CREATEUSERNAME as createusername,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    0 as issq,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_CASE
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || PATIENTID as patientid,
    IDCARDNO as idcardno,
    AGE as age,
    AGEUNIT as ageunit,
    MZH as mzh,
    MZTYPE as mztype,
    ISCZ as iscz,
    ISGM as isgm,
    GHDATE as ghdate,
    CREATEUSERNAME as createusername,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    0 as issq,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_CASE
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
      AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
