findList
===
* 按时间区间查询列表
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || MJZBLID as mjzblid,
    ZDRQ as zdrq,
    ZDLX as zdlx,
    ZDMC as zdmc,
    ZDCODE as zdcode,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_CASE_DIAG
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || MJZBLID as mjzblid,
    ZDRQ as zdrq,
    ZDLX as zdlx,
    ZDMC as zdmc,
    ZDCODE as zdcode,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
    FROM VIEW_SAN_CLINIC_CASE_DIAG
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
      AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```

