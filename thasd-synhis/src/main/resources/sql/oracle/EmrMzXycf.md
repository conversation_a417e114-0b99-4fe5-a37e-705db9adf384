findList
===
* 按时间区间查询列表
```sql
SELECT * from (
   SELECT
    ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AG<PERSON> as age,
    AG<PERSON><PERSON><PERSON> as ageunit,
    <PERSON>Z<PERSON> as mzh,
    CFLX as cflx,
    CFR<PERSON> as cfrq,
    CFFYY_JS as cffyy_js,
    CFSHY_JS as cfshy_js,
    CFTPY_JS as cftpy_js,
    ZD as zd,
    ZDCODE as zdcode,
    CASH as cash,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
    SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AGE as age,
    AGEUNIT as ageunit,
    MZH as mzh,
    CFLX as cflx,
    CFRQ as cfrq,
    CFFYY_JS as cffyy_js,
    CFSHY_JS as cfshy_js,
    CFTPY_JS as cftpy_js,
    ZD as zd,
    ZDCODE as zdcode,
    CASH as cash,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
