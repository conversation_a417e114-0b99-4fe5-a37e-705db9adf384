findList
===
* 按时间区间查询列表
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    XYCF<PERSON> as xycfid,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as hisxuhao,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as hiszuxuhao,
    MXCODE as mxcode,
    MXNAME as mxname,
    <PERSON>XG<PERSON> as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    <PERSON><PERSON><PERSON> as dcjl,
    <PERSON><PERSON><PERSON> as jldw,
    <PERSON><PERSON><PERSON> as zjl,
    YY<PERSON> as yyts,
    <PERSON>YP<PERSON> as sypl,
    SYTJ as sytj,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
ORDER BY bustime
```

findListByBus
===
* 增量查询
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    XYCFID as xycfid,
    HISXUHAO as hisxuhao,
    HISZUXUHAO as hiszuxuhao,
    MXCODE as mxcode,
    MXNAME as mxname,
    MXGG as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    DCJL as dcjl,
    JLDW as jldw,
    ZJL as zjl,
    YYTS as yyts,
    SYPL as sypl,
    SYTJ as sytj,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
ORDER BY bustime
```
