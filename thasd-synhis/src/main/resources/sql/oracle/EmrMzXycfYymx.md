findList
===
* 按时间区间查询列表
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || XYCFID as xycfid,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as his<PERSON><PERSON><PERSON>,
    HISZ<PERSON><PERSON><PERSON><PERSON><PERSON> as his<PERSON><PERSON><PERSON><PERSON>,
    MXCODE as mxcode,
    MXNAME as mxname,
    MXG<PERSON> as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    DCJL as dcjl,
    JLD<PERSON> as jldw,
    <PERSON>J<PERSON> as zjl,
    YY<PERSON> as yyts,
    SYP<PERSON> as sypl,
    SYTJ as sytj,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    ${orgcode} || XYCFID as xycfid,
    HISXUHAO as hisxuhao,
    HISZUXUHAO as hiszuxuhao,
    MXCODE as mxcode,
    MXNAME as mxname,
    MXGG as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    DCJL as dcjl,
    JLDW as jldw,
    ZJL as zjl,
    YYTS as yyts,
    SYPL as sypl,
    SYTJ as sytj,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
