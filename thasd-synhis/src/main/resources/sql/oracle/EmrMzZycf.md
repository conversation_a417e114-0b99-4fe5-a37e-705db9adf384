findList
===
* 按时间区间查询列表
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AGE as age,
    AGEUNIT as ageunit,
    <PERSON><PERSON><PERSON> as mzh,
    CFLX as cflx,
    <PERSON>R<PERSON> as cfrq,
    CFFYY_J<PERSON> as cffyy_js,
    CFSHY_J<PERSON> as cfshy_js,
    CFTPY_JS as cftpy_js,
    CFHDY_JS as cfhdy_js,
    ZD as zd,
    ZDCODE as zdcode,
    CASH as cash,
    ZYYY<PERSON> as zyyyff,
    ZYJZF as zyjzf,
    JISHU as jishu,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_Z_PRESC
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
ORDER BY bustime
```

findListByBus
===
* 增量查询
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AGE as age,
    AGEUNIT as ageunit,
    MZH as mzh,
    CFLX as cflx,
    CFRQ as cfrq,
    CFFYY_JS as cffyy_js,
    CFSHY_JS as cfshy_js,
    CFTPY_JS as cftpy_js,
    CFHDY_JS as cfhdy_js,
    ZD as zd,
    ZDCODE as zdcode,
    CASH as cash,
    ZYYYFF as zyyyff,
    ZYJZF as zyjzf,
    JISHU as jishu,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_Z_PRESC
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
ORDER BY bustime
```
