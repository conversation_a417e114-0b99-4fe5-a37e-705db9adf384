findList
===
* 按时间区间查询列表
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    XY<PERSON><PERSON> as xycfid,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as hisxuhao,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as hiszuxuhao,
    MXCODE as mxcode,
    MXNAME as mxname,
    <PERSON>XG<PERSON> as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    <PERSON><PERSON><PERSON> as dcjl,
    J<PERSON><PERSON> as jldw,
    <PERSON>J<PERSON> as zjl,
    PAC<PERSON> as pack,
    SYP<PERSON> as sypl,
    TSYF as tsyf,
    R<PERSON><PERSON><PERSON> as remark,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_Z_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
ORDER BY bustime
```

findListByBus
===
* 增量查询
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    XYCFID as xycfid,
    HISXUHAO as hisxuhao,
    HISZUXUHAO as hiszuxuhao,
    MXCODE as mxcode,
    MXNAME as mxname,
    MXGG as mxgg,
    MXDW as mxdw,
    MXPRICE as mxprice,
    MXQUANTITY as mxquantity,
    MXCASH as mxcash,
    DCJL as dcjl,
    JLDW as jldw,
    ZJL as zjl,
    PACK as pack,
    SYPL as sypl,
    TSYF as tsyf,
    REMARK as remark,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_CLINIC_Z_PRESC_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
ORDER BY bustime
```
