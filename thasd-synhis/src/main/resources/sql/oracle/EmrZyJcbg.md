findList
===
* 按时间区间查询列表
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AG<PERSON> as age,
    AGEUN<PERSON> as ageunit,
    <PERSON><PERSON><PERSON> as zyh,
    <PERSON>X<PERSON> as yxh,
    <PERSON><PERSON><PERSON> as jcsj,
    <PERSON><PERSON> as zd,
    ZDCODE as zdcode,
    JCBW as jcbw,
    JCMD as jcmd,
    JCYQ as jcyq,
    JCFFHJS as jcffhjs,
    YXXBX as yxxbx,
    YXXZD as yxxzd,
    BGYS as bgys,
    JYBG<PERSON> as jybgks,
    JYBGKSMC as jybgksmc,
    <PERSON>Y<PERSON> as shys,
    <PERSON><PERSON><PERSON> as bgsj,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_INPATIENT_PACS
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
ORDER BY bustime
```

findListByBus
===
* 增量查询
```sql
SELECT
    ORIGINALID as originalid,
    ${orgcode} || ORIGINALID as id,
    PATIENTID as patientid,
    AGE as age,
    AGEUNIT as ageunit,
    ZYH as zyh,
    YXH as yxh,
    JCSJ as jcsj,
    ZD as zd,
    ZDCODE as zdcode,
    JCBW as jcbw,
    JCMD as jcmd,
    JCYQ as jcyq,
    JCFFHJS as jcffhjs,
    YXXBX as yxxbx,
    YXXZD as yxxzd,
    BGYS as bgys,
    JYBGKS as jybgks,
    JYBGKSMC as jybgksmc,
    SHYS as shys,
    BGSJ as bgsj,
    DEPTID as deptid,
    DEPTNAME as deptname,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_INPATIENT_PACS
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
ORDER BY bustime
```
