findList
===
* 按时间区间查询列表
```sql
SELECT * from (
    SELECT
        ROW_NUMBER() OVER (
            PARTITION BY originalid
            ORDER BY bustime DESC
        ) AS rn,
        ORIGINALID as originalid,
        ${orgcode} || ORIGINALID as id,
        ${orgcode} || PATIENTID as patientid,
        AG<PERSON> as age,
        AGE<PERSON><PERSON> as ageunit,
        <PERSON>Y<PERSON> as zyh,
        JYB<PERSON>H as jybgbh,
        ZD as zd,
        ZDCODE as zdcode,
        BBLX as bblx,
        <PERSON><PERSON><PERSON> as jymc,
        JYBGRQ as jybgrq,
        JYBGYS as jybgys,
        <PERSON>Y<PERSON><PERSON><PERSON> as jybgks,
        <PERSON><PERSON>BGKSMC as jybgksmc,
        <PERSON>Y<PERSON> as shys,
        DEPTI<PERSON> as deptid,
        DEPTNAME as deptname,
        ${orgcode} as orgcode,
        ${orgname} as orgname,
        ${orgid} as orgid,
        uniqueid,
        timestampsec,
        bustime
    FROM VIEW_SAN_INPATIENT_LIS
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
    SELECT
      ROW_NUMBER() OVER (
            PARTITION BY originalid
            ORDER BY bustime DESC
        ) AS rn,
        ORIGINALID as originalid,
        ${orgcode} || ORIGINALID as id,
        ${orgcode} || PATIENTID as patientid,
        AGE as age,
        AGEUNIT as ageunit,
        ZYH as zyh,
        JYBGBH as jybgbh,
        ZD as zd,
        ZDCODE as zdcode,
        BBLX as bblx,
        JYMC as jymc,
        JYBGRQ as jybgrq,
        JYBGYS as jybgys,
        JYBGKS as jybgks,
        JYBGKSMC as jybgksmc,
        SHYS as shys,
        DEPTID as deptid,
        DEPTNAME as deptname,
        ${orgcode} as orgcode,
        ${orgname} as orgname,
        ${orgid} as orgid,
        uniqueid,
        timestampsec,
        bustime
    FROM VIEW_SAN_INPATIENT_LIS
    WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                      AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
      AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
