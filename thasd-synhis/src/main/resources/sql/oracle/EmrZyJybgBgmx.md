findList
===
* 按时间区间查询列表
```sql
SELECT * from (
  SELECT
      ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} ||  TO_CHAR(ORA_HASH(ORIGINALID, **********), 'FMXXXXXXXX') || TO_CHAR(ORA_HASH(TO_CHAR(bustime, 'YYYYMMDDHH24MISS'), **********), 'FMXXXXXXXX') AS id,
    BARCODE as barcode,
    ${orgcode} || JYBGID as jybgid,
    LISCODE as liscode,
    LISXMCODE as lisxmcode,
    LISXM as lisxm,
    JYJG as jyjg,
    JYD<PERSON> as jydw,
    JYJ<PERSON><PERSON> as jyjgbz,
    JYCK<PERSON> as jyckz,
    JY<PERSON><PERSON> as jyxh,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_INPATIENT_LIS_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS')
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
) where rn <= 1
ORDER BY bustime,uniqueid
```

findListByBus
===
* 增量查询
```sql
SELECT * from (
  SELECT
    ROW_NUMBER() OVER (
        PARTITION BY originalid
        ORDER BY bustime DESC
    ) AS rn,
    ORIGINALID as originalid,
    ${orgcode} ||  TO_CHAR(ORA_HASH(ORIGINALID, **********), 'FMXXXXXXXX') || TO_CHAR(ORA_HASH(TO_CHAR(bustime, 'YYYYMMDDHH24MISS'), **********), 'FMXXXXXXXX') AS id,
    BARCODE as barcode,
    ${orgcode} || JYBGID as jybgid,
    LISCODE as liscode,
    LISXMCODE as lisxmcode,
    LISXM as lisxm,
    JYJG as jyjg,
    JYDW as jydw,
    JYJGBZ as jyjgbz,
    JYCKZ as jyckz,
    JYXH as jyxh,
    ${orgcode} as orgcode,
    ${orgname} as orgname,
    ${orgid} as orgid,
    uniqueid,
    timestampsec,
    bustime
FROM VIEW_SAN_INPATIENT_LIS_DETAIL
WHERE bustime BETWEEN TO_DATE('${startTime}', 'YYYY-MM-DD HH24:MI:SS') 
                  AND TO_DATE('${endTime}', 'YYYY-MM-DD HH24:MI:SS')
  AND (timestampsec > ${timestampsec} OR (timestampsec = ${timestampsec} AND uniqueid > '${uniqueid}'))
) where rn <= 1
ORDER BY bustime,uniqueid
```
