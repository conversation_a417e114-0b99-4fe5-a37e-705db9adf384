maxIdn
===
* 查询目标库最大idn
```sql
select ISNULL(MAX(idn),0) from rmisp.dbo.emr_mz_mjzbl WITH(NOLOCK) where orgid = ${orgid}
```

maxIdnInfo
===
* 获取最大idn对应的详细信息
```sql
select uniqueid, timestampsec, bustime 
from rmisp.dbo.emr_mz_mjzbl WITH(NOLOCK) 
where orgid = ${orgid} and idn = ${idn}
```
selectInByIds
===
* 根据上传Id查询数据库中是否有记录
```sql
select id from rmisp.dbo.emr_mz_mjzbl where id in ( #{join(ids)} )
```