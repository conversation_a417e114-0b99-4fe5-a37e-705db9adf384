package cn.wingcloud.jfinal.pojo;

/**
 * TreeJson.java
 * @Package cn.eninesoft.pojo
 * @ClassName:TreeJson
 * <AUTHOR>
 * @date 2015年5月27日 上午11:07:24 
 * @version V1.0
 */
public class Tree {
	
	private Object id;
	private String name;
	private Object pId;
	private boolean clickf = true;
	private boolean open = true;
	private String target = "rightframes"; 
	private String icon;
	
	public Tree() {
	}
	public Tree(Object id,String name,Object pid) {
		this.id = id;
		this.name = name;
		this.pId = pid;
	}
	public Tree(Object id,String name,Object pid,String clickf,String open,String icon) {
		this.id = id;
		this.name = name;
		this.pId = pid;
		this.clickf = null != clickf && !"".equals(clickf) ? new Boolean(clickf) : true;
		this.open = null != open && !"".equals(open) ? new Boolean(open) : false;
		this.icon = icon;
	}
	public Tree(Object id,String name,Object pid,String clickf,String open,String icon,String target) {
		this.id = id;
		this.name = name;
		this.pId = pid;
		this.clickf = null != clickf && !"".equals(clickf) ? new Boolean(clickf) : true;
		this.open = null != open && !"".equals(open) ? new Boolean(open) : false;
		this.icon = icon;
		this.target = target;
	}
	public Object getId() {
		return id;
	}
	public void setId(Object id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public boolean isClickf() {
		return clickf;
	}
	public void setClickf(boolean clickf) {
		this.clickf = clickf;
	}
	public boolean isOpen() {
		return open;
	}
	public void setOpen(boolean open) {
		this.open = open;
	}
	public String getTarget() {
		return target;
	}
	public void setTarget(String target) {
		this.target = target;
	}
	public Object getpId() {
		return pId;
	}
	public void setpId(Object pId) {
		this.pId = pId;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	
}