package cn.wingcloud.jfinal.service;

import java.sql.SQLException;

import cn.wingcloud.jfinal.pojo.Menu;
import cn.wingcloud.jfinal.util.ParamUtil;

public interface IMenuService {
	public String get(ParamUtil param) throws SQLException;
	public String pagelst(ParamUtil param) throws SQLException;
	
	public String save(Menu menu,ParamUtil param) throws SQLException;
	public String edit(Menu menu,ParamUtil param) throws SQLException;
	public String delete(ParamUtil param) throws SQLException;
	
	public String getList(ParamUtil paramUtil) throws SQLException;
	
	public String menuTreeSingle(ParamUtil paramUtil) throws SQLException;
	
	public String list(ParamUtil param) throws SQLException;
	
	public String rootChildrenMenuTree(ParamUtil paramUtil) throws SQLException;//查询顶级节点的直系子节点
}