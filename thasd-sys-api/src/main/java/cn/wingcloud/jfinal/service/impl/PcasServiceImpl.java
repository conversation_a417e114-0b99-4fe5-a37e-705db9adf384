package cn.wingcloud.jfinal.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.wingcloud.jfinal.callback.ListDataJson;
import cn.wingcloud.jfinal.pojo.CadsDictionary;
import cn.wingcloud.jfinal.service.IPcasService;
import cn.wingcloud.jfinal.util.ParamUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.util.List;

public class PcasServiceImpl implements IPcasService {

    @Override
    public String getList(ParamUtil paramUtil) {

        List<CadsDictionary> cadsDictionaryList = CadsDictionary.dao.find("select * from cads_dictionary where level = 0;");
        return paramUtil.restful(JSON.toJSONString(new ListDataJson("0","", cadsDictionaryList), new SerializerFeature[] {SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteNonStringKeyAsString}));

    }

    @Override
    public String getTreeList(ParamUtil paramUtil) {
        String pid = paramUtil.getPara("pid","");
        StringBuilder sqlBuilder = new StringBuilder("select * from cads_dictionary ");//level in (0,1,2);
        if(pid.equals("")){
            sqlBuilder.append(" where level = 0");
        }else{
            sqlBuilder.append(" where parentcode = '"+pid+"'");
        }
        List<CadsDictionary> cadsDictionaryList = CadsDictionary.dao.find(sqlBuilder.toString());
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setIdKey("id");
        treeNodeConfig.setNameKey("name");
        treeNodeConfig.setParentIdKey("pid");
        treeNodeConfig.setWeightKey("id");
        // 最大递归深度
        treeNodeConfig.setDeep(3);
        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(cadsDictionaryList, pid.equals("") ? "0" :pid, treeNodeConfig, (treeNode, tree) -> {

                    tree.setId(treeNode.getCode());
                    tree.setName(treeNode.getName());
                    tree.setParentId(treeNode.getParentcode() == null ? "0" : treeNode.getParentcode());
                    tree.putExtra("level", treeNode.getLevel());
                    tree.putExtra("provincecode", treeNode.getProvincecode());
                    tree.putExtra("citycode", treeNode.getCitycode());
                    tree.putExtra("areacode", treeNode.getAreacode());
                    tree.putExtra("streetcode", treeNode.getStreetcode());
                    tree.putExtra("haveChild", treeNode.getLevel() == 2 ? false : true );

        });
        return paramUtil.restful(JSON.toJSONString(new ListDataJson("0","", treeNodes), new SerializerFeature[] {SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteNonStringKeyAsString}));
    }
}
