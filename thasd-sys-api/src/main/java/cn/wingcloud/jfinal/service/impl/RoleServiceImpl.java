package cn.wingcloud.jfinal.service.impl;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.nutz.log.Log;
import org.nutz.log.Logs;

import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.callback.BaseJson;
import cn.wingcloud.jfinal.callback.ListDataJson;
import cn.wingcloud.jfinal.callback.PageJson;
import cn.wingcloud.jfinal.callback.RecordJson;
import cn.wingcloud.jfinal.callback.StatusJson;
import cn.wingcloud.jfinal.pojo.Department;
import cn.wingcloud.jfinal.pojo.Menu;
import cn.wingcloud.jfinal.pojo.Organization;
import cn.wingcloud.jfinal.pojo.Resource;
import cn.wingcloud.jfinal.pojo.Role;
import cn.wingcloud.jfinal.pojo.RoleDept;
import cn.wingcloud.jfinal.pojo.RoleMenu;
import cn.wingcloud.jfinal.pojo.RoleOrg;
import cn.wingcloud.jfinal.pojo.RoleResource;
import cn.wingcloud.jfinal.pojo.Tree;
import cn.wingcloud.jfinal.pojo.TreeRoleFb;
import cn.wingcloud.jfinal.pojo.User;
import cn.wingcloud.jfinal.pojo.UserRole;
import cn.wingcloud.jfinal.service.IRoleService;
import cn.wingcloud.jfinal.util.BizUtil;
import cn.wingcloud.jfinal.util.ParamUtil;
import cn.wingcloud.jfinal.util.StringUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jfinal.kit.JsonKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.DbKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public class RoleServiceImpl implements IRoleService{
    private static final Log log = Logs.get();

	@Override
	public String get(ParamUtil param) throws SQLException {
		try {
			String id = param.getPara("id");
			Role role = Role.dao.findById(id);
			return param.restful(JsonKit.toJson(new RecordJson("200","suc", role)));
		} catch (Exception e) {
			log.error("RoleServiceImpl-get", e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("5001","系统错误-5001",false)));
		}
	}

	@Override
	public String pagelst(ParamUtil param) throws SQLException {
		try {
			String select  = "select * ";
			String name = param.getPara("name");
			StringBuilder sqlExceptSelect  = new StringBuilder("  from BASIC_ROLE where ISDEL=0 ");
			if(null != name && !"".equals(name)){
				sqlExceptSelect.append(" and NAME like '%"+ name +"%'");
			}
			String orgid = param.getPara("orgid");
			if(null != orgid && !"".equals(orgid) && !"null".equals(orgid)){
				sqlExceptSelect.append(" and orgid ='"+ orgid +"'");
			}
			User user = BizUtil.getUser(param);
			int userCategrory = Integer.parseInt(user.get("category").toString());
			if(userCategrory ==1) {//1-超级管理员
				//查询所有
			}else if(userCategrory ==2) {//2-二级管理员
				//只能查询组织权限内的
				JSONObject authUser = BizUtil.getAuthUser(param);
				String haveStr = StringUtil.valFromlistToStringHave(authUser.getString("roleorgids"), ",");
				sqlExceptSelect.append(" AND category = 0 AND orgid IN ("+haveStr+")");
			}else {//普通员工禁止查询角色
				sqlExceptSelect.append(" and ID = '' ");
			}
			sqlExceptSelect.append(" order by SORTCODE asc");
			Page<Role> page = Role.dao.paginate(param.getPageNumber(), param.getPageSize(), select, sqlExceptSelect.toString());
			return param.restful(JSON.toJSONString(new PageJson<Role>("0","", page), new SerializerFeature[] {SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteNonStringKeyAsString}));

		} catch (Exception e) {
			log.error("RoleServiceImpl-pagelst", e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
		}
	}
	
	@Override
	public String save(Role role,ParamUtil param) throws SQLException {
		try {
			String orgid = role.getOrgid();
			if(null == orgid || "".equals(orgid)) {
				return param.restful(JsonKit.toJson(new BaseJson("500","组织ID不能为空")));
			}else {
				role.init();
				role.authUserCreator(param);
				Organization org = Organization.dao.findById(orgid);
				role.setOrgname(org.getName());
				role.setOrgcode(org.getCode());
				role.save();
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String edit(Role role,ParamUtil param) throws SQLException {
		try {
			String orgid = role.getOrgid();
			if(null == orgid || "".equals(orgid)) {
				return param.restful(JsonKit.toJson(new BaseJson("500","组织ID不能为空")));
			}else {
				Organization org = Organization.dao.findById(orgid);
				role.setOrgname(org.getName());
				role.setOrgcode(org.getCode());
				role.updateObj(param);
				role.update();
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String delete(ParamUtil param) throws SQLException {
		try {
			String[] ids = param.getParaValues("id");
			for (String id : ids) {
				Role role = Role.dao.findById(id);
				role.set("isdel", 1);
				role.update();
			}
			return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	//给用户A赋角色，查询可操作的角色数据：（1）用户A所在组织的角色数据；（2）当前登录用户所创建的角色数据（createid）
	@Override
	public String getList(ParamUtil paramUtil) throws SQLException {
		try {
			String userid = paramUtil.getPara("userid");//当前需要赋予角色所操作的用户
			String category = paramUtil.getPara("category");
			
			User user = BizUtil.getUser(paramUtil);
			if(null == userid || "".equals(userid)) {
				return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
			}if(null == category || "".equals(category)) {
				return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
			}else if(null == user) {
				return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
			}else {
				StringBuilder sql  = new StringBuilder();
				if(user.getCategory() == 1) {//1-超级管理员
					sql.append("select * from BASIC_ROLE where ISDEL=0 and category='"+category+"' and id not in (select roleid from BASIC_USER_ROLE where ISDEL=0  and userid='"+userid+"') order by SORTCODE asc" );
				}else if(user.getCategory() == 2) {//2-二级管理员
					JSONObject authUser = BizUtil.getAuthUser(paramUtil);
					String haveStr = StringUtil.valFromlistToStringHave(authUser.getString("roleorgids"), ",");
					sql.append("select * from (select * from BASIC_ROLE where ISDEL=0  and category='"+category+"'"
							+ "and orgid in ("+ haveStr +") "
							+ " union "
//							+ "select * from basic_role where isdel=0 and category = '0'  and CREATEID='"+user.getId()+"') "
							+ "select * from basic_role where isdel=0 and category = '0' ) as tb "

							+ " where ID not in (select roleid from basic_user_role where  isdel=0  and userid='"+userid+"') "
							+ "order by SORTCODE asc");
				}else {//0-普通人员：禁止查询
					sql.append("select * from BASIC_ROLE where ISDEL=0 and ID='' order by SORTCODE asc" ); 
				}
				//二级管理员查询授权的组织
				
				List<Role> list = Role.dao.find(sql.toString());
				return paramUtil.restful(JSON.toJSONString(new ListDataJson("0","", list), new SerializerFeature[] {SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteNonStringKeyAsString}));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
		}
	}

	@Override
	public String saveRoleDept(ParamUtil param) throws SQLException {
		try {
			String roleid = param.getPara("roleid");
			String[] deptids = param.getPara("ids").split(",");
			Role role = Role.dao.findById(roleid);
			if(null == role) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				Db.update("DELETE FROM BASIC_ROLE_DEPT where roleid='"+roleid+"'");
				for (String deptid : deptids) {
					RoleDept roleDept = new RoleDept();
					Department dept = Department.dao.findById(deptid);
					if(null != dept) {
						roleDept.init();
						roleDept.authUserCreator(param);
						roleDept.setRoleid(roleid);
						roleDept.setDeptid(deptid);
						roleDept.save();
					}
				}
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String saveRoleMenu(ParamUtil param) throws SQLException {
		try {
			String roleid = param.getPara("roleid");
			String[] menuids = param.getParaValues("ids");
			Role role = Role.dao.findById(roleid);
			if(null == role) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				Db.update("DELETE FROM BASIC_ROLE_MENU where roleid='"+roleid+"'");
				for (String menuid : menuids) {
					RoleMenu roleMenu = new RoleMenu();
					Menu menu = Menu.dao.findById(menuid);
					if(null != menu) {
						roleMenu.init();
						roleMenu.authUserCreator(param);
						roleMenu.setRoleid(roleid);
						roleMenu.setMenuid(menuid);
						roleMenu.save();
					}
				}
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String saveRoleResource(ParamUtil param) throws SQLException {
		try {
			String roleid = param.getPara("roleid");
			String[] resourceids = param.getParaValues("ids");
			Role role = Role.dao.findById(roleid);
			if(null == role) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				Db.update("DELETE FROM BASIC_ROLE_RESOURCE where roleid='"+roleid+"'");
				for (String resourceid : resourceids) {
					RoleResource roleResrouce = new RoleResource();
					Resource resource = Resource.dao.findById(resourceid);
					if(null != resource) {
						roleResrouce.init();
						roleResrouce.authUserCreator(param);
						roleResrouce.setRoleid(roleid);
						roleResrouce.setResourceid(resourceid);
						roleResrouce.save();
					}
				}
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}
	
	public String deptTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			JSONObject authUser = ApiFunction.getAuthUser(param.getPara("alibabakey"));
			if(null == authUser) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				String orgid = (String) authUser.get("orgid");
				if(null == orgid || "".equals(orgid)) {
					return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
				}else {
					String sql = "select * from BASIC_DEPARTMENT where ISDEL=0 and orgid='"+orgid+"' order by SORTCODE asc ";
					List<Record> deptList = ApiFunction.list(sql);
					List<Tree> trees = new ArrayList<Tree>();
					trees.add(new Tree(-1, "根目录", 0, rootck, "true", pth + ricon, target));
					for (Record dept : deptList) {
						trees.add(new Tree(dept.getStr("id"), dept.getStr("name"),dept.getStr("parentid"), "", "true", "", target));
					}
					return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
				}
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}
	
	public String checkedDeptTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String id = param.getPara("id", "empty");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			JSONObject authUser = ApiFunction.getAuthUser(param.getPara("alibabakey"));
			
			if(null == id || "".equals(id)) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else if(null == authUser) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				String orgid = (String) authUser.get("orgid");
				if(null == orgid || "".equals(orgid)) {
					return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
				}else {
					String sql = "select * from BASIC_DEPARTMENT where ISDEL=0 and orgid='"+orgid+"' order by sort SORTCODE asc ";
					List<Record> deptList = ApiFunction.list(sql);
					List<Record> roleDeptList = ApiFunction.list("select * from BASIC_ROLE_DEPT where ISDEL=0 and roleid = ? ",new Object[]{id});
					List<TreeRoleFb> trees = new ArrayList<TreeRoleFb>();
					trees.add(new TreeRoleFb(-1, "根目录", 0, "",false,rootck, "true", pth + ricon, target));
					int pgLen = roleDeptList.size();
					if(pgLen > 0){
						for (int i = 0; i < pgLen; i++) {
							Record roleDept  = roleDeptList.get(i);
							for (int j = 0; j < deptList.size(); j++) {
								Record dept  = deptList.get(j);
								if(dept.getStr("id").equals(roleDept.getStr("deptid"))){
									dept.set("checked",true);
								}
							}
						}
					}
					for (Record record : deptList) {
						trees.add(new TreeRoleFb(record.getStr("id"), record.getStr("name"),record.getStr("parentid"),record.getStr("menuid"),Boolean.valueOf(record.get("checked","false").toString()), "", "true", "", target));
					}
					
				return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
				}
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String menuTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			String sql = "select * from BASIC_MENU where ISDEL=0 order by SORTCODE asc ";
			List<Record> menuList = ApiFunction.list(sql);
			List<Tree> trees = new ArrayList<Tree>();
			trees.add(new Tree(-1, "根目录", 0, rootck, "true", pth + ricon, target));
			for (Record menu : menuList) {
				trees.add(new Tree(menu.getStr("id"), menu.getStr("name"),menu.getStr("parentid"), "", "true", "", target));
			}
			return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String checkedMenuTree(ParamUtil param) throws SQLException {
		try {
		String pth = param.getPara("pth", "");
		String id = param.getPara("id", "empty");
		String ricon = param.getPara("ricon", "");
		String target = param.getPara("target", "");
		String rootck = param.getPara("rootck", "");
		
		if(null == id || "".equals(id)) {
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}else {
				String sql = "select * from BASIC_MENU where ISDEL=0 order by sort SORTCODE asc ";
				List<Record> menuList = ApiFunction.list(sql);
				List<Record> roleMenuList = ApiFunction.list("select * from BASIC_ROLE_MENU where ISDEL=0 and roleid = ? ",new Object[]{id});
				List<TreeRoleFb> trees = new ArrayList<TreeRoleFb>();
				trees.add(new TreeRoleFb(-1, "根目录", 0, "",false,rootck, "true", pth + ricon, target));
				int pgLen = roleMenuList.size();
				if(pgLen > 0){
					for (int i = 0; i < pgLen; i++) {
						Record roleMenu  = roleMenuList.get(i);
						for (int j = 0; j < menuList.size(); j++) {
							Record menu  = menuList.get(j);
							if(menu.getStr("id").equals(roleMenu.getStr("menuid"))){
								menu.set("checked",true);
							}
						}
					}
				}
				for (Record record : menuList) {
					trees.add(new TreeRoleFb(record.getStr("id"), record.getStr("name"),record.getStr("parentid"),record.getStr("menuid"),Boolean.valueOf(record.get("checked","false").toString()), "", "true", "", target));
				}
				
			return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
		}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
		
	}

	@Override
	public String resourceTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			String sql = "select * from BASIC_RESOURCE where ISDEL=0 order by SORTCODE asc ";
			List<Record> resourceList = ApiFunction.list(sql);
			List<Tree> trees = new ArrayList<Tree>();
			trees.add(new Tree(-1, "根目录", 0, rootck, "true", pth + ricon, target));
			for (Record resource : resourceList) {
				trees.add(new Tree(resource.getStr("id"), resource.getStr("name"),resource.getStr("menuid"), "", "true", "", target));
			}
			return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String checkedResourceTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String id = param.getPara("id", "empty");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			
			if(null == id || "".equals(id)) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
					String sql = "select * from BASIC_RESOURCE where ISDEL=0 order by sort SORTCODE asc ";
					List<Record> resourceList = ApiFunction.list(sql);
					List<Record> roleResourceList = ApiFunction.list("select * from BASIC_ROLE_RESOURCE where ISDEL=0 and roleid = ? ",new Object[]{id});
					List<TreeRoleFb> trees = new ArrayList<TreeRoleFb>();
					trees.add(new TreeRoleFb(-1, "根目录", 0, "",false,rootck, "true", pth + ricon, target));
					int pgLen = roleResourceList.size();
					if(pgLen > 0){
						for (int i = 0; i < pgLen; i++) {
							Record roleResource  = roleResourceList.get(i);
							for (int j = 0; j < resourceList.size(); j++) {
								Record resource  = resourceList.get(j);
								if(resource.getStr("id").equals(roleResource.getStr("resourceid"))){
									resource.set("checked",true);
								}
							}
						}
					}
					for (Record record : resourceList) {
						trees.add(new TreeRoleFb(record.getStr("id"), record.getStr("name"),record.getStr("menuid"),record.getStr("resourceid"),Boolean.valueOf(record.get("checked","false").toString()), "", "true", "", target));
					}
					
				return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String saveRoleMenuAndResource(ParamUtil param) throws SQLException {
		try {
			String roleid = param.getPara("roleid");
			String[] menuids = param.getPara("menuids").split(",");
			String[] resourceids = param.getPara("resourceids").split(",");
			
			Role role = Role.dao.findById(roleid);
			if(null == role) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				Db.update("DELETE FROM  BASIC_ROLE_MENU where roleid='"+roleid+"'");
				for (String menuid : menuids) {
					RoleMenu roleMenu = new RoleMenu();
					Menu menu = Menu.dao.findById(menuid);
					if(null != menu) {
						roleMenu.init();
						roleMenu.authUserCreator(param);
						roleMenu.setRoleid(roleid);
						roleMenu.setMenuid(menuid);
						roleMenu.save();
					}
				}
				
				Db.update("DELETE FROM  BASIC_ROLE_RESOURCE where roleid='"+roleid+"'");
				for (String resourceid : resourceids) {
					RoleResource roleResrouce = new RoleResource();
					Resource resource = Resource.dao.findById(resourceid);
					if(null != resource) {
						roleResrouce.init();
						roleResrouce.authUserCreator(param);
						roleResrouce.setRoleid(roleid);
						roleResrouce.setResourceid(resourceid);
						roleResrouce.save();
					}
				}
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String getExistingRoles(ParamUtil paramUtil) throws SQLException {
		try {
			String userid = paramUtil.getPara("userid");
			if(null == userid || "".equals(userid)) {
				return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
			}else {
				User user = User.dao.findById(userid);
				if(null == user) {
					return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
				}else {
					StringBuilder sql  = new StringBuilder();
					sql.append("select r.* from BASIC_USER_ROLE ur "
							+ " inner join BASIC_ROLE r on r.id = ur.roleid"
							+ " where ur.userid='"+userid+"' AND ur.isdel = 0 ");
					sql.append(" order by r.SORTCODE asc");
					List<Role> list = Role.dao.find(sql.toString());
					return paramUtil.restful(JSON.toJSONString(new ListDataJson("0","", list), new SerializerFeature[] {SerializerFeature.WriteMapNullValue, SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteNonStringKeyAsString}));
				}
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
		}
	}

	@Override
	public String delUserRole(ParamUtil paramUtil) throws SQLException {
		try {
			String[] roleids = paramUtil.getParaValues("id");
			String userid = paramUtil.getPara("userid");
			if(null == userid || "".equals(userid)) {
				return paramUtil.restful(JsonKit.toJson(new BaseJson("500","Server-500:系统内部错误")));
			}else {
				for (String roleid : roleids) {
					List<UserRole> userRoles = UserRole.dao.find("select * from BASIC_USER_ROLE where ISDEL=0 AND userid='"+userid+"' AND roleid='"+roleid+"'");
					for (UserRole userRole : userRoles) {
						userRole.set("isdel", 1);
						userRole.update();
					}
				}
				return paramUtil.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return paramUtil.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String saveRoleOrg(ParamUtil param) throws SQLException {
		try {
			String roleid = param.getPara("roleid");
			String[] orgids = param.getPara("ids").split(",");
			Role role = Role.dao.findById(roleid);
			if(null == role) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				Db.update("DELETE FROM BASIC_ROLE_ORG where roleid='"+roleid+"'");
				for (String orgid : orgids) {
					RoleOrg roleOrg = new RoleOrg();
					Organization org = Organization.dao.findById(orgid);
					if(null != org && !"0".equals(org.getParentid()) && !"000000000000000000000000000000000000".equals(org.getParentid())) {
						roleOrg.init();
						roleOrg.authUserCreator(param);
						roleOrg.setRoleid(roleid);
						roleOrg.setOrgid(orgid);
						roleOrg.save();
					}
				}
				return param.restful(JsonKit.toJson(new StatusJson("200","suc",true)));
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String orgTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			JSONObject authUser = ApiFunction.getAuthUser(param.getPara("alibabakey"));
			if(null == authUser) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				String orgid = (String) authUser.get("orgid");
				if(null == orgid || "".equals(orgid)) {
					return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
				}else {
					String sql = "select * from BASIC_ORGANIZATION where ISDEL=0 and orgid='"+orgid+"' order by SORTCODE asc ";
					List<Record> deptList = ApiFunction.list(sql);
					List<Tree> trees = new ArrayList<Tree>();
					trees.add(new Tree(-1, "根目录", 0, rootck, "true", pth + ricon, target));
					for (Record dept : deptList) {
						trees.add(new Tree(dept.getStr("id"), dept.getStr("name"),dept.getStr("parentid"), "", "true", "", target));
					}
					return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
				}
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}

	@Override
	public String checkedOrgTree(ParamUtil param) throws SQLException {
		try {
			String pth = param.getPara("pth", "");
			String id = param.getPara("id", "empty");
			String ricon = param.getPara("ricon", "");
			String target = param.getPara("target", "");
			String rootck = param.getPara("rootck", "");
			JSONObject authUser = ApiFunction.getAuthUser(param.getPara("alibabakey"));
			
			if(null == id || "".equals(id)) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else if(null == authUser) {
				return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
			}else {
				String orgid = (String) authUser.get("orgid");
				if(null == orgid || "".equals(orgid)) {
					return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
				}else {
					String sql = "select * from BASIC_ORGANIZATION where ISDEL=0 and orgid='"+orgid+"' order by sort SORTCODE asc ";
					List<Record> deptList = ApiFunction.list(sql);
					List<Record> roleDeptList = ApiFunction.list("select * from BASIC_ROLE_ORG where ISDEL=0 and roleid = ? ",new Object[]{id});
					List<TreeRoleFb> trees = new ArrayList<TreeRoleFb>();
					trees.add(new TreeRoleFb(-1, "根目录", 0, "",false,rootck, "true", pth + ricon, target));
					int pgLen = roleDeptList.size();
					if(pgLen > 0){
						for (int i = 0; i < pgLen; i++) {
							Record roleDept  = roleDeptList.get(i);
							for (int j = 0; j < deptList.size(); j++) {
								Record dept  = deptList.get(j);
								if(dept.getStr("id").equals(roleDept.getStr("deptid"))){
									dept.set("checked",true);
								}
							}
						}
					}
					for (Record record : deptList) {
						trees.add(new TreeRoleFb(record.getStr("id"), record.getStr("name"),record.getStr("parentid"),record.getStr("menuid"),Boolean.valueOf(record.get("checked","false").toString()), "", "true", "", target));
					}
					
				return param.restful(JsonKit.toJson(new RecordJson("200","suc", trees)));
				}
			}
		} catch (Exception e) {
			log.error(e);
			DbKit.getConfig().getConnection().rollback();
			return param.restful(JsonKit.toJson(new StatusJson("500","Server-500:系统内部错误",false)));
		}
	}
	
}
