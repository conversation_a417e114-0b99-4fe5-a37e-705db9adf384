package cn.wingcloud.jfinal.action;

import java.sql.SQLException;

import org.nutz.log.Log;
import org.nutz.log.Logs;

import com.jfinal.core.Controller;

import cn.wingcloud.jfinal.pojo.DicCategory;
import cn.wingcloud.jfinal.service.IDicCategoryService;
import cn.wingcloud.jfinal.service.impl.DicCategoryServiceImpl;
import cn.wingcloud.jfinal.util.ParamUtil;

public class DicCategoryController extends Controller {
	private static final Log log = Logs.get();
	private IDicCategoryService service = new DicCategoryServiceImpl();
	
	public void get(){
    	String json = null;
		try {
			json = service.get(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询字典类别信息时出错", e);
		} 
		renderText(json);
    }
    
    public void add(){
		String json = null;
		try {
			DicCategory dicCategory = getModel(DicCategory.class, "" , true);
			json = service.save(dicCategory, new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("添加字典类别信息时出错", e);
		}
    	renderText(json);
    }
    
    public void edit(){
    	String json = null;
    	DicCategory dicCategory = getModel(DicCategory.class, "" , true);
		try {
			json = service.edit(dicCategory, new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("修改字典类别信息时出错", e);
		}
		renderText(json);
    }
    
    public void del(){
    	String json = null;
    	try {
			json = service.delete(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("删除字典类别信息时出错", e);
		} 
		renderText(json);
    }
    
    public void pagelst(){
    	String json = null;
		try {
			json = service.pagelst(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询字典类别信息时出错", e);
		} 
		renderText(json);
    }
    
    public void getList(){
    	String json = null;
		try {
			json = service.getList(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询字典类别时出错", e);
		} 
		renderText(json);
    }
    
    public void dicCategoryTree(){
    	String json = null;
		try {
			json = service.dicCategoryTree(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询字典类别树时出错", e);
		} 
		renderText(json);
    }
    
}
