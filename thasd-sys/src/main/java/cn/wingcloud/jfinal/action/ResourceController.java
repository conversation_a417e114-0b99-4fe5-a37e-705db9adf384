package cn.wingcloud.jfinal.action;

import java.sql.SQLException;

import org.nutz.log.Log;
import org.nutz.log.Logs;

import com.jfinal.core.Controller;

import cn.wingcloud.jfinal.pojo.Resource;
import cn.wingcloud.jfinal.service.IResourceService;
import cn.wingcloud.jfinal.service.impl.ResourceServiceImpl;
import cn.wingcloud.jfinal.util.ParamUtil;

public class ResourceController extends Controller {
	private static final Log log = Logs.get();
	private IResourceService service = new ResourceServiceImpl();
	
	public void get(){
    	String json = null;
		try {
			json = service.get(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询资源信息时出错", e);
		} 
		renderText(json);
    }
    
    public void add(){
		String json = null;
		try {
			Resource Resource = getModel(Resource.class, "" , true);
			json = service.save(Resource, new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("添加资源信息时出错", e);
		}
    	renderText(json);
    }
    
    public void edit(){
    	String json = null;
    	Resource Resource = getModel(Resource.class, "" , true);
		try {
			json = service.edit(Resource, new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("修改资源信息时出错", e);
		}
		renderText(json);
    }
    
    public void del(){
    	String json = null;
    	try {
			json = service.delete(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("删除资源信息时出错", e);
		} 
		renderText(json);
    }
    
    public void pagelst(){
    	String json = null;
		try {
			json = service.pagelst(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询资源信息时出错", e);
		} 
		renderText(json);
    }
    
    public void getList(){
    	String json = null;
		try {
			json = service.getList(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询资源时出错", e);
		} 
		renderText(json);
    }
    
    public void getMenuAndResourceTree(){
    	String json = null;
		try {
			json = service.getMenuAndResourceTree(new ParamUtil(getRequest()));
		} catch (SQLException e) {
			log.error("查询菜单和资源时出错", e);
		} 
		renderText(json);
    }
    
}
