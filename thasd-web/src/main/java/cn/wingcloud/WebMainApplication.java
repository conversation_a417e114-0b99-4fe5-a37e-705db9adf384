package cn.wingcloud;

import cn.wingcloud.jfinal.config.SysConfig;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.server.undertow.UndertowServer;
import org.nutz.log.Log;
import org.nutz.log.Logs;

public class WebMainApplication {

    private static final Log log = Logs.get();
    static Prop profile;
    static String activeProject = "undertow.txt";

    /**
     * 启动入口，运行此 main 方法可以启动项目
     */
    public static void main(String[] args) {

        if (profile == null) {
            profile = PropKit.useFirstFound( "env.properties");
            activeProject = profile.get("activeProject");
            log.infof("profile: @%s",activeProject);
        }

        String undertowConfig = "undertow-"+activeProject+".txt";

        UndertowServer.createByJFinalFilter("Thasd Web",SysConfig.class,undertowConfig).configWeb(webBuilder -> {
            webBuilder.add404ErrorPage("/error/404.html");
            webBuilder.addErrorPage(403,"/error/403.html");
            webBuilder.addErrorPage(500,"/error/500.html");
        }).start();

    }

}
