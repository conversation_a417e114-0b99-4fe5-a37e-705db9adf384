package cn.wingcloud.beetl.ext.filter;

import java.io.IOException;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import cn.wingcloud.beetl.ext.render.JFinalRender;
import cn.wingcloud.beetl.ext.render.JFinalRenderFactory;
import cn.wingcloud.beetl.ext.render.WebRender;

/**
 * 可以使.html文件直接访问
 */
public class JFinalWebFilter implements Filter{

    protected String _encoding = JFinalRender.getEncoding();
    protected String _contentType = "text/html; charset=" + _encoding;


    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse rsp,FilterChain chain) throws IOException,ServletException {
        HttpServletRequest request = (HttpServletRequest)req;
        HttpServletResponse response = (HttpServletResponse)rsp;
        request.setCharacterEncoding(_encoding);
        response.setCharacterEncoding(_encoding);
        response.setContentType(_contentType);
        String urlPath = request.getServletPath();
        if(urlPath.startsWith("/plugin/script/") || urlPath.startsWith("/plugin/theme/")){
            chain.doFilter(request, response);
        }else{
            String renderPath = getRenderPath(urlPath,request, response);
            WebRender render = new WebRender(JFinalRenderFactory.groupTemplate);
            render.render(renderPath, request,response);
        }
    }

    /** 返回渲染的模板，默认就是path。
     * @param path
     * @param request
     * @param response
     * @return
     */
    protected String getRenderPath(String path, HttpServletRequest request, HttpServletResponse response)
    {
        return path;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }
}
