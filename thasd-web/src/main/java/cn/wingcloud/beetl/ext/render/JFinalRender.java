package cn.wingcloud.beetl.ext.render;

import org.beetl.core.GroupTemplate;
import com.jfinal.render.Render;
import com.jfinal.render.RenderException;

/**
 * JFinalRender Controller.render(String view)
 * @Package cn.eninesoft.beetl.ext.render
 * @ClassName:JFinalRender
 * <AUTHOR>
 * @date 2015年5月13日 下午12:34:44 
 * @version V1.0
 */
public class J<PERSON>inalRender extends Render
{
	GroupTemplate groupTemplate;
	private transient static final String encoding = getEncoding();
	private transient static final String contentType = "text/html; charset=" + encoding;

	public JFinalRender(GroupTemplate gt, String view)
	{
		super();
		super.setView(view);
		this.groupTemplate = gt;
	}
	
	@Override
	public void render()
	{
		try {
			response.setContentType(contentType);
			WebRender webRender = new WebRender(groupTemplate);
			webRender.render(view, request, response);
		} catch (Exception e) {
			throw new RenderException(e);
		}
	}

}