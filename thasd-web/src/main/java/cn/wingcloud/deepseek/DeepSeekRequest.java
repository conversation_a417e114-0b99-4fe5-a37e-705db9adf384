package cn.wingcloud.deepseek;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import okhttp3.internal.sse.RealEventSource;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

import java.util.concurrent.TimeUnit;

public class DeepSeekRequest {
    public static String CONFIGNAME = "chat.json";

    public static void main(String[] args) {
        String path = FileUtil.getAbsolutePath(CONFIGNAME);
        String configJson = FileUtil.readUtf8String(path);
        JSONObject config = JSONObject.parseObject(configJson);
        String body = config.getString("playLoad");
        System.out.println(body);
        // 设置请求
        Request request = new Request.Builder()
                .url("http://112.245.49.135:11687/api/local_doc_qa/local_doc_chat") // 替换为实际的SSE端点URL
                .header("Accept", "text/event-stream")
                .post(RequestBody.create(body, MediaType.get("application/json")))
                .build();

        // 创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.DAYS)
                .readTimeout(1, TimeUnit.DAYS)
                .build();
        // 创建 EventSource 实例并启动连接
        EventSource.Factory factory = EventSources.createFactory(client);
        // 实例化EventSource并注册监听器
        EventSource eventSource = factory.newEventSource(request, new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, Response response) {
                System.out.println("SSE 连接已打开，响应码: " + response.code());
            }

            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                System.out.println("收到事件:");
                if (id != null) {
                    System.out.println("  ID: " + id);
                }
                if (type != null) {
                    System.out.println("  类型: " + type);
                }
                System.out.println("  数据: " + data);
            }

            @Override
            public void onClosed(EventSource eventSource) {
                System.out.println("SSE 连接已关闭");
            }

            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                try {
                    if (response != null) {
                        System.err.println("SSE 连接失败: " + t.getMessage());
                        System.err.println("  响应码: " + response.code());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        ((RealEventSource)eventSource).connect(client);
    }
}
