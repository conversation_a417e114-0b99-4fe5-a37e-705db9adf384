package cn.wingcloud.deepseek;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import okio.BufferedSource;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class OllamaRequest {
    public static String CONFIGNAME = "chat.json";

    public static void sendRequest(String prompt) {
        String path = FileUtil.getAbsolutePath(CONFIGNAME);
        String configJson = FileUtil.readUtf8String(path);
        JSONObject config = JSONObject.parseObject(configJson);
        JSONObject body = config.getJSONObject("ollama");
        body.put("prompt", prompt);
        // 设置请求
        Request request = new Request.Builder()
                .url("http://112.245.49.135:11634/api/generate") // 替换为实际的SSE端点URL
                .post(RequestBody.create(body.toJSONString(), MediaType.parse("application/json")))
                .build();

        // 创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.DAYS)
                .readTimeout(1, TimeUnit.DAYS)
                .build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                e.printStackTrace();
            }
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    System.out.println("请求失败: " + response.code());
                    return;
                }
                try (BufferedSource source = response.body().source()) {
                    while (!source.exhausted()) {
                        String line = source.readUtf8Line();
                        if (line != null) {
                            System.out.println("收到数据: " + line);
                        }
                    }
                }
            }
        });
    }
    public static void main(String[] args) {
    }
}
