package cn.wingcloud.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 三高患者
 *
 * <AUTHOR>
 * @Date 2022-03-24
 */
@Data
public class Patients  implements Serializable {

	/**
	 * 主键
	 */
	private String id;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 性别
	 */
	private String gender;
	/**
	 * 年龄
	 */
	private Long age;
	/**
	 * 身份证号
	 */
	private String idcard;
	/**
	 * 出生日期
	 */
	private String birthday;
	/**
	 * 民族
	 */
	private String minzu;

	/**
	 * 家庭住址
	 */
	
	private String jtzz;

	/**
	 * 联系电话
	 */
	
	private String lxdh;

	/**
	 * 行政区划ID
	 */
	
	private String areaid;

	/**
	 * 行政区划名称
	 */
	
	private String areaname;

	/**
	 * 组织（单位）ID
	 */
	
	private String orgid;

	/**
	 * 组织（单位）编码
	 */
	
	private String orgcode;

	/**
	 * 组织（单位）名称
	 */
	private String orgname;

	/**
	 * 已管理
	 */
	private String guanli;
	/**
	 * 家庭医生ID
	 */
	private String jyuserid;
	/**
	 * 家庭医生姓名
	 */
	private String jyusername;
	/**
	 * 管理家庭医生ID
	 */
	private String gljyuserid;

	/**
	 * 管理家庭医生姓名
	 */
	private String gljyusername;

	/**
	 * 管理家庭医生姓名
	 */
	private String gljyuser;
	/**
	 * 家庭医生ID
	 */
	private String jyuser;

	/**
	 * 是否签约 0 是 1否
	 */
	private String isqy;

	/**
	 * 家庭医生电话
	 */
	private String jyPhone;

	/**
	 * 管理状态
	 */
	private String pmtype;

	/**
	 * 管理状态编码
	 */
	
	private String pmtypecode;
	/**
	 * 既往史疾病
	 */
	
	private String jwsjb;

	/**
	 * 既往史疾病其他
	 */
	
	private String jwsjbqt;

	/**
	 * 药物过敏史
	 */
	
	private String ywgms;

	/**
	 * 药物过敏史其他
	 */
	
	private String ywgmsqt;

	/**
	 * 残疾情况
	 */
	
	private String cjqk;

	/**
	 * 残疾情况其他
	 */
	private String cjqkqt;

	/**
	 * 既往史恶性肿瘤
	 */
	private String jwsjbexzl;

	/**
	 * 既往史职业病
	 */
	private String jwsjbzyb;

	/**
	 * 创建时间
	 */
	private String createtime;

	/**
	 * 创建人ID
	 */
	private String createid;

	/**
	 * 创建人姓名
	 */
	private String createname;

	/**
	 * 修改时间
	 */
	private String updatetime;

	/**
	 * 修改人ID
	 */
	private String updateid;

	/**
	 * 修改人姓名
	 */
	private String updatename;

	/**
	 * 删除标记
	 */
	private Long isdel;

	/**
	 * 家族史父亲
	 */
	
	private String jzsfq;

	/**
	 * 家族史母亲
	 */
	
	private String jzsmq;

	/**
	 * 家族史兄弟姐妹
	 */
	
	private String jzsxdjm;

	/**
	 * 家族史子女
	 */
	
	private String jzszn;

	/**
	 * 三高级别1,2,3
	 */
	
	private Long sglevel;

	/**
	 * 是否高血压0否，1是
	 */
	
	private Long isgxy = new Long(0);

	/**
	 * 是否糖尿病0否，1是
	 */
	
	private Long istnb = new Long(0);

	/**
	 * 是否高血脂0否，1是
	 */
	
	private Long isgxz = new Long(0);
	/**
	 * 公卫创建时间
	 */
	private String syntime;

	/**
	 * 失访备注
	 */
	private String sfremark;


	/**
	 * 区级单位id
	 */
	
	private String orgidl1;

	/**
	 * 区级单位名称
	 */
	
	private String orgnamel1;

	/**
	 * 乡镇、街道级别单位ID
	 */
	
	private String orgidl2;

	/**
	 * 乡镇、街道级别单位名称
	 */
	
	private String orgnamel2;

	/**
	 * 管理-区级单位id
	 */
	
	private String glorgidl1;

	/**
	 * 管理-区级单位名称
	 */
	
	private String glorgnamel1;

	/**
	 * 管理-乡镇、街道级别单位ID
	 */
	
	private String glorgidl2;

	/**
	 * 管理-乡镇、街道级别单位名称
	 */
	
	private String glorgnamel2;

	/**
	 * 管理-组织（单位）ID
	 */
	
	private String glorgid;

	/**
	 * 管理-组织（单位）名称
	 */
	
	private String glorgname;

	/**
	 * 附加查询列
	 */
	private String bpgradecode;
	private String evalid;
	private String dbgradecode;
	private String lpgradecode;
	private String adgradecode;
	private String bfzname;
	private String prepgtime;
	private String planid;
	private String year;
	private String idx;
	private String areafname;
	private String glgrade;
	private String grade;
}
