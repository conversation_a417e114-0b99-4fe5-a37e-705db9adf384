package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

public class BscasAction extends Controller {

    private static final Log log = Logs.get();

    public void index(){
        render("/bscas/index.html");
    }

    public void countMainPatients(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/bscas/countMainPatients", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void dfzHzList(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/bscas/dfzHzList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void fwjlList(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/bscas/fwjlList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }
}

