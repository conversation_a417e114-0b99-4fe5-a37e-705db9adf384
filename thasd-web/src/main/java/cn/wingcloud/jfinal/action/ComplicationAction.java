package cn.wingcloud.jfinal.action;

import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;

import java.util.HashMap;
import java.util.Map;

public class ComplicationAction extends Controller {

    public void index(){
        Map<String, Object> queryParas = new HashMap<>();
        queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
        queryParas.put("isall","1");
        String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
        setAttr("org",JSON.parse(orgResult));
        render("/comp/list.html");
    }

}
