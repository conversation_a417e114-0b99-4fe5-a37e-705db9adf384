package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.MethodMenu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.util.dto.DetevalDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.wingcloud.jfinal.action.DetevalAction.DETEVAL_URL;
import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;

/**
 * 检测评估
 */
@Menu(menu = MenuExEnum.JCPG_INDEX,url = DETEVAL_URL,parent = MenuEnum.JCPG)
public class DetevalAction extends Controller{

	public static final String DETEVAL_URL = "/v/deteval";
    private static final Log log = Logs.get();

	public void index(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));
		render("/deteval/index.html");
	}


	public void addIndex(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/deteval/add.html");
	}


	@MethodMenu(menu = MenuExEnum.JCPG_PGBG,url = "/v/deteval/pgbg",parent = MenuEnum.JCPG)
	public void pgbg(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/deteval/list.html");
	}

	@Resource(value = ResourceEnum.DEL,parentMenu = MenuExEnum.JCPG_PGBG)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/del",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void viewIndex(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));
		setAttr("id", getPara("id",""));
		render("/deteval/pgbg.html");
	}

	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);

		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

	}
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void export(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();

			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			String result = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/eval/list", queryParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			JSONObject object = JSON.parseObject(result);
			Map<String, Object> rootObjectMap = new HashMap<>();
			List<DetevalDto> list = JSON.parseArray(object.getString("data"), DetevalDto.class);
			rootObjectMap.put("list",list);
			InputStream is = new FileInputStream(TEMPLET_PATH + "/pgTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("评估报告.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}
}
