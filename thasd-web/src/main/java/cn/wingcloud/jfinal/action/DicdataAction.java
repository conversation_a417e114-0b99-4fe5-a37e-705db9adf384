package cn.wingcloud.jfinal.action;

import java.util.HashMap;
import java.util.Map;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;

import com.jfinal.core.Controller;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;

@Menu(menu = MenuExEnum.SYS_DICDATA,url = "/v/diccategory/dicdata",subf = "/v/diccategory/",visible0 = 0,visible2 = 0)
public class DicdataAction extends Controller{
	
    private static final Log log = Logs.get();

	@Resource(value = ResourceEnum.EX_ADD_DIC_DATA,classes = DiccategoryAction.class)
	public void addIndex(){
		render("/dicdata/add.html");
	}

	@Resource(value = ResourceEnum.EX_EDIT_DIC_DATA,classes = DiccategoryAction.class)
	public void editIndex(){
		setAttr("id", getPara("id",""));
		render("/dicdata/add.html");
	}
	
	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/dicdata/pagelst", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/dicdata/get", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/dicdata/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
		
	}
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/dicdata/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	@Resource(value = ResourceEnum.EX_DEL_DIC_DATA,classes = DiccategoryAction.class)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/dicdata/del", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
}
