package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

@Menu(menu = MenuExEnum.SYS_DRUGSDIC,url = "/v/drugs",parent = MenuEnum.SYS,visible0 = 0,visible2 = 0)
public class DrugsDictionaryAction extends Controller {

    private static final Log log = Logs.get();

    public void index() {
        String alibabaKey = getAttr("authUserJson").toString();

        String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw,yylx";
        Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
        setAttr("dicData", JSON.parse(dicResult));

        render("/drugs/list.html");
    }

    @Resource(ResourceEnum.ADD)
    public void addIndex() {

        String alibabaKey = getAttr("authUserJson").toString();
        String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw,yylx";

        Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();

        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("dicData", JSON.parse(dicResult));

        render("/drugs/add.html");
    }

    @Resource(ResourceEnum.EDIT)
    public void editIndex() {

        String alibabaKey = getAttr("authUserJson").toString();
        String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw,yylx";

        Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("dicData", JSON.parse(dicResult));
        setAttr("id", getPara("id", ""));

        render("/drugs/add.html");
    }

    public void list() {
        try {
            String alibabaKey = getAttr("authUserJson").toString();
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", alibabaKey);
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/drugs/pagelst", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void get() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/drugs/get", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void save() {
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/drugs/add", getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }

    }

    public void edit() {
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/drugs/edit", getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    @Resource(ResourceEnum.DEL)
    public void del() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/drugs/del", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }
}
