package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.dto.Patients;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.dto.NoticeDto;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.jfinal.util.StringUtil;
import cn.wingcloud.upload.RandomFileNamePolicy;
import cn.wingcloud.util.ExcelRead;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import com.oreilly.servlet.MultipartRequest;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;
import static cn.wingcloud.jfinal.config.SysConfig.UPLOAD_PATH;

public class ExRulesIndexAction extends Controller{
	
    private static final Log log = Logs.get();

    /**
	 * 下载文件
	 */
	public void downloadBrowser(){
		String dir = "/snems.exe";
		String savePath =  UPLOAD_PATH  + dir;
	    try {
			String filename = "chrome浏览器";
			
	    	if(filename.lastIndexOf('.') != -1){
	    		filename = filename.substring(0,filename.lastIndexOf('.')) + dir.substring(dir.lastIndexOf('.'));
	    	}else{
	    		filename = filename + dir.substring(dir.lastIndexOf('.'));

	    	}

	    	File nfile = new File(savePath);
	        if (nfile.exists()) {
	        	renderFile(nfile, filename);
	        }else{
	        	renderFile(new File(UPLOAD_PATH+"/404.txt"), "文件找不到或已删除.txt");
	        }
	    } catch (Exception e) {
	    	log.error(e);
        	renderFile(new File(UPLOAD_PATH+"/404.txt"), "文件找不到或已删除.txt");
	    }
	}
    /**
	 * 下载文件
	 */
	public void download(){
		String dir = getPara("path");
		String savePath =  UPLOAD_PATH  + dir;
	    try {
			String filename = getPara("filename");
			
	    	if(filename.lastIndexOf('.') != -1){
	    		filename = filename.substring(0,filename.lastIndexOf('.')) + dir.substring(dir.lastIndexOf('.'));
	    	}else{
	    		filename = filename + dir.substring(dir.lastIndexOf('.'));

	    	}
	    	File nfile = new File(savePath);
	        if (nfile.exists()) {
	        	renderFile(nfile, filename);
	        }else{
				renderFile(new File(UPLOAD_PATH+"/404.txt"), "文件找不到或已删除.txt");
	        }
	    } catch (Exception e) {
	    	log.error(e);
			renderFile(new File(UPLOAD_PATH+"/404.txt"), "文件找不到或已删除.txt");
	    }
	}
	
	/**
	 * 修改密码页面-首页
	 */
	public void editPasswordIndex(){
		render("/user/editPass.html");
	}
	/**
	 * 修改密码-首页
	 */
	public void editPassword(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/user/editPassword", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 赋予角色列表-用户管理
	 */
	public void setRoleSelectIndex(){
		setAttr("id", getPara("id",""));
		render("/user/roleSelectList.html");
	}
	/**
	 * 删除用户已有角色数据-用户管理
	 */
	public void delUserRole(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
		    queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String roleResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/delUserRole",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(roleResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	/**
	 * 获取用户已有角色数据-用户管理
	 */
	public void getRoleExistingList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
		    queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String roleResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/getExistingRoles",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(roleResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 获取用户可分配角色数据-用户管理
	 */
	public void getRoleList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/getList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 保存分配角色数据-用户管理
	 */
	public void addRoles(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/user/addRoles", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void patientInfo(){
		Map<String, Object> queryParas = new HashMap<>();
		String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/getPatientId",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		JSONObject result = JSON.parseObject(jsonResult);
		JSONObject patient = result.getJSONObject("data");
		setAttr("patientid",patient.getOrDefault("patientid",""));
		setAttr("username",getPara("username",""));
		render("/index/loginForHisByXyy.html");
	}

	/**
	 * 模块级联tree
	 */
	public void menuTreeSingle(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/menuTreeSingle",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 模块级联tree
	 */
	public void jcjyTreeSingle(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/tree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 字典分类
	 */
	public void dicCategoryTree(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/bizbasic/diccategory/dicCategoryTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 获取患者
	 */
	public void getRadioPatients(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			queryParas.put("isall","1");
			String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("org", JSON.parse(orgResult));
			render("/patients/radioList.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getRadioDrugs(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();

			String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw,yylx";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));

			render("/drugs/radioList.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void ai(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			setAttr("itemcode",getPara("itemcode",""));
			render("/ai/ai.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getItemPlan(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			setAttr("itemcode",getPara("itemcode",""));
			render("/plan/radioZdList.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getYyPlan(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			setAttr("itemcode",getPara("itemcode",""));
			render("/plan/radioYyList.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	/**
	 * 上传健康体检患者名单
	 */
	public void uploadJktj(){
		StringBuilder message = new StringBuilder();
		String dir = "/jktj";
		String savePath = UPLOAD_PATH  + dir;

		try {
			System.out.println(savePath);
			File nfile = new File(savePath);
			if (!nfile.exists()) {
				nfile.mkdirs();
			}
			RandomFileNamePolicy rfnp = new RandomFileNamePolicy();
			int maxPostSize =  1000 * 1024 * 1024;
			//response的编码为"utf-8",同时采用缺省的文件名冲突解决策略,实现上传
			MultipartRequest multi = new MultipartRequest(getRequest(),savePath,maxPostSize,"utf-8",rfnp);
			//输出反馈信息
			Enumeration file = multi.getFileNames();
			while (file.hasMoreElements()) {
				String name = (String) file.nextElement();
				File f = multi.getFile(name);
				if (f != null) {
					String fileName = multi.getFilesystemName(name);
					message.append("{\"code\":200,\"url\":\""+dir+"/"+fileName+"\",\"doctype\":\""+fileName.substring(fileName.lastIndexOf("."))+"\",\"size\":\""+f.length()+"\"}");
				}
			}
		} catch (Exception e) {
			log.error(e);
			message.append("{\"code\":201,\"msg\":\""+e.getMessage()+"\"}");
		}
		renderText(message.toString(),ContentType.TEXT);
	}

	/**
	 * 解析健康体检患者名单
	 */
	public void readJktj(){
		String dir = "/jktj";
		String filePath = getPara("filepath");
		String fullPath = UPLOAD_PATH  + filePath;

		JSONObject result = new JSONObject();
		result.put("code",200);
		try {
			System.out.println(fullPath);
			File nfile = new File(fullPath);
			if (!nfile.exists()) {
				throw new RuntimeException("解析文件不存在！");
			}
			List<ArrayList<String>>  list =  ExcelRead.readExcel(fullPath);
			List<String> idcardList = list.stream().map(array ->{
				String idcard = array.get(1);
				return idcard;
			}).collect(Collectors.toList());
			System.out.println(idcardList.size() + " = " + JSON.toJSONString(idcardList));

			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryExitsPatientsYgl = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("opt","=").put("idcards", StringUtil.listToString(idcardList,",")).build();
			String exitsPatientsResultYgl = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/imp/getExitsPatients", queryExitsPatientsYgl, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			List<String> yglIdCards = JSON.parseArray(JSON.parseObject(exitsPatientsResultYgl).getString("rd"),String.class);
			System.out.println(exitsPatientsResultYgl);

			Map<String, Object> queryExitsPatientsWgl = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("opt","!=").put("idcards", StringUtil.listToString(idcardList,",")).build();
			String exitsPatientsResultWgl = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/imp/getExitsPatients", queryExitsPatientsWgl, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			List<String> wglIdCards = JSON.parseArray(JSON.parseObject(exitsPatientsResultWgl).getString("rd"),String.class);
			System.out.println(exitsPatientsResultWgl);

			List<String> exitsIdCards = new ArrayList<>();
			exitsIdCards.addAll(yglIdCards);
			exitsIdCards.addAll(wglIdCards);

			List<String> notList = idcardList.stream().filter( d1 -> exitsIdCards.stream().noneMatch( d2 -> Objects.equals(d1, d2) ) ).collect(Collectors.toList());
			System.out.println(notList.size() + " = " + JSON.toJSONString(notList));

			StringBuilder titleBuid = new StringBuilder();
			titleBuid.append("上传体检名单患者共：(").append(idcardList.size()).append(")人，");
			titleBuid.append("其中已管理患者：(").append(yglIdCards.size()).append(")人，");
			titleBuid.append("未管理患者：(").append(wglIdCards.size()).append(")人");

			if(notList.size() > 0){
				Map<String, Object> syncPatientsParam = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("idcards", StringUtil.listToString(notList,",")).build();
				String syncPatientsResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/imp/syncParents", syncPatientsParam, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
				List<Patients> syncPatients = JSON.parseArray(JSON.parseObject(syncPatientsResult).getString("rd"),Patients.class);
				System.out.println(syncPatientsResult);
				titleBuid.append("，从公卫系统同步患者资料并纳入管理：(").append(syncPatients.size()).append(")人");

				if(notList.size() > syncPatients.size()){
					titleBuid.append("，无患者档案：(").append(notList.size() - syncPatients.size()).append(")人");

					List<ArrayList<String>> notImpList = list.stream().filter( d1 -> exitsIdCards.stream().noneMatch( d2 -> Objects.equals(d1.get(1), d2) ) ).collect(Collectors.toList());
					notImpList = notImpList.stream().filter( d1 -> syncPatients.stream().noneMatch( d2 -> Objects.equals(d1.get(1), d2.getIdcard()) ) ).collect(Collectors.toList());

					result.put("noList",notImpList);
				}
				result.put("synList",syncPatients);

			}
			result.put("desc",titleBuid.toString());
			renderText(result.toJSONString(),ContentType.TEXT);

		} catch (Exception e) {
			log.error(e);
			StringBuilder message = new StringBuilder();
			message.append("{\"code\":500,\"msg\":\""+e.getMessage()+"\"}");
			renderText(message.toString(),ContentType.TEXT);
		}
	}

	public void exportNotices(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();


			JSONObject authUser = getAttr("authUser");

			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("orgid",authUser.getString("orgid")).put("ALIBABAKEY", alibabaKey).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/exportNotices", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			List<NoticeDto> list = JSON.parseArray(jsonResult, NoticeDto.class);
			System.out.println(jsonResult);
			Map<String, Object> rootObjectMap = new HashMap<>();
			rootObjectMap.put("list",list);
			InputStream is = new FileInputStream(TEMPLET_PATH + "/fwjhTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("服务计划.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}
}
