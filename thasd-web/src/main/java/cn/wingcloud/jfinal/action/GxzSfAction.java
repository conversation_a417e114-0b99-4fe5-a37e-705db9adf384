package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.util.HashMap;
import java.util.Map;

/**
 * 高血脂随访
 */
public class GxzSfAction extends Controller {

    private static final Log log = Logs.get();

    public void get(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void save(){
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }

    }

    public void edit(){
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void del(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/del",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void drugs(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/drugs",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void drugsSaveOrUpdate(){
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/drugsSaveOrUpdate", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }

    }

    public void delDrugs(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/delDrugs",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void addIndex(){
        String alibabaKey = getAttr("authUserJson").toString();

        Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
        String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

        String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
        Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("area", JSON.parse(areaResult));
        setAttr("dicData", JSON.parse(dicResult));

        setAttr("ryGuid", getPara("ryGuid",""));
        setAttr("name", getPara("name",""));
        setAttr("lik", getPara("lik",""));

        render("/patients/gxzsf/add.html");
    }

    public void editIndex(){
        String alibabaKey = getAttr("authUserJson").toString();

        Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
        String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

        String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
        Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("area",JSON.parse(areaResult));
        setAttr("dicData", JSON.parse(dicResult));
        setAttr("id", getPara("id",""));
        render("/patients/gxzsf/add.html");
    }
}
