package cn.wingcloud.jfinal.action;

import cn.hutool.core.lang.tree.Tree;
import cn.wingcloud.annotation.SingleMenu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.callback.TokenJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.CookieUtil;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.jwt.Token;
import cn.wingcloud.util.JwtUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import com.jfinal.render.ContentType;
import com.jfinal.render.ErrorJsonRender;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static cn.wingcloud.authority.AuthConst.*;

public class IndexAction extends Controller{
	
    private static final Log log = Logs.get();

	public void index(){
		render("/index/pear-index.html");
	}
	public void getRoleMenu(){
		Cache authuserCache = Redis.use(REDIS_AUTH_KEY);
		JSONObject authUser = getAttr("authUser");
		String userId = authUser.getString("id");
		List<Tree> roleMenuList = authuserCache.get(userId+"RoleMenu");
		renderJson(roleMenuList);
	}

	public void getNotice(){
		try {
			JSONObject authUser = getAttr("authUser");
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("orgid",authUser.getString("orgid")).put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/getNotice", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void index2bak(){
		try {
			Cache authuserCache = Redis.use(REDIS_AUTH_KEY);
			JSONObject authUser = getAttr("authUser");
			String userId = authUser.getString("id");
			List<Tree> roleMenuList = authuserCache.get(userId+"RoleMenu");
			setAttr("roleMenuList", roleMenuList);
			render("/index/index.html");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@SingleMenu(name = "首页",code = "0001",parent = MenuEnum.ROOT)
	public void indexmain(){
		render("/index/main.html");
	}

	public void jsz(){
		render("/jsz.html");
	}
	public void empty(){
		render("/empty.html");
	}

	public void downloadBrowser(){
		render("/download/400.html");
	}
	
	
	public void noAuth(){
		redirect("/error/403.html");
	}
	public void noAuthJson(){
		renderText(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面")),ContentType.TEXT);
	}
	
	public void login(){
		render("/index/login.html");
	}

	public void loginForHis(){
		setAttr("patientid",getPara("patientid",""));
		setAttr("username",getPara("username",""));
		render("/index/loginForHis.html");
	}

	public void loginForHisMain(){
		setAttr("username",getPara("username",""));
		render("/index/loginForHisMain.html");
	}

	public void loginForHisByXyy(){
		setAttr("patientid",getPara("patientid",""));
		setAttr("username",getPara("username",""));
		render("/index/loginForHisByXyy.html");
	}

	public void fastAddPatient(){
		String name = getPara("name","");
		String idcard = getPara("idcard","");
		String username = getPara("username","");
		if(name.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","患者姓名为空")),ContentType.TEXT);
			return;
		}
		if(idcard.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","患者身份证为空")),ContentType.TEXT);
			return;
		}
		if(username.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","HIS登录用户名为空")),ContentType.TEXT);
			return;
		}

		setAttr("idcard",getPara("idcard",""));
		setAttr("name",getPara("name",""));
		setAttr("username",getPara("username",""));
		render("/index/fastAddPatient.html");
	}

	public void loginForHisByFastAdd(){
		String name = getPara("name","");
		String idcard = getPara("idcard","");
		String username = getPara("username","");
		if(name.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","患者姓名为空")),ContentType.TEXT);
			return;
		}
		if(idcard.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","患者身份证为空")),ContentType.TEXT);
			return;
		}
		if(username.equals("")){
			renderText(JsonKit.toJson(new StatusJson("500","HIS登录用户名为空")),ContentType.TEXT);
			return;
		}

		setAttr("idcard",getPara("idcard",""));
		setAttr("name",getPara("name",""));
		setAttr("username",getPara("username",""));
		render("/index/loginForHisByFastAdd.html");
	}


	public void fromxyy(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/login/fromxyy", getRequest().getQueryString());
			JSONObject userJson =  JSON.parseObject(jsonResult);
			log.debugf("userJson:%s",jsonResult);

			if(userJson.getString("code").equals("200")){

				Cache authuserCache = Redis.use(REDIS_AUTH_KEY);

				JSONObject authUser = userJson.getJSONObject("rd").getJSONObject("authUser");
				String userId = authUser.getString("id");//userId
				String clientId = getAttr(CLIENT_ID_FLAG,null);//ClinetId
				if(null == clientId){
					renderError(403,new ErrorJsonRender(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面"))));
					return;
				}
				authUser.put("jsessionId",clientId);//server端认证
				authuserCache.setex(AUTH_CLIENT_PREFIX + clientId,SESSION_TIMEOUT,authUser);
				List<Tree> roleMenuArray = JSON.parseArray(userJson.getJSONObject("rd").getString("roleMenuList"),Tree.class);
				List<String> urlMaping  = JSON.parseArray(userJson.getJSONObject("rd").getString("urlList"),String.class);
				authuserCache.setex(userId+"RoleMenu",SESSION_TIMEOUT_REDIS_URL,roleMenuArray);
				authuserCache.setex(userId+"Url",SESSION_TIMEOUT_REDIS_URL,urlMaping);

				String token = JwtUtils.encode(Token.builder().clientId(clientId).userId(userId).expDate(JwtUtils.getExpiredDate()).build());
				setCookie(CookieUtil.getJwtCookie(token));

				TokenJson tokenJson = TokenJson.builder().token(token).build();
				tokenJson.setCode("200");
				tokenJson.setMsg("sucess");

				renderText(JSON.toJSONString(tokenJson),ContentType.TEXT);
			}
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void fromhis(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/login/fromhis", getRequest().getQueryString());
			JSONObject userJson =  JSON.parseObject(jsonResult);
			log.debugf("userJson:%s",jsonResult);

			if(userJson.getString("code").equals("200")){

				Cache authuserCache = Redis.use(REDIS_AUTH_KEY);

				JSONObject authUser = userJson.getJSONObject("rd").getJSONObject("authUser");
				String userId = authUser.getString("id");//userId
				String clientId = getAttr(CLIENT_ID_FLAG,null);//ClinetId
				if(null == clientId){
					renderError(403,new ErrorJsonRender(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面"))));
					return;
				}
				authUser.put("jsessionId",clientId);//server端认证
				authuserCache.setex(AUTH_CLIENT_PREFIX + clientId,SESSION_TIMEOUT,authUser);
				List<Tree> roleMenuArray = JSON.parseArray(userJson.getJSONObject("rd").getString("roleMenuList"),Tree.class);
				List<String> urlMaping  = JSON.parseArray(userJson.getJSONObject("rd").getString("urlList"),String.class);
				authuserCache.setex(userId+"RoleMenu",SESSION_TIMEOUT_REDIS_URL,roleMenuArray);
				authuserCache.setex(userId+"Url",SESSION_TIMEOUT_REDIS_URL,urlMaping);

				String token = JwtUtils.encode(Token.builder().clientId(clientId).userId(userId).expDate(JwtUtils.getExpiredDate()).build());
				setCookie(CookieUtil.getJwtCookie(token));

				TokenJson tokenJson = TokenJson.builder().token(token).build();
				tokenJson.setCode("200");
				tokenJson.setMsg("sucess");

				renderText(JSON.toJSONString(tokenJson),ContentType.TEXT);
			}
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


	public void loginin(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/login/login", getRequest().getQueryString());
			JSONObject userJson =  JSON.parseObject(jsonResult);
			log.debugf("userJson:%s",jsonResult);

			if(userJson.getString("code").equals("200")){

				Cache authuserCache = Redis.use(REDIS_AUTH_KEY);

				JSONObject authUser = userJson.getJSONObject("rd").getJSONObject("authUser");
				String userId = authUser.getString("id");//userId
				String clientId = getAttr(CLIENT_ID_FLAG,null);//ClinetId
				if(null == clientId){
					renderError(403,new ErrorJsonRender(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面"))));
					return;
				}
				authUser.put("jsessionId",clientId);//server端认证
				authuserCache.setex(AUTH_CLIENT_PREFIX + clientId,SESSION_TIMEOUT,authUser);
				List<Tree> roleMenuArray = JSON.parseArray(userJson.getJSONObject("rd").getString("roleMenuList"),Tree.class);
				List<String> urlMaping  = JSON.parseArray(userJson.getJSONObject("rd").getString("urlList"),String.class);
			    authuserCache.setex(userId+"RoleMenu",SESSION_TIMEOUT_REDIS_URL,roleMenuArray);
			    authuserCache.setex(userId+"Url",SESSION_TIMEOUT_REDIS_URL,urlMaping);

				String token = JwtUtils.encode(Token.builder().clientId(clientId).userId(userId).expDate(JwtUtils.getExpiredDate()).build());
				setCookie(CookieUtil.getJwtCookie(token));

				TokenJson tokenJson = TokenJson.builder().token(token).build();
				tokenJson.setCode("200");
				tokenJson.setMsg("sucess");

				renderText(JSON.toJSONString(tokenJson),ContentType.TEXT);
			}
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void loginout(){
		String basePath = getRequest().getScheme() + "://" + getRequest().getServerName() + ":" + getRequest().getServerPort() + getRequest().getContextPath();
		try {
			String clientId = getAttr(CLIENT_ID_FLAG,null);//ClinetId
			if(null != clientId){
				getResponse().addCookie(CookieUtil.delClientCookie());
				getResponse().addCookie(CookieUtil.delJwtCookie());
				Cache authuserCache = Redis.use(REDIS_AUTH_KEY);
				if(authuserCache.exists(AUTH_CLIENT_PREFIX + clientId)){
					authuserCache.del(AUTH_CLIENT_PREFIX + clientId);
				}
			}
			redirect(basePath);
		} catch (Exception e) {
			log.error(e);
			redirect(basePath);
		}
	}

	public void testCors(){
		Map<String, Object> queryParas = new HashMap<>();
		String jsonResult = HttpKitUtils.post("http://localhost:9010/testCors",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		renderJson(jsonResult);
	}
}
