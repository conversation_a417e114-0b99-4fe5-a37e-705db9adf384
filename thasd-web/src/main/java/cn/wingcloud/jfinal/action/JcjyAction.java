package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.dto.HisDto;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.Filter;
import com.jayway.jsonpath.JsonPath;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.jayway.jsonpath.Criteria.where;
import static com.jayway.jsonpath.Filter.filter;

public class JcjyAction extends Controller{
	
    private static final Log log = Logs.get();

	public void index(){
		String alibabaKey = getAttr("authUserJson").toString();
		JSONObject authUser = getAttr("authUser");

		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("orgid",authUser.getString("orgid"));
		queryParas.put("orgname",authUser.getString("orgname"));
		String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/tree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("menu",JSON.parse(jsonResult));

		String multipCode = "plan_type,plan_item,plan_offer,plan_rate,plan_serve";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
		setAttr("dicData", JSON.parse(dicResult));
		render("/jcjy/list.html");
	}
	
	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/list",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/saveOrUpdate", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
		
	}
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/saveOrUpdate", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void sync(){
		try {
			String orgid = getPara("orgid",null);
			String hisJson = HttpKitUtils.get("http://localhost:9000/configjson/his.json");
			DocumentContext doct = JsonPath.parse(hisJson);
			Filter filter = filter(where("id").eq(orgid));
			List<LinkedHashMap<String,Object>>  list =  doct.read("$[?]",filter);
			List<HisDto> hisDtoList = JSON.parseArray(JSON.toJSONString(list),HisDto.class);
			HisDto hisDto = hisDtoList.get(0);
			String jdbcUrl = "";
			String user = "";
			String password = "";

			if(null != hisDto){
				jdbcUrl = hisDto.getJdbcUrl();
				user = hisDto.getUser();
				password = hisDto.getPassword();
			}

			System.out.println(hisDto);
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("jdbcUrl",jdbcUrl);
			queryParas.put("user",user);
			queryParas.put("password",password);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/jcjy/sync",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
}
