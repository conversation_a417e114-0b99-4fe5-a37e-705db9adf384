package cn.wingcloud.jfinal.action;

import java.util.HashMap;
import java.util.Map;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;

import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;

@Menu(menu = MenuExEnum.SYS_MENU,url = "/v/menu",parent = MenuEnum.SYS,visible0 = 0,visible2 = 0)
public class MenuAction extends Controller{
    private static final Log log = Logs.get();

	public void index(){
		render("/menu/list.html");
	}

	@Resource(ResourceEnum.ADD)
	public void addIndex(){
		Map<String, Object> queryParas = new HashMap<>();

		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/rootChildrenMenuTree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("menu",JSON.parse(jsonResult));
		render("/menu/add.html");
	}
	@Resource(ResourceEnum.EDIT)
	public void editIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/rootChildrenMenuTree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("menu",JSON.parse(jsonResult));
		setAttr("id", getPara("id",""));
		render("/menu/add.html");
	}
	
	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/list",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
		
	}
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@Resource(ResourceEnum.DEL)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/menu/del",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
}
