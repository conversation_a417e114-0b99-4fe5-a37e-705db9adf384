package cn.wingcloud.jfinal.action;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.MethodMenu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.dto.Patients;
import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.jfinal.util.StringUtil;
import cn.wingcloud.util.dto.PatientsDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import com.kvn.poi.exp.function.FunctionRegister;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.wingcloud.jfinal.action.DetevalAction.DETEVAL_URL;
import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;

/**
 * 以管理患者可以查看检测评估、及个人治疗方案、复诊记录
 * 待管理可以增删改，检测评估，制定个人治疗方案
 */
@Menu(menu = MenuExEnum.HZGL_GL,url = "/v/patients",parent = MenuEnum.HZGL)
public class PatientsAction extends Controller{
	/**
	 * 向StandardEvaluationContext中注册内部函数
	 */
	static {
		FunctionRegister.registerInternalFunction();
	}
    private static final Log log = Logs.get();

	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/patients/list.html");
	}

	@MethodMenu(menu = MenuExEnum.HZGL_GL0,url = "/v/patients/zero",parent = MenuEnum.HZGL)
	public void zero(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/patients/yglList.html");
	}

	@MethodMenu(menu = MenuExEnum.HZGL_QRSQ,url = "/v/patients/qrsq",parent = MenuEnum.HZGL)
	public void qrsq(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/patients/qrsq.html");
	}

	@MethodMenu(menu = MenuExEnum.HZGL_GW,url = "/v/patients/gw",parent = MenuEnum.HZGL)
	public void gw(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/patients/gwList.html");
	}

	@Resource(ResourceEnum.EX_PATIENTS_DETEVAL)
	public void deteval(){
		StrBuilder urlBuilder = new StrBuilder(DETEVAL_URL);
		String patientId = getPara("patientId","");
		redirect(urlBuilder.append("/addIndex?patientId=").append(patientId).toString());
	}

	@Resource(ResourceEnum.ADD)
	public void addIndex(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/patients/add.html");
	}
	@Resource(ResourceEnum.EDIT)
	public void editIndex(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));
		setAttr("id", getPara("id",""));
		render("/patients/add.html");
	}

	public void imp(){
		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/patients/imp/index.html");
	}
	
	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void pageLstByNot(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/pageLstByNot",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void checkPatient(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/checkPatient",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getJktjb(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
//			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getJktjb",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			String labResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getLabResult",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			String bpResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getBpResult",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			JSONObject result = new JSONObject();
			result.put("code","200");
			result.put("msg","");
//			result.put("jktj",jsonResult);
			result.put("lab",labResult);
			result.put("bp",bpResult);

			renderText(result.toJSONString(),ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


	public void removePmtype(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/removePmtype",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void changeBbfzInfo(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/changeBbfzInfo",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	public void changeDocter(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/changeDocter",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getNotice(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getNotice",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getEhrPatient(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getEhrPatient",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getEhrPatient2(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getEhrPatient2",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
		
	}

	public void addXyy(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/addXyy", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void addManage(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/addManage", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

	}

	@Resource(ResourceEnum.DEL)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/del",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void exportYgl(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();

			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			String roleOrgIds = StringUtil.valFromlistToStringHave(authUser.getString("roleorgids"),",");

			Map<String, Object> queryGxyParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryGxyParas.put("pmtypecode","0");
			queryGxyParas.put("orgids",roleOrgIds);
			String result = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryGxyParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			JSONObject object = JSON.parseObject(result);
			Map<String, Object> rootObjectMap = new HashMap<>();
			rootObjectMap.put("list",JSON.parseArray(object.getString("data"), PatientsDto.class));

			InputStream is = new FileInputStream(TEMPLET_PATH + "/yglTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("已管理名单.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}

	public void export(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();

			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			String roleOrgIds = StringUtil.valFromlistToStringHave(authUser.getString("roleorgids"),",");

			Map<String, Object> queryGxyParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryGxyParas.put("pmtypecode","0");
			queryGxyParas.put("type","gxy");
			queryGxyParas.put("orgids",roleOrgIds);
			String gxrResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryGxyParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			Map<String, Object> queryTnbParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryTnbParas.put("pmtypecode","0");
			queryTnbParas.put("type","tnb");
			queryTnbParas.put("orgids",roleOrgIds);
			String tnbResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryTnbParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			Map<String, Object> queryGxzParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryGxzParas.put("pmtypecode","0");
			queryGxzParas.put("type","gxz");
			queryGxzParas.put("orgids",roleOrgIds);
			String gxzResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryGxzParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			Map<String, Object> queryBfzParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryBfzParas.put("pmtypecode","0");
			queryBfzParas.put("orgids",roleOrgIds);
			String bfzResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryBfzParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			JSONObject gxy = JSON.parseObject(gxrResult);
			JSONObject tnb = JSON.parseObject(tnbResult);
			JSONObject gxz = JSON.parseObject(gxzResult);
			JSONObject bfz = JSON.parseObject(bfzResult);

			Map<String, Object> rootObjectMap = new HashMap<>();
			rootObjectMap.put("list",JSON.parseArray(gxy.getJSONArray("data").toJSONString(), PatientsDto.class));
			rootObjectMap.put("list2",JSON.parseArray(tnb.getJSONArray("data").toJSONString(), PatientsDto.class));
			rootObjectMap.put("list3",JSON.parseArray(gxz.getJSONArray("data").toJSONString(), PatientsDto.class));
			rootObjectMap.put("list4",JSON.parseArray(bfz.getJSONArray("data").toJSONString(), PatientsDto.class));

			InputStream is = new FileInputStream(TEMPLET_PATH + "/hmcTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("三高共管已纳入管理人员服务花名册.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}

	public void export2(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();

			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			String roleOrgIds = StringUtil.valFromlistToStringHave(authUser.getString("roleorgids"),",");

			Map<String, Object> queryGxyParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			queryGxyParas.put("pmtypecode","0");
			queryGxyParas.put("orgids",roleOrgIds);
			String gxrResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/list", queryGxyParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			JSONObject gxy = JSON.parseObject(gxrResult);

			Map<String, Object> rootObjectMap = new HashMap<>();
			rootObjectMap.put("list",JSON.parseArray(gxy.getJSONArray("data").toJSONString(), PatientsDto.class));

			InputStream is = new FileInputStream(TEMPLET_PATH + "/hmcTemplet2.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("三高共管已纳入管理人员服务花名册.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}

	public void deviceList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/deviceList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void bindDevice(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/bindDevice",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void unBindDevice(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/unBindDevice",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


}
