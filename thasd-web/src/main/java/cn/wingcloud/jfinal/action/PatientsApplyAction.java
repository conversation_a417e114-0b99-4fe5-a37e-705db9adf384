package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.util.dto.PatientsApplyDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;

public class PatientsApplyAction extends Controller{

    private static final Log log = Logs.get();

	public void pageLst(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/apply/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void apply(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/apply/apply",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void accept(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/apply/accept",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void revoke(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/apply/revoke",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void exportMe(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/apply/exportMe",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			JSONObject object = JSON.parseObject(jsonResult);
			Map<String, Object> rootObjectMap = new HashMap<>();
			rootObjectMap.put("list",JSON.parseArray(object.getString("data"), PatientsApplyDto.class));

			InputStream is = new FileInputStream(TEMPLET_PATH + "/sqqrTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("迁入申请-我的申请.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}
}
