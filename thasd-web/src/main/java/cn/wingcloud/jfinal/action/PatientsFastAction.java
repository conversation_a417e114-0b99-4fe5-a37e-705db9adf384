package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

@Menu(menu = MenuExEnum.HZGL_DJ,url = "/v/patients/fast",parent = MenuEnum.HZGL)
public class PatientsFastAction extends Controller{

    private static final Log log = Logs.get();

	public void index(){

		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/patients/fast/add.html");
	}

	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void xyyAdd(){

		String alibabaKey = getAttr("authUserJson").toString();

		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isxxy","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/patients/xyy/add.html");
	}


	public void xyySave(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


}
