package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

@Menu(menu = MenuExEnum.HZGL_SFDJ,url = "/v/patients/lost",parent = MenuEnum.HZGL)
public class PatientsLostAction extends Controller{

    private static final Log log = Logs.get();

	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/patients/lostList.html");
	}

	@Resource(ResourceEnum.EX_PATIENTS_LOST)
	public void sfdj(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/lost", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@Resource(ResourceEnum.EX_PATIENTS_LOST_CX)
	public void sfcx(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/lost", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

}
