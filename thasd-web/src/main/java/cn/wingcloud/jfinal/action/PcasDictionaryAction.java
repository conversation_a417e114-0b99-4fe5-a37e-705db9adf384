package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

@Menu(menu = MenuExEnum.SYS_PCAS,url = "/v/pcas",parent = MenuEnum.SYS,visible0 = 0,visible2 = 0)
public class PcasDictionaryAction extends Controller {

    private static final Log log = Logs.get();

    public void index() {
        render("/pcas/list.html");
    }

    public void addIndex() {

        String alibabaKey = getAttr("authUserJson").toString();
        String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw";

        Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();

        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/treeList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("dicData", JSON.parse(dicResult));

        render("/pcas/add.html");
    }

    public void editIndex() {

        String alibabaKey = getAttr("authUserJson").toString();
        String multipCode = "drugs_attrs,drugs_type,drugs_effect,drugs_jx,drugs_jldw";

        Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/treeList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        setAttr("dicData", JSON.parse(dicResult));
        setAttr("id", getPara("id", ""));

        render("/pcas/add.html");
    }

    public void list() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/list", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void treeList() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/treeList", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void get() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/get", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void save() {
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/add", getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }

    }

    public void edit() {
        try {
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/edit", getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }

    public void del() {
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/pcas/del", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult, ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500", "code:500,服务器内部错误")), ContentType.TEXT);
        }
    }
}
