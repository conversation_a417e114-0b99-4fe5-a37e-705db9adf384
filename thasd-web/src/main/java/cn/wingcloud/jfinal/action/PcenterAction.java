package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.util.HashMap;
import java.util.Map;

public class PcenterAction extends Controller{
	
    private static final Log log = Logs.get();

	public void index(){

		String alibabaKey = getAttr("authUserJson").toString();
		String patientid = getPara("patientid","null");

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
		setAttr("dicData", JSON.parse(dicResult));

		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("id",patientid);
		String patientsResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/getAll",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("patients", JSON.parse(patientsResult));

		render("/pcenter/index.html");
	}

	public void home(){
		render("/pcenter/home.html");
	}

	public void getPlan(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			queryParas.put("id",getPara("planid"));
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/viewPlan",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void getChart(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			queryParas.put("id",getPara("planid"));
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/viewPlan",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void me(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryAreaParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
		String areaResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/area/rootChildrenAreaTree",queryAreaParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("area",JSON.parse(areaResult));
		setAttr("dicData", JSON.parse(dicResult));

		render("/pcenter/me.html");
	}

	public void zwjcIndex(){
		render("/pcenter/lot.html");
	}

	public void fa(){
		render("/pcenter/falist.html");
	}

	public void pgbg(){
		render("/pcenter/pglist.html");
	}

	public void referral(){
		render("/pcenter/referrallist.html");
	}

	public void sf(){
		render("/pcenter/sflist.html");
	}
	public void fz(){
		render("/pcenter/fzlist.html");
	}

	public void sfzhxxb(){
		render("/pcenter/sfzhxxb.html");
	}


	public void jzjlData(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			queryParas.put("id",getPara("planid"));
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/jzjl/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void jzjlGetData(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			queryParas.put("id",getPara("planid"));
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/jzjl/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void zhxxbList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/gxzsf/zhxxbList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void jzjl(){
		render("/pcenter/jzjllist.html");
	}

	public void jzjlGet(){
		render("/pcenter/jzjl.html");
	}



	public void mjzblIndex(){
		render("/pcenter/mjzbl.html");
	}

	public void zyblIndex(){
		render("/pcenter/zybl.html");
	}
}
