package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.util.DateConvert;
import cn.wingcloud.util.dto.PatientsDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;

/**
 * 个性化管理方案
 */
@Menu(menu = MenuExEnum.GXHFA_INDEX,url = "/v/plan",parent = MenuEnum.GXHFA)
public class PlanAction extends Controller{

    private static final Log log = Logs.get();

	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org", JSON.parse(orgResult));
		List<Integer> years = DateConvert.getYears();
		setAttr("years", years);
		render("/plan/list.html");
	}

	public void list(){
		try {
			String model = getPara("model","");
			String method = "pagelst";
			if(model.equals("pcenter")){
				method = "pageLstSingle";
			}
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/"+method,queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void viewPlan(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/viewPlan",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("plan", JSON.parse(jsonResult));
			System.out.println(jsonResult);

			String alibabaKey = getAttr("authUserJson").toString();
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate,plan_serve";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			setAttr("planid",getPara("id",""));
			render("/plan/plan.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void pubViewPlan(){
		try {
			String alibabakey = "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";
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabakey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/viewPlan",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("plan", JSON.parse(jsonResult));
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate,plan_serve";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabakey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			setAttr("planid",getPara("id",""));
			render("/plan/planview.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void tzyy(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String alibabaKey = getAttr("authUserJson").toString();
			String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate,plan_serve";
			Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
			String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			setAttr("dicData", JSON.parse(dicResult));
			render("/plan/tzyy.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveTzyy(){
		try {
			String requestBody = getRawData();
			String jsonResult = HttpKitUtils.postBody(SysConfig.GLOAB_BUS_URL+"api/plan/savePlanTzyy", getRequest().getQueryString()==null ? "" : getRequest().getQueryString(),requestBody);
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/viewPlan",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void genPlan(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/save",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult, ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


	public void saveJkzd(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/savePlanJkzd", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void delJkzd(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/delPlanJkzd", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void jkzdList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/jkzdList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveYyzd(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/savePlanYyzd", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void delYyzd(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/delPlanYyzd", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void yyzdList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/yyzdList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveSfjh(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/savePlanSfjh", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void delSfjh(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/delPlanSfjh", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void fzjhList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/fzjhList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void fzjhAll(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/fzjhAll",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void fzjhDetailList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/fzjhDetailList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void fzjhHjList(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/fzjhHjList",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void export(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			String result = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/list", queryParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

			JSONObject object = JSON.parseObject(result);
			Map<String, Object> rootObjectMap = new HashMap<>();
			List<PatientsDto> list = JSON.parseArray(object.getString("data"), PatientsDto.class);
			rootObjectMap.put("list",list);

			InputStream is = new FileInputStream(TEMPLET_PATH + "/faTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("管理方案.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}

}
