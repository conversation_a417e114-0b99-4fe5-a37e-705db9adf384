package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

/**
 * 个性化管理方案模板
 */
@Menu(menu = MenuExEnum.GXHFA_TEMPLET,url = "/v/plan/tpl",parent = MenuEnum.GXHFA,visible0 = 0)
public class PlanTplAction extends Controller{

    private static final Log log = Logs.get();

	public void index(){
		String alibabaKey = getAttr("authUserJson").toString();

		String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate";
		Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
		String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

		setAttr("dicData", JSON.parse(dicResult));
		render("/plan/tpl/index.html");
	}

	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/tpl/selectByItem",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/tpl/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/tpl/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);

		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

	}
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/tpl/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/plan/tpl/del",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
}
