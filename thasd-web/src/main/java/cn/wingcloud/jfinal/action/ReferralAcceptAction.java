package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.callback.PageJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.jfinal.pojo.Organization;
import cn.wingcloud.test.ReferralDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Menu(menu = MenuExEnum.ZXZ_DJZ,url = "/v/referral/accept",parent = MenuEnum.ZXZ)
public class ReferralAcceptAction extends Controller {

    private static final Log log = Logs.get();

    public void index(){
        String alibabaKey = getAttr("authUserJson").toString();

        String multipCode = "sex,minzu,p_ywgms,p_jwsjb,p_jzs,p_cjqk,plan_type,plan_item,yyfs,yypl,plan_offer,plan_rate";
        Map<String, Object> queryDicParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("multipCode", multipCode).build();
        String dicResult = HttpKitUtils.post(SysConfig.GLOAB_URL + "api/bizbasic/dicdata/getMultipleList", queryDicParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());

        Map<String, Object> queryParas = new HashMap<>();
        queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
        queryParas.put("isall","1");
        String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
        setAttr("org",JSON.parse(orgResult));

        setAttr("dicData", JSON.parse(dicResult));
        render("/referral/djzList.html");
    }

    public void list(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/referral/acceptPageLst",queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void down(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/referral/down",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }

    public void accept(){
        try {
            Map<String, Object> queryParas = new HashMap<>();
            queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
            String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/referral/accept",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
            renderText(jsonResult,ContentType.TEXT);
        } catch (Exception e) {
            log.error(e);
            renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
        }
    }




}
