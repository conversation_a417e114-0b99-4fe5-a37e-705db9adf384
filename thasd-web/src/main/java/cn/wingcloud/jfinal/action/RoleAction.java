package cn.wingcloud.jfinal.action;

import java.util.HashMap;
import java.util.Map;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;

import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;

@Menu(menu = MenuExEnum.SYS_ROLE,url = "/v/role",parent = MenuEnum.SYS,visible0 = 0,visible2 = 1)
public class RoleAction extends Controller{

    private static final Log log = Logs.get();
	
	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/role/list.html");
	}

	@Resource(ResourceEnum.ADD)
	public void addIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/role/add.html");
	}

	@Resource(ResourceEnum.EDIT)
	public void editIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		setAttr("id", getPara("id",""));
		render("/role/add.html");
	}

	@Resource(ResourceEnum.EX_ROLE_DEPT_INDEX)
	public void roleDeptIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("roleid", getPara("id",""));
		String deptResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgAndDeptTree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("orgdept",JSON.parse(deptResult));
		setAttr("id",getPara("id",""));
		render("/role/setRoleDept.html");
	}

	@Resource(ResourceEnum.EX_ROLE_ORG_INDEX)
	public void roleOrgIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("roleid", getPara("id",""));
		String deptResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getOrgTree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("orgdept",JSON.parse(deptResult));
		setAttr("id",getPara("id",""));
		render("/role/setRoleOrg.html");
	}

	@Resource(ResourceEnum.EX_ROLE_MENURS_INDEX)
	public void roleMenuRsIndex(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		queryParas.put("roleid", getPara("id",""));
		String deptResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/resource/getMenuAndResourceTree",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("menurs",JSON.parse(deptResult));
		setAttr("id",getPara("id",""));
		render("/role/setRoleMenuRs.html");
	}
	
	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
		
	}
	
	public void saveRoleDept(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/saveRoleDept", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void saveRoleOrg(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/saveRoleOrg", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	
	
	public void saveRoleMenuAndResource(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/saveRoleMenuAndResource", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@Resource(ResourceEnum.DEL)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/role/del", queryParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
}
