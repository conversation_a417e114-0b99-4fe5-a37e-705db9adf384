package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.alibaba.fastjson.JSON;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

public class SynConfigAction extends Controller{
	
    private static final Log log = Logs.get();

	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/list",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("menu", JSON.parse(jsonResult));
		render("/synconfig/list.html");
	}

	public void addIndex(){
		render("/synconfig/add.html");
	}

	public void editIndex(){
		setAttr("id", getPara("id",""));
		render("/synconfig/add.html");
	}

	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/list",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void get() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/get",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void listSourceItem() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/listSourceItem",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void listFromItem() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/listFromItem",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void listCollate() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/listCollate",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveOrUpdate() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/saveOrUpdate",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveOrUpdateSource() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/saveOrUpdateSource",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveOrUpdateFrom() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/saveOrUpdateFrom",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void saveOrUpdateCollate() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/saveOrUpdateCollate",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void genTranSql() {
		try {
			Map<String, Object> queryParas = new HashMap<>();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/hisapi/synconfig/genTranSql",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


}
