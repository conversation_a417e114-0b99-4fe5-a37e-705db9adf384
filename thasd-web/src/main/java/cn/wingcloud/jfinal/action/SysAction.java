package cn.wingcloud.jfinal.action;

import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.Map;

public class SysAction extends Controller{

    private static final Log log = Logs.get();

	public void syncOrg(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/syncOrg", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void syncArea(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/syncArea", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void syncUser(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/syncUser", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void completData(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/completData", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void clearData(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/sys/clearData", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void syncPatients(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/syncPatients", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void reGenSfjhDetail(){
		try {
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", getAttr("authUserJson")).build();
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/reGenSfjhDetail", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

}
