package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.annotation.constant.ResourceEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;
import java.util.HashMap;
import java.util.Map;

@Menu(menu = MenuExEnum.GXHFA_TEAM,url = "/v/team",parent = MenuEnum.GXHFA,visible0 = 0,visible2 = 0)
public class TeamAction extends Controller{
	
    private static final Log log = Logs.get();

	public void index(){
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		render("/team/list.html");
	}

	@Resource(ResourceEnum.ADD)
	public void addIndex(){

		String alibabaKey = getAttr("authUserJson").toString();
		JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);

		Map<String, Object> queryOrgUsersParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("orgid",authUser.getString("orgid")).build();
		String orgUsersResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/getOrgUsers",queryOrgUsersParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());

		set("orgUsers", JSON.parse(orgUsersResult));

		render("/team/add.html");
	}

	@Resource(ResourceEnum.EDIT)
	public void editIndex(){

		String alibabaKey = getAttr("authUserJson").toString();
		JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
		Map<String, Object> queryMebsParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("id",getPara("id","")).put("qk","id").put("orgid",authUser.getString("orgid")).build();
		String mebsResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/getTeamMebs",queryMebsParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		set("mebers", JSON.parse(mebsResult));
		Map<String, Object> queryOrgUsersParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).put("id",getPara("id","")).put("orgid",authUser.getString("orgid")).build();
		String orgUsersResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/getOrgUsers",queryOrgUsersParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		set("orgUsers", JSON.parse(orgUsersResult));

		setAttr("id", getPara("id",""));
		render("/team/add.html");
	}
	
	public void get(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/get", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void list(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/pagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}
	
	public void save(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/add", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void edit(){
		try {
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/edit", getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@Resource(ResourceEnum.DEL)
	public void del(){
		try {
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/team/del", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

}
