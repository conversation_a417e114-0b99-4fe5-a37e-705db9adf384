package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.MethodMenu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.callback.ApiFunction;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import cn.wingcloud.jfinal.ext.http.RpcRequest;
import cn.wingcloud.jfinal.util.StringUtil;
import cn.wingcloud.util.dto.PatientsDto;
import cn.wingcloud.util.dto.PatientsDtoExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import com.kvn.poi.exception.PoiElErrorCode;
import com.kvn.poi.exp.PoiExporter;
import com.kvn.poi.exp.function.FunctionRegister;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.wingcloud.jfinal.config.SysConfig.TEMPLET_PATH;

public class TjfxAction extends Controller{

	/**
	 * 向StandardEvaluationContext中注册内部函数
	 */
	static {
		FunctionRegister.registerInternalFunction();
	}

    private static final Log log = Logs.get();

	public void index(){
		renderText(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面")),ContentType.TEXT);
	}

	public void countMainPatients(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/count/countMainPatients", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void xxgjbFc(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/count/xxgjbFc", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void bfzTjfx(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL + "api/patients/count/bfzTjfx", queryParas, getRequest().getQueryString() == null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@MethodMenu(menu = MenuExEnum.TJFX_ZHFX,url = "/v/tjfx/hzzhtj",parent = MenuEnum.TJFX)
	public void hzzhtj(){
		render("/tjfx/hzzhfx.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GXY,url = "/v/tjfx/gxyhztj",parent = MenuEnum.TJFX)
	public void gxyhztj(){
		render("/tjfx/gxytjfx.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_TNB,url = "/v/tjfx/tnbhztj",parent = MenuEnum.TJFX)
	public void tnbhztj(){
		render("/tjfx/tnbtjfx.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GXZ,url = "/v/tjfx/gxzhztj",parent = MenuEnum.TJFX)
	public void gxzhztj(){
		render("/tjfx/gxztjfx.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GZL_DJ,url = "/v/tjfx/gzltjByDj",parent = MenuEnum.TJFX)
	public void gzltjByDj(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			queryParas.put("isall","1");
			String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("org",JSON.parse(orgResult));
			render("/tjfx/gzltj-dj.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GZL_PG,url = "/v/tjfx/gzltjByPg",parent = MenuEnum.TJFX)
	public void gzltjByPg(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			queryParas.put("isall","1");
			String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("org",JSON.parse(orgResult));
			render("/tjfx/gzltj-pg.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void gzltjByPgBfz(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			queryParas.put("isall","1");
			String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			setAttr("org",JSON.parse(orgResult));
			render("/tjfx/gzltj-pgbfz.html");
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GZL_GLRS,url = "/v/tjfx/glrstj",parent = MenuEnum.TJFX)
	public void glrstj(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/tjfx/glrstj.html");
	}

	public void fwjlmx(){
		setAttr("countType",getPara("countType","0"));
		render("/tjfx/fwjlmx.html");
	}

	public void ypgry(){
		setAttr("countType","1");
		render("/tjfx/wpgry.html");
	}

	public void wpgry(){
		setAttr("countType","0");
		render("/tjfx/wpgry.html");
	}

	public void fabwzmc(){
		setAttr("fatype","0");
		render("/tjfx/fabwzmc.html");
	}
	public void fawzmc(){
		setAttr("fatype","1");
		render("/tjfx/fabwzmc.html");
	}

	public void monthpatient(){
		render("/tjfx/2monthpatient.html");
	}


	@MethodMenu(menu = MenuExEnum.TJFX_GZL_FA,url = "/v/tjfx/glfatj",parent = MenuEnum.TJFX)
	public void glfatj(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/tjfx/glfatj.html");
	}
	@MethodMenu(menu = MenuExEnum.TJFX_GZL_NR,url = "/v/tjfx/nrglhztj",parent = MenuEnum.TJFX)
	public void nrglhztj(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/tjfx/nrglhztj.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GZL_FWJL,url = "/v/tjfx/fwjltj",parent = MenuEnum.TJFX)
	public void fwjltj(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/tjfx/fwjltj.html");
	}

	@MethodMenu(menu = MenuExEnum.TJFX_GZL_FWJLWCL,url = "/v/tjfx/fwjlwcl",parent = MenuEnum.TJFX)
	public void fwjlwcl(){
		String alibabaKey = getAttr("authUserJson").toString();
		Map<String, Object> queryParas = new HashMap<>();
		queryParas.put("ALIBABAKEY", alibabaKey);
		queryParas.put("isall","1");
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_URL+"api/basic/dept/getAuthOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/tjfx/fwjlwcl.html");
	}

	public void countGlrs(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countGlrs", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countWpgry(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countWpgry", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countFwjlmx(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countFwjlmx", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countFabwzmc(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countFabwzmc", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countFa(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countFa", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countNrgl(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countNrgl", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countFwjl(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countFwjl", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void countFwjlWcl(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countFwjlWcl", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void gzltjByDjList(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countDj", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void gzltjByPgList(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countPg", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void gzltjByPgbfzList(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/countPgbfz", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void monthpatientList(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/monthpatientList", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}


	protected PatientsDtoExcel getEmptyObj(){
		PatientsDtoExcel patientsDto = new PatientsDtoExcel();
		patientsDto.setName("");
		patientsDto.setGender("");
		patientsDto.setAge("");
		patientsDto.setMinzu("");
		patientsDto.setIdcard("");
		patientsDto.setOrgname("");
		patientsDto.setJyusername("");
		patientsDto.setGlorgname("");
		patientsDto.setGljyusername("");
		return patientsDto;
	}

	public void downXxgJbfc(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			String tjfxType = getPara("tjfxType", null);
			String title = "";
			if(tjfxType.equals("bpgradecode")){
				title = "高血压";
			}
			if(tjfxType.equals("dbgradecode")){
				title = "糖尿病";
			}
			if(tjfxType.equals("lpgradecode")){
				title = "高血脂";
			}
			JSONObject authUser = ApiFunction.getAuthUser(alibabaKey);
			Map<String, Object> queryParas = RpcRequest.buildParamMapObj().put("ALIBABAKEY", alibabaKey).build();
			String result = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/patients/count/downXxgJbfc", queryParas,getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			JSONObject object = JSON.parseObject(result);
			Map<String, Object> rootObjectMap = new HashMap<>();
			List<PatientsDtoExcel> yhList = JSON.parseArray(object.getJSONObject("rd").getString("yhList"), PatientsDtoExcel.class);
			List<PatientsDtoExcel> dwList = JSON.parseArray(object.getJSONObject("rd").getString("dwList"), PatientsDtoExcel.class);
			List<PatientsDtoExcel> zwList = JSON.parseArray(object.getJSONObject("rd").getString("zwList"), PatientsDtoExcel.class);
			List<PatientsDtoExcel> gwList = JSON.parseArray(object.getJSONObject("rd").getString("gwList"), PatientsDtoExcel.class);

//			if(yhList.size() == 0 ){yhList.add(getEmptyObj());}
//			if(dwList.size() == 0 ){dwList.add(getEmptyObj());}
//			if(zwList.size() == 0 ){zwList.add(getEmptyObj());}
//			if(gwList.size() == 0 ){gwList.add(getEmptyObj());}
//
//			System.out.println(dwList.size());
//			System.out.println(JSON.toJSONString(dwList));

			rootObjectMap.put("yhList",yhList);
			rootObjectMap.put("dwList",dwList);
			rootObjectMap.put("zwList",zwList);
			rootObjectMap.put("gwList",gwList);


			InputStream is = new FileInputStream(TEMPLET_PATH + "/gradeTemplet.xlsx");
			getResponse().setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode("辖区内"+title+"患者心血管疾病分层统计.xlsx", "UTF-8"));
			getResponse().setHeader("Content-Type", " application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			getResponse().setCharacterEncoding("utf-8");
			OutputStream out = getResponse().getOutputStream();
			XSSFWorkbook wb = null;
			try {
				wb = new XSSFWorkbook(is);
			} catch (IOException e) {
				e.printStackTrace();
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}
			PoiExporter.export(wb, rootObjectMap);

			// 关闭资源
			try {
				wb.write(out);
				out.flush();
				out.close();
			} catch (IOException e) {
				throw PoiElErrorCode.SYSTEM_ERROR.exp(e);
			}

		} catch (Exception e) {
			e.printStackTrace();
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}

		renderNull();
	}

}
