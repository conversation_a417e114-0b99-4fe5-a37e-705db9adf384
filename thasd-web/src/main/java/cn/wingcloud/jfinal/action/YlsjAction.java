package cn.wingcloud.jfinal.action;

import cn.wingcloud.annotation.MethodMenu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.callback.StatusJson;
import cn.wingcloud.jfinal.config.SysConfig;
import cn.wingcloud.jfinal.ext.http.HttpKitUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.jfinal.kit.JsonKit;
import com.jfinal.render.ContentType;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

public class YlsjAction extends Controller{

    private static final Log log = Logs.get();

	public void index(){
		renderText(JsonKit.toJson(new StatusJson("403","抱歉，你无权访问该页面")),ContentType.TEXT);
	}


	@MethodMenu(menu = MenuExEnum.MZ_YLSJ,url = "/v/ylsj/mzylsj",parent = MenuEnum.YLSJSC)
	public void mzylsj(){
		JSONObject authUser = getAttr("authUser");
		Map<String, Object> queryParas = new HashMap<>();
		if(authUser.getOrDefault("grade","0").toString().equals("1")){
			queryParas.put("isall",1);
		}
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/basic/data/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org", JSON.parse(orgResult));
		render("/ylsj/mjzbl.html");
	}

	@MethodMenu(menu = MenuExEnum.ZY_YLSJ,url = "/v/ylsj/zyylsj",parent = MenuEnum.YLSJSC)
	public void zyylsj(){
		JSONObject authUser = getAttr("authUser");
		Map<String, Object> queryParas = new HashMap<>();
		if(authUser.getOrDefault("grade","0").toString().equals("1")){
			queryParas.put("isall",1);
		}
		queryParas.put("ALIBABAKEY", getAttr("authUserJson").toString());
		String orgResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/basic/data/getOrgTree", queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
		setAttr("org",JSON.parse(orgResult));
		render("/ylsj/zybl.html");
	}

	public void mzlist(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/mjzbl/mzPagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}

	public void zylist(){
		try {
			String alibabaKey = getAttr("authUserJson").toString();
			Map<String, Object> queryParas = new HashMap<>();
			queryParas.put("ALIBABAKEY", alibabaKey);
			String jsonResult = HttpKitUtils.post(SysConfig.GLOAB_BUS_URL+"api/rmisp/zybl/zyPagelst",queryParas, getRequest().getQueryString()==null ? "" : getRequest().getQueryString());
			renderText(jsonResult,ContentType.TEXT);
		} catch (Exception e) {
			log.error(e);
			renderText(JsonKit.toJson(new StatusJson("500","code:500,服务器内部错误")),ContentType.TEXT);
		}
	}



}
