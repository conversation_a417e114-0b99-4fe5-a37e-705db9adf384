package cn.wingcloud.jfinal.config;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;

import static cn.wingcloud.authority.AuthConst.CLIENT_ID_FLAG;

public class AuthInterceptor implements Interceptor {

    @Override
    public void intercept(Invocation invocation) {

        Controller controller = invocation.getController();
        String clinetId = controller.getAttr(CLIENT_ID_FLAG,null);
        if(null == clinetId){
            controller.render("/index/login.html");
        }else{
            invocation.invoke();
        }
    }
}
