package cn.wingcloud.jfinal.config;

import cn.wingcloud.jfinal.action.*;
import com.jfinal.config.Routes;

/**
 * JFINAL 路由配置
 */
public class ConfigRoutes extends Routes{

	@Override
	public void config() {

		addInterceptor(new AuthInterceptor());
		addInterceptor(new ExceptionInterceptor());

		add("/error", ErrorAction.class,"index");
		add("/", IndexAction.class,"/index");
		add("/exrule", ExRulesIndexAction.class);
		add("/sys", SysAction.class,"index");
		add("/v/organization", OrganizationAction.class,"index");
		add("/v/department", DepartmentAction.class,"index");
		add("/v/user", UserAction.class,"index");
		add("/v/menu", MenuAction.class,"index");
		add("/v/area", AreaAction.class,"index");
		add("/v/role", RoleAction.class,"index");
		add("/v/resource", ResourceAction.class,"index");
		add("/v/diccategory", DiccategoryAction.class,"index");
		add("/v/diccategory/dicdata", DicdataAction.class,"index");
		add("/v/drugs", DrugsDictionaryAction.class,"index");
		add("/v/pcas", PcasDictionaryAction.class,"index");
		add("/v/patients", PatientsAction.class,"index");
		add("/v/patients/fast", PatientsFastAction.class,"index");
		add("/v/patients/lost", PatientsLostAction.class,"index");
		add("/v/patients/gxzsf", GxzSfAction.class,"index");
		add("/v/tjfx", TjfxAction.class,"index");
		add("/v/deteval", DetevalAction.class,"index");
		add("/v/plan", PlanAction.class,"index");
		add("/v/plan/tpl", PlanTplAction.class,"index");
		add("/v/sf", SfAction.class,"index");
		add("/v/team", TeamAction.class,"index");
		add("/v/referral", ReferralAction.class,"index");
		add("/v/referral/accept", ReferralAcceptAction.class,"index");
		add("/v/pcenter", PcenterAction.class,"index");
		add("/v/pcenter/mjzbl", PcenterMjzblAction.class,"index");
		add("/v/pcenter/zybl", PcenterZyblAction.class,"index");
		add("/v/pubedu", PubEduAction.class,"index");
		add("/v/comp", ComplicationAction.class,"index");
		add("/v/patients/apply", PatientsApplyAction.class,"index");
		add("/v/ylsj", YlsjAction.class,"index");
		add("/v/plansfjl", PlanSfjlAction.class,"index");
		add("/v/jcjy", JcjyAction.class,"index");
		add("/synchis", SynConfigAction.class,"index");
		add("/bscas", BscasAction.class,"index");
	}
}

