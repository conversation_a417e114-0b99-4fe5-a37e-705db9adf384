package cn.wingcloud.jfinal.config;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.ActionException;
import com.jfinal.core.Controller;

public class ExceptionInterceptor implements Interceptor {

    @Override
    public void intercept(Invocation invocation) {

        Controller controller = invocation.getController();

        try {

            invocation.invoke();

        } catch (ActionException e) {
            e.printStackTrace();
            controller.renderError(e.getErrorCode());
        } catch (Exception e) {
            controller.renderError(500);
        } finally {

        }
    }

}
