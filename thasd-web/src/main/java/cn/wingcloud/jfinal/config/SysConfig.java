package cn.wingcloud.jfinal.config;

import cn.wingcloud.beetl.ext.handler.HtmlErrorHandler;
import cn.wingcloud.beetl.ext.render.JFinalRenderFactory;
import cn.wingcloud.jfinal.action.PatientsAction;
import cn.wingcloud.jfinal.ext.handler.AuthHandler;
import cn.wingcloud.util.CommonFunction;
import com.jfinal.config.*;
import com.jfinal.core.JFinal;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.redis.RedisPlugin;
import com.jfinal.template.Engine;
import org.beetl.core.GroupTemplate;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import java.util.HashMap;
import java.util.Map;

import static cn.wingcloud.authority.AuthConst.REDIS_AUTH_KEY;

/**
 * JFINAL API引导式配置
 */
public class SysConfig extends JFinalConfig {
	
	public static String GLOAB_URL = "http://localhost:9003/";
	public static String GLOAB_BUS_URL = "http://localhost:8000/";
	public static String TEMPLET_PATH = "";
	public static String UPLOAD_PATH = "";

	static Prop p;

	/**
	 * PropKit.useFirstFound(...) 使用参数中从左到右最先被找到的配置文件
	 * 从左到右依次去找配置，找到则立即加载并立即返回，后续配置将被忽略
	 */
	static void loadConfig() {
		if (p == null) {
			Prop profile = PropKit.useFirstFound( "env.properties");
			p = PropKit.use("config-"+profile.get("activeProject","prod")+".txt");
		}
	}

	/**
	 * 配置常量
	 */
	public void configConstant(Constants me) {
		loadConfig();
		GLOAB_URL = String.format("http://%s/",p.get("serverUrl"));
		GLOAB_BUS_URL = String.format("http://%s/",p.get("busServerUrl"));

		TEMPLET_PATH = p.get("templetPath");
		UPLOAD_PATH = p.get("uploadPath");

		me.setDevMode(p.getBoolean("devMode", false));
		//设置beetl模式
		me.setRenderFactory(new JFinalRenderFactory());
		GroupTemplate groupTemplate = JFinalRenderFactory.groupTemplate;
		Map<String, Object> sharedVars = new HashMap<>();
		sharedVars.put("breadcrumb",p.getBoolean("breadcrumb",false));
		sharedVars.put("gwServerUrl",p.get("gwServerUrl",""));
		sharedVars.put("jyServerUrl",p.get("jyServerUrl",""));
		groupTemplate.setSharedVars(sharedVars);
		groupTemplate.setErrorHandler(new HtmlErrorHandler());
		groupTemplate.registerFunctionPackage("ctl",CommonFunction.class);
	}
	
	/**
	 * 配置路由
	 */
	public void configRoute(Routes me) {
		me.add(new ConfigRoutes());
	}
	
	/**
	 * 配置插件
	 */
	public void configPlugin(Plugins me) {
		RedisPlugin authUser = new RedisPlugin(REDIS_AUTH_KEY, p.get("redishost"),p.getInt("redisport"),p.get("redispassword"));
		me.add(authUser);
	}
	
	/**
	 * 配置全局拦截器
	 */
	@Override
	public void configInterceptor(Interceptors me) {
	}
	/**
	 * 配置处理器
	 */
	@Override
	public void configHandler(Handlers me) {
		me.add(new AuthHandler());
	}

	@Override
	public void configEngine(Engine me) {
		
	}
}
