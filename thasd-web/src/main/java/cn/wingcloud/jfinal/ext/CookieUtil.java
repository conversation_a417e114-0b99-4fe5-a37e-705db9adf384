package cn.wingcloud.jfinal.ext;

import cn.wingcloud.authority.AuthConst;

import javax.servlet.http.Cookie;

import static cn.wingcloud.authority.AuthConst.CLIENT_ID_FLAG;
import static cn.wingcloud.authority.AuthConst.CLIENT_TOKEN_FLAG;

public class CookieUtil {

    public static Cookie getJwtCookie(String token){
        Cookie cookie = new Cookie(CLIENT_TOKEN_FLAG,token);

        cookie.setHttpOnly(true);
        cookie.setPath("/");
        cookie.setMaxAge(-1);

        return cookie;
    }

    public static Cookie delJwtCookie() {
        Cookie cookie = new Cookie(CLIENT_TOKEN_FLAG,null);
        cookie.setHttpOnly(true);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        return cookie;
    }

    public static Cookie delClientCookie() {
        Cookie cookie = new Cookie(CLIENT_ID_FLAG,null);
        cookie.setHttpOnly(true);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        return cookie;
    }
}
