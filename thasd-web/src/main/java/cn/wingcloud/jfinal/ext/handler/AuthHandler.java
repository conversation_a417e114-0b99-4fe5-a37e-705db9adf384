package cn.wingcloud.jfinal.ext.handler;

import cn.hutool.core.util.StrUtil;
import cn.wingcloud.authority.AuthUtil;
import cn.wingcloud.jfinal.ext.CookieUtil;
import cn.wingcloud.jwt.Token;
import cn.wingcloud.util.DigestConvert;
import cn.wingcloud.util.JwtUtils;
import cn.wingcloud.util.UserAgent;
import cn.wingcloud.util.UserAgentUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.handler.Handler;
import com.jfinal.kit.PropKit;
import com.jfinal.plugin.redis.Cache;
import com.jfinal.plugin.redis.Redis;
import org.nutz.log.Log;
import org.nutz.log.Logs;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static cn.wingcloud.authority.AuthConst.*;


/**
 * 自定义拦截
 * @Package cn.eninesoft.jfinal.ext.handler
 * @ClassName:CustomHandler
 * <AUTHOR>
 * @date 2015年5月13日 下午12:36:02 
 * @version V1.0
 */
public class AuthHandler extends Handler {
	
    private static final Log log = Logs.get();

	private final static String[] noAuthUrl = PropKit.use("auth.config.txt").get("noauthurl").split(";");
	private final static Set<String> NO_AUTH_ACTION= new HashSet<String>(Arrays.asList(PropKit.use("auth.config.txt").get("noauthaction").split(";")));

	protected Cookie genGetClinetCookie(HttpServletResponse response){
		String clientId = AuthUtil.getUniqueId();
		Cookie cookie = new Cookie(CLIENT_ID_FLAG,clientId);
		cookie.setHttpOnly(true);
		cookie.setPath("/");
		cookie.setMaxAge(-1);
		response.addCookie(cookie);
		return cookie;
	}
	@Override
	public void handle(String target, HttpServletRequest request,HttpServletResponse response, boolean[] isHandled) {
		AtomicReference<String> _clinet_id = new AtomicReference<>();
		Cookie[] cookies = request.getCookies();
		Cookie clinetCookie  = Arrays.stream(Optional.ofNullable(cookies).orElseGet(() -> {
			Cookie[] newCookies = new Cookie[1];
			newCookies[0] = genGetClinetCookie(response);
			return  newCookies;
		})).filter(cookie -> cookie.getName().equals(CLIENT_ID_FLAG)).findFirst().orElseGet(() -> genGetClinetCookie(response));

		_clinet_id.set(clinetCookie.getValue());
		request.setAttribute(CLIENT_ID_FLAG,clinetCookie.getValue());

		log.info("AccessURL:"+target);
        String agent= request.getHeader("User-Agent");
//		log.info("userAgent:"+agent);
		UserAgent userAgent = UserAgentUtil.getUserAgent(agent);
//		log.info(JSON.toJSONString(userAgent));
		try {

			if(null == userAgent || ( userAgent.getBrowserType().equals("IE") && !userAgent.getBrowserVersion().equals("11"))){
				//暂时取消下载浏览器
				//request.setAttribute("isdownload","true");
				//request.setAttribute("downloadBrowserPath","downloadBrowser");
			}


			Cookie jwtCookie = Arrays.stream(Optional.ofNullable(cookies).orElseGet(() -> new Cookie[]{})).filter(cookie -> cookie.getName().equals(CLIENT_TOKEN_FLAG)).findFirst().orElseGet(() ->{
				return new Cookie(CLIENT_TOKEN_TEST,"");
			});
			log.errorf("jwtCookie: %s,%s",jwtCookie.getName(),jwtCookie.getValue());

			target = validate(jwtCookie,target,_clinet_id.get(),response,request);

		} catch (Exception e) {
			target = "/error/e500";
			log.error(e.getMessage(),e);
		}
		next.handle(target, request, response, isHandled);

	}


	public String validate(Cookie jwtCookie,String requestPath,String clientId,HttpServletResponse response, HttpServletRequest request) {
		//1.放过默认路径
		if( checkAuth(requestPath) ) {
			log.warn("validate-auth-1");
			return requestPath;
		}
		//2.检查是否有token
		if(jwtCookie.getName().equals(CLIENT_TOKEN_TEST)){//空token
			log.warn("validate-auth-2");
			return  "/login";
		}
		//3.验证token
		String token = jwtCookie.getValue();
		if (StrUtil.isEmpty(token)) {
			log.warn("validate-auth-3");
			return "/login";
		}
		//4.判断token是否正确 是否合法
		Object[] tokenObj = JwtUtils.checkToken(token);
		Boolean tokenFlag = (Boolean) tokenObj[0];
		if (!tokenFlag) {
			//删除cookie
			response.addCookie(CookieUtil.delJwtCookie());
			log.warn("validate-auth-4");
			return "/login";
		}
		Token claimsToken = (Token) tokenObj[1];
		//5.token Clientid 与客户端clientId不一致
		if(!claimsToken.getClientId().equals(clientId) || JwtUtils.isTokenExpired(claimsToken.getExpDate())){
			//删除cookie
			response.addCookie(CookieUtil.delJwtCookie());
			log.warn("validate-auth-5");
			return "/login";
		}
		//6.token续期
		Cache authuserCache = Redis.use(REDIS_AUTH_KEY);
		String userId = claimsToken.getUserId();
		long betweenTime = JwtUtils.getBetweenTime(claimsToken.getExpDate());
		if(betweenTime > 0 && betweenTime <= 5) {
			if(!authuserCache.exists(AUTH_CLIENT_PREFIX + clientId)){
				//删除cookie
				response.addCookie(CookieUtil.delJwtCookie());
				log.warn("validate-auth-6");
				return "/login";
			}
			claimsToken.setExpDate(JwtUtils.getExpiredDate());
			String newToken = JwtUtils.encode(claimsToken);
			response.addCookie(CookieUtil.getJwtCookie(newToken));
			authuserCache.expire(AUTH_CLIENT_PREFIX + clientId,SESSION_TIMEOUT);
			authuserCache.expire(userId+"RoleMenu",SESSION_TIMEOUT_REDIS_URL);
			authuserCache.expire(userId+"Url",SESSION_TIMEOUT_REDIS_URL);
			authuserCache.expire(userId+"deptIdByUrl",SESSION_TIMEOUT_REDIS_URL);
		}
		//再加一层缓存
		JSONObject authUser = authuserCache.get(AUTH_CLIENT_PREFIX + clientId);
		if(authUser == null){
			//删除cookie
			response.addCookie(CookieUtil.delJwtCookie());
			log.warn("validate-auth-7");
			return "/login";
		}
		request.setAttribute("authUser",authUser);
		try {
			request.setAttribute("authUserJson", DigestConvert.encoder(authUser.toJSONString(), DigestConvert.DESPLUS));
		} catch (Exception e) {
		}
		List<String> urlList = authuserCache.get(userId+"Url");
		if( !checkActionAuth(requestPath) && !authUser.getString("id").equals("000000000000000000000000000000000000") && !urlList.contains(requestPath)){
			if(requestPath.endsWith("/downLoadEwm") ||  requestPath.endsWith("/del") || requestPath.endsWith("/setValid") || requestPath.endsWith("/resetPassword") ){
				requestPath = "/noAuthJson";
			}else{
				requestPath = "/noAuth";
			}
		}
		return requestPath;
	}
	
	/**
	 * 放过默认路径
	 * @param target
	 * @return
	 */
	protected boolean checkAuth (String target) {
		for (int i = 0; i < noAuthUrl.length; i++) {
			String authUrl = noAuthUrl[i];
			if(target.indexOf(authUrl) > -1){
				return true;
			}
		}
		return false;
	}
	

	/**
	 * session存在放过Action方法
	 * @param target
	 * @return
	 */
	protected boolean checkActionAuth (String target){
		if(target.startsWith("/bscas") || target.startsWith("/exrule") || target.startsWith("/synchis") || target.equals("/") || target.equals("/index") || target.startsWith("/v/tjfx") || target.startsWith("/v/pcenter") || target.startsWith("/v/plansfjl") || target.startsWith("/v/patients/apply") ){
			return true;
		}
		Iterator<String> it = NO_AUTH_ACTION.iterator();  
		while (it.hasNext()) {
		  String actionTarget = it.next();
		  if(target.endsWith(actionTarget)){
			  return true;
		  }
		}
		return false;
	}

}
