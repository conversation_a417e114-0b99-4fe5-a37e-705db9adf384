package cn.wingcloud.jfinal.ext.http;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.HttpUtil;
import java.nio.charset.Charset;
import java.util.Map;

public class HttpKitUtils {

	private static int READTIMEOUT = 1000 * 60 * 30;

	private HttpKitUtils() {}

	public static String get(String url, Map<String, Object> queryParas) {
		return HttpUtil.get(url,queryParas,READTIMEOUT);
	}

	public static String get(String url) {
		return get(url, null);
	}


	public static String _post(String url, Map<String, Object> queryParas) {
		return HttpUtil.post(url,queryParas,READTIMEOUT);
	}

	public static String post(String url, Map<String, Object> queryParas,String queryStr) {
		String newUrl = HttpUtil.urlWithForm(url,queryStr,Charset.defaultCharset(),false);
		return _post(newUrl,queryParas);
	}

	public static String post(String url,String queryStr) {
		String newUrl = HttpUtil.urlWithForm(url,queryStr,Charset.defaultCharset(),false);
		return _post(newUrl,null);
	}

	public static String postBody(String url,String queryStr,String body) {
		String newUrl = HttpUtil.urlWithForm(url,queryStr,Charset.defaultCharset(),false);
		return HttpUtil.createPost(newUrl).body(body,"application/json").execute().body();
	}

	public static String postBody(String url,String queryStr,String body,String contentType) {
		String newUrl = HttpUtil.urlWithForm(url,queryStr,Charset.defaultCharset(),false);
		return HttpUtil.createPost(newUrl).body(body,contentType).execute().body();
	}

}
