package cn.wingcloud.jfinal.ext.http;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;

import java.util.Map;

public class RpcRequest {

    public static MapBuilder<String,String> buildParamMap(){
        return MapUtil.builder("String","String");
    }
    public static MapBuilder<String,Object> buildParamMapObj(){
        return MapUtil.builder("String","Object");
    }
    public static String post(final String requestUrl,final Map<String, Object> queryParas,String queryString){
        return HttpKitUtils.post(requestUrl,queryParas, queryString);
    }

}
