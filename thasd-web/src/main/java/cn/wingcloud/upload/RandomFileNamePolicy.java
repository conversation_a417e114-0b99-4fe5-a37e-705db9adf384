package cn.wingcloud.upload;


import java.io.File;
import java.text.SimpleDateFormat;
import com.oreilly.servlet.multipart.FileRenamePolicy;

/**
 * 自定义的命名策略文件
 * @Package net.zhuawa.util 
 * @ClassName: RandomFileNamePolicy 
 * <AUTHOR>
 * @date 2013-12-12 下午5:29:20
 * @version V1.0
 */
public class RandomFileNamePolicy implements FileRenamePolicy {
	
	public File rename(File file) {
		int index = file.getName().lastIndexOf("."); // 获取文件名中【.】的下标
		String postfix = file.getName().substring(index); 
		String newName = java.util.UUID.randomUUID().toString().replaceAll("-","") + postfix;
		return new File(file.getParent(), newName); // 返回重命名后的文件
	}
	
	protected static String getDateString(String formtStyle){
		try {
			SimpleDateFormat formatter = new SimpleDateFormat(formtStyle);
			return formatter.format(new java.util.Date());
		} catch (Exception e) {
			return "";
		}
	}
}