package com.jfinal.render;


import java.io.IOException;

public class <PERSON>rror<PERSON>son<PERSON><PERSON> extends Render{

	protected static final String contentType = "text/html; charset=" + getEncoding();

	protected String msg;

	public ErrorJsonRender(String msg) {
		this.msg = msg;
	}
	public void render() {
		try {
			response.setContentType(contentType);
			response.getOutputStream().write(msg.getBytes());
		} catch (IOException e) {
			throw new RenderException(e);
		}

	}

	public static final String getContenttype() {
		return contentType;
	}


}
