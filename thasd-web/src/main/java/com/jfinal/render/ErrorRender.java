package com.jfinal.render;

import java.io.IOException;
import org.nutz.log.Log;
import org.nutz.log.Logs;


public class ErrorRender extends Render{
	
    private static final Log log = Logs.get();

	protected static final String contentType = "text/html; charset=" + getEncoding();
	protected static final String version = "Powered by wingcloud.cn";
	protected static final String style = "<style><!-- *{margin:0;padding:0}.main{margin:auto;width:98%;margin-top:15px}H1{font-family:Consolas,Arial,sans-serif;color:white;background-color:#a0a0a0;font-size:36px;padding-left:10px}H3{font-family:Consolas,sans-serif;color:#333;font-size:13px;font-weight:lighter;margin-top:5px;font-style:italic}BODY{font-family:Consolas,Arial,sans-serif;color:black;background-color:white}P{font-family:microsoft yahei,SimH<PERSON>,simsun,sans-serif;background:white;color:black;font-size:24px;font-weight:lighter;color:#666;padding:50px 0 50px 0}A{color:black}A.name{color:black}HR{color:#525d76}.t1{margin-top:15px}--></style>";
	protected static final String html404 = "<html><head><title>HTTP Status 404 Not Found</title>"+style+"</head><body><div class='main'><h1>HTTP Status 404 Not Found</h1><HR size='1' noshade='noshade' class='t1'><p>您访问的页面不存在或已删除！</p><HR size='1' noshade='noshade'><h3>"+version+"</h3><div></body></html>";
	protected static final String html500 = "<html><head><title>HTTP Status 500 Internal Server Error</title>"+style+"</head><body><div class='main'><h1>HTTP Status 500 Internal Server Error</h1><HR size='1' noshade='noshade' class='t1'><p>服务器遇到内部错误！</p><HR size='1' noshade='noshade'><h3>"+version+"</h3><div></body></html>";
	protected static final String html401 = "<html><head><title>HTTP Status 401 Unauthorized</title>"+style+"</head><body><div class='main'><h1>HTTP Status 401 Unauthorized</h1><HR size='1' noshade='noshade' class='t1'><p>服务器请求要求身份验证或需要登录验证！</p><HR size='1' noshade='noshade'><h3>"+version+"</h3><div></body></html>";
	protected static final String html403 = "<html><head><title>HTTP Status 403 Forbidden</title>"+style+"</head><body><div class='main'><h1>HTTP Status 403 Forbidden</h1><HR size='1' noshade='noshade' class='t1'><p>服务器拒绝请求或禁止访问！</p><HR size='1' noshade='noshade'><h3>"+version+"</h3><div></body></html>";
	
	protected int errorCode;
	
	public ErrorRender(int errorCode, String view) {
		log.error("errorCode:"+errorCode);
		log.error("view:"+view);
		this.errorCode = errorCode;
		this.view = view;
	}
	public void render() {

		response.setStatus(getErrorCode());	// HttpServletResponse.SC_XXX_XXX
		// render with view
		String view = getView();
		if (view != null) {
			RenderManager.me().getRenderFactory().getRender(view).setContext(request, response).render();
			return;
		}

//		try {
//			response.setContentType(contentType);
//			response.getOutputStream().write(getErrorHtml().getBytes());
//		} catch (IOException e) {
//			throw new RenderException(e);
//		}

		try {
			response.sendError(getErrorCode());
		} catch (IOException e) {
			e.printStackTrace();
			throw new RenderException(e);
		}
	}
	
	public String getErrorHtml() {
		int errorCode = getErrorCode();
		if (errorCode == 404)
			return html404;
		if (errorCode == 500)
			return html500;
		if (errorCode == 401)
			return html401;
		if (errorCode == 403)
			return html403;
		return "<html><head><title>HTTP Status "+errorCode+"</title>"+style+"</head><body><div class='main'><h1>HTTP Status "+errorCode+"</h1><HR size='1' noshade='noshade' class='t1'><h3>"+version+"</h3><div></body></html>";
	}
	
	public int getErrorCode() {
		return errorCode;
	}
	public static final String getVersion() {
		return version;
	}
	public static final String getContenttype() {
		return contentType;
	}
	public static final String getStyle() {
		return style;
	}
	
	public static final String getErrorHtml(int _errorCode){
		if (_errorCode == 500)
			return html500;
		if (_errorCode == 401)
			return html401;
		if (_errorCode == 403)
			return html403;
		return html404;
	}
}
