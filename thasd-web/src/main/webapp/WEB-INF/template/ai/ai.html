<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能指导</title>
    <link rel="stylesheet" type="text/css" href="${ctxPath}/css/ai/cherry-markdown.css">
    <style>
        html,
        body {
            margin: 0;
            padding: 0;
            width: 99.96%;
            height: 100%;
        }
        .chat-box
        {
            margin: 0;
            margin-left: auto;
            margin-right: auto;
            width: 70%;
            height: 100%;
            display: flex;
            flex-direction: row;
        }
        .ai-tx{
            width:100px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: flex-start;
        }
        .ai-content{
            flex:1;
            display: flex;
            flex-direction: column;
        }
        .top-bar{
            height: 48px;
            display: flex;
            align-items: center;
        }
        .gencontent{
            flex:1;
            overflow: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
        }
        /* Webkit内核浏览器隐藏原生滚动条 */
        .gencontent::-webkit-scrollbar {
            width: 0;
            height: 0;
            background: transparent;
        }
        /* 自定义滚动条样式 */
        .custom-scrollbar {
            position: absolute;
            height: 166px;
            right: 8px;
            width: 6px;
            background: rgba(200,200,200,0.3);
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .one-msg {
            margin-bottom: 15px;
        }
        .one-msg.content{
            /*margin-top: 10px;*/
        }
        .cherry {
            min-height: 0;
        }
        .j-one-msg {
            display: none;
        }
        .chat-one-msg {
            display: inline-block;
            max-width: 600px;
        }
        .buttons {
            margin-top: 50px;
            margin-bottom: 100px;
        }
        .button {
            display: inline-block;
            border: 1px solid #ccc;
            background-color: #0d68ff;
            color: #fff;
            padding: 5px 10px;
            border-radius: 5px;
            box-shadow: 1px 1px 3px #ccc;
            cursor: pointer;
        }
        .loading-templet{display: none;}
        .button.loading {
            display: none;
            flex-direction: row;justify-content: flex-start;align-items: center;
            width: fit-content;
            border: 1px solid #efefef;
            background-color: #F6F7F9;
            color: #000000;
            font-size: 13px;
        }
        .icon-text{display: none;}
        .status {
            margin-left: 10px;
        }
        .avator {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 1px solid #ccc;
            line-height: 48px;
            text-align: center;
            margin-top: 5px;
        }
    </style>
</head>
<body>
<div class="chat-box">
    <div class="ai-tx">
        <div class="avator">
            <svg t="1741831735808" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4546" width="48" height="48"><path d="M0 512a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#13a387" p-id="4547" data-spm-anchor-id="a313x.search_index.0.i0.b8c43a81DvP4nL" class="selected"></path><path d="M716.8 256H307.2a51.2 51.2 0 0 0-51.2 51.2v460.8l102.4-76.8h358.4a51.2 51.2 0 0 0 51.2-51.2V307.2a51.2 51.2 0 0 0-51.2-51.2z m-110.1056 343.0912V358.4H665.6v240.6912H606.72z m-101.8368-55.1424h-72.3712l-14.1568 55.296H358.4l76.032-240.6912h70.4l76.0832 240.6912h-61.9264v-0.1536l-14.1568-55.1424zM467.712 401.408h1.3056c6.4512 24.576 12.8512 53.376 19.2512 77.312l5.2736 20.4032h-49.8432l5.2736-20.4032c6.272-23.936 12.8256-52.096 18.7392-77.312z" fill="#FFFFFF" p-id="4548"></path></svg>
        </div>
    </div>
    <div class="ai-content">
        <div class="top-bar">
            <div class="button loading"></div>
        </div>
        <div class="gencontent">
            <div class="loading-templet">
                <svg t="1741324253201" class="icon-loading" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5320" width="16" height="16">
                    <path d="M96 512c0-19.33 15.67-35 35-35s35 15.67 35 35c0 191.09 154.91 346 346 346s346-154.91 346-346-154.91-346-346-346c-19.33 0-35-15.67-35-35s15.67-35 35-35c229.75 0 416 186.25 416 416S741.75 928 512 928 96 741.75 96 512z" fill="#2F54EB" p-id="9950"></path>
                    <animateTransform attributeName="transform" type="rotate" form="0 " to="360" dur="1s"    repeatCount="indefinite" />
                </svg>
                <svg t="1741325897774" class="icon-text" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30735" width="16" height="16"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" p-id="30736"></path><path d="M96 224H64h32z m832 0h-32 32z m0 448h32-32zM800 96v32-32zM224 96V64v32z m96 704v-32h-10.24l-8.352 5.952L320 800z m-173.408 123.84L128 897.856l18.592 26.048zM800 800v-32 32z m-96-416a32 32 0 1 0 0-64v64z m-384-64a32 32 0 1 0 0 64v-64z m256 256a32 32 0 1 0 0-64v64z m-256-64a32 32 0 1 0 0 64v-64zM64 224v673.824h64V224H64z m832 0v448h64V224h-64z m-96-160H224v64h576V64zM301.408 773.952L128 897.792l37.184 52.096 173.44-123.84-37.216-52.096zM800 768H320v64h480v-64z m160-544a160 160 0 0 0-160-160v64a96 96 0 0 1 96 96h64zM128 224a96 96 0 0 1 96-96V64a160 160 0 0 0-160 160h64zM64 897.824c0 52.064 58.848 82.336 101.184 52.064L128 897.824H64zM896 672a96 96 0 0 1-96 96v64a160 160 0 0 0 160-160h-64z m-192-352H320v64h384v-64z m-128 192h-256v64h256v-64z" fill="#000000" p-id="30737"></path></svg>
                <span class="status">
              正在组织内容...
            </span>
            </div>
            <div class="dialog j-dialog">
                <div class="one-msg">
                    <div class="think-msg"></div>
                </div>
                <div class="one-msg content">
                    <div class="content-msg"></div>
                </div>
            </div>
        </div>
    </div>
<!--    <div class="buttons">-->
<!--        <div class="button j-button">-->
<!--            停止-->
<!--        </div>-->
<!--    </div>-->
</div>
<script src="${ctxPath}/css/ai/cherry-markdown.js"></script>
<script src="${ctxPath}/css/ai/ai.js"></script>
</body>
</html>
