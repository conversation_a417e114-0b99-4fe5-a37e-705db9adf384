<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/deteval.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/html2canvas.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/jspdf.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-field-title .layui-field-box{
            padding: 25px 0 10px 0;
        }
        .layui-form-item .layui-input-inline{margin-right:0;}
        .layui-elem-field legend{font-size:16px;font-weight:bolder;}
        /*595*842 1240×1754  A4纸96dpi下的分辨率是794×1123*/
        .pgbgpage{
            margin: 0 auto;width:814px;height: auto;
            padding: 10px;
        }
        .pgbgpage .layui-card-body{overflow: hidden}
        .bg-border{border:solid 1px #000}
        .title-cus{
            background: linear-gradient(
                    89deg,
                    rgba(12, 173, 128, 1) 20%,
                    rgba(196, 255, 239, 1) 100%
            );
        }
        .bg-grjbxx{width:100%;border-collapse:collapse;}
        .bg-grjbxx td{
            height:32px;line-height: 32px;font-size:15px;font-weight:lighter;color:#333;
        }
        .bg-grjbxx .thd{
            height:32px;line-height: 32px;font-size:14px;text-indent:5px;font-weight:600;color:#000;
        }
        .layui-carousel > [carousel-item] {
            overflow: auto;
        }
        .tipw{color:#888888;font-size:13px}
    </style>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="fulllistbox">
    <%if(breadcrumb){%>
    <div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>检测评估</cite></a>
			  <a><cite>患者评估</cite></a>
			</span>
        <div class="title-name"></div>
    </div>
    <%}%>
    <div class="listbox">
        <div class="listbox-body">
            <div class="layui-card-body body-tree-table">
                <div class="layui-carousel" id="stepForm" lay-filter="stepForm" style="margin: 0 auto;">
                    <div carousel-item>

                        <div>
                            <div class="form-content-box">
                                <form class="layui-form" lay-filter="patientForm" action="javascript:void(0);" style="margin: 0 auto;max-width: 460px;padding-top: 40px;">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>姓名</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="name" id="name" readonly="readonly" lay-verify="required|len20" data-type="selectPatients" placeholder="请选择患者" autocomplete="off" class="layui-input so-it">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>性别</label>
                                        <div class="layui-input-block">
                                            <input type="hidden" name="gendercode" id="gendercode" >
                                            <input type="text" name="gender" id="gender" readonly="readonly" disabled autocomplete="off" class="layui-input layui-disabled" lay-verify="required">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>年龄</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="age" id="age" readonly="readonly" disabled autocomplete="off" class="layui-input layui-disabled" lay-verify="required">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>身份证号</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="idcard" id="idcard" readonly="readonly" disabled lay-verify="required" placeholder="" autocomplete="off" class="layui-input layui-disabled">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>联系电话</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="lxdh" id="lxdh"  disabled lay-verify="required|len11"  placeholder="" autocomplete="off" class="layui-input layui-disabled">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><i>*</i>家庭住址</label>
                                        <div class="layui-input-block" >
                                            <input type="text" name="jtzz" id="jtzz" lay-verify="required"  disabled placeholder="" autocomplete="off" class="layui-input layui-disabled">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <div class="layui-input-block">
                                            <button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formStep">下一步</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div>
                            <form class="layui-form formbox pgbg" id="evalForm" lay-filter="evalForm"  action="javascript:void(0);" style="margin: 0 auto;width:100%;height: 100%;overflow: auto;">
                                <input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
                                <input type="hidden" id="patientid" name="patientid"/>
                                <input type="hidden" name="name"/>
                                <input type="hidden" name="namepy"/>
                                <input type="hidden" name="idcard"/>
                                <input type="hidden" name="gender"/>
                                <input type="hidden" name="gendercode"/>
                                <input type="hidden" name="birthday"/>
                                <input type="hidden" name="age"/>
                                <input type="hidden" name="minzu"/>
                                <input type="hidden" name="minzucode"/>


                                <input type="hidden" name="bprisk" id="bprisk"/>
                                <input type="hidden" name="dprisk" id="dprisk"/>
                                <input type="hidden" name="lprisk" id="lprisk"/>

                                <input type="hidden" name="ascvd" id="ascvd"/>
                                <input type="hidden" name="adgradecode" id="adgradecode"/>
                                <input type="hidden" name="adgradename" id="adgradename"/>
                                <input type="hidden" name="adgradebfb" id="adgradebfb"/>

                                <input type="hidden" id="bplevelcode" name="bplevelcode"/>
                                <input type="hidden" id="bplevelname" name="bplevelname">
                                <input type="hidden" id="bpgradecode" name="bpgradecode"/>
                                <input type="hidden" id="bpgradename" name="bpgradename">
                                <input type="hidden" id="bpresult" name="bpresult">

                                <input type="hidden" id="dblevelcode" name="dblevelcode"/>
                                <input type="hidden" id="dblevelname" name="dblevelname">
                                <input type="hidden" id="dbgradecode" name="dbgradecode"/>
                                <input type="hidden" id="dbgradename" name="dbgradename">
                                <input type="hidden" id="dbresult" name="dbresult">
                                <input type="hidden" id="lplevelcode" name="lplevelcode"/>
                                <input type="hidden" id="lplevelname" name="lplevelname">
                                <input type="hidden" id="lpgradecode" name="lpgradecode"/>
                                <input type="hidden" id="lpgradename" name="lpgradename">
                                <input type="hidden" id="lpresult" name="lpresult">
                                <div class="form-content-box" style="width: 960px;margin: 0 auto;">
                                    <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
                                        <legend>基本指标</legend>
                                        <div class="layui-field-box">
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0">体重</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" id="pgtzcode" name="pgtzcode"/>
                                                        <input type="hidden" id="pgtzname" name="pgtzname">
                                                        <input type="number" id="weight" name="weight" lay-verify="required" min="0" max="1000"step="0.10" regex="number1" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">千克</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0">身高</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="height" name="height" min="0" max="1000" step="0" lay-verify="required" regex="number1" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">厘米</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0">BMI</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="bmi" name="bmi" readonly lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0">腰围</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" id="pgywcode" name="pgywcode"/>
                                                        <input type="hidden" id="pgywname" name="pgywname">
                                                        <input type="number" id="waistline" name="waistline"  lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">厘米</div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
                                        <legend>辅助检查</legend>
                                        <div class="layui-field-box">
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0">血压</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" id="pgxycode" name="pgxycode"/>
                                                        <input type="hidden" id="pgxyname" name="pgxyname">
                                                        <input type="number" id="ssy" name="ssy"  lay-verify="required" min="0" max="300" regex="positive" placeholder="收缩压" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid">/</div>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="szy" name="szy"  lay-verify="required" min="0" max="300" regex="positive" placeholder="舒张压" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmHg</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0">空腹血糖</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" id="pgxtcode" name="pgxtcode"/>
                                                        <input type="hidden" id="pgxtname" name="pgxtname">
                                                        <input type="number" id="kfxt" name="kfxt" lay-verify="bindRequired" bind="ckxt" min="0" max="1000" step="0.10" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmol/L</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0" style="width: 94px;padding-left: 0">餐后2小时血糖</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="ckxt" name="ckxt" lay-verify="bindRequired" bind="kfxt" min="0" max="1000" step="0.10" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmol/L</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0">总胆固醇</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" id="pgxzcode" name="pgxzcode"/>
                                                        <input type="hidden" id="pgxzname" name="pgxzname">
                                                        <input type="number" id="zdgc" name="zdgc" lay-verify="required" min="0" max="1000" step="0.10" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmol/L</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">甘油三酯</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="gysz" name="gysz" lay-verify="required" min="0" max="1000" step="0.10" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmol/L</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:94px;">低密度脂蛋白</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="dmdzdb" name="dmdzdb" lay-verify="required" min="0" max="1000" step="0.10" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">mmol/L</div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
                                        <legend>生活方式</legend>
                                        <div class="layui-field-box">
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label">体力劳动</label>
                                                    <div class="layui-input-block">
                                                        <input type="hidden" name="pgtlldcode" id="pgtlldcode" >
                                                        <input type="hidden" name="pgtlldname" id="pgtlldname" >
                                                        <input type="radio" name="tlld" title="Ⅰ轻劳动" value="0" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                        <input type="radio" name="tlld" title="Ⅱ中等劳动" value="1" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                        <input type="radio" name="tlld" title="Ⅲ重劳动" value="2" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                        <input type="radio" name="tlld" title="Ⅳ极重劳动" value="3" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label pr0" style="width:106px;">每日食盐摄入</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="pgysxgcode" id="pgysxgcode" >
                                                        <input type="hidden" name="pgysxgname" id="pgysxgname" >
                                                        <input type="number" id="yssysr" name="yssysr" lay-verify="required" min="0" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:70px;">每日糖摄入</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="ystsr" name="ystsr" lay-verify="required" min="0" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:70px;">每日油摄入</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="ysysr" name="ysysr"  lay-verify="required" min="0"  regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline">
                                                    <label class="layui-form-label">吸烟状况</label>
                                                    <div class="layui-input-inline" style="width:280px;">
                                                        <input type="hidden" name="pgsmockcode" id="pgsmockcode" >
                                                        <input type="hidden" name="pgsmockname" id="pgsmockname" >
                                                        <input type="radio" name="xyzk" title="从不吸烟" value="1" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                        <input type="radio" name="xyzk" title="已戒烟" value="2" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                        <input type="radio" name="xyzk" title="吸烟" value="3" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
                                                    </div>
                                                </div>
                                            </div>
<!--                                            <div class="layui-form-item">-->
<!--                                                <div class="layui-inline">-->
<!--                                                    <label class="layui-form-label pr0" style="width:106px;">日吸烟量平均</label>-->
<!--                                                    <div class="layui-input-inline ex-input-min">-->
<!--                                                        <input type="number" id="rxyl" name="rxyl"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">-->
<!--                                                    </div>-->
<!--                                                    <div class="layui-form-mid ">支</div>-->
<!--                                                </div>-->
<!--                                                <div class="layui-inline">-->
<!--                                                    <label class="layui-form-label ex-label-min pr0" style="width:94px;">开始吸烟年龄</label>-->
<!--                                                    <div class="layui-input-inline ex-input-min">-->
<!--                                                        <input type="number" id="ksxynl" name="ksxynl"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">-->
<!--                                                    </div>-->
<!--                                                    <div class="layui-form-mid ">岁</div>-->
<!--                                                </div>-->
<!--                                                <div class="layui-inline">-->
<!--                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">戒烟年龄</label>-->
<!--                                                    <div class="layui-input-inline ex-input-min">-->
<!--                                                        <input type="number" id="jynl" name="jynl"  placeholder="" regex="positive" autocomplete="off" class="layui-input">-->
<!--                                                    </div>-->
<!--                                                    <div class="layui-form-mid ">岁</div>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                            <div class="layui-form-item">-->
<!--                                                <div class="layui-inline">-->
<!--                                                    <label class="layui-form-label">饮酒状况</label>-->
<!--                                                    <div class="layui-input-inline" style="width:280px;">-->
<!--                                                        <input type="radio" name="xyzk" title="从不饮酒" value="1" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">-->
<!--                                                        <input type="radio" name="xyzk" title="饮酒" value="3" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">-->
<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                            </div>-->
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label pr0" style="width:180px;">每月饮用高度白酒(≥42度)</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="pgyjcode" id="pgyjcode" >
                                                        <input type="hidden" name="pgyjname" id="pgyjname" >
                                                        <input type="hidden" name="rjyjks" id="rjyjks">
                                                        <input type="number" id="yj1cs" name="yj1cs"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid " style="margin-right: 0">次 ,</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">每次饮用</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="yj1cks" id="yj1cks" >
                                                        <input type="number" id="yj1ks" name="yj1ks"  min="0" max="1000" regex="number" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克（<span class="tipw">根据实际情况填写，没有可不填</span>）</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label pr0" style="width:186px;">每月饮用高度白酒(＜42度)</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="yj2cs" name="yj2cs"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid " style="margin-right: 0">次 ,</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">每次饮用</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="yj2cks" id="yj2cks" >
                                                        <input type="number" id="yj2ks" name="yj2ks"  min="0" max="1000" regex="number" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">（<span class="tipw">根据实际情况填写，没有可不填</span>）</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label pr0" style="width:186px;">每月饮用啤酒</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="yj3cs" name="yj3cs"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid " style="margin-right: 0">次 ,</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">每次饮用</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="yj3cks" id="yj3cks" >
                                                        <input type="number" id="yj3ks" name="yj3ks"  min="0" max="1000" regex="number" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克（<span class="tipw">根据实际情况填写，没有可不填</span>）</div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label pr0" style="width:186px;">每月饮用葡萄酒</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="number" id="yj4cs" name="yj4cs"  min="0" max="1000" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid " style="margin-right: 0">次 ,</div>
                                                </div>
                                                <div class="layui-inline">
                                                    <label class="layui-form-label ex-label-min pr0" style="width:60px;">每次饮用</label>
                                                    <div class="layui-input-inline ex-input-min">
                                                        <input type="hidden" name="yj4cks" id="yj4cks" >
                                                        <input type="number" id="yj4ks" name="yj4ks"  min="0" max="1000" regex="number" placeholder="" autocomplete="off" class="layui-input">
                                                    </div>
                                                    <div class="layui-form-mid ">克（<span class="tipw">根据实际情况填写，没有可不填</span>）</div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                    <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
                                        <legend>风险因素</legend>
                                        <div class="layui-field-box">
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label " >危险因素</label>
                                                    <div class="layui-input-block " style="">
                                                        <input type="hidden" name="wxys" id="wxys-name" >
                                                        <input type="hidden" name="wxyscode" id="wxys-val" >
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk"  title="无" value="0" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="心血管病既往史" level="dp" value="1" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="早发性心血管病家族史" level="bp" value="2" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="早发缺血性心血管疾病家族史" level="lp" value="3" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="代谢综合征" value="4" level="dp" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="血同型半胱氨酸升高" level="bp" value="5" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="高同型半胱胺酸升高" value="6" level="dp" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="高尿酸血症" value="7" level="dp" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="wxys-chk" lay-verify="formChk" title="冠心病及其他危症" level="lp"  value="8" lay-skin="primary" lay-filter="chk-special-filter">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label " >靶器官损害</label>
                                                    <div class="layui-input-block ">
                                                        <input type="hidden" name="bqgsh" id="bqgsh-name" >
                                                        <input type="hidden" name="bqgshcode" id="bqgsh-val" >
                                                        <input type="checkbox" name="bqgsh-chk" title="未筛查" lay-verify="formChk" value="0" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="左心室肥厚" level="bp,dp" lay-verify="formChk" value="1" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="颈动脉超声或动脉粥样斑块" level="bp" lay-verify="formChk" value="2" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="血清肌酐轻度升高" level="bp" lay-verify="formChk" value="3" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="微量白蛋白尿" level="bp,dp" lay-verify="formChk" value="4" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="肾脏损害" level="dp" lay-verify="formChk" value="5" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="心房颤动" level="dp" lay-verify="formChk" value="6" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bqgsh-chk" title="视网膜病变" level="dp" lay-verify="formChk" value="7" lay-skin="primary" lay-filter="chk-special-filter">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item" style="margin-bottom:0;">
                                                <div class="layui-inline" style="margin-right: 0;">
                                                    <label class="layui-form-label " >伴临床疾患</label>
                                                    <div class="layui-input-block " >
                                                        <input type="hidden" name="lcjh" id="lcjh-name" >
                                                        <input type="hidden" name="lcjhcode" id="lcjh-val" >
                                                        <input type="checkbox" name="lcjh-chk" title="无" lay-verify="formChk" value="0" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="lcjh-chk" title="脑血管病" level="bp" lay-verify="formChk" value="1" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="lcjh-chk" title="心脏疾病" level="bp" lay-verify="formChk" value="2" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="lcjh-chk" title="肾脏疾病" level="bp" lay-verify="formChk" value="3" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="lcjh-chk" title="外周血管病" level="bp" lay-verify="formChk" value="4" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="lcjh-chk" title="视网膜病变" level="bp" lay-verify="formChk" value="5" lay-skin="primary" lay-filter="chk-special-filter">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <label class="layui-form-label " ></label>
                                                    <div class="layui-input-block " >
                                                        <div class="layui-inline">
                                                            <label class="layui-form-label" style="font-size: 13px;padding: 9px 5px 9px 0;width: auto;">糖尿病病程</label>
                                                            <div class="layui-input-inline ex-input-min">
                                                                <input type="number" id="tnbbc" name="tnbbc" min="0" max="1000" step="1" placeholder="" autocomplete="off" class="layui-input">
                                                            </div>
                                                            <div class="layui-form-mid " style="font-size: 13px;">年</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>

                                    <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
                                        <legend>伴并发症</legend>
                                        <div class="layui-field-box">
                                            <div class="layui-form-item">
                                                <div class="layui-inline" style="margin-right: 0">
                                                    <div class="layui-input-block " style="">
                                                        <input type="hidden" name="bfz" id="bfz-val" >
                                                        <input type="hidden" name="bfzname" id="bfz-name" >
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level=""  title="无" value="0" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="冠心病"  value="1" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="脑卒中" value="2" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="肾病综合征" value="3" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="眼底病变" value="4" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="周围神经病变" value="5" lay-skin="primary" lay-filter="chk-special-filter">
                                                        <input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="周围血管病变" value="6" lay-skin="primary" lay-filter="chk-special-filter">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>

                                </div>
                                <div class="form-opt-box-center">
                                    <button class="layui-btn layui-btn-normal form-opt-btn" id="pre"  >上一步</button>
                                    <button class="layui-btn layui-btn-normal form-opt-btn" id="zbpost"  lay-submit="" lay-filter="formStep2">评估</button>
                                </div>
                            </form>
                        </div>

                        <div style="overflow: auto">
                            <div class="layui-form pgbgpage " lay-filter="bgForm" id="bgForm">
                                <div class="prtbg" style="z-index: 999999999999999999999;">
                                    <a class="layui-btn layui-btn-normal" data-type="printBg" style="margin-right:0;">
                                        <i class="layui-icon layui-icon-print"></i>
                                    </a>
                                </div>
                                <div class="layui-row layui-col-space10" style="text-align: center;font-weight:bold;font-size:20px;padding: 20px 0 25px 0">
                                    三高患者健康评估报告
                                </div>
                                <div class="layui-row layui-col-space10 " style="font-weight:bold;font-size:14px;padding: 5px 0 5px 0">
                                    <table class="bg-grjbxx">
                                        <tr>
                                            <td class="thd" style="width:48px">姓名：</td>
                                            <td id="name-num"></td>
                                            <td class="thd" style="width:48px">性别：</td>
                                            <td id="gender-num"></td>
                                            <td class="thd"  style="width:48px">年龄：</td>
                                            <td id="age-num"></td>
                                            <td class="thd" style="width:76px">报告日期：</td>
                                            <td id="createtime-num" style="width: 160px;"></td>
                                            <td class="thd" style="width:48px">编号：</td>
                                            <td id="evalno-num"></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="layui-row layui-col-space10 title-cus" style="text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px">
                                    评估结果
                                </div>
                                <div class="layui-row layui-col-space10" id="evalResult" style="margin-top:15px;">
                                </div>
                                <div class="layui-row layui-col-space10 title-cus" style="margin-top:15px;text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px">
                                    ASCVD风险评估
                                </div>
                                <div class="layui-row layui-col-space10" id="ascvdResult" style="margin-top:15px;">
                                    <div style="position: absolute;width:814px;">
                                        <div class="layui-col-xs6 layui-col-md4" style="text-align:center;background:#F5DD0C;line-height:32px;color: #ffffff;">低危(＜5%)</div>
                                        <div class="layui-col-xs6 layui-col-md4" style="text-align:center;background:#FF8D02;line-height:32px;color: #ffffff;">中危(5%~10%)</div>
                                        <div class="layui-col-xs6 layui-col-md4" style="text-align:center;background:#FF2222;line-height:32px;color: #ffffff;">高危(＞10%)</div>
                                    </div>
                                    <div id="echarts-ascvd" style="background-color:#ffffff;min-height:300px;"></div>
                                    <div style="position: absolute;width:814px;text-align:center;margin-top:-51px;font-size:16px;">
                                        您的心脑血管病10年发病风险为<span class="bb" style="font-size:16px;font-weight: bold;padding: 0 5px"></span>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space10 title-cus" style="margin-top:15px;text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px">
                                    基础指标
                                </div>
                                <div class="layui-row layui-col-space10" style="margin-top:15px;">
                                    <div class="layui-col-xs6 layui-col-md3">
                                        <div class="layui-card top-panel">
                                            <div class="layui-card-header layui-card-header1">体重</div>
                                            <div class="layui-card-body">
                                                <div class="layui-row layui-col-space5">
                                                    <div class="layui-col-xs4 layui-col-md4 top-panel-tips">
                                                        <svg t="1650780322385" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3217" width="128" height="128"><path d="M739.712 831.88224H282.73664a36.41856 36.41856 0 0 1-36.71552-36.1216V226.70848a36.42368 36.42368 0 0 1 36.71552-36.1216h456.97536a36.42368 36.42368 0 0 1 36.71552 36.1216v569.05216a36.42368 36.42368 0 0 1-36.71552 36.1216z" fill="#f4ea2a" p-id="3218" data-spm-anchor-id="a313x.7781069.0.i9" class=""></path><path d="M774.656 81.92H249.344A105.18528 105.18528 0 0 0 143.36 186.04544v651.90912A105.18528 105.18528 0 0 0 249.344 942.08h525.312a105.18528 105.18528 0 0 0 105.984-104.12544V186.04544A105.18528 105.18528 0 0 0 774.656 81.92z m41.10848 756.03456a40.80128 40.80128 0 0 1-41.10848 40.3968H249.344a40.8064 40.8064 0 0 1-41.11872-40.3968V186.04544a40.8064 40.8064 0 0 1 41.11872-40.3968h525.312a40.80128 40.80128 0 0 1 41.10848 40.3968v651.90912zM282.112 361.73312l41.14944 49.26464a316.17024 316.17024 0 0 1 379.15136-12.06272l37.84192-51.7632A381.952 381.952 0 0 0 282.112 361.73312z m229.888 183.38304a108.35456 108.35456 0 0 0-33.08032 5.12L394.05056 431.34464 340.9408 467.9424l85.38112 119.6032a103.936 103.936 0 0 0-21.05856 62.464A106.76736 106.76736 0 1 0 512 545.11616z m0 146.02752a41.56928 41.56928 0 0 1-41.8816-41.14944 40.56576 40.56576 0 0 1 17.29536-33.31072 42.02496 42.02496 0 0 1 24.576-7.8336 41.15456 41.15456 0 1 1 0.01024 82.29376z" fill="#242F44" p-id="3219"></path></svg>
                                                    </div>
                                                    <div class="layui-col-xs8 layui-col-md8 top-panel-number" unit="kg" style="color: #28333E;" id="weight-num">
                                                        0
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6 layui-col-md3">
                                        <div class="layui-card top-panel">
                                            <div class="layui-card-header layui-card-header1">身高</div>
                                            <div class="layui-card-body">
                                                <div class="layui-row layui-col-space5">
                                                    <div class="layui-col-xs4 layui-col-md4 top-panel-tips">
                                                        <svg t="1650780274753" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2974" width="128" height="128"><path d="M360.533333 256m-96 0a96 96 0 1 0 192 0 96 96 0 1 0-192 0Z" fill="#FFFFFF" p-id="2975"></path><path d="M360.533333 192c36.266667 0 64 27.733333 64 64s-27.733333 64-64 64-64-27.733333-64-64 29.866667-64 64-64m0-64c-70.4 0-128 57.6-128 128s57.6 128 128 128 128-57.6 128-128-57.6-128-128-128z" fill="#05C6C1" p-id="2976"></path><path d="M177.066667 642.133333V490.666667c0-40.533333 34.133333-74.666667 74.666666-74.666667H469.333333c40.533333 0 74.666667 34.133333 74.666667 74.666667v151.466666H177.066667z" fill="#5EDDD3" p-id="2977"></path><path d="M469.333333 448c23.466667 0 42.666667 19.2 42.666667 42.666667v119.466666H209.066667V490.666667c0-23.466667 19.2-42.666667 42.666666-42.666667H469.333333m0-64H251.733333c-59.733333 0-106.666667 49.066667-106.666666 106.666667v162.133333c0 12.8 8.533333 21.333333 21.333333 21.333333H554.666667c12.8 0 21.333333-8.533333 21.333333-21.333333V490.666667c0-57.6-49.066667-106.666667-106.666667-106.666667zM846.933333 202.666667h-162.133333c-17.066667 0-32-14.933333-32-32s14.933333-32 32-32h162.133333c17.066667 0 32 14.933333 32 32s-14.933333 32-32 32zM846.933333 422.4h-162.133333c-17.066667 0-32-14.933333-32-32s14.933333-32 32-32h162.133333c17.066667 0 32 14.933333 32 32s-14.933333 32-32 32zM846.933333 637.866667h-162.133333c-17.066667 0-32-14.933333-32-32s14.933333-32 32-32h162.133333c17.066667 0 32 14.933333 32 32s-14.933333 32-32 32zM846.933333 885.333333h-162.133333c-17.066667 0-32-14.933333-32-32s14.933333-32 32-32h162.133333c17.066667 0 32 14.933333 32 32s-14.933333 32-32 32z" fill="#05C6C1" p-id="2978"></path><path d="M846.933333 885.333333c-17.066667 0-32-14.933333-32-32V170.666667c0-17.066667 14.933333-32 32-32s32 14.933333 32 32v682.666666c0 17.066667-14.933333 32-32 32z" fill="#05C6C1" p-id="2979"></path><path d="M277.333333 644.266667h166.4v219.733333H277.333333z" fill="#5EDDD3" p-id="2980"></path><path d="M411.733333 676.266667V832h-102.4v-155.733333h102.4m64-64H245.333333V896h230.4V612.266667z" fill="#05C6C1" p-id="2981"></path></svg>
                                                    </div>
                                                    <div class="layui-col-xs8 layui-col-md8 top-panel-number" unit="cm" style="color: #28333E;" id="height-num">
                                                        0
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6 layui-col-md3">
                                        <div class="layui-card top-panel">
                                            <div class="layui-card-header layui-card-header1" style="padding: 0 8px 0 0;">
                                                <div class="layui-col-xs4 layui-col-md4 " style="color: #28333E;">BMI</div>
                                                <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;" id="bmiResult"></div>
                                            </div>
                                            <div class="layui-card-body">
                                                <div class="layui-row layui-col-space5">
                                                    <div class="layui-col-xs4 layui-col-md4 top-panel-tips">
                                                        <svg t="1650780056789" class="icon bmi"  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2734" width="128" height="128"><path d="M192 16h640A176 176 0 0 1 1008 192v640A176 176 0 0 1 832 1008H192A176 176 0 0 1 16 832V192A176 176 0 0 1 192 16z m0 96c-44.16 0-80 35.84-80 80v640c0 44.16 35.84 80 80 80h640c44.16 0 80-35.84 80-80V192c0-44.16-35.84-80-80-80H192z" fill="#667CFF" p-id="2735"></path><path d="M384 512a32 32 0 0 1 32-32h128a96 96 0 0 1 0 192h-128A32 32 0 0 1 384 640V512z m64 32v64h96a32 32 0 0 0 0-64H448z" fill="#31D0B6" p-id="2736"></path><path d="M384 640a32 32 0 0 1 32-32h128a96 96 0 0 1 0 192h-128A32 32 0 0 1 384 768v-128z m64 32v64h96a32 32 0 0 0 0-64H448z" fill="#31D0B6" p-id="2737"></path><path d="M217.024 355.968C287.552 257.92 387.456 208 512.064 208c124.544 0 224.448 49.92 294.912 147.968a48 48 0 0 1-77.952 56.064C676.8 339.392 606.08 304 512.064 304c-94.08 0-164.864 35.392-217.088 108.032a48 48 0 0 1-77.952-56.064z" fill="#667CFF" p-id="2738"></path></svg>
                                                    </div>
                                                    <div class="layui-col-xs8 layui-col-md8 top-panel-number" style="color: #28333E;" id="bmi-num">
                                                        0
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6 layui-col-md3">
                                        <div class="layui-card top-panel">
                                            <div class="layui-card-header layui-card-header1" style="padding: 0 8px 0 0;">
                                                <div class="layui-col-xs4 layui-col-md4 " style="color: #28333E;">腰围</div>
                                                <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;" id="waistlineResult"></div>
                                            </div>
                                            <div class="layui-card-body">
                                                <div class="layui-row layui-col-space5">
                                                    <div class="layui-col-xs4 layui-col-md4 top-panel-tips">
                                                        <svg t="1650780392955" class="icon wateline" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3735" width="128" height="128"><path d="M0 0m512 0l0 0q512 0 512 512l0 0q0 512-512 512l0 0q-512 0-512-512l0 0q0-512 512-512Z" fill="#00B9FF" p-id="3736"></path><path d="M356.503273 514.56l70.365091 70.353455a17.454545 17.454545 0 0 0 24.680727-24.692364l-70.353455-70.353455 35.584-35.584 37.457455 37.434182a17.454545 17.454545 0 0 0 23.435636 1.140364l1.245091-1.140364a17.454545 17.454545 0 0 0 0-24.680727l-37.457454-37.434182 35.607272-35.607273 70.353455 70.365091a17.454545 17.454545 0 0 0 23.435636 1.128728l1.245091-1.128728a17.454545 17.454545 0 0 0 0-24.680727l-70.353454-70.365091 104.168727-104.157091a46.545455 46.545455 0 0 1 65.826909 0l98.734545 98.734546a46.545455 46.545455 0 0 1 0 65.826909L441.367273 758.842182a46.545455 46.545455 0 0 1-65.826909 0l-98.734546-98.734546a46.545455 46.545455 0 0 1 0-65.826909l79.709091-79.732363z" fill="#FFFFFF" p-id="3737"></path></svg>
                                                    </div>
                                                    <div class="layui-col-xs8 layui-col-md8 top-panel-number" unit="cm" style="color: #28333E;" id="waistline-num">
                                                        0
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space10 title-cus" style="margin-top:15px;text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px;">
                                    辅助检查
                                </div>
                                <div class="layui-row layui-col-space10" style="margin-top:15px;">
                                    <div class="layui-col-xs6 layui-col-md4">
                                        <div class="layui-card top-panel top-panel2">
                                            <div class="layui-card-header layui-card-header1" style="padding-left:8px;">
                                                <div class="layui-col-xs2 layui-col-md2 top-panel-tips top-panel-tips1">
                                                    <svg t="1650783294760" class="title-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21954" width="256" height="256"><path d="M726.552381 541.257143c-56.07619 0-102.4-46.32381-102.4-104.838095s46.32381-104.838095 102.4-104.838096 102.4 46.32381 102.4 104.838096c2.438095 56.07619-43.885714 104.838095-102.4 104.838095z m0-153.6c-26.819048 0-46.32381 21.942857-46.32381 46.323809s21.942857 46.32381 46.32381 46.32381c26.819048 0 46.32381-21.942857 46.323809-46.32381s-19.504762-46.32381-46.323809-46.323809zM455.92381 711.92381c-12.190476 0-21.942857-7.314286-26.819048-17.066667l-82.895238-204.8-26.819048 117.028571c-2.438095 12.190476-14.628571 21.942857-26.819047 21.942857l-221.866667 12.190477c-14.628571 0-29.257143-12.190476-29.257143-26.819048 0-7.314286 2.438095-14.628571 7.314286-19.504762 4.87619-4.87619 12.190476-9.752381 19.504762-9.752381l202.361904-9.752381 41.447619-180.419047c2.438095-12.190476 12.190476-19.504762 24.380953-21.942858 12.190476 0 24.380952 4.87619 29.257143 17.066667l90.209524 219.428572 68.266666-173.104762c4.87619-9.752381 14.628571-17.066667 26.819048-17.066667h92.647619c14.628571 0 29.257143 12.190476 29.257143 29.257143 0 14.628571-12.190476 29.257143-29.257143 29.257143h-75.580953l-85.333333 219.428571c-4.87619 7.314286-14.628571 14.628571-26.819047 14.628572z m51.2 238.933333c-9.752381 0-19.504762-4.87619-26.819048-12.190476l-17.066667-17.066667c-17.066667-14.628571-34.133333-26.819048-58.514285-41.447619l-14.628572-12.190476c-70.704762-46.32381-138.971429-99.961905-202.361905-158.476191-9.752381-9.752381-14.628571-24.380952-12.190476-36.571428 2.438095-14.628571 14.628571-24.380952 26.819048-26.819048 12.190476-2.438095 26.819048 0 36.571428 9.752381 58.514286 56.07619 124.342857 104.838095 192.609524 151.161905l17.066667 12.190476c24.380952 17.066667 41.447619 29.257143 58.514286 41.447619l43.885714-29.257143c12.190476-7.314286 26.819048-17.066667 41.447619-26.819047 131.657143-90.209524 331.580952-226.742857 331.580952-438.857143 0-121.904762-97.52381-219.428571-216.990476-216.990476-65.828571 0-126.780952 29.257143-170.666667 78.019047-7.314286 7.314286-17.066667 12.190476-29.257142 12.190476s-19.504762-4.87619-26.819048-12.190476c-39.009524-48.761905-99.961905-78.019048-160.914286-78.019047-58.514286 0-112.152381 21.942857-153.6 63.390476s-63.390476 97.52381-63.390476 156.038095c0 48.761905 9.752381 95.085714 31.695238 138.971429 4.87619 12.190476 4.87619 26.819048-2.438095 39.009523-7.314286 12.190476-21.942857 17.066667-34.133333 14.628572s-24.380952-9.752381-31.695239-21.942857c-24.380952-53.638095-36.571429-112.152381-36.571428-170.666667 0-78.019048 31.695238-153.6 85.333333-209.676191S241.371429 73.142857 316.952381 73.142857c70.704762 0 138.971429 26.819048 190.171429 75.580953C563.2 99.961905 633.904762 73.142857 707.047619 73.142857c160.914286 0 292.571429 131.657143 292.571429 295.009524 0 251.12381-219.428571 399.847619-363.276191 499.809524-17.066667 12.190476-31.695238 19.504762-46.323809 29.257143-14.628571 7.314286-26.819048 17.066667-39.009524 24.380952l-17.066667 17.066667c-7.314286 7.314286-17.066667 12.190476-26.819047 12.190476z" fill="#4F8BFF" p-id="21955"></path></svg>
                                                </div>
                                                <div class="layui-col-xs2 layui-col-md2 " style="color: #28333E;">血压</div>
                                                <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;" id="xyResult"></div>
                                            </div>
                                            <div class="layui-card-body fzjcbox">
                                                <div class="">收缩压:<span id="ssy-num"></span>mmHg</div>
                                                <div class="">舒张压:<span id="szy-num"></span>mmHg</div>
                                                <div class="">高血压分级:<span id="xy-fx-level"></span></div>
                                                <div class="">心血管病风险分层:<span id="xy-fx-grade"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6 layui-col-md4">
                                        <div class="layui-card top-panel top-panel2">
                                            <div class="layui-card-header layui-card-header1" style="padding-left:8px;">
                                                <div class="layui-col-xs2 layui-col-md2 top-panel-tips top-panel-tips1">
                                                    <svg t="1650782884792" class="title-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12856" data-spm-anchor-id="a313x.7781069.0.i72" width="256" height="256"><path d="M444.2 883.9c-75.1 0-139.5-24.2-186.2-70.1-46.7-45.8-72.9-110.5-75.7-187-1.3-33.7 5.7-72 20.8-113.9 13.5-37.7 33.6-78.5 59.6-121.3 44.9-73.7 107-152.3 175-221.2 1-1 2.3-1.5 3.6-1.5 1.1 0 2.2 0.4 3.1 1.1 2 1.6 2.6 4.4 1.2 6.6-0.6 0.9-56.2 93.2-99.4 205-25.3 65.5-41.5 124.7-48 176.1-8.1 63.6-1.3 115.1 20 153 14.6 25.9 36.4 46 64.8 59.6 31.3 15 71.5 22.6 119.4 22.6 43 0 93.3-6.1 149.4-18.3 0.4-0.1 0.8-0.1 1.3-0.1 2.5 0 4.7 1.6 5.3 4.1 0.5 1.9 0.6 4.6-4.7 12.8-2.4 3.8-6.8 9.9-13.8 17.8-10.5 11.9-28.1 28.9-52.1 42.8-19.2 11-43.3 19.4-71.6 25-29.2 5.7-55.3 6.9-72 6.9z" fill="#eecdce" p-id="12857" data-spm-anchor-id="a313x.7781069.0.i67" class=""></path><path d="M444.2 961c-185.3 0-336-150.4-336-335.2 0-47.1 13.9-101.7 41.3-162.3 23-50.9 55.8-106.4 97.3-165 72.9-102.9 154.5-190 197.4-233.5 35.4 35.4 88.5 91.4 143.4 160.1-10.4 14.2-30.1 48.5-39.7 65.3-40.4-51.1-70.3-87.3-103-120.1-1-1-2.2-1.5-3.6-1.5s-2.6 0.5-3.6 1.5c-67.9 68.9-130.1 147.5-175 221.2-26 42.8-46.1 83.6-59.6 121.3-15 41.9-22 80.3-20.8 113.9 2.9 76.5 29 141.2 75.7 187 46.7 45.9 111.1 70.1 186.2 70.1 138.1 0 234.6-81.1 264.8-222.6 0-0.2 0.1-0.4 0.1-0.5l0.2-2c6.6 0.5 18.7 1.2 28.3 1.2 16.9 0 30.7-1.4 41.1-4.1C763.5 827.2 617 961 444.2 961z" fill="#ff5555" p-id="12858" data-spm-anchor-id="a313x.7781069.0.i63" class=""></path><path d="M424.5 743.5h31.8c19.4 0 35.2-15.8 35.2-35.2v-44.4H539c19.4 0 35.2-15.8 35.2-35.1V597c0-19.4-15.8-35.2-35.2-35.2h-47.5v-44.4c0-19.4-15.8-35.1-35.2-35.1h-31.8c-19.4 0-35.2 15.8-35.2 35.1v44.4h-41.2c-19.4 0-35.2 15.8-35.2 35.2v31.8c0 19.4 15.8 35.1 35.2 35.1h41.2v44.4c0 19.4 15.8 35.2 35.2 35.2zM738 99.7l-2.4-2.3-2.4 2.4c-65.6 65.5-182.8 233.4-182.8 334.8 0 100.6 83.1 182.3 185.2 182.3s185.2-81.8 185.2-182.3c0.1-103.3-117.1-270.4-182.8-334.9z" fill="#ff5555" p-id="12859" data-spm-anchor-id="a313x.7781069.0.i64" class=""></path></svg>
                                                </div>
                                                <div class="layui-col-xs2 layui-col-md2 " style="color: #28333E;">血糖</div>
                                                <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;" id="xtResult"></div>
                                            </div>
                                            <div class="layui-card-body fzjcbox">
                                                <div class="">空腹血糖:<span id="kfxt-num"></span>mmol/L</div>
                                                <div class="">餐后2H血糖:<span id="ckxt-num"></span>mmol/L</div>
                                                <div class="">心血管病风险分层:<span id="xt-fx-grade"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs6 layui-col-md4">
                                        <div class="layui-card top-panel top-panel2">
                                            <div class="layui-card-header layui-card-header1" style="padding-left:8px;">
                                                <div class="layui-col-xs2 layui-col-md2 top-panel-tips top-panel-tips1">
                                                    <svg t="1650782359548" class="title-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3973" width="128" height="128"><path d="M511.947481 1024A419.400176 419.400176 0 0 1 95.131564 601.482041a440.60777 440.60777 0 0 1 30.144127-158.790192 529.789703 529.789703 0 0 1 60.555017-105.037611l16.539255-22.858185 18.339901-24.025269 9.720147-12.45446 20.774105-25.67586 22.374678-26.676219 23.975252-27.809958 25.575825-28.843661 27.143052-29.860693c4.668338-5.001791 9.386694-10.220326 14.205087-15.322153q30.010746-33.345273 61.27194-65.356736l37.696831-37.646813A66.973982 66.973982 0 0 1 511.947481 0a76.460712 76.460712 0 0 1 43.132111 13.904979l5.551988 4.668338 36.179622 37.163307 20.42398 21.207594 42.215116 44.44925 28.210101 31.277866a3441.065486 3441.065486 0 0 1 26.55951 30.144127l25.008956 28.943698 23.241655 27.859975 21.674428 26.676219 10.220326 12.887948 19.023478 24.892247 8.953206 12.004298 16.539256 23.108275a581.87502 581.87502 0 0 1 56.903709 96.968055A423.484972 423.484972 0 0 1 511.947481 1024z m0-72.942786a350.542186 350.542186 0 0 0 316.496662-486.424175 352.376176 352.376176 0 0 0-20.624051-39.39744l-11.287375-18.139829q-9.753492-15.088736-20.140545-29.760657l-15.55557-21.491028-17.172816-22.808167-18.923443-24.02527-20.524016-25.29239-22.174606-26.459474q-5.81875-6.852454-11.670846-13.671562l-24.558794-28.210101-26.209385-29.327168q-6.819108-7.552704-13.671562-15.005373l-41.431502-43.582273-42.065062-43.582272-9.286659-9.436712-21.157576 20.907486c-23.341691 23.491745-48.350646 49.651112-73.876453 78.061285l-26.676219 29.027061-25.192354 28.060047-23.691817 26.992999-22.074571 25.92595-10.403725 12.554495-19.690384 24.225342q-4.651666 5.81875-9.23664 11.670845l-17.222834 22.474714-15.55557 21.257612-13.854961 20.007164a405.82865 405.82865 0 0 0-41.248103 73.576346 367.531603 367.531603 0 0 0-25.192354 132.380735A346.424045 346.424045 0 0 0 511.947481 951.073887z" fill="#2C2C2C" p-id="3974"></path><path d="M295.886782 478.337947c0 53.05233 96.701293 96.05106 216.12739 96.051059s216.127389-42.99873 216.127389-96.051059-96.701293-96.05106-216.127389-96.05106-216.127389 42.99873-216.12739 96.05106z" fill="#FE6058" p-id="3975"></path><path d="M295.886782 718.540623c0 53.05233 96.701293 96.05106 216.12739 96.05106s216.127389-42.99873 216.127389-96.05106-96.701293-96.05106-216.127389-96.05106-216.127389 42.99873-216.12739 96.05106z" fill="#4BCAAF" p-id="3976"></path></svg>
                                                </div>
                                                <div class="layui-col-xs2 layui-col-md2 " style="color: #28333E;">血脂</div>
                                                <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;" id="xzResult"></div>
                                            </div>
                                            <div class="layui-card-body fzjcbox">
                                                <div class="">总胆固醇:<span id="zdgc-num"></span>mmol/L</div>
                                                <div class="">甘油三酯:<span id="gysz-num"></span>mmol/L</div>
                                                <div class="">低密度脂蛋白:<span id="dmdzdb-num"></span>mmol/L</div>
                                                <div class="">心血管病风险分层:<span id="xz-fx-grade"></span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-row layui-col-space10 title-cus" style="margin-top:15px;text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px">
                                    风险因素
                                </div>
                                <div class="layui-row layui-col-space10" style="margin-top:15px;">
                                    <table class="layui-form shfs-tb" border="0" cellspacing="0" cellpadding="0" border="0" >
                                        <tr>
                                            <td class="th" >高血压</td>
                                            <td class="th" >糖尿病</td>
                                            <td class="th" >高血脂</td>
                                        </tr>
                                        <tr>
                                            <td class="ttt" id="bprisk-box"></td>
                                            <td class="ttt" id="dprisk-box"></td>
                                            <td class="ttt" id="lprisk-box"></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="layui-row layui-col-space10 " style="margin-top:15px;text-align:left;color:#fff;font-weight:bold;font-size:16px;padding: 10px 0 10px 10px;">
                                    <div class="layui-form-item">
                                        <div class="layui-input-block" style="margin-left: 0;text-align: center;">

<!--                                            <button class="layui-btn layui-btn-normal form-opt-btn" id="replay"  lay-submit="" lay-filter="formStep3">继续评估</button>-->

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script id="detevaljs" type="text/javascript" src="${ctxPath}/services/deteval/index.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>