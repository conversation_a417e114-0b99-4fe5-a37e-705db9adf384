<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/add.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
	<style type="text/css">
		.layui-form-label{
			width:110px;
			padding: 9px 5px 9px 0;
		}
		.layui-form-item .layui-input-inline {
			width: 375px;
		}
		.layui-form-item .layui-inline {
			margin-bottom: 24px;
		}
		.layui-form-item .layui-input-inline{margin-right:0;}
		.layui-elem-field legend{font-size:16px;font-weight:bolder;}
	</style>
<script type="text/javascript">
</script>
</head>
<body>
	<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<form class="layui-form formbox" lay-filter="formtable" >
		<input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
		<input type="hidden" name="id" id="id">
		<input type="hidden" name="enabled" id="enabled">
		<div class="form-content-box">
<!--			<fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">-->
<!--				<legend>药品基本信息</legend>-->
<!--			</fieldset>-->
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>药品编码</label>
					<div class="layui-input-inline">
						<input type="text" name="dcode" id="dcode" lay-verify="required|len30" placeholder="请输入药品编码" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>药品名称</label>
					<div class="layui-input-inline">
						<input type="text" name="dname" id="dname" lay-verify="required|len200" placeholder="请输入药品名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>拼音码</label>
					<div class="layui-input-inline">
						<input type="text" name="dpym" id="dpym" lay-verify="required|len200" placeholder="请输入拼音码" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>药品属性</label>
					<div class="layui-input-inline">
						<input type="hidden" name="dattrs" id="dattrs" >
						<select name="dattrscode" id="dattrscode" accept-name="dattrs" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择药品属性或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
							for(mzBean in dicData.drugs_attrs){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>药品类型</label>
					<div class="layui-input-inline">
						<input type="hidden" name="dtype" id="dtype" >
						<select name="dtypecode" id="dtypecode" accept-name="dtype" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择药品类型或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
								for(mzBean in dicData.drugs_type){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">药理类型</label>
					<div class="layui-input-inline">
						<input type="hidden" name="deffect" id="deffect" >
						<select name="deffectcode" id="deffectcode" accept-name="deffect" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择药理类型或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
								for(mzBean in dicData.drugs_effect){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">剂型</label>
					<div class="layui-input-inline">
						<input type="hidden" name="djx" id="djx" >
						<select name="djxcode" id="djxcode" accept-name="djx" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择剂型或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
								for(mzBean in dicData.drugs_jx){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">规格</label>
					<div class="layui-input-inline">
						<input type="text" name="dguige" id="dguige" lay-verify="len100" placeholder="请输入药品规格" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">包装单位</label>
					<div class="layui-input-inline">
						<input type="hidden" name="dzxbzdw" id="dzxbzdw" >
						<select name="dzxbzdwcode" id="dzxbzdwcode" accept-name="dzxbzdw" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择包装单位或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
								for(mzBean in dicData.drugs_jldw){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">剂量单位</label>
					<div class="layui-input-inline">
						<input type="hidden" name="dzxjldw" id="dzxjldw" >
						<select name="dzxjldwcode" id="dzxjldwcode" accept-name="dzxjldw" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择剂量单位或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
								for(mzBean in dicData.drugs_jldw){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">用药类型</label>
					<div class="layui-input-inline">
						<input type="hidden" name="yylx" id="yylx" >
						<select name="yylxcode" id="yylxcode" accept-name="yylx" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择用药类型或输入检索</option>
							<%
							if(isNotEmpty(dicData)){
							for(mzBean in dicData.yylx){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
			</div>
		</div>
	  	<div class="form-opt-box">
			<button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formsb">确定</button>
			<a class="layui-btn layui-btn-primary form-opt-btn" id="subcancel" data-type="cancel">取消</a>
	  	</div>
	</form>
</body>
<script id="adtjs" type="text/javascript" src="${ctxPath}/services/drugs/adt.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&mainid=${id!''}"></script>
</html>