<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台-用户登录</title>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/error.css">
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript">
	var username = "${username!''}";
	var patientid = "${patientid!''}";
</script>
<style>
    html,body{width:100%;height:100%;}
    body{
    }
    .initbox{
        position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
    }
    .initbox > span[class*="-icon"] {
        width: 45px;
        height: 45px;
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
    }
</style>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="ant-layout-content" >
    <div class="exception___19n05" style="min-height: 500px; height: 80%;">
        <div class="imgBlock___2g-kj">
            <div class="imgEle___cXgra" style="background-image: url(${ctxPath}/css/rs/RVRUAYdCGeYNBWoKiIwB.svg);">
            </div>
        </div>
        <div class="content___3PvOs">
            <h1 id="code">
                500
            </h1>
            <div class="desc___3G5g3">
                抱歉，服务器出错了,<span  id="msg"></span>
            </div>
            <div class="actions___1lAdW" >
                请联系管理员
                <!--
                    <a href="#/">
                        返回首页
                    </a>
                     -->
            </div>
        </div>
    </div>
</div>
</body>
<script id="loginjs" type="text/javascript" src="${ctxPath}/services/loginForHis.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>