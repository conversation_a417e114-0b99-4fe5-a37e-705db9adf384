<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/admin.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/services/idcard/shensi.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .dds {
            display: block !important;
        }
        .pear-notice {
            width: 1096px!important;
            background: #03214059;
            padding: 5px 5px;
        }
        .pear-notice .layui-table{color: #000000}
        .pear-admin .layui-layout-right .layui-nav-child {
            border: 1px solid #d2d5db;
        }
        .pear-notice li {
            border:none!important;
            moz-user-select:text!important;
            -moz-user-select: text!important;
            -o-user-select: text!important;
            -khtml-user-select: text!important;
            -webkit-user-select: text!important;
            -ms-user-select: text!important;
            user-select: text!important;
        }
        .pear-notice .list-item:nth-child(odd){
            background-color: #e9eaeb;
        }
        .pear-notice .layui-card-body {
            padding:10px 8px;
            line-height: 32px;
        }
        .layui-table-page .layui-laypage a{display: inline-block}
        .layui-card-body .layui-form.layui-table-view{margin: 0}
        .layui-table-page .layui-laypage a {
            display: inline-block;
            padding: 0 12px;
        }
        .layui-anim-upbit{
            user-select:text;
        }
        .layui-table[lay-even] tr:nth-child(even){
            background-color: #f6f6fd;
        }
        .layui-form .layui-form-text .layui-input-block,.layui-form-pane .layui-form-text .layui-input-block{
            width:100%;
        }
        .layui-btn .layui-icon{
            margin: 0;
        }
        .layui-form-label{
            font-size:12px;
            width:60px;
            text-align:left;
        }
        .layui-form-item{margin-bottom:0;}
        .layui-form-item .layui-inline .layui-form-label{padding:4px 1px}
        .layui-form-item .layui-input{height:28px;line-height:28px;font-size:12px;padding-left: 3px}

        .layui-form-pane .layui-input {
            height: 38px;
            line-height: 1.3;
            line-height: 38px\9;
            border-width: 1px;
            border-style: solid;
            background-color: #fff;
            border-radius: 2px;
        }

        .layui-form-item .layui-btn{height:28px;line-height:28px}
        .layui-form-item .layui-btn.user-search{
            height: 27px;
            line-height: 27px;
            margin-bottom: 5px;
        }
        .layui-form-item .layui-inline{margin-right:0;}
        .layui-form-item .layui-input-inline{width:110px;}
        /********/
        .layui-input-block {
            float: left;
            margin-left:0;
        }
    </style>
    <script>
        var userName = "${authUser.name}";
        var alibabaKey = "${authUserJson}";
        var authUser = JSON.parse('${json(authUser)}');
    </script>
</head>
<body class="layui-layout-body pear-admin">
<div class="layui-layout layui-layout-admin">
    <div class="layui-header">
        <ul class="layui-nav layui-layout-left">
            <li class="collaspe layui-nav-item"><a href="#" class="layui-icon layui-icon-shrink-right"></a></li>
            <li class="refresh layui-nav-item"><a href="#" class="layui-icon layui-icon-refresh-1" loading=600></a></li>
        </ul>
        <!-- 顶 部 右 侧 菜 单 -->
        <div id="control" class="layui-layout-control"></div>
        <ul class="layui-nav layui-layout-right" lay-filter="message">
            <li class="layui-nav-item layui-hide-xs message"></li>
            <li class="layui-nav-item">
                <!-- 头 像 -->
                <a href="javascript:;">
                    <img src="${ctxPath}/css/rs/tx.png" class="layui-nav-img">
                </a>
                <!-- 功 能 菜 单 -->
                <dl class="layui-nav-child" style="width:116px;left:-10px;">
                    <dd><a href="javascript:void(0);" id="editPass" class="menu___2IuJy"><i class="anticon anticon-unlock"></i>修改密码</a></dd>
                    <dd><a href="javascript:void(0);" id="loginOut" class="menu___2IuJy"><i class="anticon anticon-logout"></i>退出登录</a></dd>
                </dl>
            </li>
        </ul>
    </div>
    <!-- 侧 边 区 域 -->
    <div class="layui-side layui-bg-black">
        <!-- 菜 单 顶 部 -->
        <div class="layui-logo">
            <!-- 图 标 -->
            <img class="logo"/>
            <!-- 标 题 -->
            <span class="title wfont"></span>
        </div>
        <!-- 菜 单 内 容 -->
        <div class="layui-side-scroll">
            <div id="sideMenu"></div>
        </div>
    </div>
    <!-- 视 图 页 面 -->
    <div class="layui-body">
        <!-- 内 容 页 面 -->
        <div id="content"></div>
    </div>
    <!-- 遮 盖 层 -->
    <div class="pear-cover"></div>
    <!-- 加 载 动 画-->
    <div class="loader-main">
        <div class="loader"></div>
    </div>
</div>
<!--<div class="admin-menu layui-form">-->
<!--    <div class="layui-card">-->
<!--        <div class="layui-card-header">系统初始化</div>-->
<!--        <div class="layui-card-body">-->
<!--            用于系统初始化操作，主要作用是导入公卫的组织、行政区划、用户等数据，只有超级管理员才能执行！！！<br><br>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="clear-basic" class="layui-btn layui-btn-cus3">0.删除基础数据</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="import-area" class="layui-btn ">1.导入公卫行政区划</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="import-org" class="layui-btn  layui-btn-warm ">2.导入公卫组织机构</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="import-user" class="layui-btn layui-btn-cus1 ">3.导入公卫用户信息</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="import-data" class="layui-btn layui-btn-danger ">4.补全冗余数据信息</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="import-patients" class="layui-btn layui-btn-danger ">5.导入公卫患者信息</button>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-form-item layui-form-text">-->
<!--                <div class="layui-input-block">-->
<!--                    <button id="reGenJh" class="layui-btn layui-btn-danger ">8.重新生成年度复诊计划</button>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
<%include("/patients/plansfjl/add.html",{'authUserJson':authUserJson,'authUser': authUser}){} %>
<script id="plansfjl" type="text/javascript" src="${ctxPath}/services/plansfjl/sfjl.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
<script id="indexjs" type="text/javascript" src="${ctxPath}/services/pear-admin.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</body>
</html>