<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/add.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
	<style type="text/css">

		.layui-form-label{
			width:110px;
			padding: 9px 5px 9px 0;
		}
		.layui-form-item .layui-input-inline {
			width: 375px;
		}
		.layui-form-item .layui-input-inline.sdf{
			width: 208px;
		}
		.layui-form-item .layui-inline {
			margin-bottom: 24px;
		}
		.layui-form-item .layui-input-inline{margin-right:0;}
		.layui-elem-field legend{font-size:16px;font-weight:bolder;}
	</style>
<script type="text/javascript">
	var zNodesJsonSfxm = JSON.parse('${ctl.jsonToString(sfxm.list)}');
</script>
</head>
<body>
	<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<form class="layui-form formbox" lay-filter="formtable" >
		<input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
		<input type="hidden" name="id" id="id">
		<div class="form-content-box">
			<div class="layui-form-item" style="margin-bottom:0;">
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>所属项目</label>
					<div class="layui-input-inline">
						<input type="hidden" name="parentid" id="parentid"/>
						<div id="sfxm-item" rid = "parentcode"  rname="parentname" class="layui-form-select select-tree"></div>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>项目编码</label>
					<div class="layui-input-inline">
						<input type="text" name="code" id="code" lay-verify="required|len30" placeholder="请输入项目编码" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><i>*</i>项目名称</label>
					<div class="layui-input-inline">
						<input type="text" name="name" id="name" lay-verify="required|len200" placeholder="请输入项目名称" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">计价单位</label>
					<div class="layui-input-inline">
						<input type="hidden" name="jjdw" id="jjdw" >
						<select name="jjdwcode" id="jjdwcode" accept-name="jjdw" lay-filter="dicdata" lay-verify="" lay-search="">
							<option value="" datacode = "">请选择计价单位或输入检索</option>
							<%
							if(isNotEmpty(dicData) && isNotEmpty(dicData.items_jjdw)){
							for(mzBean in dicData.items_jjdw){
							%>
							<option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
							<%}}%>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">项目内容</label>
				<div class="layui-input-block">
					<textarea style="max-width:880px;height:180px" placeholder="请输入项目内容" lay-verify="len2000" name="xmnr" id="xmnr" class="layui-textarea"></textarea>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">除外内容</label>
				<div class="layui-input-block">
					<textarea style="max-width:880px;height:180px" placeholder="请输入除外内容" lay-verify="len2000" name="cwnr" id="cwnr" class="layui-textarea"></textarea>
				</div>
			</div>
			<div class="layui-form-item" style="margin-bottom:0;">
				<div class="layui-inline">
					<label class="layui-form-label">三级医院</label>
					<div class="layui-input-inline sdf">
						<input type="text" name="sjdwjg" id="sjdwjg" lay-verify="len20" placeholder="请输入三级医院价格" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">二级医院</label>
					<div class="layui-input-inline sdf">
						<input type="text" name="ejdwjg" id="ejdwjg" lay-verify="len20" placeholder="请输入二级医院价格" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">一级医院</label>
					<div class="layui-input-inline sdf">
						<input type="text" name="yjdwjg" id="yjdwjg" lay-verify="len20" placeholder="请输入一级医院价格" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>
			<div class="layui-form-item" style="margin-bottom: 20px;">
				<label class="layui-form-label">说明</label>
				<div class="layui-input-block">
					<textarea style="max-width:880px;height:200px" placeholder="请输入说明" lay-verify="len2000" name="remark" id="remark" class="layui-textarea"></textarea>
				</div>
			</div>
		</div>
	  	<div class="form-opt-box">
			<button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formsb">确定</button>
			<a class="layui-btn layui-btn-primary form-opt-btn" id="subcancel" data-type="cancel">取消</a>
	  	</div>
	</form>
</body>
<script id="adtjs" type="text/javascript" src="${ctxPath}/services/items/adt.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&mainid=${id!''}"></script>
</html>