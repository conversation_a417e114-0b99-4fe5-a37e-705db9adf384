<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"><meta http-equiv="Expires" content="0"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Cache-control" content="no-cache"><meta http-equiv="Cache" content="no-cache">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/css/rs/add.css?ver=${ctl.randomstr()}">
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}&ver=${ctl.randomstr()}"></script>
    <link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-form-label{
            width:110px;
            padding: 9px 5px 9px 0;
        }
        .layui-field-title .layui-field-box{
            padding: 25px 0 10px 0;
        }
        .layui-form-item .layui-input-inline{margin-right:0;}
        .layui-elem-field legend{font-size:16px;font-weight:bolder;}
    </style>
    <script type="text/javascript">
        var authUser = JSON.parse('${json(authUser)}');
        var zNodesAreaJson = JSON.parse('${json(area.list)}') ;
    </script>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<form class="layui-form formbox" lay-filter="formtable" id="fastForm">
    <input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
    <input type="hidden" name="id" id="id">
    <input type="hidden" name="age" id="age">
    <input type="hidden" name="isgxy" id="isgxy">
    <input type="hidden" name="istnb" id="istnb">
    <input type="hidden" name="isgxz" id="isgxz">
    <input type="hidden" name="orgid" id="orgid">
    <input type="hidden" name="orgcode" id="orgcode">
    <input type="hidden" name="orgname" id="orgname">
    <input type="hidden" name="pmtype" id="pmtype" value="管理中">
    <input type="hidden" name="pmtypecode" id="pmtypecode" value="0">
    <div class="form-content-box">
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
            <legend>人员基本信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" id="name" lay-verify="required|len20" placeholder="请输入姓名" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>性别</label>
                        <div class="layui-input-inline">
                            <input type="hidden" name="gender" id="gender" >
                            <select name="gendercode" id="gendercode" accept-name="gender" lay-filter="dicdata" lay-verify="required" lay-search="">
                                <option value="" datacode = "">请选择性别或输入检索</option>
                                <%
                                if(isNotEmpty(dicData)){
                                for(mzBean in dicData.sex){
                                %>
                                <option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
                                <%}}%>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>出生日期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="birthday" id="birthday" readonly="readonly"  placeholder="请选择出生日期" autocomplete="off" class="layui-input date-it" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>民族</label>
                        <div class="layui-input-inline">
                            <input type="hidden" name="minzu" id="minzu" >
                            <select name="minzucode" id="minzucode" accept-name="minzu" lay-filter="dicdata" lay-verify="required" lay-search="">
                                <option value="" datacode = "">请选择民族或输入检索</option>
                                <%
                                if(isNotEmpty(dicData)){
                                for(mzBean in dicData.minzu){
                                %>
                                <option value="${mzBean.val}" datacode = "${mzBean.code}">${mzBean.name}</option>
                                <%}}%>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>身份证号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="idcard" id="idcard" lay-verify="required" regex="positive" placeholder="请输入身份证号" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>联系电话</label>
                        <div class="layui-input-inline">
                            <input type="text" name="lxdh" id="lxdh" lay-verify="required|len11"  placeholder="请输入联系电话" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>家庭住址</label>
                        <div class="layui-input-inline" style="width: 510px;">
                            <input type="text" name="jtzz" id="jtzz" lay-verify="required"  placeholder="请输入家庭住址" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">行政区划</label>
                        <div class="layui-input-inline" style="width:509px;">
                            <div id="sjarea" rid = "areaid"  rname="areaname" class="layui-form-select select-tree"></div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
            <legend>药物过敏史</legend>
            <div class="layui-field-box" style="padding-top:15px;">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="ywgms" id="ywgms-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_ywgms){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="ywgms"' : ""} name="ywgms-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="ywgmsqt" id="ywgmsqt" lay-verify=""  placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="" style="height:28px;margin-top: 10px">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
            <legend>既往史</legend>
            <div class="layui-field-box" style="padding-top:15px;">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="jwsjb" id="jwsjb-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_jwsjb){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="jwsjb"' : ""} name="jwsjb-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
            <legend>家族史</legend>
            <div class="layui-field-box" style="padding-top:15px;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><i>*</i>父亲</label>
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="jzsfq" id="jzsfq-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_jzs){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="jzsfq"' : ""}  name="jzsfq-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><i>*</i>母亲</label>
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="jzsmq" id="jzsmq-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_jzs){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="jzsmq"' : ""}  name="jzsmq-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">兄弟姐妹</label>
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="jzsxdjm" id="jzsxdjm-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_jzs){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="jzsxdjm"' : ""}  name="jzsxdjm-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">子女</label>
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="jzszn" id="jzszn-val" >
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.p_jzs){
                        %>
                        <input type="checkbox" lay-filter="chkfilter" ${mzBean.val == '1' ? 'id="jzszn"' : ""}  name="jzszn-chk" title="${mzBean.name}" datacode = "${mzBean.code}" value="${mzBean.val}" lay-skin="primary">
                        <%}}%>
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
    <div class="form-opt-box">
        <button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formsb">确定</button>
        <a class="layui-btn layui-btn-primary form-opt-btn" id="subcancel" data-type="cancel">取消</a>
    </div>
</form>
</body>
<script id="adtjs" type="text/javascript" src="${ctxPath}/services/patients/adt.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&mainid=${id!''}"></script>
</html>