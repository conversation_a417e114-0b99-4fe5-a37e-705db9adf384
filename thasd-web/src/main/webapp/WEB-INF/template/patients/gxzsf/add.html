<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"><meta http-equiv="Expires" content="0"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Cache-control" content="no-cache"><meta http-equiv="Cache" content="no-cache">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/css/rs/add.css?ver=${ctl.randomstr()}">
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}&ver=${ctl.randomstr()}"></script>
    <link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-form-label{
            width:110px;
            padding: 9px 5px 9px 0;
        }
        .layui-field-title .layui-field-box{
            padding: 25px 0 10px 0;
        }
        .layui-form-item .layui-input-inline{margin-right:0;}
        .layui-elem-field legend{font-size:16px;font-weight:bolder;}
        .layui-table-tool{background-color: #f7f8f9}
        .layui-form .layui-form-text .layui-input-block,.layui-form-pane .layui-form-text .layui-input-block{
            width:100%;
        }
        .viewform {
        }

        .viewform input,.viewform select,.viewform textarea {
            pointer-events: none;
        }
    </style>
    <script type="text/javascript">
        var authUser = JSON.parse('${json(authUser)}');
        var zNodesAreaJson = JSON.parse('${json(area.list)}') ;
    </script>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<form class="layui-form formbox ${param.view!'' == '1' ? 'viewform' : ''}" lay-filter="formtable" id="sfForm" name="sfForm">
    <input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
    <input type="hidden" name="guid" id="guid">
    <input type="hidden" name="ryGuid" id="ryguid" value="${ryGuid!''}">
    <div class="form-content-box">
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
            <legend>基本信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" id="name" value="${name!''}" ${lik!'' == '1' ? '' : 'data-type="selectPatients"'}   lay-verify="required|len20" readonly placeholder="请选择患者" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>随访日期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="sfrq" id="sfrq" readonly="readonly"  placeholder="请选择随访日期" autocomplete="off" class="layui-input date-it" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>随访方式</label>
                        <div class="layui-input-inline">
                            <select name="sffs" id="sffs" lay-verify="required">
                                <option value="" >请选择随访方式</option>
                                <option value="1" >门诊</option>
                                <option value="2" >家庭</option>
                                <option value="3" >电话</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
            <legend>症状</legend>
            <div class="layui-field-box" style="padding-top:15px;">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left:32px;">
                        <input type="hidden" name="zhengzhuang" id="zhengzhuang-val" >
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" id="zhengzhuang" name="zhengzhuang-chk" title="无症状"  value="1" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="神疲乏力"  value="2" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="失眠健忘"  value="3" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="胸闷气短"  value="4" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="心慌胸痛"  value="5" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="口角歪斜"  value="6" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="肢体麻木"  value="7" lay-skin="primary">
                        <input type="checkbox" lay-verify="formChk" lay-filter="chkfilter" name="zhengzhuang-chk" title="其他"  value="0" lay-skin="primary">

                        <div class="layui-inline">
                            <div class="layui-input-inline">
                                <input type="text" name="zzqt" id="zzqt" lay-verify=""  placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="" style="height:28px;margin-top: 10px">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
            <legend>体征</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>收缩压(mmHg)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ssy" id="ssy"  lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>舒张压(mmHg)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="szy" id="szy" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>体重(Kg)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="tizhong" id="tizhong" lay-verify="required" min="0" max="300" regex="number1" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>体重目标(Kg)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="tizhongmb" id="tizhongmb" lay-verify="required" min="0" max="300" regex="number1" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>体质指数(kg/㎡)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="bmi" id="bmi" lay-verify="required" min="0" max="300" regex="number1" autocomplete="off" class="layui-input ">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>体质指数目标(kg/㎡)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="bmimb" id="bmimb" lay-verify="required" min="0" max="300" regex="number1" placeholder="" autocomplete="off" class="layui-input ">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>心率(次/分钟)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="xinlv" id="xinlv" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
<!--                    <div class="layui-inline">-->
<!--                        <label class="layui-form-label"><i>*</i>足背动脉搏动*</label>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <select name="zbdmbd" id="zbdmbd" lay-verify="required">-->
<!--                                <option value="" >===请选择===</option>-->
<!--                                <option value="1" >触及正常</option>-->
<!--                                <option value="2" >减弱</option>-->
<!--                                <option value="3" >消失</option>-->
<!--                            </select>-->
<!--                        </div>-->
<!--                        <div class="layui-input-inline">-->
<!--                            <select name="zbdmbdyc" id="zbdmbdyc" disabled lay-verify="required">-->
<!--                                <option value="" >===请选择===</option>-->
<!--                                <option value="1" >双侧</option>-->
<!--                                <option value="2" >左侧</option>-->
<!--                                <option value="3" >右侧</option>-->
<!--                            </select>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">体征其他</label>
                    <div class="layui-input-block">
                        <textarea style="max-width:99%;height:180px" placeholder="" lay-verify="len2000" name="tzqt" id="tzqt" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
            <legend>生活方式</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>日吸烟量(支)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="rxyl" id="rxyl" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>日吸烟量目标(支)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="rxylmb" id="rxylmb" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>日饮酒量(两)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ryjl" id="ryjl" lay-verify="required" min="0" max="300" regex="number1" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>日饮酒量目标(两)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ryjlmb" id="ryjlmb" lay-verify="required" min="0" max="300" regex="number1" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>运动(次/周)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ydcs" id="ydcs" lay-verify="required" min="0" max="300" regex="positive"  placeholder="" autocomplete="off" class="layui-input " >
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>运动(分钟/次)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ydsc" id="ydsc" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input " >
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>运动目标(次/周)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ydcsmb" id="ydcsmb" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>运动目标(分钟/次)</label>
                        <div class="layui-input-inline">
                            <input type="number" name="ydscmb" id="ydscmb" lay-verify="required" min="0" max="300" regex="positive" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>摄盐情况(咸淡)</label>
                        <div class="layui-input-inline">
                            <select name="syqk" id="syqk" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >轻</option>
                                <option value="2" >中</option>
                                <option value="3" >重</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>摄盐情况目标(咸淡)</label>
                        <div class="layui-input-inline">
                            <select name="syqkmb" id="syqkmb" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >轻</option>
                                <option value="2" >中</option>
                                <option value="3" >重</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>心理调整</label>
                        <div class="layui-input-inline">
                            <select name="xltz" id="xltz" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >良好</option>
                                <option value="2" >一般</option>
                                <option value="3" >差</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>遵医行为</label>
                        <div class="layui-input-inline">
                            <select name="zyxw" id="zyxw" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >良好</option>
                                <option value="2" >一般</option>
                                <option value="3" >差</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
            <legend>用药信息</legend>
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>服药依从性</label>
                        <div class="layui-input-inline">
                            <select name="fyycx" id="fyycx" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >规律</option>
                                <option value="2" >间断</option>
                                <option value="3" >不服药</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>药物不良反应</label>
                        <div class="layui-input-inline">
                            <select name="ywblfy" id="ywblfy" lay-filter="ywblfy" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >无</option>
                                <option value="2" >有</option>
                            </select>
                        </div>
                        <div class="layui-input-inline">
                            <input type="text" name="ywblfyy" id="ywblfyy"  placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="" style="">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" style="padding: 5px 10px">
                    <div class="tb-form dqyy">
                        <div class="tb-form-table">
                            <table id="dqyyTable" lay-filter="listtable1"></table>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item" style="padding: 5px 10px">
                    <div class="tb-form tzyy">
                        <div class="tb-form-table">
                            <table id="tzyyTable" lay-filter="listtable2"></table>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;border: none;">
            <div class="layui-field-box">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>此次随访分类</label>
                        <div class="layui-input-inline">
                            <select name="sffl" id="sffl" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >控制满意</option>
                                <option value="2" >控制不满意</option>
                                <option value="3" >不良反应</option>
                                <option value="4" >并发症</option>

                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>下一步管理措施</label>
                        <div class="layui-input-inline">
                            <select name="xybglcs" id="xybglcs" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >常规随访</option>
                                <option value="2" >第1次控制不满意2周随访</option>
                                <option value="3" >两次控制不满意转诊随访</option>
                                <option value="4" >紧急转诊</option>

                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊</label>
                        <div class="layui-input-inline">
                            <select name="sfzz" id="sfzz" lay-filter="sfzz" lay-verify="required">
                                <option value="" >===请选择===</option>
                                <option value="1" >无</option>
                                <option value="2" >有</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊原因</label>
                        <div class="layui-input-inline">
                            <input type="text" name="zzyy" id="zzyy"  placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊机构及科室</label>
                        <div class="layui-input-inline">
                            <input type="text" name="zzjgjks" id="zzjgjks" placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊联系人</label>
                        <div class="layui-input-inline">
                            <input type="text" name="zzlxr" id="zzlxr" placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊联系方式</label>
                        <div class="layui-input-inline">
                            <input type="text" name="zzlxfs" id="zzlxfs" placeholder="" autocomplete="off" class="layui-input layui-disabled" disabled="">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>转诊结果</label>
                        <div class="layui-input-inline">
                            <select name="zzjg" id="zzjg" class="layui-input layui-disabled" disabled="">
                                <option value="" >===请选择===</option>
                                <option value="1" >到位</option>
                                <option value="2" >未到位</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label"><i>*</i>下次随访日期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="xcsfrq" id="xcsfrq" readonly="readonly"  placeholder="请选择下次随访日期" autocomplete="off" class="layui-input date-it" lay-verify="required">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea style="max-width:99%;height:180px" placeholder="" lay-verify="len2000" name="beizhu" id="beizhu" class="layui-textarea"></textarea>

                    </div>
                </div>
            </div>
        </fieldset>
    </div>
    <%if(param.view!'' == ''){%>
    <div class="form-opt-box">
        <button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formsb">确定</button>
        <a class="layui-btn layui-btn-primary form-opt-btn" id="subcancel" data-type="cancel">取消</a>
    </div>
    <%}%>
</form>
</body>
<script type="text/html" id="listtable-number">
    {{d.LAY_INDEX}}
</script>
<script type="text/html" id="listtable-opt">
    <%if(id!''==''){%>
    <a class="layui-btn layui-btn-danger layui-btn-xs " lay-event="delYY" title="删除"><i class="layui-icon layui-icon-delete"></i></a>
    <%}%>
    <%if(id!''!=''){%>
    <a class="layui-btn layui-btn-xs " lay-event="editYY" title="编辑"><i class="layui-icon layui-icon-edit"></i></a>
    <a class="layui-btn layui-btn-danger layui-btn-xs " lay-event="delYY" title="删除"><i class="layui-icon layui-icon-delete"></i></a>
    <%}%>
</script>
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <div class="layui-btn" style="background: none;border: none;color: #0C0C0C;font-size:16px;font-weight:600;text-align: left;padding-left:0px;">当前用药</div>
        <%if(param.view!'' == ''){%>
        <a class="layui-btn layui-btn-normal icon" data-type="add1"><i class="layui-icon layui-icon-add-1"></i>添加</a>
            <%if(id!''!=''){%>
            <a class="layui-btn layui-btn-normal icon" data-type="batchdel1"><i class="layui-icon layui-icon-delete "></i>删除</a>
            <%}%>
        <%}%>
    </div>
</script>
<script type="text/html" id="toolbar2">
    <div class="layui-btn-container">
        <div class="layui-btn" style="background: none;border: none;color: #0C0C0C;font-size:16px;font-weight:600;text-align: left;padding-left:0px;">调整用药</div>
        <%if(param.view!'' == ''){%>
        <a class="layui-btn layui-btn-normal icon" data-type="add2"><i class="layui-icon layui-icon-add-1"></i>添加</a>
            <%if(id!''!=''){%>
            <a class="layui-btn layui-btn-normal icon" data-type="batchdel2"><i class="layui-icon layui-icon-delete "></i>删除</a>
            <%}%>
        <%}%>
    </div>
</script>
<form class="layui-form layui-form-pane" lay-filter="yyBox" style="display:none;padding: 0 5px;" id="yyBox" name="yyBox">
    <input type="hidden" name="sfGuid" id = "sfguid" value="" class="ipt-hidden">
    <input type="hidden" name="guid" id = "yyguid" value="" class="ipt-hidden">
    <input type="hidden" name="istz" id = "istz" value="0" class="ipt-hidden">
    <input type="hidden" name="isvadd" id = "isvadd" value="" class="ipt-hidden">
    <div class="layui-form-item" style="margin-top:10px;">
        <label class="layui-form-label" style="font-size:13px">用药名称</label>
        <div class="layui-input-block" >
            <input type="text" name="ywmc" id="ywmc" lay-verify="required" readonly  autocomplete="off" data-type="selectDrugs" placeholder="请输入用药名称" value="" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="font-size:13px">用法</label>
        <div class="layui-input-block" >
            <select name="ywyf" id="ywyf" lay-verify="required" lay-search="">
                <option value="">请选择用法或输入检索</option>
                <option value="1">口服</option><option value="2" >直肠给药</option><option value="3">舌下用药</option><option value="4">注射用药</option><option value="401">皮下注射</option><option value="402">皮内注射</option><option value="403">肌肉注射</option><option value="404">静脉注射或静脉滴注</option><option value="5">吸人用药</option><option value="6">局部用药</option><option value="601">椎管内用药</option><option value="602">关节腔内用药</option><option value="603">胸膜腔用药</option><option value="604">腹腔用药</option><option value="605">阴道用药</option><option value="606">气管内用药</option><option value="607">滴眼</option><option value="608">滴鼻</option><option value="609">喷喉</option><option value="610">含化</option><option value="611">敷伤口</option><option value="612">擦皮肤</option><option value="699">其他局部用药途径</option><option value="9">其他用药途径</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="font-size:13px">用药频率</label>
        <div class="layui-input-block" >
            <select name="ywpl" id="ywpl" lay-verify="required" lay-search="">
                <option value="">请选择用药频率或输入检索</option>
                <option value="qd">每天一次（qd）</option><option value="bid">每天二次（bid）</option><option value="tid">每天三次（tid）</option><option value="qid">每天四次（qid）</option><option value="qw">每周一次（qw）</option><option value="biw">每周二次（biw）</option><option value="tiw">每周三次（tiw）</option><option value="qh">每小时一次（qh）</option><option value="q2h">每2小时一次（q2h）</option><option value="q4h">每4小时一次（q4h）</option><option value="q5h">每5小时一次（q5h)</option><option value="q6h">每6小时一次（q6h）</option><option value="q8h">每8小时一次（q8h）</option><option value="q12h">每12小时一次（q12h）</option><option value="qn">每晚一次（qn）</option><option value="qod">隔天一次（qod）</option><option value="q5d">五天一次（q5d）</option><option value="q10d">十天一次（q10d）</option><option value="12cx">12小时维持</option><option value="24cx">24小时维持</option><option value="st">立即（st）</option><option value="prn">必要时使用（prn）</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item" >
        <label class="layui-form-label" style="font-size:13px">用量</label>
        <div class="layui-input-block" >
            <input type="number" name="ywyl" id="ywyl" lay-verify="required|number"  autocomplete="off" placeholder="请输入用量" value="" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item" >
        <label class="layui-form-label" style="font-size:13px">单位</label>
        <div class="layui-input-block" >
            <input type="text" name="ywdw" id="ywdw" lay-verify="required"  autocomplete="off" placeholder="请输入单位" value="" class="layui-input">
        </div>
    </div>
</form>
<script id="adtjs" type="text/javascript" src="${ctxPath}/services/patients/gxzsf/adt.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&mainid=${id!''}&lik=${lik!''}&view=${param.view!''}"></script>
</html>