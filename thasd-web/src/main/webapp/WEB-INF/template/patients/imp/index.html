<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/imp.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/html2canvas.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/jspdf.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-field-title .layui-field-box{
            padding: 25px 0 10px 0;
        }
        .layui-form-item .layui-input-inline{margin-right:0;}
        .layui-elem-field legend{font-size:16px;font-weight:bolder;}
        /*595*842 1240×1754  A4纸96dpi下的分辨率是794×1123*/
        .pgbgpage{
            margin: 0 auto;width:814px;height: auto;
            padding: 10px;
        }
        .pgbgpage .layui-card-body{overflow: hidden}
        .bg-border{border:solid 1px #000}
        .title-cus{
            background: linear-gradient(
                    89deg,
                    rgba(12, 173, 128, 1) 20%,
                    rgba(196, 255, 239, 1) 100%
            );
        }
        .bg-grjbxx{width:100%;border-collapse:collapse;}
        .bg-grjbxx td{
            height:32px;line-height: 32px;font-size:15px;font-weight:lighter;color:#333;
        }
        .bg-grjbxx .thd{
            height:32px;line-height: 32px;font-size:14px;text-indent:5px;font-weight:600;color:#000;
        }
        .layui-carousel > [carousel-item] {
            overflow: auto;
        }
        .tipw{color:#888888;font-size:13px}
        .imp-up a.layui-btn{height: 37px;line-height: 37px;border-radius: 0 !important}
        .imp-up {line-height: 38px;margin-top: -1px}
        .layui-input.imp-input{border-top-right-radius: 0;!important;border-bottom-right-radius:0!important;}
        .result {
            text-align: center;

        }
        .result .success svg {
            color: #32C682;
            text-align: center;
            margin-top: 40px;

        }
        .result .error svg {
            color: #f56c6c;
            text-align: center;
            margin-top: 40px;

        }
        .result .title {
            margin-top: 25px;

        }
        .result .desc {
            margin-top: 25px;
            width: 60%;
            margin-left: 20%;
            color: rgba(0, 0, 0, .45);
        }
        .result .content {
            margin-top: 20px;
            width: 80%;
            border-radius: 10px;
            border: solid 2px #a9adaf;
            height: 200px;
            margin-left: 10%;
            text-align: left;
            padding: 15px 10px;
            overflow: auto;
        }
        .result .action {
            padding-top: 10px;
            /*border-top: 1px whitesmoke solid;*/
            margin-top: 25px;
        }
        .muban{
            position: fixed;
            top: 27px;
            right: 29px;
            z-index: 9999999999999999999;
        }
        .result .content p{display: inline-block;width: auto;padding: 2px 3px}
        .result .content p:nth-of-type(even){
            background-color: #dee3df;
        }

    </style>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="fulllistbox">
    <%if(breadcrumb){%>
    <div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>患者管理</cite></a>
			  <a><cite>导入体检患者</cite></a>
			</span>
        <div class="title-name"></div>
    </div>
    <%}%>
    <div class="listbox">
        <div class="muban">
            <a class="layui-btn layui-btn-normal" href="${ctxPath}/services/体检患者模板.xlsx" style="height: 70px;line-height: 24px;background-color: #607D8B;padding: 11px 8px 0 8px; ">
                <i class="layui-icon layui-icon-download-circle" style="font-size: 25px;"></i></br>模板下载
            </a>
        </div>
        <div class="listbox-body">
            <div class="layui-card-body body-tree-table">
                <div class="layui-carousel" id="stepForm" lay-filter="stepForm" style="margin: 0 auto;">
                    <div carousel-item>
                        <!-- #################################### STEP 1 #################################### -->
                        <div>
                            <div class="form-content-box">
                                <form class="layui-form" lay-filter="patientForm" action="javascript:void(0);" style="margin: 0 auto;max-width: 520px;padding-top: 40px;">
                                    <input type="hidden" name="filepath" id="filepath">
                                    <div class="layui-form-item" style="margin-top: 30px;">
                                        <label class="layui-form-label"><i>*</i>附件名称</label>
                                        <div class="layui-input-inline" style="width: 340px">
                                            <input type="text" readonly name="attachmentname" id="attachmentname" lay-verify="required" autocomplete="off" placeholder="请选择文件" class="layui-input imp-input">
                                        </div>
                                        <div class="layui-form-mid layui-word-aux imp-up" style="padding:0 !important;">
                                            <a class="layui-btn  layui-btn-normal"  id="upload"><i class="layui-icon layui-icon-upload-drag"></i></a>
                                        </div>
                                    </div>
                                    <div class="layui-form-item" style="margin-bottom:0;width:97%;display:none;" id = "up-progress">
                                        <label class="layui-form-label"></label>
                                        <div class="layui-input-block">
                                            <div class="layui-progress"  lay-showpercent="true" lay-filter="up-progress" >
                                                <div class="layui-progress-bar layui-bg-engreen" lay-percent="0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item" style="margin-top: 48px;">
                                        <div class="layui-input-block">
                                            <button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formStep">下一步</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <!-- #################################### STEP 2 #################################### -->
                        <div>
                        </div>
                        <!-- #################################### STEP 3 #################################### -->
                        <div>
                            <form class="layui-form formbox pgbg" id="readForm" lay-filter="readForm"  action="javascript:void(0);" style="margin: 0 auto;width:100%;height: 100%;overflow: auto;">
                                <div class="layui-card" style="flex: 1;-ms-flex: 1; padding: 24px 24px 0 24px;overflow: auto;height: 0;">
                                    <div class="layui-card-body" style="overflow:hidden;height: 100%;" id="result-card-box">
                                    </div>
                                </div>
                                <!-- <div class="form-opt-box-center"></div>-->
                            </form>
                        </div>
                        <!-- #################################### END #################################### -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/html" id="result-card">
    <div class="result">
        {{#  if(d.code==200){ }}
        <div class="success">
            <svg viewBox="64 64 896 896" data-icon="check-circle" width="80px" height="80px" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0 0 51.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"></path><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path></svg>
        </div>
        <h2 class="title">操作成功</h2>
        {{#  }; }}
        {{#  if(d.code==500){ }}
        <div class="error">
            <svg viewBox="64 64 896 896" data-icon="close-circle" width="80px" height="80px" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M685.4 354.8c0-4.4-3.6-8-8-8l-66 .3L512 465.6l-99.3-118.4-66.1-.3c-4.4 0-8 3.5-8 8 0 1.9.7 3.7 1.9 5.2l130.1 155L340.5 670a8.32 8.32 0 0 0-1.9 5.2c0 4.4 3.6 8 8 8l66.1-.3L512 564.4l99.3 118.4 66 .3c4.4 0 8-3.5 8-8 0-1.9-.7-3.7-1.9-5.2L553.5 515l130.1-155c1.2-1.4 1.8-3.3 1.8-5.2z"></path><path d="M512 65C264.6 65 64 265.6 64 513s200.6 448 448 448 448-200.6 448-448S759.4 65 512 65zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path></svg>
        </div>
        <h2 class="title">操作失败</h2>
        {{#  }; }}
        <p class="desc">
            {{d.msg}}
        </p>
        {{#  if(d.noList && d.noList.length > 0){ }}
        <div class="content">
            <div>无档案患者</div>
            {{#  layui.each(d.noList, function(index, item){ }}
            <p>{{item[0]}}，{{item[1]}}，{{item[2]}}</p>
            {{#  }); }}
        </div>
        {{#  }; }}
        <div class="action">
            {{#  if(d.code==200){ }}
            <button class="layui-btn layui-btn-normal form-opt-btn" data-type="replay">再次上传</button>
            {{#  }; }}
            {{#  if(d.code==500){ }}
            <button class="layui-btn layui-btn-normal form-opt-btn" data-type="pre">返回重试</button>
            {{#  }; }}
        </div>
    </div>
</script>
<script id="detevaljs" type="text/javascript" src="${ctxPath}/services/patients/imp/index.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>