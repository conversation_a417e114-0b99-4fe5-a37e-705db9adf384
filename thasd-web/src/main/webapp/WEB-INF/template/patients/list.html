<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript" src="${ctxPath}/services/idcard/shensi.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	.layui-form .layui-form-text .layui-input-block,.layui-form-pane .layui-form-text .layui-input-block{
		width:100%;
	}
	.layui-btn .layui-icon{
		margin: 0;
	}
</style>
<script type="text/javascript">
	var zNodesJson = JSON.parse('${json(org.list)}');
	var ALIBABAKEY = '${authUserJson}';
	var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body>
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<div class="fulllistbox">
		<%if(breadcrumb){%>
		<div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>患者管理</cite></a>
			  <a><cite>患者数据库</cite></a>
			</span>
			<div class="title-name"></div>
		</div>
		<%}%>
		<div class="listbox">
			<div class="listbox-body">
			    <div class="layui-card search-box">
			    	<div class="layui-form layui-card-header layuiadmin-card-header-auto">
				        <div class="layui-form-item">
						  <div class="layui-inline">
								<label class="layui-form-label">患者姓名</label>
								<div class="layui-input-inline">
								  <input type="text" name="name" id="name" placeholder="请输入" autocomplete="off" class="layui-input">
								</div>
						  </div>
							<div class="layui-inline">
								<label class="layui-form-label">身份证号</label>
								<div class="layui-input-inline" style="margin-right: 0;width: 147px;">
									<input type="text" name="idcard" id="idcard" placeholder="请输入" autocomplete="off" class="layui-input">
								</div>
								<div class="layui-form-mid layui-word-aux" style="margin:0;padding: 0 0 0 0!important;"><a class="layui-btn layui-btn-xs user-search" style="height: 23px;line-height: 23px;margin-top: 2px;" data-method="readcard"><i class="layui-icon layui-icon-friends"></i></a></div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">所属单位</label>
								<div class="layui-input-inline">
									<div id="scbm" rid = "orgid"  rname="orgname" class="layui-form-select select-tree"></div>
								</div>
							</div>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width: 157px;">
									<input type="checkbox" name="islower" id="islower" title="是否包含下级单位" lay-skin="primary" value="1" checked autocomplete="off" class="layui-input">
								</div>
							</div>
				            <a class="layui-btn layui-btn-normal user-search" data-method="reload">
				              <i class="layui-icon layui-icon-search "></i>
				            </a>
				        </div>
			      </div>
				</div>
				<div class="layui-card opt-box" style="box-shadow:none;">
					<div class="opt">
					  <%if(authUser.grade == "3" || authUser.grade == "2"  || authUser.isshowbus == 1 ) {%>
						  <button class="layui-btn layui-btn-normal icon" id="batchdel" data-type="batchdel"><i class="layui-icon layui-icon-delete "></i>删除</button>
						  <button class="layui-btn layui-btn-normal icon" id="add" data-type="add"><i class="layui-icon layui-icon-add-1"></i>添加</button>
						  <button class="layui-btn layui-btn-normal icon" id="lost" data-type="lost"><i class="layui-icon layui-icon-add-circle"></i>失访登记</button>
						  <button class="layui-btn layui-btn-normal icon" id="addManage" data-type="addManage"><i class="layui-icon layui-icon-spread-left"></i>纳入管理</button>
					  <%}%>
			        </div>
				</div>
				<div class="layui-card-body adaptive-table">
			       <table id="listtable" lay-filter="listtable"></table>
			        <script type="text/html" id="listtable-opt">
						<a class="layui-btn layui-btn-danger layui-btn-xs" href="${ctxPath}/v/pcenter?patientid={{d.id}}&referralid={{d.referralid}}" target="_blank" title="个人中心"><i style="margin-right:0" class="layui-icon layui-icon-username"></i></a>
						{{# if((d.orgid == authUser.orgid || d.orgidl2 == authUser.orgid )){ }}
						<a class="layui-btn layui-btn-xs " lay-event="edit" title="编辑"><i class="layui-icon layui-icon-edit"></i></a>
						<a class="layui-btn layui-btn-danger layui-btn-xs " lay-event="del" title="删除"><i class="layui-icon layui-icon-delete"></i></a>
						{{# } }}
						{{# if(d.pmtypecode != "0" && (d.orgid == authUser.orgid || d.orgidl2 == authUser.orgid )){ }}
						<a class="layui-btn layui-btn-warm layui-btn-xs" title="纳入管理" lay-event="addManage"><i class="layui-icon layui-icon-spread-left"></i></a>
						{{# } }}
						{{# if(authUser.grade == "2" && d.orgidl2 != authUser.orgid){ }}
						<a class="layui-btn layui-btn-cus3 layui-btn-xs" title="申请迁入" lay-event="applyMove"><i class="layui-icon layui-icon-survey"></i></a>
						{{# } }}
						{{# if(authUser.grade == "3" && d.orgid != authUser.orgid){ }}
						<a class="layui-btn layui-btn-cus3 layui-btn-xs" title="申请迁入" lay-event="applyMove"><i class="layui-icon layui-icon-survey"></i></a>
						{{# } }}
						<!--第一人民医院
						{{# if(d.pmtypecode != "0" && authUser.grade == "1" && d.orgid != authUser.orgid){ }}
						<a class="layui-btn layui-btn-cus3 layui-btn-xs" title="申请迁入" lay-event="applyMove"><i class="layui-icon layui-icon-survey"></i></a>
						{{# } }}
						-->
					</script>
		      </div>
			</div>
		</div>
	</div>
</body>
<form class="layui-form layui-form-pane" lay-filter="formtable" style="display: none;padding:20px 30px;" id="sfdjBox">
	<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}">
	<input type="hidden" name="ids" id = "sf-ids" value="">
	<input type="hidden" name="pmtype" id = "sf-pmtype" value="失访">
	<input type="hidden" name="pmtypecode" id = "sf-pmtypecode" value="1">
	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label" style="font-size:13px">姓名</label>
		<div class="layui-input-block" >
			<div class="layui-form-mid layui-word-aux" style="padding:5px 10px 5px 10px!important;font-size:13px;" id="sf-names"></div>
		</div>
	</div>
	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label" style="font-size:13px">失访原因</label>
		<div class="layui-input-block">
			<textarea name="sfremark" id="sfremark" placeholder="请输入失访原因" class="layui-textarea"></textarea>
		</div>
	</div>
</form>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/patients/list.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&gwServerUrl=${gwServerUrl}&jyServerUrl=${jyServerUrl}"></script>
</html>