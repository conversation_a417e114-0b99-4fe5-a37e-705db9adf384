<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	.listbox{
		-ms-flex: 1;
		flex: 1;
		overflow-x: hidden;
		overflow-x: hidden;
		background: #f0f2f5;
		padding:15px;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.listbox .listbox-body{
		background:#ffffff;
		-ms-flex: 1;
		flex: 1;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
		height: 0;
	}

	.layui-card-body{
		-ms-flex:1;
		flex: 1;
		padding: 0;
		overflow:auto;
		height: 0;
	}
	.layui-card-body.body-tree-table{
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-card-body.body-tree-table .ew-tree-table{
		-ms-flex:1;
		flex: 1;
		height: 0;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-tool{
		flex-shrink: 0;
	}
	.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-head{
		flex-shrink: 0;
	}
	.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-box{
		-ms-flex:1;
		flex: 1;
		height: 0;
	}
	.layui-tab{
		margin: 0;
		-ms-flex:1;
		flex: 1;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-tab-title{
		padding: 5px 0 0 0;
		box-shadow: 0 1px 4px rgb(207 207 207 / 70%);
		flex-shrink: 2;
		flex-basis: 40px;
	}
	.layui-tab-content{
		-ms-flex: 1;
		flex: 1;
		height: 0;
		flex-shrink: 0;
		flex-basis: 40px;

		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-tab-content .layui-tab-item.layui-show{
		-ms-flex: 1;
		flex: 1;
		display: -ms-flexbox!important;
		display: flex!important;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-tab-title .layui-this{
		background: #13a387!important;
		color: #FFFFFF!important;
		border-radius: 6px 6px 0 0!important;
	}

	.layui-tab-title li:first-child{margin-left: 15px;}

	.layui-tab-title .layui-this:after{
		border-color: #13a387!important;
		border-radius: 6px 6px 0 0!important;
	}

	.layui-col-space10{margin: 0!important;}
	.layui-col-space10>*{padding: 0!important;}

	.layui-form-pane .layui-input-block {
		float: none;
		margin-left: 110px;
		min-height: 36px;
	}
	.layui-form-pane .layui-input-block  .layui-input{
		height: 38px;
		line-height:38px;
	}
	.layui-form-label{width:76px;}

	.c-dsp{color: #17baec !important;}
	.c-ysp{color: #13a387!important;}
	.c-cx{color: #96c5f1 !important;}

	.c-yes{color: #13a387 !important;}
	.c-no{color: #de6139 !important;}
	.ultoolbar{
		position: absolute;
		width: 100%;
		height: 45px;
		justify-content: end;
		align-items: center;
		display: inline-flex;
	}
	.ultoolbar button{ margin-right: 14px; position: absolute;z-index: 9999999999999;}
	.ultoolbar button#exportMe{display: none}
</style>
<script type="text/javascript">
var zNodesJson = JSON.parse('${json(org.list)}');
var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="fulllistbox">
	<div class="listbox">
		<div class="listbox-body">
			<div class="layui-card-body body-tree-table">
				<!-- TAB-START-->
				<div class="layui-tab qrsqTab" lay-filter="qrsqTab">
					<div class="ultoolbar"><button class="layui-btn layui-btn-normal icon" id="exportMe" data-type="exportMe"><i class="layui-icon layui-icon-download-circle"></i>导出</button></div>
					<ul class="layui-tab-title" id="planTab-title">
						<%if(authUser.grade == "1" && authUser.orgcode == "000001") {%>
						<li  load-stetup="0" tid="sq" class="layui-this sqTable">我的申请</li>
						<%} else {%>
						<li  load-stetup="0" tid="wsp" applystatue="0" class="layui-this wspTable">待审批</li>
						<li  load-stetup="0" tid="ysp" applystatue="1" class="yspTable">已审批</li>
						<li  load-stetup="0" tid="sq" class="sqTable">我的申请</li>
						<%}%>
					</ul>
					<div class="layui-tab-content qrsqTabContent">
						<%if(authUser.grade == "1" && authUser.orgcode == "000001") {%>
						<div class="layui-tab-item layui-show">
							<div id="sqTable" lay-filter="listtable3"></div>
						</div>
						<%} else {%>
							<div class="layui-tab-item layui-show">
								<div id="wspTable" lay-filter="listtable"></div>
							</div>
							<div class="layui-tab-item">
								<div id="yspTable" lay-filter="listtable2"></div>
							</div>
							<div class="layui-tab-item">
								<div id="sqTable" lay-filter="listtable3"></div>
							</div>
						<%}%>
					</div>
				</div>
				<!-- TAB-END -->
			</div>
		</div>
	</div>
</div>
</body>
<script type="text/html" id="sp-opt">
	{{# if(authUser.category == "2" || authUser.category == "1"){ }}
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="sp">审批</a>
	{{# } }}
</script>
<script type="text/html" id="cx-opt">
	{{# if(d.applystatue == "0"){ }}
		<a class="layui-btn layui-btn-cus3 layui-btn-xs" lay-event="cx">撤销申请</a>
	{{# } }}
</script>
<form class="layui-form layui-form-pane" lay-filter="spForm" style="display:none;padding:10px;" id="spForm" name="spForm">
	<input type="hidden" name="id" id = "id"  class="ipt-hidden">
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">审批结果</label>
		<div class="layui-input-block" >
			<input type="radio" name="applyresult" value="1" title="同意" lay-verify="formRadio">
			<input type="radio" name="applyresult" value="2" title="不同意" lay-verify="formRadio">
		</div>
	</div>
	<div class="layui-form-item layui-form-text" style="margin-top: 1px">
		<div class="layui-input-block">
			<textarea name="applyremark" id="applyremark" lay-verify="len4000" placeholder="请输入审批意见或备注" class="layui-textarea" style="height:220px"></textarea>
		</div>
	</div>
</form>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/patients/qrsqlist.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>