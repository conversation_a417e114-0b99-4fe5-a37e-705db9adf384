<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript" src="${ctxPath}/services/idcard/shensi.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	.icon-svg {
		width: 1.6em;
		height: 1.6em;
		vertical-align: -0.45em;
		fill: currentColor;
		overflow: hidden;
	}
	.layui-form-pane .layui-input-block {
		float: none;
		margin-left: 110px;
		min-height: 36px;
	}
	.layui-form-pane .layui-input-block  .layui-input{
		height: 38px;
		line-height:38px;
	}
	.layui-btn+.layui-btn{
		margin-left:5px;
	}
	.layui-btn-xs{padding: 0 3px;}
	.layui-form-label{width:82px;}
	.layui-field-title .layui-field-box{
		padding: 25px 0 10px 0;
	}
	.layui-elem-field legend{font-size:16px;font-weight:bolder;}
	.layui-table-tool{background-color: #f7f8f9}
	.lotpdv-box .layui-table-view{margin-top: 0;}

</style>
<script type="text/javascript">
var zNodesJson = JSON.parse('${json(org.list)}');
var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body>
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<div class="fulllistbox">
		<%if(breadcrumb){%>
		<div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>患者管理</cite></a>
			  <a><cite>已管理患者</cite></a>
			</span>
			<div class="title-name"></div>
		</div>
		<%}%>
		<div class="listbox">
			<div class="listbox-body">
			    <div class="layui-card search-box">
			    	<div class="layui-form layui-card-header layuiadmin-card-header-auto">
				        <div class="layui-form-item search-form-container">
				        	<div class="search-fields">
					          <div class="layui-inline">
						            <label class="layui-form-label">患者姓名</label>
						            <div class="layui-input-inline">
						              <input type="text" name="name" id="name" placeholder="请输入" autocomplete="off" class="layui-input">
						            </div>
						      </div>
								<div class="layui-inline">
									<label class="layui-form-label">身份证号</label>
									<div class="layui-input-inline" style="margin-right: 0;width: 147px;">
										<input type="text" name="idcard" id="idcard" placeholder="请输入" autocomplete="off" class="layui-input">
									</div>
									<div class="layui-form-mid layui-word-aux" style="margin:0;padding: 0 0 0 0!important;"><a class="layui-btn layui-btn-xs user-search" style="height: 23px;line-height: 23px;margin-top: 2px;" data-method="readcard"><i class="layui-icon layui-icon-friends"></i></a></div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">所属单位</label>
									<div class="layui-input-inline">
										<div id="scbm" rid = "orgid"  rname="orgname" class="layui-form-select select-tree"></div>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">高血压分层</label>
									<div class="layui-input-inline">
										<select name="bpgradecode" id="bpgradecode"  lay-verify="" lay-search="">
											<option value="" datacode = "">请选择高血压分层或输入检索</option>
											<option value="0">正常</option>
											<option value="6">易患</option>
											<option value="1">低危</option>
											<option value="2">中危</option>
											<option value="3">高危</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">糖尿病分层</label>
									<div class="layui-input-inline">
										<select name="dbgradecode" id="dbgradecode"  lay-verify="" lay-search="">
											<option value="" datacode = "">请选择糖尿病分层或输入检索</option>
											<option value="0">正常</option>
											<option value="6">易患</option>
											<option value="1">低危</option>
											<option value="2">中危</option>
											<option value="3">高危</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">高血脂分层</label>
									<div class="layui-input-inline">
										<select name="lpgradecode" id="lpgradecode"  lay-verify="" lay-search="">
											<option value="" datacode = "">请选择高血脂分层或输入检索</option>
											<option value="0">正常</option>
											<option value="6">易患</option>
											<option value="1">低危</option>
											<option value="2">中危</option>
											<option value="3">高危</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">ASCVD分层</label>
									<div class="layui-input-inline">
										<select name="lpgradecode" id="adgradecode"  lay-verify="" lay-search="">
											<option value="" datacode = "">请选择ASCVD分层或输入检索</option>
											<option value="0">正常</option>
											<option value="1">低危</option>
											<option value="2">中危</option>
											<option value="3">高危</option>
										</select>
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">所属医师</label>
									<div class="layui-input-inline">
										<input type="text" name="jyusername" id="jyusername" placeholder="请输入" autocomplete="off" class="layui-input">
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">医师账号</label>
									<div class="layui-input-inline">
										<input type="text" name="jyuser" id="jyuser" placeholder="请输入所属医师账号" autocomplete="off" class="layui-input">
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">当前管理医师</label>
									<div class="layui-input-inline">
										<input type="text" name="gljyusername" id="gljyusername" placeholder="请输入" autocomplete="off" class="layui-input">
									</div>
								</div>
								<div class="layui-inline">
									<label class="layui-form-label">医师账号</label>
									<div class="layui-input-inline">
										<input type="text" name="gljyuser" id="gljyuser" placeholder="请输入当前管理医师账号" autocomplete="off" class="layui-input">
									</div>
								</div>
								<%if(authUser.grade=="1"){%>
								<div class="layui-inline">
									<label class="layui-form-label" style="width:82px;">当前所属单位</label>
									<div class="layui-input-inline">
										<div id="glorgid" rid = "orgid"  rname="orgname" class="layui-form-select select-tree"></div>
									</div>
								</div>
								<%}%>
								<div class="layui-inline">
									<div class="layui-input-inline" style="width: 157px;">
										<input type="checkbox" name="islower" id="islower" title="是否包含下级单位" lay-skin="primary" value="1" checked autocomplete="off" class="layui-input">
									</div>
								</div>
							</div>
							<div class="search-button-container">
					            <a class="layui-btn layui-btn-normal user-search" data-method="reload">
					              <i class="layui-icon layui-icon-search "></i>
					            </a>
				            </div>
				        </div>
			      </div>
				</div>
				<div class="layui-card opt-box opt-box-flex" style="box-shadow:none;">
					<div class="opt">
<!--						<%if(authUser.grade == "1" && authUser.orgcode == "000001") {%>-->
<!--						<button class="layui-btn layui-btn-normal icon" id="removePmtype" data-type="removePmtype"><i class="layui-icon layui-icon-subtraction"></i>移除管理</button>-->
<!--						<%}%>-->
						<%if(authUser.grade == "3" || authUser.grade == "2"  || authUser.isshowbus == 1 ) {%>
							<button class="layui-btn layui-btn-normal icon" id="lost" data-type="lost"><i class="layui-icon layui-icon-add-circle"></i>失访登记</button>
							<button class="layui-btn layui-btn-normal icon" id="addDeteval" data-type="addDeteval"><i class="layui-icon layui-icon-form"></i>评估</button>
							<button class="layui-btn layui-btn-normal icon" id="removePmtype" data-type="removePmtype"><i class="layui-icon layui-icon-subtraction"></i>移除管理</button>
<!--							<button class="layui-btn layui-btn-primary more-btn-sf">-->
<!--								随&nbsp;&nbsp;访-->
<!--								<i class="layui-icon layui-icon-down layui-font-12"></i>-->
<!--							</button>-->
<!--							<button class="layui-btn layui-btn-normal icon" id="qy" data-type="qy"><i class="layui-icon layui-icon-add-circle"></i>签约</button>-->
							<%if(authUser.grade == "3" ) {%>
							<button class="layui-btn layui-btn-normal icon" id="upReferral" data-type="upReferral"><svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-shuangxiangzhuanzhen"></use></svg>向上协诊</button>
							<%}%>
							<%if(authUser.grade == "2") {%>
							<button class="layui-btn layui-btn-normal icon" id="upReferral2" data-type="upReferral2"><svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-shuangxiangzhuanzhen"></use></svg>向上协诊</button>
							<%}%>
							<button class="layui-btn layui-btn-primary more-btn">
								更&nbsp;&nbsp;多
								<i class="layui-icon layui-icon-down layui-font-12"></i>
							</button>
						<%}%>
			        </div>
					<div class="extBox">
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#FF2222"></path></svg>高危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#FF8D02"></path></svg>中危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#F5DD0C"></path></svg>低危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#07B0FF"></path></svg>易患</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#2CBD32"></path></svg>正常</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#EFEFEF"></path></svg>未知</i>
					</div>
				</div>
				<div class="layui-card-body adaptive-table">
			       <table id="listtable" lay-filter="listtable"></table>
			        <script type="text/html" id="listtable-opt">
						<%if(authUser.grade == "3" || authUser.grade == "2"  || authUser.isshowbus == 1 ) {%><a class="layui-btn layui-btn-xs" lay-event="edit"><i style="margin-right:0" class="layui-icon layui-icon-edit"></i></a><%}%>
						<a class="layui-btn layui-btn-danger layui-btn-xs" href="${ctxPath}/v/pcenter?patientid={{d.id}}&referralid={{d.referralid}}" target="_blank" title="个人中心"><i style="margin-right:0" class="layui-icon layui-icon-username"></i></a>
						{{# if(d.planid && d.planid != ""){ }}
						<a class="layui-btn layui-btn-cus2 layui-btn-xs" lay-event="viewPlan">管理方案</a>
						{{# } else if(d.evalid && d.evalid !="" ){ }}
						<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="genPlan">创建方案</a>
						{{# } else {  }}
						<a class="layui-btn layui-btn-xs" lay-event="addDeteval" title="评估"><i style="margin-right:0" class="layui-icon layui-icon-form"></i></a>
						{{# } }}
        			</script>
		      </div>
			</div>
		</div>
	</div>
</body>
<form class="layui-form layui-form-pane" lay-filter="changeBbfzInfo" style="display: none;padding:20px 30px;" id="changeBbfzInfo">
	<fieldset class="layui-elem-field layui-field-title" style="margin-top:10px;">
		<legend>伴并发症</legend>
		<div class="layui-field-box">
			<div class="layui-form-item">
				<div class="layui-inline" style="margin-right: 0">
					<div class="layui-input-block " style="margin-left:0;">
						<input type="hidden" name="bfz" id="bfz-val" >
						<input type="hidden" name="bfzname" id="bfz-name" >
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level=""  title="无" value="0" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="冠心病"  value="1" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="脑卒中" value="2" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="肾病综合征" value="3" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="眼底病变" value="4" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="周围神经病变" value="5" lay-skin="primary" lay-filter="chk-special-filter">
						<input type="checkbox" name="bfz-chk" lay-verify="formChk" level="" title="周围血管病变" value="6" lay-skin="primary" lay-filter="chk-special-filter">
					</div>
				</div>
			</div>
		</div>
	</fieldset>
</form>
<div id="lotpdv-box" class="lotpdv-box" style="display: none;">
</div>
<form class="layui-form" lay-filter="formtablePlan" style="display: none;">
	<input type="hidden" name="ALIBABAKEY"  value="${authUserJson}">
</form>
<!--<form class="layui-form layui-form-pane" lay-filter="formtable" style="display: none;padding:20px 30px;" id="removeTypeBox">-->
<!--	<input type="hidden" name="ALIBABAKEY"  value="${authUserJson}">-->
<!--</form>-->
<form class="layui-form layui-form-pane" lay-filter="formtable" style="display: none;padding:20px 30px;" id="sfdjBox">
	<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}">
	<input type="hidden" name="ids" id = "sf-ids" value="">
	<input type="hidden" name="pmtype" id = "sf-pmtype" value="失访">
	<input type="hidden" name="pmtypecode" id = "sf-pmtypecode" value="1">
	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label" style="font-size:13px">姓名</label>
		<div class="layui-input-block" >
			<div class="layui-form-mid layui-word-aux" style="padding:5px 10px 5px 10px!important;font-size:13px;" id="sf-names"></div>
		</div>
	</div>
	<div class="layui-form-item layui-form-text">
		<label class="layui-form-label" style="font-size:13px">失访原因</label>
		<div class="layui-input-block">
			<textarea name="sfremark" id="sfremark" placeholder="请输入失访原因" class="layui-textarea"></textarea>
		</div>
	</div>
</form>
<form class="layui-form layui-form-pane" lay-filter="upReferralForm" style="display:none;padding:10px;" id="upReferralForm" name="upReferralForm">
	<input type="hidden" name="ALIBABAKEY" value="${authUserJson}" class="ipt-hidden">
	<input type="hidden" name="zcorgid" id = "zcorgid" value="${authUser.orgid}" class="ipt-hidden">
	<input type="hidden" name="zcysid" id = "zcysid" value="${authUser.id}" class="ipt-hidden">
	<input type="hidden" name="zrorgid" id = "zrorgid" value="${authUser.porgid}" class="ipt-hidden">
	<input type="hidden" name="zrorgname" id = "zrorgname" value="${authUser.porgname}" class="ipt-hidden">
	<input type="hidden" name="id" id = "id" class="ipt-hidden">
	<input type="hidden" name="type" id = "type" value="1" class="ipt-hidden">
	<input type="hidden" name="patientid" id = "patientid" class="ipt-hidden">

	<input type="hidden" name="ojyuserid" id = "ojyuserid" class="ipt-hidden">
	<input type="hidden" name="ojyusername" id = "ojyusername" class="ipt-hidden">
	<input type="hidden" name="ojyuser" id = "ojyuser" class="ipt-hidden">


	<!--	<input type="hidden" name="evalid" id = "evalid" class="ipt-hidden">-->
<!--	<input type="hidden" name="bpgradecode" id = "bpgradecode" class="ipt-hidden">-->
<!--	<input type="hidden" name="dbgradecode" id = "dbgradecode" class="ipt-hidden">-->
<!--	<input type="hidden" name="lpgradecode" id = "lpgradecode" class="ipt-hidden">-->
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">患者姓名</label>
		<div class="layui-input-block" >
			<input type="text"  id="patientname" lay-verify="required" readonly autocomplete="off"  class="layui-input">
		</div>
	</div>
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">转出单位</label>
		<div class="layui-input-block" >
			<input type="text" name="zcorgname" id="zcorgname" lay-verify="required" readonly autocomplete="off" value="${authUser.orgname}" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">转出医生</label>
		<div class="layui-input-block" >
			<input type="text" name="zcys" id="zcys" lay-verify="required" readonly autocomplete="off" value="${authUser.name}" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">转出时间</label>
		<div class="layui-input-block" >
			<input type="text" name="zcsj" id="zcsj" lay-verify="required" readonly autocomplete="off" value="" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item layui-form-text" style="margin-top: 1px">
		<div class="layui-input-block">
			<textarea name="xzyy" id="xzyy" placeholder="请输入协诊原因" class="layui-textarea" style="height:220px"></textarea>
		</div>
	</div>
</form>
<form class="layui-form layui-form-pane" lay-filter="bindDeviceForm" style="display:none;padding:10px;" id="bindDeviceForm" name="bindDeviceForm">
	<input type="hidden" name="ALIBABAKEY" value="${authUserJson}" class="ipt-hidden">
	<input type="hidden" name="glorgid" id = "lotglorgid" value="${authUser.orgid}" class="ipt-hidden">
	<input type="hidden" name="isvalid" id = "lotisvalid" value="0" class="ipt-hidden">
	<input type="hidden" name="idcard" id = "lotidcard" class="ipt-hidden">
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">患者姓名</label>
		<div class="layui-input-block" >
			<input type="text"  id="lotname" name="name" lay-verify="required" readonly autocomplete="off"  class="layui-input">
		</div>
	</div>
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">当前管理单位</label>
		<div class="layui-input-block" >
			<input type="text" name="glorgname" id="lotglorgname" lay-verify="required" readonly autocomplete="off" value="${authUser.orgname}" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item" >
		<label class="layui-form-label" style="font-size:13px">设备编码</label>
		<div class="layui-input-block" >
			<input type="text" name="serialnumber" id="serialnumber" lay-verify="required"  autocomplete="off"  class="layui-input">
		</div>
	</div>
</form>

<script id="listjs" type="text/javascript" src="${ctxPath}/services/patients/list.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&model=ygl&pmtypecode=0&gwServerUrl=${gwServerUrl}&jyServerUrl=${jyServerUrl}"></script>
<script type="text/html" id="bpgrade">
	{{#
	let color = "#EFEFEF";
	color = d.bpgradecode == '0'?'#2CBD32':color;
	color = d.bpgradecode == '6'?'#07B0FF':color;
	color = d.bpgradecode == '1'?'#F5DD0C':color;
	color = d.bpgradecode == '2'?'#FF8D02':color;
	color = d.bpgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>
<script type="text/html" id="dpgrade">
	{{#
	let color = "#EFEFEF";
	color = d.dbgradecode == '0'?'#2CBD32':color;
	color = d.dbgradecode == '6'?'#07B0FF':color;
	color = d.dbgradecode == '1'?'#F5DD0C':color;
	color = d.dbgradecode == '2'?'#FF8D02':color;
	color = d.dbgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>
<script type="text/html" id="lpgrade">
	{{#
	let color = "#EFEFEF";
	color = d.lpgradecode == '0' ? '#2CBD32' : color;
	color = d.lpgradecode == '6' ? '#07B0FF' : color;
	color = d.lpgradecode == '1' ? '#F5DD0C' : color;
	color = d.lpgradecode == '2' ? '#FF8D02' : color;
	color = d.lpgradecode == '3' ? '#FF2222' : color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>

<script type="text/html" id="ascvd">
	{{#
	let color = "#EFEFEF";
	color = d.adgradecode == '0'?'#2CBD32':color;
	color = d.adgradecode == '1'?'#F5DD0C':color;
	color = d.adgradecode == '2'?'#FF8D02':color;
	color = d.adgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>
<script>
	!function(t){var e,n,o,c,i,a='<svg><symbol id="icon-shuangxiangzhuanzhen" viewBox="0 0 1024 1024"><path d="M451.395918 715.755102c-11.493878 0-20.897959-9.404082-20.897959-20.897959V405.942857c0-11.493878 9.404082-20.897959 20.897959-20.897959s20.897959 9.404082 20.89796 20.897959V694.857143c0 11.493878-9.404082 20.897959-20.89796 20.897959z"  ></path><path d="M348.995918 529.240816c-5.22449 0-10.44898-2.089796-14.628571-6.269387-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.404082 6.269388-15.151021 6.269387z"  ></path><path d="M572.604082 638.955102c-11.493878 0-20.897959-9.404082-20.89796-20.897959V329.142857c0-11.493878 9.404082-20.897959 20.89796-20.897959s20.897959 9.404082 20.897959 20.897959v288.914286c0 11.493878-9.404082 20.897959-20.897959 20.897959z"  ></path><path d="M572.604082 638.955102c-5.22449 0-10.44898-2.089796-14.628572-6.269388-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.926531 6.269388-15.15102 6.269388z"  ></path><path d="M512 929.959184c-230.4 0-417.959184-187.559184-417.959184-417.959184s187.559184-417.959184 417.959184-417.959184 417.959184 187.559184 417.959184 417.959184-187.559184 417.959184-417.959184 417.959184z m0-794.122449c-207.412245 0-376.163265 168.75102-376.163265 376.163265s168.75102 376.163265 376.163265 376.163265 376.163265-168.75102 376.163265-376.163265-168.75102-376.163265-376.163265-376.163265z"  ></path></symbol></svg>',d=(d=document.getElementsByTagName("script"))[d.length-1].getAttribute("data-injectcss"),s=function(t,e){e.parentNode.insertBefore(t,e)};if(d&&!t.__iconfont__svg__cssinject__){t.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}function l(){i||(i=!0,o())}function r(){try{c.documentElement.doScroll("left")}catch(t){return void setTimeout(r,50)}l()}e=function(){var t,e=document.createElement("div");e.innerHTML=a,a=null,(e=e.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",e=e,(t=document.body).firstChild?s(e,t.firstChild):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(e,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),e()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(o=e,c=t.document,i=!1,r(),c.onreadystatechange=function(){"complete"==c.readyState&&(c.onreadystatechange=null,l())})}(window);
</script>
<script type="text/html" id="lottoolbar">
	<div class="layui-btn-container">
		<button class="layui-btn layui-btn-normal icon" id="addDevice" data-type="addDevice"><i class="layui-icon layui-icon-add-1"></i>添加设备</button>
		<button class="layui-btn layui-btn-normal icon" id="reloadDevice" data-type="reloadDevice" style="padding: 0 8px 0 8px;"><i class="layui-icon layui-icon-refresh-1" style="margin-right: 0;"></i></button>
	</div>
</script>
<script type="text/html" id="lottoolbar-opt2">
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="unbind">解除绑定</a>
</script>
</html>