<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	.layui-progress{margin-top:6px!important;}
	.zymxbox{height: 100%;overflow: auto}
	table.mxtable,table.mxtable td,table.mxtable th{
		font-family: "宋体",SimSun;
	}
	table.mxtable{
		margin: 0 auto;
		width:100%;
		height:auto;
		border-spacing: 1px;
		background-color: #FFFFFF;
	}

	.mxtable th{text-align: center;font-size:13px;font-weight:600;background-color: #FFFFFF;border-bottom:solid 2px #000000}
	.mxtable td{text-align: center;}


	table.mxtable tr th,table.mxtable tr td.head {
		height:36px;
		font-size:14px;
		font-weight:540;
	}
	table.mxtable tr td.tl {background-color: #efefef;font-weight: 600}
	table.mxtable tr td.tl1 {font-weight: 600}

	table.mxtable tr td.left {text-align: left}
	table.mxtable tr td {
		height:28px;
		font-size:15px;
		font-weight:500;
		text-align: center;
		text-indent:6px;
	}

</style>
<script type="text/javascript">
	var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body>
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<div class="fulllistbox">
		<%if(breadcrumb){%>
		<div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>复诊(随访记录)</cite></a>
			  <a><cite>服务记录</cite></a>
			</span>
			<div class="title-name"></div>
		</div>
		<%}%>
		<div class="listbox">
			<div class="listbox-body">
			    <div class="layui-card search-box">
				</div>
				<div class="layui-card opt-box " style="box-shadow:none;">
					<div class="opt"></div>
				</div>
				<div class="layui-card-body adaptive-table">
			       <table id="listtable" lay-filter="listtable"></table>
			        <script type="text/html" id="listtable-opt">
						<a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        			</script>
		      </div>
			</div>
		</div>
	</div>
</body>
<div  class="zymxbox" id="zymxbox" style="display: none;">
</div>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/pcenter/fzlist.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&gwServerUrl=${gwServerUrl}&jyServerUrl=${jyServerUrl}"></script>
<script type="text/html" id="jybg-templet">
	<table class="mxtable" style="border:0;">
		<tr>
			<th class="col1">序号</th>
			<th class="col1">名称</th>
			<th class="col1">检验结果</th>
			<th class="col1">异常结果提示</th>
			<th class="col1">参考范围</th>
		</tr>
		{{#  layui.each(d, function(index, item2){ }}
		<tr class="{{item2.jyjgbz == 'H' ?  'yc' : '' }}{{item2.jyjgbz == 'L' ?  'yc' : '' }}">
			<td >{{index+1 }}</td>
			<td >{{item2.lisxm || '' }}</td>
			<td >{{item2.jyjg || '' }}{{item2.jydw || '' }}</td>
			<td >{{item2.jyjgbz == "H" ?  '↑' : '' }}{{item2.jyjgbz == "L" ?  '↓' : '' }}</td>
			<td >{{item2.jyckz || '' }}</td>
		</tr>
		{{#  }); }}
	</table>
</script>
<script type="text/html" id="gtp">
{{d.gtp}}
</script>
<script type="text/html" id="sffs">
	{{#
	let sffsText = "";
	sffsText = d.sffs == '1'?'门诊':sffsText;
	sffsText = d.sffs == '2'?'家庭':sffsText;
	sffsText = d.sffs == '3'?'电话':sffsText;
	}}
	{{# }}
	{{sffsText}}
</script>
<script type="text/html" id="wcl">
	<div class="layui-progress layui-progress-big" lay-showPercent="true">
		<div class="layui-progress-bar layui-bg-blue" lay-percent="{{ d.wcl || 0 }}%"></div>
	</div>
</script>
</html>