<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/console2.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
<style type="text/css">
    .layui-tab-content {
        height: 420px;
        overflow: auto;
    }
    .layui-tab-title{
        height: 43px;
    }
    p.jkzd-content {
        font-size: 13px;
        font-weight: normal;
        line-height: 24px;
        white-space: pre-line;
    }
    b.fzInfo{font-weight: normal}
    .tpstatus,.tpstatus-li,.tpstatus-title,.tpstatus-color{display: inline;}
    .tpstatus{
        display: inline-flex;
        flex-direction: row;
        float: right;
        padding-right: 10px;
        justify-content: center;
        align-items: center;
    }
    .tpstatus-li{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-left: 10px;
        padding-right: 10px;
        font-size: 12px;
    }
</style>
<script>
    var isPcenterHome = true,isTopIndex =false;
    var alibabaKey = "${authUserJson}";
    var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body class="pear-container">
<div>
    <div class="layui-row layui-col-space10">
        <div class="layui-card" style=" margin:10px 5px; background: #ddefec; border: solid 1px #009688;">
            <div class="layui-card-body" style="padding: 0 0">
                <div class="layui-row layui-col-space5">
                    <div class="layui-col-xs4 layui-col-md12 " >
                        <div class="tpstatus" style="padding-left:10px;padding-right: 0;float: left;height: 48px;">当前状态:<span class="pmtype" style="padding-left:5px"></span>&nbsp;&nbsp;&nbsp;&nbsp;目前诊断:<span class="hzlx" style="padding-left:5px"></span>&nbsp;&nbsp;&nbsp;&nbsp;<span style="padding-left:5px">复诊计划:<b class="fzInfo" ></b></span></div>
                        <div class="tpstatus">
                            <div class="tpstatus-li gxy">
                                <div class="tpstatus-title">高血压</div>
                                <div class="tpstatus-color"></div>
                            </div>
                            <div class="tpstatus-li tnb">
                                <div class="tpstatus-title">糖尿病</div>
                                <div class="tpstatus-color"></div>
                            </div>
                            <div class="tpstatus-li gxz">
                                <div class="tpstatus-title">高血脂</div>
                                <div class="tpstatus-color"></div>
                            </div>
                            <div class="tpstatus-li ascvd">
                                <div class="tpstatus-title">ASCVD风险</div>
                                <div class="tpstatus-color"></div>
                            </div>
                            <div class="tpstatus-li pgsj">
                                <div class="tpstatus-title">上次评估时间</div>
                                <div class="tpstatus-color"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md5">
            <div class="layui-card" id="tab-card-fzjh">
                <div class="layui-tab fzjhTab" lay-filter="fzjhTab">
                    <ul class="layui-tab-title" id="fzjhTab-title">
                        <li class="layui-this" val="dfz" load-stetup="0">待进行复诊</li>
                        <li val="fz" load-stetup="0">年度内复诊计划</li>
                    </ul>
                    <div class="layui-tab-content" id="fzjhTab-content">
                        <div class="layui-tab-item layui-show" >
                            <div id="dfzTable" lay-filter="dfzlisttable"></div>
                        </div>
                        <div class="layui-tab-item" >
                            <div id="fzTable" lay-filter="fzlisttable2"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md7">
            <div class="layui-card">
                <div class="layui-card-header">指标趋势分析</div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6 loading-chart1">
                            <div id="echarts-gxy" style="background-color:#ffffff;min-height:400px;padding: 10px"></div>
                        </div>
                        <div class="layui-col-md6 loading-chart2">
                            <div id="echarts-tnb" style="background-color:#ffffff;min-height:400px;padding: 10px"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md5">
            <div class="layui-card" id="tab-card-jkda">
                <div class="layui-tab" lay-filter="planTab">
                    <ul class="layui-tab-title" id="planTab-title">
                    </ul>
                    <div class="layui-tab-content" id="planTab-content">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-col-md7">
            <div class="layui-card">
                <div class="layui-card-header layui-row" style="border-bottom: 1px solid #e6e6e6;height: 43px;">
                    <div class="layui-col-md3">
                        历史用药
                    </div>
                    <div class="layui-col-md9" style="text-align: right">
                        <button class="layui-btn layui-btn-normal icon" data-type="editYyzd" id="editYyzd"><i class="layui-icon layui-icon-senior "></i>调整用药</button>
                    </div>
                </div>
                <div class="layui-card-body">
                    <div id="hisTable" lay-filter="hisyylisttable"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<%include("/patients/plansfjl/add.html",{'authUserJson':authUserJson,'authUser': authUser}){} %>
<script id="plansfjl" type="text/javascript" src="${ctxPath}/services/plansfjl/sfjl.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
<script id="consolejs" type="text/javascript" src="${ctxPath}/services/console2.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
<script type="text/html" id="fwqdTemplet">
    {{#  layui.each(d.jkzd, function(index, jkzd){ }}
    <li  {{ index == 0 ? 'class="layui-this"' : '' }}>{{jkzd.item}}</li>
    {{#  }); }}
    <li>用药指导</li>
</script>
<script type="text/html" id="fwqdnrTemplet">
    {{#  layui.each(d.jkzd, function(index, jkzd){ }}
    <div class="layui-tab-item  {{index == 0 ? 'layui-show' : '' }}" load-stetup="0">
        {{# if(jkzd.list.length == 0){ }}
            暂无数据
        {{# } }}
        {{#  layui.each(jkzd.list, function(indexm, mBean){ }}
            <p class="jkzd-content">{{mBean.content}}</p>
        {{#  }); }}
    </div>
    {{#  }); }}
    <div class="layui-tab-item" id="yyzdResult" load-stetup="0">
        <table class="listtable" lay-filter="listtable"></table>
    </div>
</script>
<script type="text/html" id="bpgrade">
    {{#
    let color = "#EFEFEF";
    color = d.bpgradecode == '0'?'#2CBD32':color;
    color = d.bpgradecode == '6'?'#07B0FF':color;
    color = d.bpgradecode == '1'?'#F5DD0C':color;
    color = d.bpgradecode == '2'?'#FF8D02':color;
    color = d.bpgradecode == '3'?'#FF2222':color;
    }}
    <svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
    {{#   }}
</script>
<script type="text/html" id="dpgrade">
    {{#
    let color = "#EFEFEF";
    color = d.dbgradecode == '0'?'#2CBD32':color;
    color = d.dbgradecode == '6'?'#07B0FF':color;
    color = d.dbgradecode == '1'?'#F5DD0C':color;
    color = d.dbgradecode == '2'?'#FF8D02':color;
    color = d.dbgradecode == '3'?'#FF2222':color;
    }}
    <svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
    {{#   }}
</script>
<script type="text/html" id="lpgrade">
    {{#
    let color = "#EFEFEF";
    color = d.lpgradecode == '0' ? '#2CBD32' : color;
    color = d.lpgradecode == '6' ? '#07B0FF' : color;
    color = d.lpgradecode == '1' ? '#F5DD0C' : color;
    color = d.lpgradecode == '2' ? '#FF8D02' : color;
    color = d.lpgradecode == '3' ? '#FF2222' : color;
    }}
    <svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
    {{#   }}
</script>

<script type="text/html" id="ascvd">
    {{#
    let color = "#EFEFEF";
    color = d.adgradecode == '0'?'#2CBD32':color;
    color = d.adgradecode == '1'?'#F5DD0C':color;
    color = d.adgradecode == '2'?'#FF8D02':color;
    color = d.adgradecode == '3'?'#FF2222':color;
    }}
    <svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
    {{#   }}
</script>
<script type="text/html" id="pgsj">
{{d.prepgtime || "&nbsp;"}}
</script>
<script>
    !function(t){var e,n,o,c,i,a='<svg><symbol id="icon-shuangxiangzhuanzhen" viewBox="0 0 1024 1024"><path d="M451.395918 715.755102c-11.493878 0-20.897959-9.404082-20.897959-20.897959V405.942857c0-11.493878 9.404082-20.897959 20.897959-20.897959s20.897959 9.404082 20.89796 20.897959V694.857143c0 11.493878-9.404082 20.897959-20.89796 20.897959z"  ></path><path d="M348.995918 529.240816c-5.22449 0-10.44898-2.089796-14.628571-6.269387-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.404082 6.269388-15.151021 6.269387z"  ></path><path d="M572.604082 638.955102c-11.493878 0-20.897959-9.404082-20.89796-20.897959V329.142857c0-11.493878 9.404082-20.897959 20.89796-20.897959s20.897959 9.404082 20.897959 20.897959v288.914286c0 11.493878-9.404082 20.897959-20.897959 20.897959z"  ></path><path d="M572.604082 638.955102c-5.22449 0-10.44898-2.089796-14.628572-6.269388-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.926531 6.269388-15.15102 6.269388z"  ></path><path d="M512 929.959184c-230.4 0-417.959184-187.559184-417.959184-417.959184s187.559184-417.959184 417.959184-417.959184 417.959184 187.559184 417.959184 417.959184-187.559184 417.959184-417.959184 417.959184z m0-794.122449c-207.412245 0-376.163265 168.75102-376.163265 376.163265s168.75102 376.163265 376.163265 376.163265 376.163265-168.75102 376.163265-376.163265-168.75102-376.163265-376.163265-376.163265z"  ></path></symbol></svg>',d=(d=document.getElementsByTagName("script"))[d.length-1].getAttribute("data-injectcss"),s=function(t,e){e.parentNode.insertBefore(t,e)};if(d&&!t.__iconfont__svg__cssinject__){t.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}function l(){i||(i=!0,o())}function r(){try{c.documentElement.doScroll("left")}catch(t){return void setTimeout(r,50)}l()}e=function(){var t,e=document.createElement("div");e.innerHTML=a,a=null,(e=e.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",e=e,(t=document.body).firstChild?s(e,t.firstChild):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(e,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),e()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(o=e,c=t.document,i=!1,r(),c.onreadystatechange=function(){"complete"==c.readyState&&(c.onreadystatechange=null,l())})}(window);
</script>
</html>