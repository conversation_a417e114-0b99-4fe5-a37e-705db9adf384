<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/admin.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-nav .layui-nav-item a{
            padding: 0 10px;
        }
        .pear-nav-tree{
            width: 168px;
        }
        .pear-admin .layui-header{
            background-color: #10b596;
            left: 168px;
            width: calc(100% - 168px);
            border-bottom: none;
            z-index: 777;
        }
        .pear-admin .layui-body {
            left: 168px;
        }
        .pear-admin .layui-logo {
            width: 168px;
        }
        .pear-admin .layui-side {
            width: 168px;
        }

        .pear-admin .layui-logo{justify-content: flex-start;padding-left: 16px;}
        .pear-admin .layui-logo .title{padding-left:6px;font-size: 2.0vh;}
        .pear-admin .layui-layout-control{
            left:0;
        }
        .row-patient{height:60px;}
        .col-patient{}
        .grid-text-b{
            /*background: #f0f2f5;*/
            padding-left:5px;font-weight:bolder;
            text-align: right;
        }
        .grid-text-b:after{
            content: ":";
        }

        .grid-text{
            /*background: #FFFFFF;*/
            padding-left:5px;}

        .grid-text-b,.grid-text{
            color:#ffffff;
            background-color: #10b596;
            line-height:29px;font-size:12px;
        }
        .col-patient .grid-text-b{
            /*border-right: solid 1px #dedede;border-bottom: solid 1px #dedede;*/
        }
        .col-patient .grid-text{
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            /*border-right: solid 1px #dedede;border-bottom: solid 1px #dedede;*/
        }
        .pear-frame .pear-frame-title{
            z-index: 888;
            border: solid 1px #dedede;
        }
        .layui-home-menu{
            width: 100%;
            position: absolute;
            height: 48px;
            bottom: 0;
            display: flex;
            -ms-flex-direction: row;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }
        .layui-home-menu a{
            margin: 0 auto;
            background-color: #427c72;
        }
        .pear-frame .pear-frame-content {
            width: 100%;
            height: calc(100% - 42px) !important;
        }
    </style>
    <script>
        var patient = JSON.parse('${json(patients.rd)}');
        var name = patient.name;
        var patientid = patient.id;
        var hzlx = '${ctl.getHzlx(json(patients.rd))}';
        var fzInfo = "${patients.otherParams.fzInfo!''}";
        var alibabaKey = "${authUserJson}";
    </script>
</head>
<body class="layui-layout-body pear-admin">
<div class="layui-layout layui-layout-admin">
    <div class="layui-header">
        <div class="layui-row row-patient" >
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">姓名</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.name}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">性别</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.gender}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">年龄</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.age}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">民族</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.minzu}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">身份证号</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.idcard}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">联系电话</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.lxdh}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b">家庭住址</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text" title="${patients.rd.jtzz}">${patients.rd.jtzz}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">既往病史</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${ctl.getJws(patients.rd.jwsjb,json(dicData.p_jwsjb))}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">所属单位</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.orgname}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">所属医生</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.jyusername}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">管理单位</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.glorgname}</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2 col-patient">
                <div class="layui-row grid-patient">
                    <div class="layui-col-md4">
                        <div class="grid-text-b ">管理医生</div>
                    </div>
                    <div class="layui-col-md8">
                        <div class="grid-text">${patients.rd.gljyusername}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 患者基本信息 -->
        <div id="control" class="layui-layout-control"></div>
    </div>
    <!-- 侧 边 区 域 -->
    <div class="layui-side layui-bg-black">
        <!-- 菜 单 顶 部 -->
        <div class="layui-logo">
            <!-- 图 标 -->
            <img class="logo"/>
            <!-- 标 题 -->
            <span class="title wfont"></span>
        </div>
        <!-- 菜 单 内 容 -->
        <div class="layui-side-scroll">
            <div id="sideMenu"></div>
        </div>
        <%if(param.form == "his"){%>
        <div class="layui-home-menu">
            <a type="button" class="layui-btn" href="${ctxPath}/index">
                <i class="layui-icon layui-icon-return layui-font-12"></i> 返回管理中心
            </a>
        </div>
        <%}%>
    </div>
    <!-- 视 图 页 面 -->
    <div class="layui-body">
        <!-- 内 容 页 面 -->
        <div id="content"></div>
    </div>
    <!-- 遮 盖 层 -->
    <div class="pear-cover"></div>
    <!-- 加 载 动 画-->
    <div class="loader-main">
        <div class="loader"></div>
    </div>
</div>
<script id="indexjs" type="text/javascript" src="${ctxPath}/services/pcenter.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&patientid=${patients.rd.id}"></script>
</body>
</html>