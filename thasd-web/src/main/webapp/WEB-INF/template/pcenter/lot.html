<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/console2.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
<style type="text/css">
    .layui-tab-content {
        height: 420px;
        overflow: auto;
    }
    .layui-tab-title{
        height: 43px;
    }
    .layui-table-view{
        margin: 0;
    }
</style>
<script>
    var alibabaKey = "${authUserJson}";
</script>
</head>
<body class="pear-container">
<div>
    <div class="layui-row layui-col-space10" >
        <div class="layui-card">
            <div class="layui-card-header" style="border-bottom: 1px solid #9f9898;">指标趋势分析</div>
            <div class="layui-card-body" style="padding: 0 0">
                <div class="layui-row layui-col-space5">
                    <div class="layui-col-md6 loading-chart1">
                        <div id="echarts-gxy" style="background-color:#ffffff;min-height:400px;padding: 10px"></div>
                    </div>
                    <div class="layui-col-md6 loading-chart2">
                        <div id="echarts-tnb" style="background-color:#ffffff;min-height:400px;padding: 10px"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-row layui-col-space10" style="margin-top: 10px">
        <div class="layui-card">
            <div class="layui-card-body" style="padding: 0 0">
                <div class="layui-row layui-col-space5">
                    <div class="layui-col-xs4 layui-col-md12 " >
                        <div class=""layui-card id="tab-card-fzjh">
                            <div class="layui-tab zwjcTab" lay-filter="zwjcTab">
                                <ul class="layui-tab-title" id="zwjcTab-title">
                                    <li class="layui-this" val="xy" load-stetup="0">血压监测</li>
                                    <li val="xt" load-stetup="0">血糖监测</li>
                                </ul>
                                <div class="layui-tab-content" id="zwjcTab-content">
                                    <div class="layui-tab-item layui-show" >
                                        <div id="xyTable" lay-filter="xylisttable"></div>
                                    </div>
                                    <div class="layui-tab-item" >
                                        <div id="xtTable" lay-filter="xzlisttable"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script id="lotjs" type="text/javascript" src="${ctxPath}/services/pcenter/lot.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
<script>
    !function(t){var e,n,o,c,i,a='<svg><symbol id="icon-shuangxiangzhuanzhen" viewBox="0 0 1024 1024"><path d="M451.395918 715.755102c-11.493878 0-20.897959-9.404082-20.897959-20.897959V405.942857c0-11.493878 9.404082-20.897959 20.897959-20.897959s20.897959 9.404082 20.89796 20.897959V694.857143c0 11.493878-9.404082 20.897959-20.89796 20.897959z"  ></path><path d="M348.995918 529.240816c-5.22449 0-10.44898-2.089796-14.628571-6.269387-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.404082 6.269388-15.151021 6.269387z"  ></path><path d="M572.604082 638.955102c-11.493878 0-20.897959-9.404082-20.89796-20.897959V329.142857c0-11.493878 9.404082-20.897959 20.89796-20.897959s20.897959 9.404082 20.897959 20.897959v288.914286c0 11.493878-9.404082 20.897959-20.897959 20.897959z"  ></path><path d="M572.604082 638.955102c-5.22449 0-10.44898-2.089796-14.628572-6.269388-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.926531 6.269388-15.15102 6.269388z"  ></path><path d="M512 929.959184c-230.4 0-417.959184-187.559184-417.959184-417.959184s187.559184-417.959184 417.959184-417.959184 417.959184 187.559184 417.959184 417.959184-187.559184 417.959184-417.959184 417.959184z m0-794.122449c-207.412245 0-376.163265 168.75102-376.163265 376.163265s168.75102 376.163265 376.163265 376.163265 376.163265-168.75102 376.163265-376.163265-168.75102-376.163265-376.163265-376.163265z"  ></path></symbol></svg>',d=(d=document.getElementsByTagName("script"))[d.length-1].getAttribute("data-injectcss"),s=function(t,e){e.parentNode.insertBefore(t,e)};if(d&&!t.__iconfont__svg__cssinject__){t.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}function l(){i||(i=!0,o())}function r(){try{c.documentElement.doScroll("left")}catch(t){return void setTimeout(r,50)}l()}e=function(){var t,e=document.createElement("div");e.innerHTML=a,a=null,(e=e.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",e=e,(t=document.body).firstChild?s(e,t.firstChild):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(e,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),e()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(o=e,c=t.document,i=!1,r(),c.onreadystatechange=function(){"complete"==c.readyState&&(c.onreadystatechange=null,l())})}(window);
</script>
</html>