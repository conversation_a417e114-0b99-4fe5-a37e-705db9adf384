<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	html,body{
		height:100%;
	}

	.initbox{
		position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms;
	}
	.initbox > span[class*="-icon"] {
		width: 45px;
		height: 45px;
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		margin: auto;
	}

	.layui-tab{margin:0 0!important;}
	.mainTab{
		height: 100%;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-tab-content-fix{flex-shrink:4;height: 5px;}

	.layui-tab-content {
		-ms-flex: 1;
		flex: 1;
		height: 0;
		flex-shrink: 0;
		flex-basis: 40px;
		padding:0 10px !important;
		overflow: auto;
	}
	.layui-tab-title{
		flex-shrink: 2;
		height: 43px;
	}
	table,table td,table th{
		font-family: "宋体",SimSun;
	}
	table{
		margin: 0 auto;
		width:100%;
		height:auto;
		border-spacing: 1px;
		background-color: #000;
	}

	table tr th,table tr td {
		background-color: #ffffff;
	}

	table table.innerTable{
		border:none;
		background-color: #CFCFCF;
	}
	table tr th,table tr td.head {
		height:36px;
		font-size:14px;
		font-weight:540;
	}
	table tr td.tl {background-color: #efefef;font-weight: 600}
	table tr td.tl1 {font-weight: 600}

	table tr td.left {text-align: left}
	table tr td {
		height:28px;
		font-size:15px;
		font-weight:500;
		text-align: left;
		text-indent:6px;
	}
	td.hct{text-indent:10px;font-size: 14px}
	table tr th.head,table tr td.head{font-size:13px;font-weight:600;}
	table tr.title td{background:#efefef;font-size:14px;height:36px;}

	table.mxtable{
		width:100%;padding:0;margin:0;border: 0;
		background-color: #CFCFCF;
	}

	.innerTable td.head,.mxtable th{text-align: center;font-size:13px;font-weight:600;background-color: #efefef;}
	.mxtable td{text-align: center;}

	.noborder{border:none;}
	.nobg{background-color: #FFFFFF;}

	.layui-tab-item .list_table{}
	.layui-tab-item .list_table:first-child{}
	.layui-tab-item .c_hr{width: 960px;margin:0 auto;border: dotted 1px #b9b9b9;height:0px;margin-top:10px;margin-bottom: 10px; }
	.layui-tab-item .c_hr:last-child{ border: none;}

	tr.yc td{
		background-color: #fadede;
	}
	.emptyBox{
		width: 100%;
		height: 100%;
		min-height: 400px;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 600;
	}
	td.tybw{
		width: 81px;text-align: right;font-weight:bold;padding: 0;
		vertical-align: text-top;
		line-height: 24px;
		text-indent:0;
	}
	td.nr{
		vertical-align: text-top;
		padding-left:0;
		text-indent: 0;
		line-height: 24px;
	}
	td.nr1{
		text-indent: 0;
		line-height: 16px;
		height: 20px;
	}
	.zymxbox{
		vertical-align: text-top;
		min-height: 336px;
	}
	.zymx{
		width: 100%;
		height: auto;
		display: grid;
		grid-template-columns: 33.33% 33.33% 33.33% ;
		justify-content: space-around;
		align-items: center;
	}
	.zymx p.zymxmc{
		font-size: 15px;
		margin-top:12px;
		white-space: nowrap;
		font-size: clamp(12px, 1vw, 15px);
	}
	.col1{background: #fff !important;}
</style>
<script type="text/javascript">
	var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body style="padding: 0 5px 0 5px;">
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	 <div class="layui-tab mainTab" lay-filter="mainTab">
		 <ul class="layui-tab-title" id="mainTab-title">
			 <li class="layui-this" val="bl" load-stetup="0">门急诊病历</li>
			 <li val="xycf" load-stetup="0">西药处方</li>
			 <li val="zycf" load-stetup="0">中药处方</li>
			 <li val="jcbg" load-stetup="0">检查报告</li>
			 <li val="jybg" load-stetup="0">检验报告</li>
		 </ul>
		 <div class="layui-tab-content-fix"></div>
		 <div class="layui-tab-content" id="mainTab-content">
			 <div class="layui-tab-item layui-show" id="bl">

			 </div>
			 <div class="layui-tab-item" id="xycf">
			 </div>
			 <div class="layui-tab-item" id="zycf">
			 </div>
			 <div class="layui-tab-item" id="jcbg">
			 </div>
			 <div class="layui-tab-item" id="jybg">
			 </div>
		 </div>
		 <div class="layui-tab-content-fix"></div>
	 </div>
</body>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/pcenter/mjzblIndex.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&id=${param.id}&mzh=${param.mzh}&orgcode=${param.orgcode}&model=pcenter"></script>
<script type="text/html" id="bl-templet">
	{{#  if(d ){ }}
		<table style="width: 595px;margin:15px auto;background-color: #FFF">
			<tr>
				<td class="tl1" colspan="8" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{d.orgname}}</td>
			</tr>
			<tr>
				<td class="tl1" colspan="8" style="text-align: center;font-size:20px;letter-spacing: 3px;">门诊病历</td>
			</tr>
			<tr>
				<td class="nr1" colspan="8">门诊号：{{d.mzh || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="8">姓名：{{d.name || '' }}&nbsp;&nbsp;性别：{{d.sex || '' }}&nbsp;&nbsp;年龄：{{d.age || '' }}{{d.ageunit || '' }}&nbsp;&nbsp;民族：{{d.minzu || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="8">就诊日期：{{ formatDateTime(d.ghdate || '' ,'yyyy年MM月dd日 HH:mm') }}&nbsp;&nbsp;科室：{{d.deptname || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="8" style="height:5px;border-top:solid 2px #000000;"></td>
			</tr>
<!--			<tr>-->
<!--				<td class="nr" colspan="8">T&nbsp;&nbsp;{{d.tw == null ? "" :d.tw }}℃&nbsp;&nbsp;, BP&nbsp;&nbsp;{{d.ssy == null ? "" :d.ssy}}/{{d.szy == null ? "" :d.szy}}mmHg&nbsp;&nbsp;, 药物过敏史：</td>-->
<!--			</tr>-->
			<tr><td style="height: 15px;"></td></tr>
			<tr>
				<td class="tybw"><b style="letter-spacing: 8px;">主诉</b>：</td><td class="nr" colspan="7">{{d.zs == null ? "" :d.zs }}</td>
			</tr>
			<tr>
				<td class="tybw" ><b style="letter-spacing: 5px;">现病史</b>：</td><td class="nr" colspan="7">{{d.zs == null ? "" :d.zs }}</td>
			</tr>
			<tr>
				<td class="tybw" ><b style="letter-spacing: 5px;">既往史</b>：</td><td class="nr" colspan="7">{{d.jws == null ? "" :d.jws }}</td>
			</tr>
			<tr>
				<td class="tybw">体格检查：</td><td class="nr" colspan="7">{{d.ybtgjc == null ? "" :d.ybtgjc }}</td>
			</tr>
			<tr>
				<td class="tybw">辅助检查：</td><td class="nr" colspan="7">{{d.fzjc == null ? "" :d.fzjc }}</td>
			</tr>
			<tr>
				<td class="tybw">初步诊断：</td>
				<td class="nr" colspan="7">
					{{#  layui.each(d.zdList, function(index, item){ }}
					<p>{{index+1}}.{{item.zdmc || '' }}</p>
					{{#  }); }}
				</td>
			</tr>
			<tr><td style="height: 15px;"></td></tr>
			<tr>
				<td class="tybw">诊疗意见：</td>
				<td class="nr" colspan="7">
					{{#  layui.each(d.zlyjList, function(index, item2){ }}
					<p class="zlyj-t">{{index+1}}.{{item2 || '' }}</p>
					{{#  }); }}
					<p class="zlyj-t">{{d.zlyj == null ? "" :d.zlyj }}</p>
				</td>
			</tr>
			<tr><td style="height: 15px;"></td></tr>
			<tr>
				<td class="nr1" colspan="8" style="height:5px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td class="nr" colspan="8" style="text-align: right"><b>接诊医生：</b>{{d.createusername == null ? "" :d.createusername }}</td>
			</tr>
		</table>
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="xycf-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table" style="width: 495px;margin:15px auto;background-color: #FFF">
			<tr>
				<td class="tl1" colspan="6" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
			</tr>
			<tr>
				<td class="tl1" colspan="6" style="text-align: center;font-size:20px;letter-spacing: 3px;">门诊处方笺</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">科室：{{item.deptname || '' }}&nbsp;&nbsp;门诊号：{{item.mzh || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;&nbsp;{{ formatDateTime(item.cfrq || '' ,'yyyy年MM月dd日') }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">临床诊断：{{item.zd || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td style="text-indent:0;padding:5px 5px !important;" colspan="6">
					<div  class="zymxbox">
						<table class="mxtable" style="background-color: #fff;border: none;">
							{{#  layui.each(item.yymxList, function(index, item2){ }}
							<tr>
								<td style="font-weight: bold;">{{item2.mxname || '' }}&nbsp;{{item2.mxgg || '' }}×{{item2.mxquantity || '' }}{{item2.mxdw || '' }}</td>
							</tr>
							<tr>
								<td style="height: 18px;">用法：{{item2.dcjl || '' }}{{item2.jldw || '' }}&nbsp;{{item2.sypl || '' }}&nbsp;{{item2.sytj || '' }}</td>
							</tr>
							{{#  }); }}
						</table>
					</div>
				</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" style="width:33%;">医师：{{item.createusername || '' }}</td>
				<td class="nr1" style="width:33%;">审核：{{item.cfshyjs || '' }}</td>
				<td class="nr1" style="width:33%;">金额：{{item.cash || '' }}元</td>
			</tr>
			<tr>
				<td class="nr1" style="width:33%;">调配：{{item.cftpyjs || '' }}</td>
				<td class="nr1" style="width:33%;">核对：{{item.cfhdyjs || '' }}</td>
				<td class="nr1" style="width:33%;">发药：{{item.cffyyjs || '' }}</td>
			</tr>
		</table>
		<hr class="c_hr" style="width: 495px;margin-top: 20px;"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="zycf-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table" style="width: 495px;margin:15px auto;background-color: #FFF">
			<tr>
				<td class="tl1" colspan="6" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
			</tr>
			<tr>
				<td class="tl1" colspan="6" style="text-align: center;font-size:20px;letter-spacing: 3px;">门诊处方笺</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">科室：{{item.deptname || '' }}&nbsp;&nbsp;门诊号：{{item.mzh || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;&nbsp;{{ formatDateTime(item.cfrq || '' ,'yyyy年MM月dd日') }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="6">临床诊断：{{item.zd || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td style="text-indent:0;padding:5px 5px !important;" colspan="6">
					<div  class="zymxbox">
						<div class="zymx">
							{{#
							var packVal = "";var tsygVal = "";
							layui.each(item.yymxList, function(index, item2){
								packVal = item2.pack;
								tsygVal = item2.tsyf;
							}}
							<p class="zymxmc" style="">{{item2.mxname || '' }}&nbsp;{{item2.dcjl || '' }}{{item2.dcjldw || '' }}</p>
							{{#  }); }}
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="vertical-align: text-top;padding-top:10px"><b style="font-size: 18px;width: 47px;display: inline-block;">{{packVal || '' }}付</b>用量：{{item.zyyyff || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="vertical-align: text-top;padding-left: 47px;">用发：{{item.zyjzf || '' }}{{tsygVal || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="6" style="height:1px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" style="width:33%;">医师：{{item.createusername || '' }}</td>
				<td class="nr1" style="width:33%;">审核：{{item.cfshyjs || '' }}</td>
				<td class="nr1" style="width:33%;">金额：{{item.cash || '' }}元</td>
			</tr>
			<tr>
				<td class="nr1" style="width:33%;">调配：{{item.cftpyjs || '' }}</td>
				<td class="nr1" style="width:33%;">核对：{{item.cfhdyjs || '' }}</td>
				<td class="nr1" style="width:33%;">发药：{{item.cffyyjs || '' }}</td>
			</tr>
		</table>
		<hr class="c_hr" style="width: 495px;margin-top: 20px;"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="jcbg-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table" style="width: 852px;margin:15px auto;background-color: #FFF">
			<tr>
				<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
			</tr>
			<tr>
				<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">医学影像学报告单</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;门诊号：{{item.mzh || '' }}&nbsp;科室：{{item.deptname || '' }}&nbsp;检查日期：{{ formatDateTime(item.jcsj || '' ,'yyyy年MM月dd日')  }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="10">临床诊断：{{item.zd || '-' }}&nbsp;检查部位：{{item.jcbw || '-' }}&nbsp;检查目的：{{item.jcmd || '-' }}&nbsp;检查方法/技术：{{item.jcyq || '-' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr><td style="height: 15px;"></td></tr>
			<tr>
				<td class="tybw" style="width: 125px;vertical-align: top;"><b style="letter-spacing: 1px;">影像医学表现</b>：</td>
				<td class="nr" colspan="9">
					<div  class="zymxbox" style="min-height:160px">{{item.yxxbx || '' }}</div>
				</td>
			</tr>
			<tr>
				<td class="tybw" style="width: 125px;vertical-align: top;"><b style="letter-spacing: 1px;">影像医学诊断</b>：</td>
				<td class="nr" colspan="9">
					<div  class="zymxbox" style="min-height:160px">{{item.yxxzd || '' }}</div>
				</td>
			</tr>
			<tr><td style="height: 15px;"></td></tr>
			<tr>
				<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td style="text-indent: 0;" colspan="10">报告医师：{{item.bgys || '' }}&nbsp;审核医师：{{item.shys || '' }}&nbsp;报告时间：{{formatDateTime(item.bgsj || '' ,'yyyy年MM月dd日') }}&nbsp;</td>
			</tr>
		</table>
<!--		<hr class="c_hr" style="width: 852px;margin-top: 20px;"/>-->
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="jybg-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table" style="width: 852px;margin:15px auto;background-color: #FFF">
			<tr>
				<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
			</tr>
			<tr>
				<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">检验报告单</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;科室：{{item.deptname || '' }}&nbsp;&nbsp;门诊号：{{item.mzh || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="10">标本类型：{{item.bblx || '' }}&nbsp;检验项目：{{item.jymc || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="10">临床诊断：{{item.zd || '' }}</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td style="text-indent:0;padding:0 0!important;" colspan="10">
					<div  class="zymxbox">
						<table class="mxtable" style="border-spacing:0;border:0;background: #fff;">
							<tr>
								<th class="col1">序号</th>
								<th class="col1">名称</th>
								<th class="col1">检验结果</th>
								<th class="col1">异常结果提示</th>
								<th class="col1">参考范围</th>
							</tr>
							{{#  layui.each(item.mxList, function(index, item2){ }}
							<tr class="{{item2.jyjgbz == 'H' ?  'yc' : '' }}{{item2.jyjgbz == 'L' ?  'yc' : '' }}">
								<td >{{index+1 }}</td>
								<td >{{item2.lisxm || '' }}</td>
								<td >{{item2.jyjg || '' }}{{item2.jydw || '' }}</td>
								<td >{{item2.jyjgbz == "H" ?  '↑' : '' }}{{item2.jyjgbz == "L" ?  '↓' : '' }}</td>
								<td >{{item2.jyckz || '' }}</td>
							</tr>
							{{#  }); }}
						</table>
					</div>
				</td>
			</tr>
			<tr>
				<td class="nr1" colspan="10" style="height:1px;border-top:solid 2px #000000;"></td>
			</tr>
			<tr>
				<td class="nr1" colspan="2">报告日期：{{ formatDateTime(item.jybgrq || '' ,'yyyy年MM月dd日')  }}</td>
				<td class="nr1" colspan="2">申请：{{ item.createusername || ''  }}</td>
				<td class="nr1" colspan="2">检验：{{item.jybgys || '' }}</td>
				<td class="nr1" colspan="2">审核：{{item.shys || '' }}</td>
				<td class="nr1" colspan="2"></td>
			</tr>
		</table>
<!--		<hr class="c_hr" style="width: 852px;"/>-->
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
</html>