<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
</style>
<script type="text/javascript">
	var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body>
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<div class="fulllistbox">
		<%if(breadcrumb){%>
		<div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>检测评估</cite></a>
			  <a><cite>评估报告</cite></a>
			</span>
			<div class="title-name"></div>
		</div>
		<%}%>
		<div class="listbox">
			<div class="listbox-body">
			    <div class="layui-card search-box">
				</div>
				<div class="layui-card opt-box opt-box-flex" style="box-shadow:none;">
					<div class="opt"></div>
					<div class="extBox">
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#FF2222"></path></svg>高危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#FF8D02"></path></svg>中危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#F5DD0C"></path></svg>低危</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#07B0FF"></path></svg>易患</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#2CBD32"></path></svg>正常</i>
						<i><svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="#EFEFEF"></path></svg>未知</i>
					</div>
				</div>
				<div class="layui-card-body adaptive-table">
			       <table id="listtable" lay-filter="listtable"></table>
			        <script type="text/html" id="listtable-opt">
						<a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
        			</script>
		      </div>
			</div>
		</div>
	</div>
</body>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/pcenter/pgbglist.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&model=pcenter"></script>
<script type="text/html" id="bpgrade">
	{{#
	let color = "#2CBD32";
	color = d.bpgradecode == '0'?'#2CBD32':color;
	color = d.bpgradecode == '6'?'#07B0FF':color;
	color = d.bpgradecode == '1'?'#F5DD0C':color;
	color = d.bpgradecode == '2'?'#FF8D02':color;
	color = d.bpgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>

<script type="text/html" id="dpgrade">
	{{#
	let color = "#2CBD32";
	color = d.dbgradecode == '0'?'#2CBD32':color;
	color = d.dbgradecode == '6'?'#07B0FF':color;
	color = d.dbgradecode == '1'?'#F5DD0C':color;
	color = d.dbgradecode == '2'?'#FF8D02':color;
	color = d.dbgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>


<script type="text/html" id="lpgrade">
	{{#
	let color = "#2CBD32";
	color = d.lpgradecode == '0' ? '#2CBD32' : color;
	color = d.lpgradecode == '6' ? '#07B0FF' : color;
	color = d.lpgradecode == '1' ? '#F5DD0C' : color;
	color = d.lpgradecode == '2' ? '#FF8D02' : color;
	color = d.lpgradecode == '3' ? '#FF2222' : color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>

<script type="text/html" id="ascvd">
	{{#
	let color = "#2CBD32";
	color = d.adgradecode == '0'?'#2CBD32':color;
	color = d.adgradecode == '1'?'#F5DD0C':color;
	color = d.adgradecode == '2'?'#FF8D02':color;
	color = d.adgradecode == '3'?'#FF2222':color;
	}}
	<svg t="1650935930901" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4457" width="16" height="16"><path d="M514.048 128q79.872 0 149.504 30.208t121.856 82.432 82.432 122.368 30.208 150.016q0 78.848-30.208 148.48t-82.432 121.856-121.856 82.432-149.504 30.208-149.504-30.208-121.856-82.432-82.432-121.856-30.208-148.48q0-79.872 30.208-150.016t82.432-122.368 121.856-82.432 149.504-30.208z" p-id="4458" fill="{{color}}"></path></svg>
	{{#   }}
</script>
</html>