<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"><meta http-equiv="Expires" content="0"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Cache-control" content="no-cache"><meta http-equiv="Cache" content="no-cache">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}">
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <script type="text/javascript">
        var authUser = JSON.parse('${json(authUser)}');
    </script>
    <style type="text/css">
        body{width:100%;height: 100%;}
        .layui-form-label{width:auto;}
        .layui-form-item .layui-inline .layui-form-label{padding-right:5px}
        .layui-disabled, .layui-disabled:hover{border-color:#d2d2d2 !important}
        .layui-disabled{border-color:#cccccc;background:#efefef}
        .layui-form-item .layui-inline,.layui-form-item .layui-btn.user-search{margin-bottom: 0}
        .initbox{
            position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
        }
        .initbox > span[class*="-icon"] {
            width: 45px;
            height: 45px;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
        }
        .mainBox{
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction:column;
            flex-direction: column;
            height: 100vh;
        }
        .user-search{
            flex-shrink: 0;
        }
        .tableBox{
            flex:1;
            -ms-flex:1;
            padding:10px 10px 10px 10px;
            overflow: auto;
            height: 0;
        }
        .layui-card-header {
            line-height: normal;
            height: 36px;
        }
        .tableBox .outherTable{
            width: 100%;
        }
        .tableBox .outherTable tr.outherTableTr{
            width: 100%;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction:row;
            flex-direction: row;
            height: 100vh;
        }
        .tableBox .outherTable .rowTitle{
            width:280px;
            flex-basis:280px;
            flex-shrink: 2;
        }
        .tableBox .outherTable .rowContent{
            -ms-flex: 1;
            flex: 1;
            width: 0;
            flex-shrink: 0;
            flex-basis:280px;

            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction:row;
            flex-direction: row;
            justify-content: flex-start;
        }


        table.titleTable {width:100%;border: solid #000000 1px;border-collapse: collapse}
        table.titleTable td {height: 32px; line-height: 32px;text-align: center;border: solid 1px #000000;}

        table.rowTable {min-width:260px;border-collapse: collapse;border: solid #000000 1px;border-left: none;}
        table.rowTable td {height: 32px; line-height: 32px;text-align: center;border: solid 1px #000000;border-left: none;}

        td.tle{font-weight: 600;background-color: #F9F9F9}

    </style>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="mainBox">
    <div class="layui-card search-box">
        <div class="layui-card-header layui-form">
            <div class="layui-col-xs4 layui-col-md4 " style="color: #28333E; line-height: 36px;font-weight: 600;">三高共管患者随访服务整合信息表</div>
            <div class="layui-col-xs8 layui-col-md8 " style="text-align: right;">
                <div class="layui-form-item" style="line-height: 36px;">
                    <div class="layui-inline">
                        <label class="layui-form-label">年度</label>
                        <div class="layui-input-inline">
                            <input type="text" name="nd" id="nd" readonly="readonly"  placeholder="年度" autocomplete="off" class="layui-input date-it">
                        </div>
                    </div>
                    <button class="layui-btn layui-btn-normal user-search" data-method="reload">
                        <i class="layui-icon layui-icon-search "></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div id="chartBox" class="tableBox">
        <table class="outherTable">
            <tr class="outherTableTr">
                <td class="rowTitle">
                    <table class="titleTable">
                        <tr><td colspan="2" class="tle">随访日期</td></tr>
                        <tr><td colspan="2" class="tle">随访方式</td></tr>
                        <tr><td colspan="2" class="tle">症状</td></tr>
                        <tr><td rowspan="6" class="tle">体征</td><td class="tle">血压（mmHg）</td></tr>
                        <tr><td class="tle">体重（kg）</td></tr>
                        <tr><td class="tle">体质指数(kg/m2)</td></tr>
                        <tr><td class="tle">心率（次/每分钟）</td></tr>
                        <tr><td class="tle">足背动脉搏动</td></tr>
                        <tr><td class="tle">其他</td></tr>
                        <tr><td rowspan="8" class="tle">生活方式指导</td><td class="tle">日吸烟量（支）</td></tr>
                        <tr><td class="tle">日饮酒量（两）</td></tr>
                        <tr><td class="tle">运动(次/周)</td></tr>
                        <tr><td class="tle">运动(分钟/次)</td></tr>
                        <tr><td class="tle">主食（克/天）</td></tr>
                        <tr><td class="tle">摄盐情况（咸淡）</td></tr>
                        <tr><td class="tle">心理调整</td></tr>
                        <tr><td class="tle">遵医行为</td></tr>
                        <tr><td colspan="2" class="tle">服药依从性</td></tr>
                        <tr><td colspan="2" class="tle">药物不良反应</td></tr>
                        <tr><td colspan="2" class="tle">低血糖反应</td></tr>
                        <tr><td rowspan="2" class="tle">辅助检查</td><td class="tle">空腹血糖值(mmol/L)</td></tr>
                        <tr><td class="tle">糖化血红蛋白(%)</td></tr>
                        <tr><td rowspan="5" class="tle">协诊或转诊到位情况</td><td class="tle">有无转诊</td></tr>
                        <tr><td class="tle">原   因</td></tr>
                        <tr><td class="tle">机构及科别</td></tr>
                        <tr><td class="tle">联系人及电话</td></tr>
                        <tr><td class="tle">结果</td></tr>
                        <tr><td colspan="2" class="tle">此次随访分类</td></tr>
                        <tr><td colspan="2" class="tle">下一步管理措施</td></tr>
                        <tr><td colspan="2" class="tle">下次随访日期</td></tr>
                        <tr><td colspan="2" class="tle">备注</td></tr>
                    </table>
                </td>
                <td class="rowContent" id="rowContent">

                </td>
            </tr>
        </table>
    </div>
</div>
</body>

<script>
    String.prototype.toFloat = function(fix) {
        let tofix = 0;
        if(fix){
            tofix = fix;
        }
        var val = this;
        if(val.length > 0){
            return  parseFloat(val).toFixed(tofix)
        }else{
            return 0;
        }
    };
    String.prototype.toInt = function() {
        var val = this;
        if(val.length > 0){
            return  parseInt(val);
        }else{
            return 0;
        }
    };

    String.prototype.eq = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Map.prototype.eq = function (key) {
        return this.hasOwnProperty(key);
    }
    Array.prototype.of = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Array.prototype.removeByVal = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }

    var sffs = {"":"","1":"门诊","2":"门诊","3":"电话"};

    var gxyZz = {"":"","1":"无症状","2":"头痛头晕","3":"恶心呕吐","4":"眼花耳鸣","5":"呼吸困难","6":"心悸胸闷","7":"鼻衄出血不止","8":"四肢发麻","9":"下肢水肿","10":"其他"};
    var gxzZz = {"":"","1":"无症状","2":"神疲乏力","3":"失眠健忘","4":"胸闷气短","5":"心慌胸痛","6":"口角歪斜","7":"肢体麻木","0":"其他"};
    var tnbZz = {"":"","1":"无症状","2":"多饮","3":"多食","4":"多尿","5":"视力模糊","6":"感染","7":"手脚麻木","8":"下肢浮肿","9":"体重明显下降","10":"其他"};

    var zdm = {"":"","1":"触及正常","2":"减弱","3":"消失"};
    var zdmyc = {"":"","1":"双侧","2":"左侧","3":"右侧"};
    var syqk = {"":"","1":"轻","2":"中","3":"重"};

    var xltz = {"":"","1":"良好","2":"一般","3":"差"};
    var zyxw = {"":"","1":"良好","2":"一般","3":"差"};
    var fyycx = {"":"","1":"规律","2":"间断","3":"不服药"};

    var sffl = {"":"","1":"控制满意","2":"控制不满意","3":"不良反应 ","4":"并发症"};
    var xybglcs = {"":"","1":"常规随访","2":"第1次控制不满意2周随访","3":"两次控制不满意转诊随访","3":"紧急转诊"};

    function rqYmd(str){
       if(str && str.trim().length > 0){
           return  str.replace(/ \d+(:\d+){2}/,'');
       }else{
           return '';
       }
    }
    function getZz (gtp,zz,qt){
        if(zz=="10" || zz == "0"){
            return "其他：" + qt;
        }
        let zzArr = zz.Split(",");
        let result = "";
        for (let i = 0; i < zzArr.length; i++) {
            if(i==0){
                if(gtp=="gxy"){ result += gxyZz[zzArr[i]]; }
                if(gtp=="gxz"){ result += gxzZz[zzArr[i]]; }
                if(gtp=="tnb"){ result += tnbZz[zzArr[i]];}
            }else{
                if(gtp=="gxy"){ result += (',' +gxyZz[zzArr[i]]); }
                if(gtp=="gxz"){ result += (',' +gxzZz[zzArr[i]]); }
                if(gtp=="tnb"){ result += (',' +tnbZz[zzArr[i]]); }
            }
        }
        return result;
    }
    function getNStr(str){
        if(str && str.trim().length > 0){
            return ","+ str;
        }else{
            return "";
        }
    }
</script>
<script type="text/html" id="rowTableTemplet">
    {{#  layui.each(d.data, function(index, item){ }}
    <table class="rowTable">
        <tr><td class="cnt">{{rqYmd(item.sfrq)}}</td></tr>
        <tr><td class="cnt">{{sffs[item.sffs]}}</td></tr>
        <tr><td class="cnt">{{getZz(item.gtp,item.zhengzhuang,item.zzqt)}}</td></tr>
        <tr><td class="cnt">{{item.ssy}} / {{item.ssy}}</td></tr>
        <tr><td class="cnt">{{item.tizhong}}</td></tr>
        <tr><td class="cnt">{{item.bmi}}</td></tr>
        <tr><td class="cnt">{{item.xinlv}}</td></tr>
        <tr><td class="cnt">{{zdm[item.zbdmbd]}} {{zdmyc[item.zbdmbdyc]}}</td></tr>
        <tr><td class="cnt">{{item.tzqt}}</td></tr>
        <tr><td class="cnt">{{item.rxylmb}}</td></tr>
        <tr><td class="cnt">{{item.ryjlmb}}</td></tr>
        <tr><td class="cnt">{{item.ydcsmb}}</td></tr>
        <tr><td class="cnt">{{item.ydscmb}}</td></tr>
        <tr><td class="cnt">{{item.zsqkmb}}</td></tr>
        <tr><td class="cnt">{{syqk[item.syqkmb]}}</td></tr>
        <tr><td class="cnt">{{xltz[item.xltz]}}</td></tr>
        <tr><td class="cnt">{{zyxw[item.zyxw]}}</td></tr>
        <tr><td  class="cnt">{{fyycx[item.fyycx]}}</td></tr>
        <tr><td  class="cnt">{{item.ywblfy=="2" ? "有" : (item.ywblfy=="1" ? "无": "")}}{{getNStr(item.ywblfyy)}}</td></tr>
        <tr><td  class="cnt">{{item.dxtfy}}</td></tr>
        <tr><td class="cnt">{{item.kfxt}}</td></tr>
        <tr><td class="cnt">{{item.thxhdb}}</td></tr>
        <tr><td class="cnt">{{item.sfzz == "2" ? "有" : "无"}}</td></tr>
        <tr><td class="cnt">{{item.zzyy}}</td></tr>
        <tr><td class="cnt">{{item.zzjgjks}}</td></tr>
        <tr><td class="cnt">{{item.zzlxr}},{{item.zzlxfs}}</td></tr>
        <tr><td class="cnt">{{item.zzjg=="2" ? "未到位" : (item.zzjg=="1" ? "到位": "")}}</td></tr>
        <tr><td  class="cnt">{{sffl[item.sffl]}}</td></tr>
        <tr><td  class="cnt">{{xybglcs[item.xybglcs]}}</td></tr>
        <tr><td  class="cnt">{{rqYmd(item.xcsfrq)}}</td></tr>
        <tr><td  class="cnt">{{item.beizhu}}</td></tr>
    </table>
    {{#  }); }}
</script>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/pcenter/sfzhxxb.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>


<!--<table class="titleTable">-->
<!--    <tr><td class="cnt">随访日期</td></tr>-->
<!--    <tr><td class="cnt">随访方式</td></tr>-->
<!--    <tr><td class="cnt">症状</td></tr>-->
<!--    <tr><td class="cnt">血压（mmHg）</td></tr>-->
<!--    <tr><td class="cnt">体重（kg）</td></tr>-->
<!--    <tr><td class="cnt">身高(cm)</td></tr>-->
<!--    <tr><td class="cnt">体质指数(kg/m2)</td></tr>-->
<!--    <tr><td class="cnt">腰围(cm)</td></tr>-->
<!--    <tr><td class="cnt">臀围(cm)</td></tr>-->
<!--    <tr><td class="cnt">心率（次/每分钟）</td></tr>-->
<!--    <tr><td class="cnt">足背动脉搏动</td></tr>-->
<!--    <tr><td class="cnt">其他</td></tr>-->
<!--    <tr><td class="cnt">日吸烟量（支）</td></tr>-->
<!--    <tr><td class="cnt">日饮酒量（两）</td></tr>-->
<!--    <tr><td class="cnt">运   动</td></tr>-->
<!--    <tr><td class="cnt">主食（克/天）</td></tr>-->
<!--    <tr><td class="cnt">摄盐情况（咸淡）</td></tr>-->
<!--    <tr><td class="cnt">心理调整</td></tr>-->
<!--    <tr><td class="cnt">遵医行为</td></tr>-->
<!--    <tr><td  class="cnt">服药依从性</td></tr>-->
<!--    <tr><td  class="cnt">药物不良反应</td></tr>-->
<!--    <tr><td  class="cnt">低血糖反应</td></tr>-->
<!--    <tr><td class="cnt">检查日期</td></tr>-->
<!--    <tr><td class="cnt">空腹血糖值(mmol/L)</td></tr>-->
<!--    <tr><td class="cnt">糖化血红蛋白(%)</td></tr>-->
<!--    <tr><td class="cnt">有无转诊</td></tr>-->
<!--    <tr><td class="cnt">原   因</td></tr>-->
<!--    <tr><td class="cnt">机构及科别</td></tr>-->
<!--    <tr><td class="cnt">联系人及电话</td></tr>-->
<!--    <tr><td class="cnt">结果</td></tr>-->
<!--    <tr><td  class="cnt">此次随访分类</td></tr>-->
<!--    <tr><td  class="cnt">下一步管理措施</td></tr>-->
<!--    <tr><td  class="cnt">下次随访日期</td></tr>-->
<!--    <tr><td  class="cnt">备注</td></tr>-->
<!--</table>-->