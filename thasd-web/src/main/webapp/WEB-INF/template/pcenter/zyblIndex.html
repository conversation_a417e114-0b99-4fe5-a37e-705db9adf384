<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
	html,body{height:100%;}

	.initbox{
		position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
	}
	.initbox > span[class*="-icon"] {
		width: 45px;
		height: 45px;
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		margin: auto;
	}

	.layui-tab{margin:0 0!important;}
	.mainTab{
		height: 100%;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
	}
	.layui-tab-content-fix{flex-shrink:4;height: 5px;}

	.layui-tab-content {
		-ms-flex: 1;
		flex: 1;
		height: 0;
		flex-shrink: 0;
		flex-basis: 40px;
		padding:0 10px !important;
		overflow: auto;
	}
	.layui-tab-title{
		flex-shrink: 2;
		height: 43px;
	}

	table{
		margin: 0 auto;
		width:100%;
		height:auto;
		border-spacing: 1px;
		background-color: #000;
	}

	table tr th,table tr td {
		background-color: #ffffff;
	}

	table table.innerTable{
		border:none;
		background-color: #CFCFCF;
	}
	table tr th,table tr td.head {
		height:36px;
		font-size:14px;
		font-weight:540;
	}
	table tr td.tl {background-color: #efefef;font-weight: 600}
	table tr td.left {text-align: left}
	table tr td {
		height:28px;
		font-size:13px;
		font-weight:500;
		text-align: left;
		text-indent:6px;
	}
	table tr th.head,table tr td.head{font-size:13px;font-weight:600;}
	table tr.title td{background:#efefef;font-size:14px;height:36px;}

	table.mxtable{
		width:100%;padding:0;margin:0;border: 0;
		background-color: #CFCFCF;
	}

	.innerTable td.head,.mxtable th{text-align: center;font-size:13px;font-weight:600;background-color: #efefef;}
	.mxtable td{text-align: center;}

	table.sigtable,table.sigcoltable{
		width:100%;padding:0;margin:0;border: 0;
		background-color: #FFFFFF;
	}
	table.sigcoltable{

	}
	.sigcoltable td .tdBox{
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}
	.sigcoltable td span.tdt{
		text-indent:6px;
		padding:0;
		border-bottom:none;
		width: auto;
		min-width: auto;
		font-size: 14px!important;
		height: 18px!important;
		margin-bottom: 0!important;
	}
	.sigcoltable td span.nlt{
		flex: auto;
		height: 18px!important;
		margin-bottom: 0!important;
	}

	.sigtable td{
		text-align: left;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;

	}

	.sigcoltable td,.sigtable td{font-size: 14px!important;}

	.noborder{border:none;}
	.nobg{background-color: #FFFFFF;}

	table span{ padding: 0px 8px 0 8px;text-indent: 0; border-bottom: solid 1px #000000;display: inline-block;height: 20px; min-width: 32px;font-size: 13px;margin-bottom: 5px;}
	table span.m100{min-width: 100px;}
	table span.m75{min-width: 100px;}
	table span.m50{min-width: 100px;}
	table span.m35{min-width: 100px;}
	table span.c{text-align: center;}
	table span u {text-decoration: none;}

	.col{text-align: center;font-weight: 500;background-color:#efefef;font-size: 14px!important; height: 36px;}

	.layui-tab-item .list_table{}
	.layui-tab-item .list_table:first-child{}
	.layui-tab-item .c_hr{width: 960px;margin:0 auto;border: dotted 1px #b9b9b9;height:0px;margin-top:10px;margin-bottom: 10px;}
	.layui-tab-item .c_hr:last-child{ border: none;}
	.list_table_nob{
		width:100%;padding:0;margin:0;border: 0;
		background-color: #FFFFFF;
	}
	.list_table_nob table.inner{
		width:66%;
		border:none;
		background-color: #FFFFFF;
	}
	tr.yc td{
		background-color: #fadede;
	}
	.emptyBox{
		width: 100%;
		height: 100%;
		min-height: 400px;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 600;
	}

	table,table td,table th{
		font-family: "宋体",SimSun;
	}
	b.qtzdcls{color: #666666;}
	u.mw70{
		display: flex;
		width: 36px;
		border-bottom: solid 1px #000000;
		text-decoration: none;
		justify-content: center;
		padding: 0;
		text-indent: 0;
	}
	td.tybw{
		width: 81px;text-align: right;font-weight:bold;padding: 0;
		vertical-align: text-top;
		line-height: 24px;
		text-indent:0;
	}
	td.nr{
		vertical-align: text-top;
		padding-left:0;
		text-indent: 0;
		line-height: 24px;
	}
	td.nr1{
		text-indent: 0;
		line-height: 16px;
		height: 20px;
	}
	.zymxbox{
		vertical-align: text-top;
		min-height: 336px;
	}
	.zymx{
		width: 100%;
		height: auto;
		display: grid;
		grid-template-columns: 33.33% 33.33% 33.33% ;
		justify-content: space-around;
		align-items: center;
	}
	.zymx p.zymxmc{
		font-size: 15px;
		margin-top:12px;
		white-space: nowrap;
		font-size: clamp(12px, 1vw, 15px);
	}
	.col1{background: #fff !important;}
</style>
<script type="text/javascript">
	var authUser = JSON.parse('${json(authUser)}');
</script>
</head>
<body style="padding: 0 5px 0 5px;">
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);|| '-' }}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);|| '-' }}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;|| '-' }}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;|| '-' }}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></u></span></div>
	 <div class="layui-tab mainTab" lay-filter="mainTab">
		 <ul class="layui-tab-title" id="mainTab-title">
			 <li class="layui-this" val="basy" load-stetup="0">病案首页</li>
			 <li val="ryjl" load-stetup="0">入院记录</li>
			 <li val="scbcjl" load-stetup="0">首次病程记录</li>
			 <li val="rcbcjl" load-stetup="0">日常病程记录</li>
<!--			 <li val="yzd" load-stetup="0">医嘱单</li>-->
<!--			 <li val="lsyzd" load-stetup="0">临时医嘱单</li>-->
			 <li val="jcbg" load-stetup="0">检查报告</li>
			 <li val="jybg" load-stetup="0">检验报告</li>
			 <li val="cyjl" load-stetup="0">出院记录</li>
		 </ul>
		 <div class="layui-tab-content-fix"></div>
		 <div class="layui-tab-content" id="mainTab-content">
			 <div class="layui-tab-item layui-show" id="basy">
			 </div>
			 <div class="layui-tab-item" id="ryjl">
			 </div>
			 <div class="layui-tab-item" id="scbcjl">
			 </div>
			 <div class="layui-tab-item" id="rcbcjl">
			 </div>
<!--			 <div class="layui-tab-item" id="yzd">-->
<!--			 </div>-->
<!--			 <div class="layui-tab-item" id="lsyzd">-->
<!--			 </div>-->
			 <div class="layui-tab-item" id="jcbg">
			 </div>
			 <div class="layui-tab-item" id="jybg">
			 </div>
			 <div class="layui-tab-item" id="cyjl">
			 </div>
		 </div>
		 <div class="layui-tab-content-fix"></div>
	 </div>
</body>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/pcenter/zyblIndex.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&id=${param.id}&orgcode=${param.orgcode}&bah=${param.bah}&zyh=${param.zyh}&model=pcenter"></script>
<script type="text/html" id="basy-templet">
	{{#  if(d ){ }}
		<table class="list_table_nob" style="width:960px;margin: 0 auto;margin-bottom: 3px;margin-top: 10px;">
			<tr >
				<td class="nr" style="text-align: center;font-size:20px;font-weight: bold;letter-spacing: 3px;">{{d.orgname || '' }}</td>
			</tr>
			<tr >
				<td class="nr" style="text-align: center;font-size:20px;font-weight: bold;letter-spacing: 3px;">病案首页</td>
			</tr>
			<tr >
				<td class="nr" style="font-size:16px;font-weight: bold;text-align: right">病案号：{{d.bah || '' }}</td>
			</tr>
		</table>
		<table style="width: 960px;">
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">医疗机构名称<span class="m100"><u>{{d.orgname|| '-' }}</u></span>机构代码<span class="m100"><u>{{d.baorgcode|| '-' }}</u></span></td>
					</tr>
					<tr>
						<td class="">医疗付费方式<span class="m100"><u>{{d.ylfffs|| '-' }}</u></span>健康卡号<span class="m100"><u>{{d.jkkno|| '-' }}</u></span>第<span class="c"><u>{{d.djczy|| '-' }}</u></span>次住院</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigcoltable">
					<tr>
						<td class="" ><div class="tdBox"><span class="tdt">姓名</span><span class="nlt"><u>{{d.name|| '-' }}</u></span></div></td>
						<td class="" ><div class="tdBox"><span class="tdt">性别</span><span class="nlt"><u>{{d.sex|| '-' }}</u></span></div></td>
						<td class="" ><div class="tdBox"><span class="tdt">出生日期</span><span class="nlt"><u>{{ new Date(d.csrq || '-').Format("yyyy年MM月dd日")}}</u></span></div></td>
						<td class="" ><div class="tdBox"><span class="tdt">年龄</span><span class="nlt"><u>{{d.age|| '-' }}{{d.ageunit|| '' }}</u></span></div></td>
						<td class="" ><div class="tdBox"><span class="tdt">国籍</span><span class="nlt"><u>{{d.gj|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">出生地</span><span class="nlt m75"><u>{{d.csdprovince|| '-' }}{{d.csdcity|| '' }}{{d.csdarea|| '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">籍贯</span><span class="nlt m75"><u>{{d.jgprovince|| '-' }}{{d.jgcity|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">民族</span><span class="nlt"><u>{{d.minzu|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class=""><div class="tdBox"><span class="tdt">身份证件类别</span><span class="nlt"><u>{{d.idtype|| '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">证件号码</span><span class="nlt"><u>{{d.idcardno|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">职业</span><span class="nlt"><u>{{d.zy|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">婚姻</span><span class="nlt"><u>{{d.hy|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class="" colspan="3"><div class="tdBox"><span class="tdt">现住址</span><span class="nlt m75"><u>{{d.xzzaddress|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">电话</span><span class="nlt"><u>{{d.lxdh|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">邮编</span><span class="nlt"><u>{{d.xzzpostcode|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class="" colspan="4"><div class="tdBox"><span class="tdt">户口地址</span><span class="nlt m75"><u>{{d.hkaddress|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">邮编</span><span class="nlt"><u>{{d.hkpostcode|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class=""><div class="tdBox"><span class="tdt">工作单位</span><span class="nlt m75"><u>{{d.gzdw|| '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">地址</span><span class="nlt m75"><u>{{d.dwaddress|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">电话</span><span class="nlt"><u>{{d.dwdh|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">邮编</span><span class="nlt"><u>{{d.dwpostcode|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class=""><div class="tdBox"><span class="tdt">联系人姓名</span><span class="nlt"><u>{{d.lxrname|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">与患者关系</span><span class="nlt"><u>{{d.lxrgx|| '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">地址</span><span class="nlt"><u>{{d.lxraddress|| '-' }}</u></span></div></td>
						<td class=""><div class="tdBox"><span class="tdt">电话</span><span class="nlt"><u>{{d.lxrdh|| '-' }}</u></span></div></td>
					</tr>
					<tr>
						<td class="" colspan="5">入院途径：{{d.rytjjgmc == '' ? '' :  '其他医疗机构转入，转诊医疗机构名称'|| '' }}<span><u>{{d.rytjjgmc!= '' ? d.rytjjgmc || '-' : d.rytj || '-' }}</u></span></td>
					</tr>
					<tr>
						<td class="" colspan="5">入院时间<span><u>{{ new Date(d.rysj || '-').Format("yyyy年MM月dd日 hh时")}}</u></span>入院科别<span><u>{{d.rykb|| '-' }}</u></span>病房<span><u>{{d.rybf|| '-' }}</u></span>专科科别<span><u>{{d.zykb|| '-' }}</u></span></td>
					</tr>
					<tr>
						<td class="" colspan="5">出院时间<span><u>{{ new Date(d.cysj || '-').Format("yyyy年MM月dd日 hh时")}}</u></span>出院科别<span><u>{{d.cykb|| '-' }}</u></span>病房<span><u>{{d.cybf|| '-' }}</u></span>实际住院<span><u>{{d.sjzyts|| '-' }}</u></span>天</td>
					</tr>
					<tr>

						<td class="" colspan="3"><div class="tdBox"><span class="tdt">门（急）诊诊断</span><span class="nlt"><u>{{ d.mjzZd ? d.mjzZd.zdmc || '-' : '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">疾病编码</span><span class="nlt"><u>{{  d.mjzZd ? d.mjzZd.zdcode || '-' : '-'  }}</u></span></div></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="col" colspan="5">出院诊断</td>
			<td class="col" colspan="2">疾病编码</td>
			<td class="col" colspan="4">入院病情</td>
		</tr>
		{{#  if(d.cyzdList.length == 0 ){ }}
		<tr>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="5">-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="2">-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="4">-</td>
		</tr>
		{{#  }; }}
		{{#  layui.each(d.cyzdList, function(index, item){ }}
		<tr>
			<td class="nr" style="text-indent: 12px;" colspan="5">{{item.cyzdfl == '2' ? '<b>主要诊断：</b>' : '<b class="qtzdcls">其他诊断：</b>'}}{{item.zdmc|| '-'}}</td>
			<td class="nr" style="text-indent: 12px;" colspan="2">{{item.zdcode|| '-'}}</td>
			<td class="nr" style="text-indent: 12px;" colspan="4">{{item.rybq|| '-'}}</td>
		</tr>
		{{#  }); }}
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigcoltable">
					<tr>
						<td class="" colspan="3"><div class="tdBox"><span class="tdt">损伤、中毒的外部原因：</span><span class="nlt"><u>{{ d.sszdZd ? d.sszdZd.zdmc || '-' : '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">疾病编码</span><span class="nlt"><u>{{  d.sszdZd ? d.sszdZd.zdcode || '-' : '-'  }}</u></span></div></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigcoltable">
					<tr>
						<td class="" colspan="3"><div class="tdBox"><span class="tdt">病理诊断：</span><span class="nlt"><u>{{ d.bllZd ? d.bllZd.zdmc || '-' : '-' }}</u></span></div></td>
						<td class="" colspan="2"><div class="tdBox"><span class="tdt">疾病编码</span><span class="nlt"><u>{{  d.bllZd ? d.bllZd.zdcode || '-' : '-'  }}</u></span></div></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">药物过敏：{{d.sfywgm == '2' ? '有' : '无'}}，过敏药物：<span class="c"><u>{{d.gmyw || '无'}}</u></span>，死亡患者尸检：{{d.swhzsj == '1' ? '是' : '否'}}</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">血型：{{d.xuexing || '-'}}&nbsp;&nbsp;&nbsp;Rh：{{d.rh || '-'}}</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">护理级别：1.特级护理<span class="c"><u>{{d.tjhlts || '-'}}</u></span>天&nbsp;&nbsp;&nbsp;&nbsp;2.Ⅰ护理<span class="c"><u>{{d.yjhlts || '-'}}</u></span>天&nbsp;&nbsp;&nbsp;&nbsp;3.Ⅱ护理<span class="c"><u>{{d.ejhlts || '-'}}</u></span>天&nbsp;&nbsp;&nbsp;&nbsp;4.Ⅲ护理<span class="c"><u>{{d.sjhlts || '-'}}</u></span>天</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">科主任<span class="c"><u>{{d.kzr || '-'}}</u></span>医疗组长<span class="c"><u>{{d.ylzz || '-'}}</u></span>主任（副主任）医师<span class="c"><u>{{d.zrfzrys || '-'}}</u></span>主治医师<span class="c"><u>{{d.zzys || '-'}}</u></span>住院医师<span class="c"><u>{{d.zyys || '-'}}</u></span></td>
					</tr>
					<tr>
						<td class="">责任护士<span class="c"><u>{{d.zrhs || '-'}}</u></span>进修医师<span class="c"><u>{{d.jxys || '-'}}</u></span>实习医师<span class="c"><u>{{d.sxys || '-'}}</u></span>编码员<span class="c"><u>{{d.bmy || '-'}}</u></span></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">病案质量：{{d.bazl || '-'}}&nbsp;&nbsp;&nbsp;质控医师：<span class="c"><u>{{d.zkys || '-'}}</u></span>质控护士：<span class="c"><u>{{d.zkhs || '-'}}</u></span>质控日期：<span class="c"><u>{{ new Date(d.zkrq || '').Format("yyyy年MM月dd日")}}</u></span></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="col" rowspan="2">手术及操作编码</td>
			<td class="col" rowspan="2">手术及操作日期</td>
			<td class="col" rowspan="2">手术及操作名称</td>
			<td class="col" rowspan="2">手术级别</td>
			<td class="col" rowspan="2">手术类型</td>
			<td class="col" colspan="3">手术及操作人员</td>
			<td class="col" rowspan="2">切口愈合等级</td>
			<td class="col" rowspan="2">麻醉方式</td>
			<td class="col" rowspan="2">麻醉医师</td>
		</tr>
		<tr>
			<td class="col" >术者</td>
			<td class="col" >Ⅰ助</td>
			<td class="col" >Ⅱ助</td>
		</tr>
		{{#  if(d.ssList.length == 0 ){ }}
		<tr>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" >-</td>
		</tr>
		{{#  }; }}
		{{#  layui.each(d.ssList, function(index, item){ }}
		<tr>
			<td class="nr"  style="text-indent: 0px;text-align: center;">{{item.ssjczbm|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{new Date(parseInt(item.ssjczrq)).Format("yyyy年MM月dd日 hh时") || '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.ssjczmc|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.ssjb|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.sslx|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.sz|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.yz|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.ez|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.qkyhdj|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.mzfs|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;">{{item.mzys|| '-'}}</td>
		</tr>
		{{#  }); }}
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">是否为日间手术：{{d.sfwrjss == '1' ? '是' : '否'}}&nbsp;&nbsp;&nbsp;有创呼吸机使用时间：<span class="c"><u>{{FormatMinute(d.ychxjsysj || '0')}}</u></span></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">临床路径：入径情况<span class="c"><u>{{d.rjqk == '1' ? '是' : '否'}}</u></span>&nbsp;&nbsp;&nbsp;完成情况<span class="c"><u>{{d.wcqk == '1' ? '完成' : '退出'}}</u></span>&nbsp;&nbsp;&nbsp;变异情况<span class="c"><u>{{d.byqk == '1' ? '有' : '无'}}</u></span></td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">颅脑损伤患者昏迷时间：入院前<u class="mw70">{{splitStr(d.ryqlnsshmsj,0)}}</u>天<u class="mw70">{{splitStr(d.ryqlnsshmsj,1)}}</u>小时<u class="mw70">{{splitStr(d.ryqlnsshmsj,2)}}</u>分钟&nbsp;&nbsp;&nbsp;入院后<u class="mw70">{{splitStr(d.ryhlnsshmsj,0)}}</u>天<u class="mw70">{{splitStr(d.ryhlnsshmsj,1)}}</u>小时<u class="mw70">{{splitStr(d.ryhlnsshmsj,2)}}</u>分钟</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="col" colspan="3">重症监护病房类型</td>
			<td class="col" colspan="3">进重症监护室时间合计</td>
			<td class="col" colspan="3">出重症监护室时间合计</td>
			<td class="col" colspan="2">合计（小时）</td>
		</tr>
		{{#  if(d.zzjhList.length == 0 ){ }}
		<tr>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">-</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="2">-</td>
		</tr>
		{{#  }; }}
		{{#  layui.each(d.zzjhList, function(index, item){ }}
		<tr>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">{{item.zzjhsbflx|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">{{item.jrsj|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="3">{{item.rybq|| '-'}}</td>
			<td class="nr" style="text-indent: 0px;text-align: center;" colspan="2">{{item.hj|| '-'}}</td>
		</tr>
		{{#  }); }}
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">离院方式：{{d.lyfs || '-' }}{{d.lyfscode == '2' ?  '，拟接收医疗机构名称' : '' }}{{d.lyfscode == '3' ?  '，拟接收医疗机构名称' : '' }}{{d.lyfsnjsjg != '' ? '<span class="c"><u>'+d.lyfsnjsjg || '-' +'</u></span>' : '' }}</td>
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td class="nr" style="text-indent:0;padding:5px 5px !important;" colspan="11">
				<table class="sigtable">
					<tr>
						<td class="">是否有出院31天内再住院计划目的：<span class="c"><u>{{d.sfyzzyjh == '2' ? '有' : '无'}}</u></span>&nbsp;&nbsp;&nbsp;{{d.sfyzzyjh == '2' ? '目的<span class="c"><u>'+ d.zzymd || '-' +'</u></span>' : ''}}</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="ryjl-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table_nob" style="width:768px;margin: 0 auto;border-spacing: 0px;margin-bottom: 10px;margin-top: 10px;">
			<tr >
				<td class="nr" style="border-bottom:solid 1px #000000">姓名：{{item.name || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000">医疗机构：{{item.orgname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000">科室：{{item.deptname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000;width: 150px;">病案号：{{item.bah || '' }}</td>
			</tr>
		</table>
		<table class="list_table_nob" style="width:768px;margin: 0 auto">
			<tr>
				<td class="" colspan="4">
					<table class="inner" style="margin: 0 auto">
						<tr>
							<td class="nr" style="width:40%">姓名：{{item.name || '' }}</td>
							<td class="nr">出生地：{{item.province || '' }}{{item.city || '' }}{{item.area || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">性别：{{item.sex || '' }}</td>
							<td class="nr">职业：{{item.zy || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">年龄：{{item.age || '' }}{{item.ageunit || '' }}</td>
							<td class="nr">入院时间：{{item.rysj || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">民族：{{item.minzu || '' }}</td>
							<td class="nr">记录时间：{{item.jlsj || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">婚姻：{{item.hy || '' }}</td>
							<td class="nr">病史陈述人：{{item.bscsz || '' }}</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>主诉:</b>{{item.zs || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>既往史:</b>{{item.jws || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>个人史:</b>{{item.grs || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>婚姻史:</b>{{item.hys || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>月经生育史:</b>{{item.yjsys || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4"><b>家族史:</b>{{item.jzs || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4" style="text-align: center"><b>体格检查</b></td>
			</tr>
			<tr>
				<td class="nr" colspan="4" >{{item.tgjc || '' }}</br><b>专科检查：</b>{{item.zkqk || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="4" style="text-align: center"><b>辅助检查</b></td>
			</tr>
			<tr>
				<td class="nr" colspan="4" >{{item.fzjc || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="2" style="width: 56%;">&nbsp;</td>
				<td class="nr" colspan="2" >
					<b>初步诊断：</b>
					{{#  layui.each(item.zdList, function(index, item2){ }}
					{{item2.zdmc}}</br>
					{{#  }); }}
				</td>
			</tr>
			<tr>
				<td class="nr" colspan="2" >&nbsp;</td>
				<td class="nr" colspan="2" style="text-align: right">
					<b>医生签名：</b>{{item.ysqm || '' }}
				</td>
			</tr>
		</table>
		<hr class="c_hr"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="cyjl-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table_nob" style="width:768px;margin: 0 auto;border-spacing: 0px;margin-bottom: 10px;margin-top: 10px;">
			<tr >
				<td class="nr" style="border-bottom:solid 1px #000000">姓名：{{item.name || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000" colspan="2">医疗机构：{{item.orgname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000">科室：{{item.deptname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000;width: 150px;">病案号：{{item.bah || '' }}</td>
			</tr>
		</table>
		<table class="list_table_nob" style="width:768px;margin: 0 auto">
			<tr>
				<td class="" colspan="5">
					<table class="inner" style="margin: 0 auto">
						<tr>
							<td class="nr" style="width:40%">姓名：{{item.name || '' }}</td>
							<td class="nr">入院时间：{{item.rysj || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">性别：{{item.sex || '' }}</td>
							<td class="nr">出院时间：{{item.cysj || '' }}</td>
						</tr>
						<tr>
							<td class="nr" style="width:40%">年龄：{{item.age || '' }}{{item.ageunit || '' }}</td>
							<td class="nr">住院天数：{{item.zyts || '' }}天</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>入院情况:</b>{{item.ryqk || '' }}</td>

			</tr>
			<tr>
				<td class="nr" colspan="5">
					<b>入院诊断:</b>
					{{#  layui.each(item.ryzdList, function(index, item2){ }}
					{{item2.zdmc}},
					{{#  }); }}
				</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>诊疗经过:</b>{{item.zljg || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>出院情况:</b>{{item.cyqk || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5">
					<b>出院诊断:</b>
					{{#  layui.each(item.cyzdList, function(index, item2){ }}
					{{item2.zdmc}},
					{{#  }); }}
				</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>出院医嘱:</b>{{item.cyyz || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="2" >&nbsp;</td>
				<td class="nr" colspan="2" style="text-align: right">
					<b>医生签名：</b>{{item.ysqm || '' }}
				</td>
			</tr>
		</table>
		<hr class="c_hr" style="width: 768px;margin: 15px auto;"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>

<script type="text/html" id="scbcjl-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table_nob" style="width:768px;margin: 0 auto;border-spacing: 0px;margin-bottom: 10px;margin-top: 10px;">
			<tr >
				<td class="nr" style="border-bottom:solid 1px #000000">姓名：{{item.name || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000" colspan="2">医疗机构：{{item.orgname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000">科室：{{item.deptname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000;width: 150px;">病案号：{{item.bah || '' }}</td>
			</tr>
		</table>
		<table class="list_table_nob" style="width:768px;margin: 0 auto">
			<tr>
				<td class="nr" colspan="5">记录时间:{{item.jlsj || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5">患者&nbsp;{{item.name || '' }}&nbsp;,{{item.sex || '' }}&nbsp;,{{item.age || '' }}{{item.ageunit || '' }}&nbsp;,{{item.zs || '' }}&nbsp;于{{new Date( item.rysj ||'').Format("yyyy年MM月dd日hh时mm分") || '-' }}收入院。</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>病历特点:</b>{{item.bltd || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>诊断依据:</b>{{item.zdyj || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>鉴别诊断:</b>{{item.jbzd || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5"><b>诊疗计划:</b>{{item.zljh || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="2" >&nbsp;</td>
				<td class="nr" colspan="2" style="text-align: right">
					<b>医生签名：</b>{{item.ysqm || '' }}
				</td>
			</tr>
		</table>
		<hr class="c_hr" style="width: 768px;margin: 15px auto;"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>

<script type="text/html" id="rcbcjl-templet">
	{{#  if(d && d.length > 0){ }}
		{{#  layui.each(d, function(index, item){ }}
		<table class="list_table_nob" style="width:768px;margin: 0 auto;border-spacing: 0px;margin-bottom: 10px;margin-top: 10px;">
			<tr >
				<td class="nr" style="border-bottom:solid 1px #000000">姓名：{{item.name || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000" colspan="2">医疗机构：{{item.orgname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000">科室：{{item.deptname || '' }}</td>
				<td class="nr" style="border-bottom:solid 1px #000000;width: 150px;">病案号：{{item.bah || '' }}</td>
			</tr>
		</table>
		<table class="list_table_nob" style="width:768px;margin: 0 auto">
			<tr>
				<td class="nr" colspan="5">记录时间:{{new Date( item.jlsj ||'').Format("yyyy年MM月dd日hh时mm分") || '-' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="5">{{item.jinr || '' }}</td>
			</tr>
			<tr>
				<td class="nr" colspan="2" >&nbsp;</td>
				<td class="nr" colspan="2" style="text-align: right">
					<b>医生签名：</b>{{item.ysqm || '' }}
				</td>
			</tr>
		</table>
		<hr class="c_hr" style="width: 768px;margin: 15px auto;"/>
		{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="jcbg-templet">
	{{#  if(d && d.length > 0){ }}
	{{#  layui.each(d, function(index, item){ }}
	<table class="list_table" style="width: 852px;margin:15px auto;background-color: #FFF">
		<tr>
			<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
		</tr>
		<tr>
			<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">医学影像学报告单</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;住院号：{{item.zyh || '' }}&nbsp;科室：{{item.deptname || '' }}&nbsp;检查日期：{{ formatDateTime(item.jcsj || '' ,'yyyy年MM月dd日')  }}</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
		</tr>
		<tr>
			<td class="nr1" colspan="10">临床诊断：{{item.zd || '-' }}&nbsp;检查部位：{{item.jcbw || '-' }}&nbsp;检查目的：{{item.jcmd || '-' }}&nbsp;检查方法/技术：{{item.jcyq || '-' }}</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
		</tr>
		<tr><td style="height: 15px;"></td></tr>
		<tr>
			<td class="tybw" style="width: 125px;vertical-align: top;"><b style="letter-spacing: 1px;">影像医学表现</b>：</td>
			<td class="nr" colspan="9">
				<div  class="zymxbox" style="min-height:160px">{{item.yxxbx || '' }}</div>
			</td>
		</tr>
		<tr>
			<td class="tybw" style="width: 125px;vertical-align: top;"><b style="letter-spacing: 1px;">影像医学诊断</b>：</td>
			<td class="nr" colspan="9">
				<div  class="zymxbox" style="min-height:160px">{{item.yxxzd || '' }}</div>
			</td>
		</tr>
		<tr><td style="height: 15px;"></td></tr>
		<tr>
			<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
		</tr>
		<tr>
			<td style="text-indent: 0;" colspan="10">报告医师：{{item.bgys || '' }}&nbsp;审核医师：{{item.shys || '' }}&nbsp;报告时间：{{formatDateTime(item.bgsj || '' ,'yyyy年MM月dd日') }}&nbsp;</td>
		</tr>
	</table>
	<!--		<hr class="c_hr" style="width: 852px;margin-top: 20px;"/>-->
	{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
<script type="text/html" id="jybg-templet">
	{{#  if(d && d.length > 0){ }}
	{{#  layui.each(d, function(index, item){ }}
	<table class="list_table" style="width: 852px;margin:15px auto;background-color: #FFF">
		<tr>
			<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">{{item.orgname}}</td>
		</tr>
		<tr>
			<td class="tl1" colspan="10" style="text-align: center;font-size:20px;letter-spacing: 3px;">检验报告单</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10">姓名：{{item.name || '' }}&nbsp;&nbsp;性别：{{item.sex || '' }}&nbsp;&nbsp;年龄：{{item.age || '' }}{{item.ageunit || '' }}&nbsp;科室：{{item.deptname || '' }}&nbsp;&nbsp;住院号：{{item.zyh || '' }}</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
		</tr>
		<tr>
			<td class="nr1" colspan="10">标本类型：{{item.bblx || '' }}&nbsp;检验项目：{{item.jymc || '' }}</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:1px;border-top:solid 1px #000000;"></td>
		</tr>
		<tr>
			<td class="nr1" colspan="10">临床诊断：{{item.zd || '' }}</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:2px;border-top:solid 2px #000000;"></td>
		</tr>
		<tr>
			<td style="text-indent:0;padding:0 0!important;" colspan="10">
				<div  class="zymxbox">
					<table class="mxtable" style="border-spacing:0;border:0;background: #fff;">
						<tr>
							<th class="col1">序号</th>
							<th class="col1">名称</th>
							<th class="col1">检验结果</th>
							<th class="col1">异常结果提示</th>
							<th class="col1">参考范围</th>
						</tr>
						{{#  layui.each(item.mxList, function(index, item2){ }}
						<tr class="{{item2.jyjgbz == 'H' ?  'yc' : '' }}{{item2.jyjgbz == 'L' ?  'yc' : '' }}">
							<td >{{index+1 }}</td>
							<td >{{item2.lisxm || '' }}</td>
							<td >{{item2.jyjg || '' }}{{item2.jydw || '' }}</td>
							<td >{{item2.jyjgbz == "H" ?  '↑' : '' }}{{item2.jyjgbz == "L" ?  '↓' : '' }}</td>
							<td >{{item2.jyckz || '' }}</td>
						</tr>
						{{#  }); }}
					</table>
				</div>
			</td>
		</tr>
		<tr>
			<td class="nr1" colspan="10" style="height:1px;border-top:solid 2px #000000;"></td>
		</tr>
		<tr>
			<td class="nr1" colspan="2">报告日期：{{ formatDateTime(item.jybgrq || '' ,'yyyy年MM月dd日')  }}</td>
			<td class="nr1" colspan="2">申请：{{ item.createusername || ''  }}</td>
			<td class="nr1" colspan="2">检验：{{item.jybgys || '' }}</td>
			<td class="nr1" colspan="2">审核：{{item.shys || '' }}</td>
			<td class="nr1" colspan="2"></td>
		</tr>
	</table>
	<!--		<hr class="c_hr" style="width: 852px;"/>-->
	{{#  }); }}
	{{#  }else{ }}
	<div class="emptyBox">暂无数据</div>
	{{#  }; }}
</script>
</html>