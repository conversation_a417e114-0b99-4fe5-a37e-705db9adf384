<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}" class="ipt-hidden">
<input type="hidden" name="patientid" id = "patientid" value="${patientid}" class="ipt-hidden">
<input type="hidden" name="createtime"  value="" class="ipt-hidden">
<input type="hidden" name="createid"  value="" class="ipt-hidden">
<input type="hidden" name="createname"  value="" class="ipt-hidden">
<input type="hidden" name="orgid" id = "orgid" value="${authUser.orgid}" class="ipt-hidden">
<input type="hidden" name="orgcode" id = "orgcode" value="${authUser.orgcode}" class="ipt-hidden">
<input type="hidden" name="orgname" id = "orgname" value="${authUser.orgname}" class="ipt-hidden">
<input type="hidden" name="proid" id = "proid" class="ipt-hidden">
<input type="hidden" name="prorate" id = "prorate" class="ipt-hidden">
<input type="hidden" name="proname" id = "proname" class="ipt-hidden">
<input type="hidden" name="ratelevel" id = "ratelevel" class="ipt-hidden">
<input type="hidden" name="id" id = "id" class="ipt-hidden">
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">服务项目</label>
  <div class="layui-input-block" >
    <select name="pronamecode" id="pronamecode"  accept-name="proname" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择服务项目或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_serve){
      %>
      <option value="${mzBean.val}" idv="${mzBean.id}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">服务频次</label>
  <div class="layui-input-block" >
    <select name="proratecode" id="proratecode" accept-name="prorate" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择服务频次或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_rate){
      %>
        <%if(mzBean.val == "#") {%>
        <option value="${mzBean.val}" ratelevel="${mzBean.ratelevel}" realtext = "指定时间">指定时间</option>
        <%}else{%>
        <option value="${mzBean.val}" ratelevel="${mzBean.ratelevel}" realtext = "${mzBean.name}">${mzBean.name}</option>
        <%}%>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">复诊时间</label>
  <div class="layui-input-block" >
    <input type="text" name="executetime" id="executetime" disabled autocomplete="off" placeholder="请输入选择复诊时间" value="" class="layui-input layui-disabled">
  </div>
</div>