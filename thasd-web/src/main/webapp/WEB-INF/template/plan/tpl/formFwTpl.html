<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}" class="ipt-hidden">
<input type="hidden" name="orgid" id = "orgid" value="000000000000000000000000000000000000" class="ipt-hidden">
<input type="hidden" name="orgcode" id = "orgcode" value="0" class="ipt-hidden">
<input type="hidden" name="orgname" id = "orgname" value="根节点" class="ipt-hidden">
<input type="hidden" name="item" id = "item" value="${item}" class="ipt-hidden">
<input type="hidden" name="itemcode" id = "itemcode" value="${itemcode}" class="ipt-hidden">
<input type="hidden" name="prorate" id = "prorate" class="ipt-hidden">
<input type="hidden" name="prorate2" id = "prorate2" class="ipt-hidden">
<input type="hidden" name="prorate3" id = "prorate3" class="ipt-hidden">
<input type="hidden" name="prooffer" id = "prooffer" class="ipt-hidden">
<input type="hidden" name="prooffer2" id = "prooffer2" class="ipt-hidden">
<input type="hidden" name="prooffer3" id = "prooffer3" class="ipt-hidden">
<input type="hidden" name="createtime"  value="" class="ipt-hidden">
<input type="hidden" name="createid"  value="" class="ipt-hidden">
<input type="hidden" name="createname"  value="" class="ipt-hidden">
<input type="hidden" name="id" id = "id" class="ipt-hidden">
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">服务项目</label>
  <div class="layui-input-block" >
    <input type="text" name="proname" id="proname" lay-verify="required" autocomplete="off" placeholder="请输入服务项目" value="" class="layui-input">
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">低危服务频次</label>
  <div class="layui-input-block" >
    <select name="proratecode" id="proratecode" accept-name="prorate" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择服务频次或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_rate){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">中危服务频次</label>
  <div class="layui-input-block" >
    <select name="prorate2code" id="prorate2code" accept-name="prorate2" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择服务频次或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_rate){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">高危服务频次</label>
  <div class="layui-input-block" >
    <select name="prorate3code" id="prorate3code" accept-name="prorate3" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择服务频次或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_rate){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">三高之家服务提供</label>
  <div class="layui-input-block" >
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_offer){
      %>
      <input type="radio" name="prooffercode" title="${mzBean.name}"  ${ mzBeanLP.index == 1 ? 'checked' : '' } accept-name="prooffer" value="${mzBean.val}" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">三高基地服务提供</label>
  <div class="layui-input-block" >
    <%if(isNotEmpty(dicData)){
    for(mzBean in dicData.plan_offer){
    %>
    <input type="radio" name="prooffer2code" title="${mzBean.name}" ${ mzBeanLP.index == 1 ? 'checked' : '' } accept-name="prooffer2" value="${mzBean.val}" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
    <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">三高中心服务提供</label>
  <div class="layui-input-block" >
    <%if(isNotEmpty(dicData)){
    for(mzBean in dicData.plan_offer){
    %>
    <input type="radio" name="prooffer3code" title="${mzBean.name}" ${ mzBeanLP.index == 1 ? 'checked' : '' } accept-name="prooffer3" value="${mzBean.val}" lay-verify="formRadio" lay-skin="primary" lay-filter="chk-radio-filter">
    <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">排序码</label>
  <div class="layui-input-block" >
    <input type="number" name="sortcode" id="sortcode" lay-verify="required|number" min="-99999999" max="99999999" autocomplete="off" placeholder="请输入排序码" value="" class="layui-input">
  </div>
</div>
