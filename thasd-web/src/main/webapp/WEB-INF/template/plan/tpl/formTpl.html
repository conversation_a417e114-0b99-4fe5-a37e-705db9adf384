<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}" class="ipt-hidden">
<input type="hidden" name="orgid" id = "orgid" value="${authUser.orgid}" class="ipt-hidden">
<input type="hidden" name="orgcode" id = "orgcode" value="${authUser.orgcode}" class="ipt-hidden">
<input type="hidden" name="orgname" id = "orgname" value="${authUser.orgname}" class="ipt-hidden">
<input type="hidden" name="item" id = "item" value="${item}" class="ipt-hidden">
<input type="hidden" name="itemcode" id = "itemcode" value="${itemcode}" class="ipt-hidden">
<input type="hidden" name="plantype" id = "plantype" class="ipt-hidden">
<input type="hidden" name="id" id = "id" class="ipt-hidden">
<input type="hidden" name="createtime"  value="" class="ipt-hidden">
<input type="hidden" name="createid"  value="" class="ipt-hidden">
<input type="hidden" name="createname"  value="" class="ipt-hidden">
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">匹配类型</label>
  <div class="layui-input-block" >
    <select name="plantypecode" id="plantypecode" accept-name="plantype" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择匹配类型或输入检索</option>
      <%if(isNotEmpty(dicData)){
        for(mzBean in dicData.plan_type){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">排序码</label>
  <div class="layui-input-block" >
    <input type="number" name="sortcode" id="sortcode" lay-verify="required|number" min="-99999999" max="99999999" autocomplete="off" placeholder="请输入排序码" value="" class="layui-input">
  </div>
</div>
<div class="layui-form-item layui-form-text" style="margin-top: 1px">
  <div class="layui-input-block">
    <textarea name="content" id="content" placeholder="请输入内容" lay-verify="required" class="layui-textarea" style="height:220px"></textarea>
  </div>
</div>