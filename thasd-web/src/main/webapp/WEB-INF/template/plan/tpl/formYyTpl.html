<input type="hidden" name="ALIBABAKEY" id = "alibabakey" value="${authUserJson}" class="ipt-hidden">
<input type="hidden" name="orgid" id = "orgid" value="${authUser.orgid}" class="ipt-hidden">
<input type="hidden" name="orgcode" id = "orgcode" value="${authUser.orgcode}" class="ipt-hidden">
<input type="hidden" name="orgname" id = "orgname" value="${authUser.orgname}" class="ipt-hidden">
<input type="hidden" name="item" id = "item" value="${item}" class="ipt-hidden">
<input type="hidden" name="itemcode" id = "itemcode" value="${itemcode}" class="ipt-hidden">
<input type="hidden" name="plantype" id = "plantype" class="ipt-hidden">
<input type="hidden" name="ywmcpy" id = "ywmcpy" class="ipt-hidden">
<input type="hidden" name="yypl" id = "yypl" class="ipt-hidden">
<input type="hidden" name="yyff" id = "yyff" class="ipt-hidden">
<input type="hidden" name="yytype" id = "yytype" class="ipt-hidden">
<input type="hidden" name="yytypecode" id = "yytypecode" class="ipt-hidden">
<input type="hidden" name="createtime"  value="" class="ipt-hidden">
<input type="hidden" name="createid"  value="" class="ipt-hidden">
<input type="hidden" name="createname"  value="" class="ipt-hidden">
<input type="hidden" name="id" id = "id" class="ipt-hidden">
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">匹配类型</label>
  <div class="layui-input-block" >
    <select name="plantypecode" id="plantypecode" accept-name="plantype" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" datacode="">请选择匹配类型或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.plan_type){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">伴并发症</label>
  <div class="layui-input-block" >
    <input type="text" name="bbfz" id="bbfz" lay-verify=""   autocomplete="off"  placeholder="请输入伴并发症名称" value="" class="layui-input">
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">用药名称</label>
  <div class="layui-input-block" >
    <input type="text" name="ywmc" id="ywmc" lay-verify="required" readonly  autocomplete="off" data-type="selectDrugs" placeholder="请输入用药名称" value="" class="layui-input">
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">用量</label>
  <div class="layui-input-block" >
    <input type="text" name="yyjl" id="yyjl" lay-verify="required"  autocomplete="off" placeholder="请输入用量" value="" class="layui-input">
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">用法</label>
  <div class="layui-input-block" >
    <select name="yyffcode" id="yyffcode" accept-name="yyff" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" realtext="">请选择用法或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.yyfs){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${isNotEmpty(mzBean.ename) ? '[' +mzBean.ename + ']' : ''}${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item">
  <label class="layui-form-label" style="font-size:13px">用药频率</label>
  <div class="layui-input-block" >
    <select name="yyplcode" id="yyplcode" accept-name="yypl" lay-filter="dicdata" lay-verify="required" lay-search="">
      <option value="" realtext="">请选择用药频率或输入检索</option>
      <%if(isNotEmpty(dicData)){
      for(mzBean in dicData.yypl){
      %>
      <option value="${mzBean.val}" realtext = "${mzBean.name}">${isNotEmpty(mzBean.ename) ? '[' +mzBean.ename + ']' : ''}${mzBean.name}</option>
      <%}}%>
    </select>
  </div>
</div>
<div class="layui-form-item" >
  <label class="layui-form-label" style="font-size:13px">排序码</label>
  <div class="layui-input-block" >
    <input type="number" name="sortcode" id="sortcode" lay-verify="required|number" min="-99999999" max="99999999" autocomplete="off" placeholder="请输入排序码" value="" class="layui-input">
  </div>
</div>
