<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/plantpl.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-form .layui-form-text .layui-input-block,.layui-form-pane .layui-form-text .layui-input-block{
            width:100%;
        }
    </style>
    <script type="text/javascript">
        var authUser = JSON.parse('${json(authUser)}');
        var alibabakey = "${authUserJson}";
    </script>
    <%
    if(isNotEmpty(dicData)){
    for(mzBean in dicData.plan_item){
    %>
    <form class="layui-form layui-form-pane ${(mzBean.val=='06' || mzBean.val=='07' || mzBean.val=='08') ? 'fwqd-pane':''}" lay-filter="zdBox${mzBean.val}" style="display:none;" id="zdBox${mzBean.val}" name="zdBox${mzBean.val}">
        <%if(mzBean.val=="06" || mzBean.val=="07" || mzBean.val=="08"){ %>
        <%include("/plan/tpl/formFwTpl.html",{'item':mzBean.name,'itemcode':mzBean.val,'authUser': authUser,'dicData':dicData}){} %>
        <%} else if(mzBean.val=="04"){ %>
        <%include("/plan/tpl/formYyTpl.html",{'item':mzBean.name,'itemcode':mzBean.val,'authUser': authUser,'dicData':dicData}){} %>
        <%} else { %>
        <%include("/plan/tpl/formTpl.html",{'item':mzBean.name,'itemcode':mzBean.val,'authUser': authUser,'dicData':dicData}){} %>
        <%}%>
    </form>
    <%}}%>
    <script type="text/html" id="tleTemplet">
        {{# for (var i=0,len=d.len; i < len; i++){ }}
        <div class="layui-col-cus">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">序号</div>
                <div class="layui-form-mid "  style="width:145px;margin-right:0;">匹配类型</div>
                <div class="layui-form-mid " style="width:260px;text-align:left;">内容</div>
                <div class="layui-form-mid " style="width:42px;margin-right:0;">排序</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">操作</div>
            </div>
        </div>
        {{# }}}
    </script>
    <script type="text/html" id="aiTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="ai#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" plantypecode="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid content" style="width:260px;text-align:left;" ><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content">{{ item.content }}</div></div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="ysTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="ys#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" plantypecode="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid content" style="width:260px;text-align:left;" ><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content">{{ item.content }}</div></div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="ydTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="yd#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" plantypecode="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid content" style="width:260px;text-align:left;" ><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content">{{ item.content }}</div></div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}"  itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="jyTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="jy#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" plantypecode="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid content" style="width:260px;text-align:left;" ><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content">{{ item.content }}</div></div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}"  itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="jcTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="jc#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" plantypecode="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid content" style="width:260px;text-align:left;" ><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content">{{ item.content }}</div></div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}"  itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="tle2Templet">
        {{# for (var i=0,len=d.len; i < len; i++){ }}
        <div class="layui-col-cus">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">序号</div>
                <div class="layui-form-mid "  style="width:145px;margin-right:0;">匹配类型</div>
                <div class="layui-form-mid "  style="width:70px;margin-right:0;">用药类型</div>
                <div class="layui-form-mid "  style="width:140px;margin-right:0;">伴并发症</div>
                <div class="layui-form-mid "  style="width:175px;margin-right:0;">药品名称</div>
                <div class="layui-form-mid "  style="width:136px;margin-right:0;">频率</div>
                <div class="layui-form-mid "  style="width:100px;margin-right:0;">用量</div>
                <div class="layui-form-mid "  style="width:100px;margin-right:0;">用发</div>
                <div class="layui-form-mid " style="width:42px;margin-right:0;">排序</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">操作</div>
            </div>
        </div>
        {{# }}}
    </script>
    <script type="text/html" id="yyTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="yy#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:37px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid plantypecode" code="{{ item.plantypecode }}" style="width:145px;margin-right:0;">{{ item.plantype }}</div>
                <div class="layui-form-mid plantypecode" code="{{ item.yytypecode }}" style="width:70px;margin-right:0;">{{ item.yytype }}</div>
                <div class="layui-form-mid plantypecode content" style="width:140px;margin-right:0;"><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content" title="{{ item.ywmc }}">{{ item.bbfz==null ? "" : item.bbfz }}</div></div>
                <div class="layui-form-mid plantypecode content" style="width:175px;margin-right:0;"><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content" title="{{ item.ywmc }}">{{ item.ywmc }}</div></div>
                <div class="layui-form-mid plantypecode" code="{{ item.yyplcode }}" style="width:136px;margin-right:0;">{{ item.yypl }}</div>
                <div class="layui-form-mid plantypecode" style="width:100px;margin-right:0;">{{ item.yyjl }}</div>
                <div class="layui-form-mid plantypecode" code="{{ item.yyffcode }}" style="width:100px;margin-right:0;">{{ item.yyff }}</div>
                <div class="layui-form-mid sortcode" style="width:42px;margin-right:0;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:72px;margin-right:0;">
                    <a class="layui-btn layui-btn-warm layui-btn-xs" did="{{ item.id }}" itemcode="{{ item.itemcode }}" title="编辑" data-type="edit"><i class="layui-icon layui-icon-edit"></i></a>
                    <a class="layui-btn layui-btn-danger layui-btn-xs" did="{{ item.id }}"  itemcode="{{ item.itemcode }}" title="删除" data-type="del"><i class="layui-icon layui-icon-delete"></i></a>
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
    <script type="text/html" id="tle3Templet">
        {{# for (var i=0,len=d.len; i < len; i++){ }}
        <div class="layui-col-cus">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:70px;margin-right:0;">序号</div>
                <div class="layui-form-mid " style="width:280px;text-align:center;">服务项目</div>
                <div class="layui-form-mid th-col" style="width:300px;text-align:center;">
                    <div class="layui-form-mid th-row" style="width:300px;text-align:center;">服务频次</div>
                    <div class="layui-form-mid th-row" style="width:300px;text-align:center;">
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">低危患者</div>
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">中危患者</div>
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">高危患者</div>
                    </div>
                </div>
                <div class="layui-form-mid th-col" style="width:300px;text-align:center;">
                    <div class="layui-form-mid th-row" style="width:300px;text-align:center;">服务提供</div>
                    <div class="layui-form-mid th-row" style="width:300px;text-align:center;">
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">三高之家</div>
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">三高基地</div>
                        <div class="layui-form-mid td-col" style="width:100px;text-align:center;">三高中心</div>
                    </div>
                </div>
                <div class="layui-form-mid " style="width:70px;margin-right:0;text-align:center;">排序</div>
                <div class="layui-form-mid" style="width:70px;margin-right:0;text-align:center;"></div>
            </div>
        </div>
        {{# }}}
    </script>
    <script type="text/html" id="fwqdTemplet">
        {{#  layui.each(d.data, function(index, item){ }}
        <div class="layui-col-cus" id="fwqd#{{ item.id }}">
            <div class="layui-form-item">
                <div class="layui-form-mid" style="width:71px;margin-right:0;">{{ index+1 }}.</div>
                <div class="layui-form-mid proname content" style="width:281px;margin-right:0;text-align:center;"><a class="layui-btn layui-btn-xs close" data-type="close"><i class="layui-icon layui-icon-close"></i></a><div class='content-copy' data-type="content" title="{{ item.proname }}">{{ item.proname }}</div></div>
                <div class="layui-form-mid prorate" code="{{ item.proratecode }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prorate }}</div>
                <div class="layui-form-mid prorate2" code="{{ item.prorate2code }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prorate2 }}</div>
                <div class="layui-form-mid prorate3" code="{{ item.prorate3code }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prorate3 }}</div>
                <div class="layui-form-mid prooffer" code="{{ item.prooffercode }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prooffer }}</div>
                <div class="layui-form-mid prooffer" code="{{ item.prooffer2code }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prooffer2 }}</div>
                <div class="layui-form-mid prooffer" code="{{ item.prooffer3code }}" style="width:101px;margin-right:0;text-align:center;">{{ item.prooffer3 }}</div>
                <div class="layui-form-mid sortcode" style="width:71px;margin-right:0;text-align:center;">{{ item.sortcode }}</div>
                <div class="layui-form-mid" style="width:71px;margin-right:0;text-align:center;">
                </div>
            </div>
        </div>
        {{#  }); }}
    </script>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="fulllistbox">
    <%if(breadcrumb){%>
    <div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>个性化管理方案</cite></a>
			  <a><cite>方案模板</cite></a>
			</span>
        <div class="title-name"></div>
    </div>
    <%}%>
    <div class="listbox">
        <div class="listbox-body">
            <div class="layui-card-body body-tree-table">
                <div class="layui-tab" lay-filter="planTab">
                    <ul class="layui-tab-title">
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.plan_item){
                        %>
                        <li  ${ mzBeanLP.index == 1 ? 'class="layui-this"' : '' } val="${mzBean.val}">${mzBean.name}</li>
                        <%}}%>
                    </ul>
                    <div class="layui-tab-content">
                        <%
                        if(isNotEmpty(dicData)){
                        for(mzBean in dicData.plan_item){
                        %>
                        <div class="layui-tab-item tab-${mzBean.val}Content ${ mzBeanLP.index == 1 ? 'layui-show' : '' }" load-stetup="0">
                            <div class="layui-card opt-box" style="box-shadow:none;">
                                <div class="opt">
                                    <%
                                    if(mzBean.val != "06" && mzBean.val != "07" && mzBean.val != "08"){
                                    %>
                                    <button class="layui-btn layui-btn-normal icon"  data-type="addYs" itemcode="${mzBean.val}" formBoxId="zdBox${mzBean.val}" formBoxName="${mzBean.name}"><i class="layui-icon layui-icon-add-1"></i>添加</button>
                                    <button class="layui-btn layui-btn-normal icon"  data-type="refresh" itemcode="${mzBean.val}"><i class="layui-icon layui-icon-refresh-1"></i>刷新</button>
                                    <%}%>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10 layui-row-title">

                            </div>
                            <div class="layui-row layui-col-space10 layui-row-cus">
                                <div class="tplbox"></div>
                            </div>
                        </div>
                        <%}}%>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script id="plantpljs" type="text/javascript" src="${ctxPath}/services/plan/tpl/index.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>