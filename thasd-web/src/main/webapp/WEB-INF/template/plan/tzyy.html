<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/pear/css/pear.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/tzyy.css?ver=${ctl.randomstr()}"/>
    <link rel="stylesheet" href="${ctxPath}/css/rs/planForm.css?ver=${ctl.randomstr()}"/>
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/pear/pear.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <style type="text/css">
        .layui-field-title .layui-field-box{
            padding: 25px 0 10px 0;
        }
        .layui-form-item .layui-input-inline{margin-right:0;}
        .layui-elem-field legend{font-size:16px;font-weight:bolder;}
        .layui-table-view{margin: 0;}
        .layui-form .layui-form-text .layui-input-block,.layui-form-pane .layui-form-text .layui-input-block{
            width:100%;
        }
        .layui-table-tool{padding: 8px 15px;}
        .layui-table-tips-main{
            max-height: 420px;
            white-space: pre-line;
        }
        .layui-layer.layui-layer-page .layui-layer-content{
            padding: 10px 15px;
        }
        .layui-table-fixed .layui-table-body {
            height: auto!important;
        }
        .layui-table-fixed.layui-hide {
            display: block!important;
        }
        .layui-disabled{border-color:#cccccc;background:#efefef}
        .tzyybox{
            width: 100%;
            height: 100%;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-direction: row;
            flex-direction: row;
            overflow: hidden;
        }
        .leftTable{
            width:600px;
            flex-shrink: 2;
            flex-basis: 600px;
        }
        .rightTable{
            -ms-flex: 1;
            flex: 1;
            width: 0;
            flex-shrink: 0;
            flex-basis: 600px;
        }

        .layui-table-tool-temp{font-weight: bold}
        .icon-svg {
            width: 1.4em;
            height: 1.4em;
            vertical-align: -0.38em;
            fill: currentColor;
            overflow: hidden;
            margin-right: 5px;
        }
    </style>
    <script type="text/javascript">
        var isPlanTable = true;
        var authUser = JSON.parse('${json(authUser)}');
        var alibabakey = "${authUserJson}";
        var planid = "${param.planid!''}";
        var patientid = "${param.patientid!''}";
    </script>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="tzyybox">
    <div class="leftTable">
        <div id="yyzdFormTable" lay-filter="listtable">
        </div>
    </div>
    <div class="rightTable">
        <div id="yyzdFormTable2" lay-filter="listtable2">
        </div>
    </div>
</div>
</body>
<script type="text/html" id="listtable-number">
    {{d.LAY_INDEX}}
</script>
<script type="text/html" id="listtable-check">
<input type="checkbox" name="dqyy-chk" value="{{d.id}}" lay-skin="primary" lay-filter="chk-dqyy-filter">
</script>
<script type="text/html" id="toolbarYyzd">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-normal icon" data-type="add"><i class="layui-icon layui-icon-add-1"></i>添加用药</button>
        <button class="layui-btn layui-btn-normal icon" data-type="save"><svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-baocun"></use></svg>保存调整用药指导方案</button>
    </div>
</script>
<form class="layui-form layui-form-pane" lay-filter="zdBox04" style="display:none;" id="zdBox04" name="zdBox04">
    <input type="hidden" name="planid" id = "planid" value="${param.planid!''}" class="ipt-hidden">
    <%include("/plan/mtpl/formYyTpl.html",{'authUserJson':authUserJson,'item':'用药指导','itemcode':'04','authUser':authUser,'dicData':dicData,'patientid':param.patientid!''}){} %>
</form>
<script type="text/html" id="listtable-opt2">
    {{# if(d.isadd == "0"){ }}
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">移除</a>
    {{# }else{ }}
    保留用药
    {{# } }}
</script>
<script  type="text/javascript">
    !function(t){var e,n,c,o,i,a='<svg><symbol id="icon-baocun" viewBox="0 0 1024 1024"><path d="M819.823 83.694H206.991c-67.703 0-122.588 54.885-122.588 122.588v612.833c0 67.703 54.885 122.588 122.588 122.588h612.833c67.703 0 122.588-54.885 122.588-122.588V206.282c-0.001-67.703-54.885-122.588-122.589-122.588z m-124.435 63.313v241.142H331.772V147.007h363.616z m185.787 672.274c0.027 33.765-27.323 61.158-61.088 61.185H207.133c-16.389 0-31.864-6.297-43.454-17.887s-18.039-26.91-18.039-43.298v-612.94c0.061-33.923 27.57-61.395 61.493-61.41h61.327v245.294c-0.05 33.771 27.286 61.187 61.057 61.237h367.888c33.853 0 61.299-27.387 61.299-61.237V144.931h61.206c33.872 0.036 61.301 27.524 61.265 61.396V819.281z" fill="" ></path><path d="M574.817 329.936c17.483 0 31.656-14.173 31.656-31.656v-61.292c0-17.483-14.173-31.656-31.656-31.656s-31.656 14.173-31.656 31.656v61.292c0 17.483 14.173 31.656 31.656 31.656z" fill="" ></path></symbol><symbol id="icon-shuangxiangzhuanzhen" viewBox="0 0 1024 1024"><path d="M451.395918 715.755102c-11.493878 0-20.897959-9.404082-20.897959-20.897959V405.942857c0-11.493878 9.404082-20.897959 20.897959-20.897959s20.897959 9.404082 20.89796 20.897959V694.857143c0 11.493878-9.404082 20.897959-20.89796 20.897959z"  ></path><path d="M348.995918 529.240816c-5.22449 0-10.44898-2.089796-14.628571-6.269387-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.404082 6.269388-15.151021 6.269387z"  ></path><path d="M572.604082 638.955102c-11.493878 0-20.897959-9.404082-20.89796-20.897959V329.142857c0-11.493878 9.404082-20.897959 20.89796-20.897959s20.897959 9.404082 20.897959 20.897959v288.914286c0 11.493878-9.404082 20.897959-20.897959 20.897959z"  ></path><path d="M572.604082 638.955102c-5.22449 0-10.44898-2.089796-14.628572-6.269388-8.359184-8.359184-8.359184-21.420408 0-29.779592l101.877551-101.877551c8.359184-8.359184 21.420408-8.359184 29.779592 0 8.359184 8.359184 8.359184 21.420408 0 29.779592l-101.877551 101.877551c-4.179592 4.179592-9.926531 6.269388-15.15102 6.269388z"  ></path><path d="M512 929.959184c-230.4 0-417.959184-187.559184-417.959184-417.959184s187.559184-417.959184 417.959184-417.959184 417.959184 187.559184 417.959184 417.959184-187.559184 417.959184-417.959184 417.959184z m0-794.122449c-207.412245 0-376.163265 168.75102-376.163265 376.163265s168.75102 376.163265 376.163265 376.163265 376.163265-168.75102 376.163265-376.163265-168.75102-376.163265-376.163265-376.163265z"  ></path></symbol></svg>',d=(d=document.getElementsByTagName("script"))[d.length-1].getAttribute("data-injectcss"),l=function(t,e){e.parentNode.insertBefore(t,e)};if(d&&!t.__iconfont__svg__cssinject__){t.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}function s(){i||(i=!0,c())}function h(){try{o.documentElement.doScroll("left")}catch(t){return void setTimeout(h,50)}s()}e=function(){var t,e=document.createElement("div");e.innerHTML=a,a=null,(e=e.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",e=e,(t=document.body).firstChild?l(e,t.firstChild):t.appendChild(e))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(e,0):(n=function(){document.removeEventListener("DOMContentLoaded",n,!1),e()},document.addEventListener("DOMContentLoaded",n,!1)):document.attachEvent&&(c=e,o=t.document,i=!1,h(),o.onreadystatechange=function(){"complete"==o.readyState&&(o.onreadystatechange=null,s())})}(window);
</script>
<script id="planjs" type="text/javascript" src="${ctxPath}/services/plan/tzyy.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>