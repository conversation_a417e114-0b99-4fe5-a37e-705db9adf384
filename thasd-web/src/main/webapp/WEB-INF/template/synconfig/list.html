<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<style type="text/css">
#menuTable .ztree li span.button.switch.level0 {visibility:hidden; width:1px;}
#menuTable  .ztree li ul.level0 {padding:0; background:none;}
.layui-border {
	border-width: 1px;
	border-style: solid;
	color: #5f5f5f!important;
}
.dis{display: none;}
</style>
<script type="text/javascript">
	var zNodesJsonString = JSON.parse('${json(menu.data)}');
	var authUser = JSON.parse('${json(authUser)}');
	var dicData = [];
</script>
</head>
<body>
	 <div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<div class="fulllistbox">
		<%if(breadcrumb){%>
		<div class="titlebox">
			<span class="layui-breadcrumb">
			  <a href="${ctxPath}/indexmain">首页</a>
			  <a><cite>同步数据对照工具</cite></a>
			  <a><cite>列表</cite></a>
			</span>
			<div class="title-name"></div>
		</div>
		<%}%>
		<div class="listbox">
			<div class="listbox-body-menu">
				<div class="leftMenu">
					<div class="leftMenu-title">
						表配置
					 <div class="layui-btn-group" style="float:right;padding-right:10px;">
						 <button class="layui-btn layui-btn-xs" data-type="addFl"><i class="layui-icon">&#xe654;</i></button>
						 <button class="layui-btn layui-btn-warm layui-btn-xs" data-type="editFl"><i class="layui-icon">&#xe642;</i></button>
						 <button id="delDicFl" class="layui-btn layui-btn-danger layui-btn-xs" data-type="delFl"><i class="layui-icon">&#xe640;</i></button>
						 <button id="refFl" class="layui-btn layui-btn-primary layui-btn-xs" data-type="refFl"><i class="layui-icon">&#xe669;</i></button>
					  </div>
					</div>
					<div id="menuTable" class="layui-form-select select-tree treeMe"></div>
				</div>
				<div class="menuTabel">
					<div class="layui-card search-box">
						<div class="layui-form layui-card-header layuiadmin-card-header-auto">
							<div class="layui-form-item">
								<button class="layui-btn layui-btn-normal user-search" data-method="genitem">
									替换对照字段
								</button>
							</div>
						</div>
					</div>
					 <div class="layui-card opt-box" style="box-shadow:none;">
					 </div>
					 <div class="layui-card-body adaptive-table">
				       <table id="listtable" lay-filter="listtable"></table>
		      		</div>
				</div>
			</div>
		</div>
	</div>
</body>
<script type="text/html" id="edit-box">
	<select name="dzpro" id="dzpro-{{d.source}}" class="layui-border dis" lay-ignore>
		<option value="" val="empty" dataid = "{{d.source}}">请选择对照的来源字段</option>
		{{#  layui.each(dicData, function(index, item){ }}
		<option value="{{item.name}}" val="{{item.name}}" {{ d.from == item.name ? 'selected' :''}} dataid = "{{d.source}}">{{item.name}}</option>
		{{#  }); }}
	</select>
</script>
<script type="text/html" id="prefix-box">
	<input type="text" name="prefix" id="prefix-{{d.id}}" data-id="{{d.id}}" class="layui-input dis" />
</script>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/synconfig/list.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}"></script>
</html>