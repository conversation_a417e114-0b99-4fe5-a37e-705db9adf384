<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>三高共管 六病同防 医防管融合信息化管理平台</title>
<link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}"/>
<link rel="stylesheet" href="${ctxPath}/css/rs/add.css?ver=${ctl.randomstr()}"/>
<script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
	<style type="text/css">
		.layui-form-label{
			width:110px;
			padding: 9px 5px 9px 0;
		}
		.layui-form-item .layui-input-inline {
			width: 375px;
		}
		.layui-form-item .layui-inline {
			margin-bottom: 24px;
		}
		.layui-form-item .layui-input-inline{margin-right:0;}
		.layui-elem-field legend{font-size:16px;font-weight:bolder;}
		.layui-input-block{margin-left: 115px;}
	</style>
<script type="text/javascript">
	var mebers = JSON.parse('${isNotEmpty(mebers) ? json(mebers.list) : "[]"}');
	var orgUsers = JSON.parse('${json(orgUsers.list)}');
</script>
</head>
<body>
	<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
	<form class="layui-form formbox" lay-filter="formtable" >
		<input type="hidden" name="ALIBABAKEY" value="${authUserJson}">
		<input type="hidden" name="id" id="id">
		<input type="hidden" name="leader" id="leader" >
		<input type="hidden" name="mebsid" id="mebsid">
		<div class="form-content-box">
			<div class="layui-form-item">
				<label class="layui-form-label"><i>*</i>团队名称</label>
				<div class="layui-input-block">
					<input type="text" name="name" id="name" lay-verify="required|len30" placeholder="请输入团队名称" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><i>*</i>队长</label>
				<div class="layui-input-block" id="leaderBox">
					<select id="leaderid" name="leaderid" accept-name="leader" lay-filter="dicdata" lay-verify="" lay-search="">
						<option value="" >请选择队长或输入检索</option>
						<%
						if(isNotEmpty(orgUsers)){
						for(mzBean in orgUsers.list){
						%>
						<option value="${mzBean.value}" realtext = "${mzBean.title}">${mzBean.title}(${mzBean.orgname})</option>
						<%}}%>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><i>*</i>团队成员</label>
				<div class="layui-input-block" id="mebsBox">
				</div>
			</div>
		</div>
	  	<div class="form-opt-box">
			<button class="layui-btn layui-btn-normal form-opt-btn" id="subpost"  lay-submit="" lay-filter="formsb">确定</button>
			<a class="layui-btn layui-btn-primary form-opt-btn" id="subcancel" data-type="cancel">取消</a>
	  	</div>
	</form>
</body>
<script id="adtjs" type="text/javascript" src="${ctxPath}/services/team/adt.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&mainid=${id!''}"></script>
</html>