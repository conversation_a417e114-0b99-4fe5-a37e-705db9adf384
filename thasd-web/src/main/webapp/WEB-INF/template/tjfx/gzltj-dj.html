<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0"><meta http-equiv="Expires" content="0"><meta http-equiv="Pragma" content="no-cache"><meta http-equiv="Cache-control" content="no-cache"><meta http-equiv="Cache" content="no-cache">
    <title>三高共管 六病同防 医防管融合信息化管理平台</title>
    <link rel="stylesheet" href="${ctxPath}/layui/css/layui.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/css/rs/list.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/css/rs/page.css?ver=${ctl.randomstr()}">
    <link rel="stylesheet" href="${ctxPath}/layui/css/modules/ztree/metroStyle/metroStyle.css?ver=${ctl.randomstr()}">
    <script type="text/javascript" src="${ctxPath}/layui/layui.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/excel/xlsx.full.min.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/excel/FileSaver.js?ver=${ctl.randomstr()}"></script>
    <script type="text/javascript" src="${ctxPath}/layui/excel/Export2Excel.js?ver=${ctl.randomstr()}"></script>
    <link href="${ctxPath}/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <script type="text/javascript">
        var zNodesJsonOrg = JSON.parse('${json(org.list)}');
        var authUser = JSON.parse('${json(authUser)}');
    </script>
    <style type="text/css">
        .layui-form-label{width:auto;}
        .layui-form-item .layui-inline .layui-form-label{padding-right:5px}
        .layui-disabled, .layui-disabled:hover{border-color:#d2d2d2 !important}
        .layui-disabled{border-color:#cccccc;background:#efefef}
        #chartBox table{
            margin: 0 auto;
            border-collapse: collapse;
            border-spacing: 0;
            width:1024px;height:auto;
            border:solid 1px #000;
        }
        #chartBox table tr th,#chartBox table tr td.head {
            height:36px;
            border:solid 1px #000;
            font-size:14px;
            font-weight:540;
        }
        #chartBox table tr td {
            height:28px;
            border:solid 1px #000;
            font-size:13px;
            font-weight:500;
            text-align: center;
        }
        #chartBox table tr th.head,#chartBox table tr td.head{font-size:16px;font-weight:600;}
        #chartBox table tr.title td{background:#efefef;font-size:14px;height:36px;}
        #chartBox table tr.hj td{background:#efefef}

        #chartBox table tr td.hetext{
            /*
            text-align:right;padding-right:10px;
            */
        }
        #chartBox table tr td a{color:#000;cursor:pointer;text-decoration:underline;}
        #chartBox table tr td a:HOVER {
            color:#1e9fff
        }
        #chartBox.flex{
            -ms-flex:1;
            flex: 1;
            display: -ms-flexbox;
            display: flex;
            justify-content:center;

        }
        #chartBox.printCht{display:inline-block;width:100%;height:auto;}
        #chartBox.printCht table{width:100%;min-width:100%;}

        .fulllistbox.dis{display:inline-block;width:100%;height:auto;}
        .layui-card-body.print{padding:0;}
        .listbox.print{padding:5px;}
        .initbox{
            position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
        }
        .initbox > span[class*="-icon"] {
            width: 45px;
            height: 45px;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
        }
    </style>
</head>
<body>
<div class="initbox notiflix-block-wrap with-animation"><span class="notiflix-block-icon" style="width:45px;height:45px;top:0;"><svg id="NXLoadingCircle" width="45px" height="45px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="25 25 50 50" xml:space="preserve" version="1.1"><style>#NXLoadingCircle{-webkit-animation: rotate 2s linear infinite; animation: rotate 2s linear infinite; height: 45px; -webkit-transform-origin: center center; -ms-transform-origin: center center; transform-origin: center center; width: 45px; position: absolute; top: 0; left: 0; margin: auto;}.notiflix-loader-circle-path{stroke-dasharray: 150,200; stroke-dashoffset: -10; -webkit-animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; animation: dash 1.5s ease-in-out infinite, color 1.5s ease-in-out infinite; stroke-linecap: round;}@-webkit-keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@keyframes rotate{100%{-webkit-transform: rotate(360deg); transform: rotate(360deg);}}@-webkit-keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}@keyframes dash{0%{stroke-dasharray: 1,200; stroke-dashoffset: 0;}50%{stroke-dasharray: 89,200; stroke-dashoffset: -35;}100%{stroke-dasharray: 89,200; stroke-dashoffset: -124;}}</style><circle class="notiflix-loader-circle-path" cx="50" cy="50" r="20" fill="none" stroke="#13a387" stroke-width="2"></circle></svg></span></div>
<div class="layui-card search-box">
    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">所属单位</label>
                <div class="layui-input-inline">
                    <div id="scbm" rid = "orgid"  rname="orgname" class="layui-form-select select-tree"></div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="sttime" id="sttime" readonly="readonly"  placeholder="开始时间" autocomplete="off" class="layui-input date-it">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">结束日期</label>
                <div class="layui-input-inline">
                    <input type="text" name="ettime" id="ettime" readonly="readonly"  placeholder="结束日期" disabled autocomplete="off" class="layui-input date-it layui-input-disabled layui-disabled">
                </div>
            </div>
            <button class="layui-btn layui-btn-normal user-search" data-method="reload">
                <i class="layui-icon layui-icon-search "></i>
            </button>
            <button class="layui-btn layui-bg-cyan user-search" data-method="print">
                <i class="layui-icon layui-icon-print"></i>打印
            </button>
            <button class="layui-btn layui-bg-green user-search" style="margin-left:0px;" data-method="exportd">
                <i class="layui-icon layui-icon-export"></i>导出
            </button>
        </div>
    </div>
</div>
<div id="chartBox" class="ptable"></div>
</body>
<script type="text/html" id="chart-table">
    <table>
        <tr><td colspan="4" class="head">按登记患者人数工作量统计</td></tr>
        <tr class="title"><td>所属单位</td><td>姓名</td><td>登记人数</td><td>登录账号</td></tr>
        {{#  layui.each(d.list, function(index, item){ }}
        <tr><td>{{item.orgname}}</td><td>{{item.createname==null?"":item.createname}}</td><td>{{item.num}}</td><td>{{item.username}}</td></tr>
        {{#  }); }}
    </table>
</script>
<script id="listjs" type="text/javascript" src="${ctxPath}/services/analysis/gzltj.js?ver=${ctl.randomstr()}&ctxPath=${ctxPath}&id=${param.id!''}&orgname=${param.orgname!''}"></script>
</html>