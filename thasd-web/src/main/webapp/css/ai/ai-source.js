class SessionStorageAPI {
    // 验证浏览器支持性
    static isSupported() {
        return typeof window !== 'undefined' && 'sessionStorage' in window;
    }

    // 存储数据（支持对象、数组等复杂类型）
    static setItem(key, value) {
        if (!this.isSupported())  throw new Error('sessionStorage not supported');

        try {
            const data = typeof value === 'string' ? value : JSON.stringify(value);
            window.sessionStorage.setItem(key,  data);
        } catch (e) {
            if (e.name  === 'QuotaExceededError') {
                console.error(' 存储空间已满，无法保存数据');
            }
            throw e;
        }
    }

    // 读取数据（自动解析JSON格式）
    static getItem(key) {
        if (!this.isSupported())  return null;

        const data = window.sessionStorage.getItem(key);
        try {
            return data ? JSON.parse(data)  : data;
        } catch {
            return data; // 返回原始字符串
        }
    }

    // 删除指定数据
    static removeItem(key) {
        if (!this.isSupported())  return;
        window.sessionStorage.removeItem(key);
    }

    // 清空所有存储
    static clear() {
        if (!this.isSupported())  return;
        window.sessionStorage.clear();
    }

    // 获取键名
    static key(index) {
        if (!this.isSupported())  return null;
        return window.sessionStorage.key(index);
    }

    // 获取存储数量（只读属性）
    static get length() {
        if (!this.isSupported())  return 0;
        return window.sessionStorage.length;
    }
}
var yyzdConfig = {
    bp6: "高血压易患无需药物，以生活方式干预为主",
    bp1: "高血压低危可以使用氨氯地平或氯沙坦 剂量及用法：氨氯地平：5 mg/次 每日1次（晨服），氯沙坦：50 mg/次 每日1次（晨服）",
    bp2: "高血压中危可以使用依那普利+硝苯地平或氨氯地平+氢氯噻嗪 剂量及用法：依那普利：10 mg/次 每日1次 + 硝苯地平缓释片：20 mg/次 每日2次；氨氯地平：5 mg + 氢氯噻嗪：12.5 mg 均每日1次",
    bp3: "高血压高危可以使用依那普利+氨氯地平+氢氯噻嗪 剂量及用法：依那普利：20 mg/次 + 氨氯地平：10 mg/次 + 氢氯噻嗪：25 mg/次 均每日1次",
    db6: "糖尿病易患无需药物，以生活方式干预为主",
    db1: "糖尿病低危可以使用二甲双胍 剂量及用法：起始500 mg/次，每日1次（餐后），最大剂量2000 mg/日",
    db2: "糖尿病中危可以使用二甲双胍+达格列净或利拉鲁肽 剂量及用法：二甲双胍：起始500 mg/次，每日1次（餐后），最大剂量2000 mg/日 + 达格列净：10 mg/次 每日1次（晨服）；利拉鲁肽：起始0.6 mg/次，皮下注射，每周递增至1.8 mg",
    db3: "糖尿病高危可以使用胰岛素（基础+餐时）+ SGLT-2抑制剂 剂量及用法：基础胰岛素（甘精胰岛素）：10-20 U/次 睡前皮下注射 + 餐时胰岛素（门冬胰岛素）：4-6 U/次 每日3次（餐前）",
    lp6: "高血脂易患无需药物，以饮食和运动干预为主",
    lp1: "高血脂低危可以使用辛伐他汀或瑞舒伐他汀（低剂量） 剂量及用法：辛伐他汀：20 mg/次 每晚1次口服；瑞舒伐他汀：5-10 mg/次 每日1次（晚间）",
    lp2: "高血脂中危可以使用阿托伐他汀或联用依折麦布 剂量及用法：阿托伐他汀：20-40 mg/次 每日1次（晚间）；依折麦布：10 mg/次 每日1次（晨服）",
    lp3: "高血脂高危可以使用瑞舒伐他汀 + 依洛尤单抗 剂量及用法：瑞舒伐他汀：20 mg/次 每日1次（晚间）；依洛尤单抗：140 mg/次 每2周1次（皮下注射）",
}
var cherryConfig = {
    editor: {
        height: 'auto',
        defaultModel: 'previewOnly',
    },
    themeSettings: {
        // 主题列表，用于切换主题
        themeList: [
            { className: 'default', label: '默认' },
            { className: 'dark', label: '黑' },
            { className: 'light', label: '白' },
            { className: 'green', label: '绿' },
            { className: 'red', label: '粉' },
            { className: 'violet', label: '紫' },
            { className: 'blue', label: '蓝' },
        ],
        // 目前应用的主题
        mainTheme: 'think',
        // 目前应用的代码块主题
        codeBlockTheme: 'default',
    },
    engine: {
        global: {
            // 开启流式模式 （默认 true）
            flowSessionContext: false,
            flowSessionCursor: 'default',
        },
        syntax: {
            codeBlock: {
                selfClosing: false,
            },
            header: {
                anchorStyle: 'none',
            },
            table: {
                selfClosing: false,
            },
            fontEmphasis: {
                selfClosing: false,
            }
        }
    },
    previewer: {
        enablePreviewerBubble: false,
    },
    isPreviewOnly: true,
};
var cherryConfig2 = {
    editor: {
        height: 'auto',
        defaultModel: 'previewOnly',
        keepDocumentScrollAfterInit: false,
    },
    engine: {
        global: {
            // 开启流式模式 （默认 true）
            flowSessionContext: true,
            flowSessionCursor: 'default',
        },
        syntax: {
            codeBlock: {
                selfClosing: false,
            },
            header: {
                anchorStyle: 'none',
            },
            table: {
                selfClosing: false,
            },
            fontEmphasis: {
                selfClosing: false,
            }
        }
    },
    previewer: {
        enablePreviewerBubble: false,
    },
    isPreviewOnly: true,
};

function scrollToBottom() {
    const gencontent = document.querySelector(".gencontent");
    const height = gencontent.scrollHeight;
    gencontent.scrollTo({
        top: height,
        behavior: 'smooth'
    });
}

const dialog = document.querySelector('.j-dialog');
const loadingBtn = document.querySelector('.button.loading');
const loadingTemplet = document.querySelector('.loading-templet');

const thinkMsg = document.querySelector('.think-msg');
const contentMsg = document.querySelector('.content-msg');
// const button = document.querySelector('.j-button');
let currentCherry = null;
let currentCherry2 = null;
let requestConfig = {
    "model": "deepseek-r1:7b-qwen-distill-q4_K_M",
    "stream": true,
    "options":{
        "num_ctx": 32768,
        "num_threads": 14,
        "num_batch": 2048,
        "temperature": 0.38,
        "top_p": 0.9,
        "repeat_penalty":1.1,
        // "presence_penalty": 1.2,
        // "frequency_penalty": 1.5,
        "num_predict": 8192,
    }
    ,"prompt": ""
}
// button.addEventListener('click', function () {
//     loadingBtn.innerHTML = loadingTemplet.innerHTML;
//     loadingBtn.style.display = 'flex';
//     streamOllamaResponse().then(finalResult => {
//         console.log('\n 完整响应:', finalResult);
//         parent.zdData = finalResult;
//         parent.layer.close(parent.layIndex1)
//     }).catch(console.error);
// });

function unicodeToText(str) {
    return str.replace(/\\u([\dA-F]{4})/gi,  (match, grp) => {
        return String.fromCharCode(parseInt(grp,  16));
    });
}
function removeLeadingEmptyLines(str) {
    const lines = str.split(/\r?\n/);

    // 找到第一个非空行的索引
    const firstNonEmptyIndex = lines.findIndex(line => line.trim() !== '');

    // 全空行时返回空字符串
    if (firstNonEmptyIndex === -1) return '';

    // 从第一个非空行开始截取并重新拼接
    return lines.slice(firstNonEmptyIndex).join('\n');
}
function formatTimeDiff(startTime, endTime) {
    // 计算毫秒差值并转为秒 
    const diffMs = endTime - startTime;
    const totalSeconds = Math.floor(diffMs  / 1000);

    // 分解分钟和秒数 
    const minutes = Math.floor(totalSeconds  / 60);
    const seconds = totalSeconds % 60;

    // 格式化输出 
    return minutes > 0
        ? `${minutes}分${seconds}秒`
        : `${seconds}秒`;
}

// 流式调用 Ollama API 的完整实现 
async function streamOllamaResponse() {
    loadingBtn.querySelector('.status').innerHTML = '发起请求中...';
    const start = new Date();
    const controller = new AbortController();
    const signal = controller.signal;
    try {
        var aiHost = '************'
        var aiPort = 11434;
        if(location.hostname == '**************' || location.hostname == 'localhost'){
            aiHost = "**************"
            aiPort = 11634;
        }
        // 配置请求参数
        const endpoint = 'http://' + aiHost + ":"+ aiPort + '/api/generate';
        // const endpoint = 'http://**************:11634/api/generate'; // Ollama 默认本地地址
        // 发起 fetch 请求 
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestConfig),
            signal              // 支持请求中止 
        });

        // 处理 HTTP 错误 
        if (!response.ok)  {
            throw new Error(`HTTP ${response.status}:  ${await response.text()}`);
        }
        loadingBtn.querySelector('.status').innerHTML = '正在思考...';
        currentCherry = new Cherry(Object.assign({}, cherryConfig, { el: thinkMsg }));
        // 流式处理配置 
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let thinkResult = '';
        let thinkEnd = false;
        let result = '';
        // 逐块处理数据 
        while (true) {
            const { done, value } = await reader.read();
            console.log('源：',done, value);
            if (done) break;
            let buffer = decoder.decode(value,  { stream: true });
            let content = ''
            console.log('buffer：',buffer);
            try {
                if(buffer.includes('\n')){
                    buffer = buffer.split('\n');
                    for(let i = 0; i < buffer.length; i++){
                        if(buffer[i] == ''){
                            continue;
                        }
                        try {
                            const parsed = JSON.parse(buffer[i]);
                            const content_text = unicodeToText(parsed.response)  || '';
                            content += content_text;
                        } catch(e){
                            console.warn('JSON  解析失败:', e);
                        }
                    }
                }else{
                    const parsed = JSON.parse(buffer);
                    content = unicodeToText(parsed.response)  || '';
                }
                console.log('content：',content,content == '</think>',content == '<think>');// 实时输出内容
                if(thinkEnd){
                    result += content;
                    currentCherry2.setMarkdown(result,true);
                    scrollToBottom()
                }else{
                    if(content != '<think>' && content != '</think>'){
                        thinkResult += content;
                        currentCherry.setMarkdown(thinkResult,true);
                        scrollToBottom()
                    }else{
                        if(content.includes('<think>')){
                            thinkResult += content.replace('<think>', '');
                        }
                        if(content.includes('</think>')){
                            var spTextArr = content.split('</think>');
                            thinkResult += spTextArr[0].replace('</think>', '');
                            if(spTextArr[1] && spTextArr[1].length > 1){
                                result += spTextArr[1];
                            }
                        }
                    }
                }
                if(content == '</think>' || content.includes('</think>')){
                    loadingBtn.querySelector('.icon-loading').style.display = 'none';
                    loadingBtn.querySelector('.icon-text').style.display = 'block';
                    loadingBtn.querySelector('.status').innerHTML = '已深度思考 (用时'+formatTimeDiff(start, new Date())+')';
                    thinkEnd = true
                    currentCherry2 = new Cherry(Object.assign({}, cherryConfig2, { el: contentMsg }));
                }
            } catch (e) {
                console.warn('JSON  解析失败:', e);
            }
        }
        return result; // 返回完整结果 
    } catch (error) {
        if (error.name  === 'AbortError') {
            console.log(' 请求已中止');
        } else {
            console.error(' 请求失败:', error);
        }
        throw error;
    } finally {
        controller.abort();  // 确保请求中止 
    }
}
function getJbxx(detevalRd){
    var djArray = [];
    var tip = "基本信息："
    djArray.push("性别：" + detevalRd.gender);
    djArray.push("年龄：" + detevalRd.age + "岁");
    djArray.push("体重：" + detevalRd.pgtzname +"(BMI "+detevalRd.bmi+")" );
    djArray.push("腰围：" + detevalRd.pgywname );
    // djArray.push( "BMI：" + detevalRd.bmi);
    if(djArray.length > 1){
        return tip + djArray.join(" | ") + "";
    }else{
        return tip + djArray.join("") + "";
    }
}
function getTz(detevalRd){
    var djArray = [];
    var tip = "生活方式："
    djArray.push("吸烟：" + detevalRd.pgsmockname || "从不吸烟");
    djArray.push("饮酒：" + detevalRd.pgyjname || "正常饮酒");
    djArray.push("饮食习惯：" + detevalRd.pgysxgname  || "正常饮食");
    djArray.push("体力劳动：" + detevalRd.pgtlldname);
    if(djArray.length > 1){
        return tip + djArray.join(" | ") + "";
    }else{
        return tip + djArray.join("") + "";
    }
}
function getZd(detevalRd){
    var djArray = [];
    var tip = "临床确诊："

    if(detevalRd.bplevelcode != '0'){
        djArray.push(detevalRd.bplevelname);
    }
    if(detevalRd.pgxtcode != '0'){
        djArray.push(detevalRd.pgxtname);
    }
    if(!detevalRd.pgxzcode != '0'){
        djArray.push(detevalRd.pgxzname);
    }
    if(djArray.length > 1){
        return tip + djArray.join(" | ") + "";
    }else{
        return tip + djArray.join("") + "";
    }
}
function getFxdj(detevalRd){
    var djArray = [];
    if(detevalRd.bpgradecode != '0'){
        djArray.push('高血压：'+detevalRd.bpgradename);
    }
    if(detevalRd.dbgradecode != '0'){
        djArray.push('糖尿病：'+detevalRd.dbgradename);
    }
    if(detevalRd.lpgradecode != '0'){
        djArray.push('高血脂：'+detevalRd.lpgradename);
    }
    if(djArray.length > 1){
        return djArray.join(" | ")
    }else{
        return djArray.join("")
    }
}
function getYYzd(detevalRd){
    var djArray = [];
    if(detevalRd.bpgradecode != '0'){
        djArray.push(yyzdConfig["bp" + detevalRd.bpgradecode]);
    }
    if(detevalRd.dbgradecode != '0'){
        djArray.push(yyzdConfig["db" + detevalRd.dbgradecode]);
    }
    if(detevalRd.lpgradecode != '0'){
        djArray.push(yyzdConfig["lp" + detevalRd.lpgradecode]);
    }
    if(djArray.length > 1){
        return djArray.join(" | ") +"。";
    }else{
        return djArray.join("") +"。";
    }
}
window.onload = function(){
    let timeout;
    const container = document.querySelector('.chat-box');
    const scrollContainer = document.querySelector('.gencontent');

    const scrollbar = document.createElement('div');
    scrollbar.className  = 'custom-scrollbar';
    container.appendChild(scrollbar);
    // 初始化隐藏
    scrollbar.style.opacity  = '0';
    // 滚动事件监听
    scrollContainer.addEventListener('scroll',  () => {
        const scrollbar = document.querySelector('.custom-scrollbar');
        // 显示滚动条
        scrollbar.style.opacity  = '1';
        scrollbar.style.top  = scrollContainer.scrollTop  + 'px'
        // 重置隐藏计时
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            scrollbar.style.opacity  = '0';
        }, 2000);
    });

    loadingBtn.innerHTML = loadingTemplet.innerHTML;
    loadingBtn.style.display = 'flex';
    var detevalRd = SessionStorageAPI.getItem('detevalRd');
    var task = "\n\n任务：生成一个个性化管理方案。"
    var yaoqiu = "\n\n要求：\n1. 请用中文纯文本格式回答，不要使用任何markdown语法，禁用所有格式符号（包括*、-、#等）\n2. 严格禁止使用中英文混杂的术语，所有专业术语要转化为更易懂的中文描述，必须使用中文全称或生活化比喻：\n  - 错误示例：每周weighing → 正确示例：每周用家用体重秤测量\n  - 避免晚餐过 late → 正确示例：注意饮食规律，避免晚餐饮食过量\n3. 按以下顺序用「一、二...」分点，不设标题\n4. 每点用至少3条具体建议不超过8条，每条建议包含可操作步骤 \n\n请按此结构：\n一、饮食指导\n（直接开始具体建议...）\n二、运动指导\n（建议包含运动类型/时间和频率/强度/形式/注意事项）\n三、教育指导\n(减轻精神压力，保持平衡心理/通过健康大讲堂、张贴海报、制作板报及宣传栏、制作、发放宣传品、义诊、咨询、免费体检来开展/限制饮酒；白酒每天少于50毫升；葡萄酒每天少于100毫升；啤酒每天少于300毫升/彻底戒烟；避免被动吸烟)\n四、监测指导\n（明确监测项目/频率/记录方式）\n五、用药指导\n（包含药物名称、剂量、用法、频率、用途等）\n\n（不添加任何结尾总结）\n"
    // var prompt = "已知：\n该患者"+getJbxx(detevalRd) + getTz(detevalRd) + getZd(detevalRd)+"心血管疾病风险等级有："+getFxdj(detevalRd)+"。" + getYYzd(detevalRd) + task + yaoqiu;
    var prompt = "患者信息：\n"+getJbxx(detevalRd)  + "\n" + getTz(detevalRd) + "\n" + getZd(detevalRd) + "\n" +"心血管疾病风险等级："+getFxdj(detevalRd)+ "\n" + getYYzd(detevalRd) + task + yaoqiu;
    requestConfig.prompt = prompt;
    console.log(requestConfig.prompt);
    streamOllamaResponse().then(finalResult => {
        console.log('\n 完整响应:', finalResult);
        parent.zdData = removeLeadingEmptyLines(finalResult);
        setTimeout(function (){
            parent.layer.close(parent.layIndex1);
        }, 1000);

    })
}