@charset "UTF-8";
/* BASICS */
.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */
.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}

.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */
.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}

.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker {
  color: black;
}

.CodeMirror-guttermarker-subtle {
  color: #999;
}

/* CURSOR */
.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}

/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}

.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}

.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}

.cm-fat-cursor-mark {
  background-color: rgba(20, 255, 20, 0.5);
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
}

.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}

@-moz-keyframes blink {
  50% {
    background-color: transparent;
  }
}
@-webkit-keyframes blink {
  50% {
    background-color: transparent;
  }
}
@keyframes blink {
  50% {
    background-color: transparent;
  }
}
/* Can style cursor different in overwrite (non-insert) mode */
.cm-tab {
  display: inline-block;
  text-decoration: inherit;
}

.CodeMirror-rulers {
  position: absolute;
  left: 0;
  right: 0;
  top: -50px;
  bottom: 0;
  overflow: hidden;
}

.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0;
  bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */
.cm-s-default .cm-header {
  color: blue;
}

.cm-s-default .cm-quote {
  color: #090;
}

.cm-negative {
  color: #d44;
}

.cm-positive {
  color: #292;
}

.cm-header, .cm-strong {
  font-weight: bold;
}

.cm-em {
  font-style: italic;
}

.cm-link {
  text-decoration: underline;
}

.cm-strikethrough {
  text-decoration: line-through;
}

.cm-s-default .cm-keyword {
  color: #708;
}

.cm-s-default .cm-atom {
  color: #219;
}

.cm-s-default .cm-number {
  color: #164;
}

.cm-s-default .cm-def {
  color: #00f;
}

.cm-s-default .cm-variable-2 {
  color: #05a;
}

.cm-s-default .cm-variable-3, .cm-s-default .cm-type {
  color: #085;
}

.cm-s-default .cm-comment {
  color: #a50;
}

.cm-s-default .cm-string {
  color: #a11;
}

.cm-s-default .cm-string-2 {
  color: #f50;
}

.cm-s-default .cm-meta {
  color: #555;
}

.cm-s-default .cm-qualifier {
  color: #555;
}

.cm-s-default .cm-builtin {
  color: #30a;
}

.cm-s-default .cm-bracket {
  color: #997;
}

.cm-s-default .cm-tag {
  color: #170;
}

.cm-s-default .cm-attribute {
  color: #00c;
}

.cm-s-default .cm-hr {
  color: #999;
}

.cm-s-default .cm-link {
  color: #00c;
}

.cm-s-default .cm-error {
  color: #f00;
}

.cm-invalidchar {
  color: #f00;
}

.CodeMirror-composing {
  border-bottom: 2px solid;
}

/* Default styles for common addons */
div.CodeMirror span.CodeMirror-matchingbracket {
  color: #0b0;
}

div.CodeMirror span.CodeMirror-nonmatchingbracket {
  color: #a22;
}

.CodeMirror-matchingtag {
  background: rgba(255, 150, 0, 0.3);
}

.CodeMirror-activeline-background {
  background: #e8f2ff;
}

/* STOP */
/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */
.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important; /* Things will break if this is overridden */
  /* 50px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -50px;
  margin-right: -50px;
  padding-bottom: 50px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
}

.CodeMirror-sizer {
  position: relative;
  border-right: 50px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
  outline: none;
}

.CodeMirror-vscrollbar {
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}

.CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}

.CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}

.CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}

.CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}

.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -50px;
}

.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}

.CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}

.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}

.CodeMirror-gutter-wrapper ::selection {
  background-color: transparent;
}

.CodeMirror-gutter-wrapper ::-moz-selection {
  background-color: transparent;
}

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}

.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  /* Reset some styles that the rest of the page might have set */
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: contextual;
  font-variant-ligatures: contextual;
}

.CodeMirror-wrap pre.CodeMirror-line,
.CodeMirror-wrap pre.CodeMirror-line-like {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px; /* Force widget margins to stay inside of the container */
}

.CodeMirror-rtl pre {
  direction: rtl;
}

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}

.CodeMirror-measure pre {
  position: static;
}

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}

div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected {
  background: #d9d9d9;
}

.CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}

.CodeMirror-crosshair {
  cursor: crosshair;
}

.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection {
  background: #d7d4f0;
}

.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection {
  background: #d7d4f0;
}

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, 0.4);
}

/* Used to force a border model for a node */
.cm-force-border {
  padding-right: 0.1px;
}

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}
/* See issue #2901 */
.cm-tab-wrap-hack:after {
  content: "";
}

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext {
  background: none;
}

.cherry *::-webkit-scrollbar {
  height: 7px;
  width: 7px;
  background: transparent;
}
.cherry *::-webkit-scrollbar:hover {
  background: rgba(128, 128, 128, 0.1);
}
.cherry *::-webkit-scrollbar-thumb {
  background: #d3d7da;
  -webkit-border-radius: 6px;
}
.cherry *::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.6);
}
.cherry *::-webkit-scrollbar-corner {
  background: transparent;
}

@font-face {
  font-family: "ch-icon";
  src: url("./fonts/ch-icon.eot");
  src: url("./fonts/ch-icon.eot?#iefix") format("eot"), url("./fonts/ch-icon.woff2") format("woff2"), url("./fonts/ch-icon.woff") format("woff"), url("./fonts/ch-icon.ttf") format("truetype"), url("./fonts/ch-icon.svg#ch-icon") format("svg");
  font-weight: normal;
  font-style: normal;
}
.ch-icon:before {
  display: inline-block;
  font-family: "ch-icon";
  font-style: normal;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ch-icon-list:before {
  content: "\ea03";
}

.ch-icon-check:before {
  content: "\ea04";
}

.ch-icon-square:before {
  content: "\ea09";
}

.ch-icon-bold:before {
  content: "\ea0a";
}

.ch-icon-code:before {
  content: "\ea0b";
}

.ch-icon-color:before {
  content: "\ea0c";
}

.ch-icon-header:before {
  content: "\ea0d";
}

.ch-icon-image:before {
  content: "\ea0e";
}

.ch-icon-italic:before {
  content: "\ea0f";
}

.ch-icon-link:before {
  content: "\ea10";
}

.ch-icon-ol:before {
  content: "\ea11";
}

.ch-icon-size:before {
  content: "\ea12";
}

.ch-icon-strike:before {
  content: "\ea13";
}

.ch-icon-table:before {
  content: "\ea14";
}

.ch-icon-ul:before {
  content: "\ea15";
}

.ch-icon-underline:before {
  content: "\ea16";
}

.ch-icon-word:before {
  content: "\ea17";
}

.ch-icon-blockquote:before {
  content: "\ea18";
}

.ch-icon-font:before {
  content: "\ea19";
}

.ch-icon-insertClass:before {
  content: "\ea1f";
}

.ch-icon-insertFlow:before {
  content: "\ea20";
}

.ch-icon-insertFormula:before {
  content: "\ea21";
}

.ch-icon-insertGantt:before {
  content: "\ea22";
}

.ch-icon-insertGraph:before {
  content: "\ea23";
}

.ch-icon-insertPie:before {
  content: "\ea24";
}

.ch-icon-insertSeq:before {
  content: "\ea25";
}

.ch-icon-insertState:before {
  content: "\ea26";
}

.ch-icon-line:before {
  content: "\ea27";
}

.ch-icon-preview:before {
  content: "\ea28";
}

.ch-icon-previewClose:before {
  content: "\ea29";
}

.ch-icon-toc:before {
  content: "\ea2a";
}

.ch-icon-sub:before {
  content: "\ea2d";
}

.ch-icon-sup:before {
  content: "\ea2e";
}

.ch-icon-h1:before {
  content: "\ea2f";
}

.ch-icon-h2:before {
  content: "\ea30";
}

.ch-icon-h3:before {
  content: "\ea31";
}

.ch-icon-h4:before {
  content: "\ea32";
}

.ch-icon-h5:before {
  content: "\ea33";
}

.ch-icon-h6:before {
  content: "\ea34";
}

.ch-icon-video:before {
  content: "\ea35";
}

.ch-icon-insert:before {
  content: "\ea36";
}

.ch-icon-little_table:before {
  content: "\ea37";
}

.ch-icon-pdf:before {
  content: "\ea38";
}

.ch-icon-checklist:before {
  content: "\ea39";
}

.ch-icon-close:before {
  content: "\ea40";
}

.ch-icon-fullscreen:before {
  content: "\ea41";
}

.ch-icon-minscreen:before {
  content: "\ea42";
}

.ch-icon-insertChart:before {
  content: "\ea43";
}

.ch-icon-question:before {
  content: "\ea44";
}

.ch-icon-settings:before {
  content: "\ea45";
}

.ch-icon-ok:before {
  content: "\ea46";
}

.ch-icon-br:before {
  content: "\ea47";
}

.ch-icon-normal:before {
  content: "\ea48";
}

.ch-icon-undo:before {
  content: "\ea49";
}

.ch-icon-redo:before {
  content: "\ea50";
}

.ch-icon-copy:before {
  content: "\ea51";
}

.ch-icon-phone:before {
  content: "\ea52";
}

.ch-icon-cherry-table-delete:before {
  content: "\ea53";
}

.ch-icon-cherry-table-insert-bottom:before {
  content: "\ea54";
}

.ch-icon-cherry-table-insert-left:before {
  content: "\ea55";
}

.ch-icon-cherry-table-insert-right:before {
  content: "\ea56";
}

.ch-icon-cherry-table-insert-top:before {
  content: "\ea57";
}

.ch-icon-sort-s:before {
  content: "\ea58";
}

.ch-icon-pinyin:before {
  content: "\ea59";
}

.ch-icon-create:before {
  content: "\ea5a";
}

.ch-icon-download:before {
  content: "\ea5b";
}

.ch-icon-edit:before {
  content: "\ea5c";
}

.ch-icon-export:before {
  content: "\ea5d";
}

.ch-icon-folder-open:before {
  content: "\ea5e";
}

.ch-icon-folder:before {
  content: "\ea5f";
}

.ch-icon-help:before {
  content: "\ea60";
}

.ch-icon-pen-fill:before {
  content: "\ea61";
}

.ch-icon-pen:before {
  content: "\ea62";
}

.ch-icon-tips:before {
  content: "\ea64";
}

.ch-icon-warn:before {
  content: "\ea65";
}

.ch-icon-mistake:before {
  content: "\ea66";
}

.ch-icon-success:before {
  content: "\ea67";
}

.ch-icon-danger:before {
  content: "\ea68";
}

.ch-icon-info:before {
  content: "\ea69";
}

.ch-icon-primary:before {
  content: "\ea6a";
}

.ch-icon-warning:before {
  content: "\ea6b";
}

.ch-icon-justify:before {
  content: "\ea6c";
}

.ch-icon-justifyCenter:before {
  content: "\ea6d";
}

.ch-icon-justifyLeft:before {
  content: "\ea6e";
}

.ch-icon-justifyRight:before {
  content: "\ea6f";
}

.ch-icon-chevronsLeft:before {
  content: "\ea70";
}

.ch-icon-chevronsRight:before {
  content: "\ea71";
}

.ch-icon-trendingUp:before {
  content: "\ea72";
}

.ch-icon-codeBlock:before {
  content: "\ea74";
}

.ch-icon-expand:before {
  content: "\ea75";
}

.ch-icon-unExpand:before {
  content: "\ea76";
}

.ch-icon-swap-vert:before {
  content: "\ea77";
}

.ch-icon-swap:before {
  content: "\ea78";
}

.ch-icon-keyboard:before {
  content: "\ea79";
}

.ch-icon-command:before {
  content: "\ea7a";
}

.ch-icon-search:before {
  content: "\ea7b";
}

.cherry-markdown {
  word-break: break-all;
  /* Specify class=linenums on a pre to get line numbering */
  /* Inline code */
  /* 数学表达式展示 */
}
.cherry-markdown h1,
.cherry-markdown h2,
.cherry-markdown h3,
.cherry-markdown h4,
.cherry-markdown h5,
.cherry-markdown h6,
.cherry-markdown .h1,
.cherry-markdown .h2,
.cherry-markdown .h3,
.cherry-markdown .h4,
.cherry-markdown .h5,
.cherry-markdown .h6 {
  font-family: inherit;
  font-weight: 700;
  line-height: 1.1;
  color: inherit;
}
.cherry-markdown h1 small,
.cherry-markdown h2 small,
.cherry-markdown h3 small,
.cherry-markdown h4 small,
.cherry-markdown h5 small,
.cherry-markdown h6 small,
.cherry-markdown .h1 small,
.cherry-markdown .h2 small,
.cherry-markdown .h3 small,
.cherry-markdown .h4 small,
.cherry-markdown .h5 small,
.cherry-markdown .h6 small,
.cherry-markdown h1 .small,
.cherry-markdown h2 .small,
.cherry-markdown h3 .small,
.cherry-markdown h4 .small,
.cherry-markdown h5 .small,
.cherry-markdown h6 .small,
.cherry-markdown .h1 .small,
.cherry-markdown .h2 .small,
.cherry-markdown .h3 .small,
.cherry-markdown .h4 .small,
.cherry-markdown .h5 .small,
.cherry-markdown .h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #999;
}
.cherry-markdown h1,
.cherry-markdown h2,
.cherry-markdown h3 {
  margin-top: 30px;
  margin-bottom: 16px;
}
.cherry-markdown h1 small,
.cherry-markdown h2 small,
.cherry-markdown h3 small,
.cherry-markdown h1 .small,
.cherry-markdown h2 .small,
.cherry-markdown h3 .small {
  font-size: 65%;
}
.cherry-markdown h4,
.cherry-markdown h5,
.cherry-markdown h6 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.cherry-markdown h4 small,
.cherry-markdown h5 small,
.cherry-markdown h6 small,
.cherry-markdown h4 .small,
.cherry-markdown h5 .small,
.cherry-markdown h6 .small {
  font-size: 75%;
}
.cherry-markdown h1,
.cherry-markdown .h1 {
  font-size: 2em;
}
.cherry-markdown h2,
.cherry-markdown .h2 {
  font-size: 1.5em;
}
.cherry-markdown h3,
.cherry-markdown .h3 {
  font-size: 1.25em;
}
.cherry-markdown h4,
.cherry-markdown .h4 {
  font-size: 1em;
}
.cherry-markdown h5,
.cherry-markdown .h5 {
  font-size: 0.875em;
}
.cherry-markdown h6,
.cherry-markdown .h6 {
  font-size: 0.85em;
}
.cherry-markdown b,
.cherry-markdown strong {
  font-weight: bold;
}
.cherry-markdown ul,
.cherry-markdown ol {
  padding-left: 24px;
  margin-bottom: 16px;
}
.cherry-markdown ul ul,
.cherry-markdown ul ol,
.cherry-markdown ol ul,
.cherry-markdown ol ol {
  margin-bottom: 0;
}
.cherry-markdown ul li,
.cherry-markdown ol li {
  list-style: inherit;
}
.cherry-markdown ul li p,
.cherry-markdown ol li p {
  margin: 0;
}
.cherry-markdown div ul,
.cherry-markdown div ol {
  margin-bottom: 0;
}
.cherry-markdown hr {
  height: 0;
  border: 0;
  border-top: 1px solid #dfe6ee;
  margin: 16px 0;
  box-sizing: content-box;
  overflow: visible;
}
.cherry-markdown kbd {
  border: 1px solid #dfe6ee;
  border-radius: 4px;
  padding: 1px 2px;
  box-sizing: border-box;
  box-shadow: inset 0px -1px #dfe6ee;
  font-size: 0.85rem;
}
.cherry-markdown table {
  border-collapse: collapse;
}
.cherry-markdown table th,
.cherry-markdown table td {
  border: 1px solid #dfe6ee;
  padding: 0.2em 0.4em;
  min-width: 100px;
}
.cherry-markdown table th {
  background-color: #eee;
}
.cherry-markdown .link-quote {
  color: #3582fb;
}
.cherry-markdown a {
  color: #3582fb;
  position: relative;
  text-decoration: none;
}
.cherry-markdown a[target=_blank] {
  padding: 0 2px;
}
.cherry-markdown a[target=_blank]::after {
  content: "\ea10";
  font-size: 12px;
  font-family: "ch-icon";
  margin: 0 2px;
}
.cherry-markdown a:hover {
  color: #056bad;
}
.cherry-markdown em {
  font-style: italic;
}
.cherry-markdown sup {
  vertical-align: super;
}
.cherry-markdown sub {
  vertical-align: sub;
}
.cherry-markdown figure {
  overflow-x: auto;
}
.cherry-markdown p,
.cherry-markdown pre,
.cherry-markdown blockquote,
.cherry-markdown table {
  margin: 0 0 16px;
}
.cherry-markdown blockquote {
  color: #6d6e6f;
  padding: 10px 15px;
  border-left: 10px solid #D6DBDF;
  background: rgba(102, 128, 153, 0.05);
}
.cherry-markdown blockquote p, .cherry-markdown blockquote blockquote, .cherry-markdown blockquote table, .cherry-markdown blockquote pre, .cherry-markdown blockquote ul, .cherry-markdown blockquote ol {
  margin: 0;
}
.cherry-markdown pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
}
.cherry-markdown .prettyprint {
  min-width: 500px;
  display: inline-block;
  background: #00212b;
  font-family: Menlo, "Bitstream Vera Sans Mono", "DejaVu Sans Mono", Monaco, Consolas, monospace;
  border: 0 !important;
}
.cherry-markdown .pln {
  color: #dfe6ee;
}
.cherry-markdown .str {
  color: #ffaf21;
}
.cherry-markdown .kwd {
  color: #f85353;
}
.cherry-markdown ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
  color: #969896;
}
.cherry-markdown li.L0,
.cherry-markdown li.L1,
.cherry-markdown li.L2,
.cherry-markdown li.L3,
.cherry-markdown li.L4,
.cherry-markdown li.L5,
.cherry-markdown li.L6,
.cherry-markdown li.L7,
.cherry-markdown li.L8,
.cherry-markdown li.L9 {
  padding-left: 1em;
  background-color: #00212b;
  list-style-type: decimal;
}
@media screen {
  .cherry-markdown .cherry-markdown {
    /* comment */
    /* type name */
    /* literal value */
    /* punctuation */
    /* lisp open bracket */
    /* lisp close bracket */
    /* markup tag name */
    /* markup attribute name */
    /* markup attribute value */
    /* declaration */
    /* variable name */
    /* function name */
  }
  .cherry-markdown .cherry-markdown .com {
    color: #969896;
  }
  .cherry-markdown .cherry-markdown .typ {
    color: #81a2be;
  }
  .cherry-markdown .cherry-markdown .lit {
    color: #de935f;
  }
  .cherry-markdown .cherry-markdown .pun {
    color: #c5c8c6;
  }
  .cherry-markdown .cherry-markdown .opn {
    color: #c5c8c6;
  }
  .cherry-markdown .cherry-markdown .clo {
    color: #c5c8c6;
  }
  .cherry-markdown .cherry-markdown .tag {
    color: #cc6666;
  }
  .cherry-markdown .cherry-markdown .atn {
    color: #de935f;
  }
  .cherry-markdown .cherry-markdown .atv {
    color: #8abeb7;
  }
  .cherry-markdown .cherry-markdown .dec {
    color: #de935f;
  }
  .cherry-markdown .cherry-markdown .var {
    color: #cc6666;
  }
  .cherry-markdown .cherry-markdown .fun {
    color: #81a2be;
  }
}
.cherry-markdown div[data-type=codeBlock] {
  display: inline-block;
  width: 100%;
  box-sizing: border-box;
  border-radius: 2px;
  margin-bottom: 16px;
  font-size: 14px;
  overflow-x: auto;
}
.cherry-markdown div[data-type=codeBlock] > pre {
  margin: 0;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] {
  counter-reset: line;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-].wrap {
  white-space: pre-wrap;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line {
  display: inline-block;
  position: relative;
  padding-left: 3em;
  height: 1.3em;
  line-height: 2em;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line:before {
  counter-increment: line;
  content: counter(line);
  margin-right: 1em;
  position: absolute;
  left: 0;
}
.cherry-markdown div[data-type=codeBlock] > pre code[class*=language-] .code-line:last-child {
  margin-bottom: 0;
}
.cherry-markdown :not(pre) > code {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
  color: #f85353;
  background-color: #e5e5e5;
}
[data-inline-code-theme=black] .cherry-markdown :not(pre) > code {
  color: #3f4a56;
  background-color: #e5e5e5;
}
.cherry-markdown a.anchor:before {
  content: "§";
  text-decoration: none;
  width: 15px;
  font-size: 0.5em;
  vertical-align: middle;
  display: inline-block;
  text-align: center;
  margin-left: -15px;
}
.cherry-markdown .toc {
  margin-bottom: 16px;
  padding-left: 0;
}
.cherry-markdown .toc .toc-title {
  font-size: 24px;
  margin-bottom: 5px;
}
.cherry-markdown .toc .toc-li {
  border-bottom: 1px ridge #dfe6ee;
  list-style: none;
}
.cherry-markdown .toc .toc-li a {
  text-decoration: none;
  color: #3f4a56;
}
.cherry-markdown .toc .toc-li a:hover {
  color: #056bad;
}
.cherry-markdown .auto-num-toc {
  counter-reset: headtoclevel1;
}
.cherry-markdown .auto-num-toc .toc-li-1 {
  counter-reset: headtoclevel2;
}
.cherry-markdown .auto-num-toc .toc-li-1 a:before {
  counter-increment: headtoclevel1;
  content: counter(headtoclevel1) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-2 {
  counter-reset: headtoclevel3;
}
.cherry-markdown .auto-num-toc .toc-li-2 a:before {
  counter-increment: headtoclevel2;
  content: counter(headtoclevel1) "." counter(headtoclevel2) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-3 {
  counter-reset: headtoclevel4;
}
.cherry-markdown .auto-num-toc .toc-li-3 a:before {
  counter-increment: headtoclevel3;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-4 {
  counter-reset: headtoclevel5;
}
.cherry-markdown .auto-num-toc .toc-li-4 a:before {
  counter-increment: headtoclevel4;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-5 {
  counter-reset: headtoclevel6;
}
.cherry-markdown .auto-num-toc .toc-li-5 a:before {
  counter-increment: headtoclevel5;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) "." counter(headtoclevel5) ". ";
}
.cherry-markdown .auto-num-toc .toc-li-6 a:before {
  counter-increment: headtoclevel6;
  content: counter(headtoclevel1) "." counter(headtoclevel2) "." counter(headtoclevel3) "." counter(headtoclevel4) "." counter(headtoclevel5) "." counter(headtoclevel6) ". ";
}
.cherry-markdown .check-list-item {
  list-style: none;
}
.cherry-markdown .check-list-item .ch-icon {
  margin: 0 6px 0 -20px;
}
.cherry-markdown .footnote:not(a) {
  padding-top: 20px;
  border-top: 1px solid #dfe6ee;
  margin-top: 50px;
}
.cherry-markdown .footnote:not(a) .footnote-title {
  font-size: 20px;
  margin-top: -38px;
  background-color: #FFF;
  width: 60px;
  margin-bottom: 16px;
}
.cherry-markdown .footnote:not(a) .one-footnote {
  color: #6d6e6f;
  margin-bottom: 16px;
  border-bottom: 1px dotted #dfe6ee;
}
.cherry-markdown .cherry-table-container {
  max-width: 100%;
  overflow-x: auto;
}
.cherry-markdown .cherry-table-container .cherry-table th,
.cherry-markdown .cherry-table-container .cherry-table td {
  border: 1px solid #dfe6ee;
  padding: 0.2em 0.4em;
  min-width: 100px;
}
.cherry-markdown .cherry-table-container .cherry-table th {
  white-space: nowrap;
}
.cherry-markdown mjx-assistive-mml {
  position: absolute;
  top: 0;
  left: 0;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 1px 0 0 0;
  border: 0;
}
.cherry-markdown.head-num {
  counter-reset: level1;
}
.cherry-markdown.head-num h1 .anchor:before,
.cherry-markdown.head-num h2 .anchor:before,
.cherry-markdown.head-num h3 .anchor:before,
.cherry-markdown.head-num h4 .anchor:before,
.cherry-markdown.head-num h5 .anchor:before,
.cherry-markdown.head-num h6 .anchor:before {
  width: auto;
  font-size: inherit;
  vertical-align: inherit;
  padding-right: 10px;
}
.cherry-markdown.head-num h1 {
  counter-reset: level2;
}
.cherry-markdown.head-num h2 {
  counter-reset: level3;
}
.cherry-markdown.head-num h3 {
  counter-reset: level4;
}
.cherry-markdown.head-num h4 {
  counter-reset: level5;
}
.cherry-markdown.head-num h5 {
  counter-reset: level6;
}
.cherry-markdown.head-num h1 .anchor:before {
  counter-increment: level1;
  content: counter(level1) ". ";
}
.cherry-markdown.head-num h2 .anchor:before {
  counter-increment: level2;
  content: counter(level1) "." counter(level2) " ";
}
.cherry-markdown.head-num h3 .anchor:before {
  counter-increment: level3;
  content: counter(level1) "." counter(level2) "." counter(level3) " ";
}
.cherry-markdown.head-num h4 .anchor:before {
  counter-increment: level4;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) " ";
}
.cherry-markdown.head-num h5 .anchor:before {
  counter-increment: level5;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) "." counter(level5) " ";
}
.cherry-markdown.head-num h6 .anchor:before {
  counter-increment: level6;
  content: counter(level1) "." counter(level2) "." counter(level3) "." counter(level4) "." counter(level5) "." counter(level6) " ";
}

div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-tomorrow&languages=markup+css+clike+javascript */
  /**
   * prism.js tomorrow night eighties for JavaScript, CoffeeScript, CSS and HTML
   * Based on https://github.com/chriskempson/tomorrow-theme
   * <AUTHOR> Pritchard
   */
  /* Code blocks */
  /* Inline code */
}
div[data-type=codeBlock] code[class*=language-],
div[data-type=codeBlock] pre[class*=language-] {
  color: #ccc;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
}
div[data-type=codeBlock] :not(pre) > code[class*=language-],
div[data-type=codeBlock] pre[class*=language-] {
  background: #2d2d2d;
}
div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
div[data-type=codeBlock] .token.comment,
div[data-type=codeBlock] .token.block-comment,
div[data-type=codeBlock] .token.prolog,
div[data-type=codeBlock] .token.doctype,
div[data-type=codeBlock] .token.cdata {
  color: #999;
}
div[data-type=codeBlock] .token.punctuation {
  color: #ccc;
}
div[data-type=codeBlock] .token.tag,
div[data-type=codeBlock] .token.attr-name,
div[data-type=codeBlock] .token.namespace,
div[data-type=codeBlock] .token.deleted {
  color: #e2777a;
}
div[data-type=codeBlock] .token.function-name {
  color: #6196cc;
}
div[data-type=codeBlock] .token.boolean,
div[data-type=codeBlock] .token.number,
div[data-type=codeBlock] .token.function {
  color: #f08d49;
}
div[data-type=codeBlock] .token.property,
div[data-type=codeBlock] .token.class-name,
div[data-type=codeBlock] .token.constant,
div[data-type=codeBlock] .token.symbol {
  color: #f8c555;
}
div[data-type=codeBlock] .token.selector,
div[data-type=codeBlock] .token.important,
div[data-type=codeBlock] .token.atrule,
div[data-type=codeBlock] .token.keyword,
div[data-type=codeBlock] .token.builtin {
  color: #cc99cd;
}
div[data-type=codeBlock] .token.string,
div[data-type=codeBlock] .token.char,
div[data-type=codeBlock] .token.attr-value,
div[data-type=codeBlock] .token.regex,
div[data-type=codeBlock] .token.variable {
  color: #7ec699;
}
div[data-type=codeBlock] .token.operator,
div[data-type=codeBlock] .token.entity,
div[data-type=codeBlock] .token.url {
  color: #67cdcc;
}
div[data-type=codeBlock] .token.important,
div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
div[data-type=codeBlock] .token.entity {
  cursor: help;
}
div[data-type=codeBlock] .token.inserted {
  color: green;
}
div[data-code-wrap=wrap] div[data-type=codeBlock] code[class*=language-] {
  white-space: pre-wrap;
}
[data-code-block-theme=default] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js default theme for JavaScript, CSS and HTML
   * Based on dabblet (http://dabblet.com)
   * <AUTHOR> Verou
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  color: black;
  background: none;
  text-shadow: 0 1px white;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}
@media print {
  [data-code-block-theme=default] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
}
[data-code-block-theme=default] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=default] div[data-type=codeBlock] pre[class*=language-] {
  background: #f5f2f0;
}
[data-code-block-theme=default] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=default] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=default] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=default] div[data-type=codeBlock] .token.cdata {
  color: slategray;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.punctuation {
  color: #999;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.property,
[data-code-block-theme=default] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=default] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=default] div[data-type=codeBlock] .token.number,
[data-code-block-theme=default] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=default] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=default] div[data-type=codeBlock] .token.deleted {
  color: #905;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=default] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=default] div[data-type=codeBlock] .token.string,
[data-code-block-theme=default] div[data-type=codeBlock] .token.char,
[data-code-block-theme=default] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=default] div[data-type=codeBlock] .token.inserted {
  color: #690;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=default] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=default] div[data-type=codeBlock] .token.url,
[data-code-block-theme=default] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=default] div[data-type=codeBlock] .style .token.string {
  color: #9a6e3a;
  /* This background color was intended by the author of this theme. */
  background: hsla(0, 0%, 100%, 0.5);
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=default] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=default] div[data-type=codeBlock] .token.keyword {
  color: #07a;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.function,
[data-code-block-theme=default] div[data-type=codeBlock] .token.class-name {
  color: #DD4A68;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=default] div[data-type=codeBlock] .token.important,
[data-code-block-theme=default] div[data-type=codeBlock] .token.variable {
  color: #e90;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.important,
[data-code-block-theme=default] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=default] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=dark] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-dark&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Dark theme for JavaScript, CSS and HTML
   * Based on the slides of the talk “/Reg(exp){2}lained/”
   * <AUTHOR> Verou
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=dark] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
  color: white;
  background: none;
  text-shadow: 0 -0.1em 0.2em black;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
@media print {
  [data-code-block-theme=dark] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  background: hsl(30, 20%, 25%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border: 0.3em solid hsl(30, 20%, 40%);
  border-radius: 0.5em;
  box-shadow: 1px 1px 0.5em black inset;
}
[data-code-block-theme=dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.15em 0.2em 0.05em;
  border-radius: 0.3em;
  border: 0.13em solid hsl(30, 20%, 40%);
  box-shadow: 1px 1px 0.3em -0.1em black inset;
  white-space: normal;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.cdata {
  color: hsl(30, 20%, 50%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.punctuation {
  opacity: 0.7;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.symbol {
  color: hsl(350, 40%, 70%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.inserted {
  color: hsl(75, 70%, 60%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.url,
[data-code-block-theme=dark] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.variable {
  color: hsl(40, 90%, 60%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.keyword {
  color: hsl(350, 40%, 70%);
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.important,
[data-code-block-theme=dark] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=dark] div[data-type=codeBlock] .token.deleted {
  color: red;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] {
  /**
   * One Dark theme for prism.js
   * Based on Atom's One Dark theme: https://github.com/atom/atom/tree/master/packages/one-dark-syntax
   */
  /**
   * One Dark colours (accurate as of commit 8ae45ca on 6 Sep 2018)
   * From colors.less
   * --mono-1: hsl(220, 14%, 71%);
   * --mono-2: hsl(220, 9%, 55%);
   * --mono-3: hsl(220, 10%, 40%);
   * --hue-1: hsl(187, 47%, 55%);
   * --hue-2: hsl(207, 82%, 66%);
   * --hue-3: hsl(286, 60%, 67%);
   * --hue-4: hsl(95, 38%, 62%);
   * --hue-5: hsl(355, 65%, 65%);
   * --hue-5-2: hsl(5, 48%, 51%);
   * --hue-6: hsl(29, 54%, 61%);
   * --hue-6-2: hsl(39, 67%, 69%);
   * --syntax-fg: hsl(220, 14%, 71%);
   * --syntax-bg: hsl(220, 13%, 18%);
   * --syntax-gutter: hsl(220, 14%, 45%);
   * --syntax-guide: hsla(220, 14%, 71%, 0.15);
   * --syntax-accent: hsl(220, 100%, 66%);
   * From syntax-variables.less
   * --syntax-selection-color: hsl(220, 13%, 28%);
   * --syntax-gutter-background-color-selected: hsl(220, 13%, 26%);
   * --syntax-cursor-line: hsla(220, 100%, 80%, 0.04);
   */
  /* Selection */
  /* Code blocks */
  /* Inline code */
  /* Print */
  /* HTML overrides */
  /* CSS overrides */
  /* JS overrides */
  /* JSON overrides */
  /* MD overrides */
  /* General */
  /* Plugin overrides */
  /* Selectors should have higher specificity than those in the plugins' default stylesheets */
  /* Show Invisibles plugin overrides */
  /* Toolbar plugin overrides */
  /* Space out all buttons and move them away from the right edge of the code block */
  /* Styling the buttons */
  /* Line Highlight plugin overrides */
  /* The highlighted line itself */
  /* Default line numbers in Line Highlight plugin */
  /* Hovering over a linkable line number (in the gutter area) */
  /* Requires Line Numbers plugin as well */
  /* Line Numbers and Command Line plugins overrides */
  /* Line separating gutter from coding area */
  /* Stuff in the gutter */
  /* Match Braces plugin overrides */
  /* Note: Outline colour is inherited from the braces */
  /* Diff Highlight plugin overrides */
  /* Taken from https://github.com/atom/github/blob/master/styles/variables.less */
  /* Previewers plugin overrides */
  /* Based on https://github.com/atom-community/atom-ide-datatip/blob/master/styles/atom-ide-datatips.less and https://github.com/atom/atom/blob/master/packages/one-dark-ui */
  /* Border around popup */
  /* Angle and time should remain as circles and are hence not included */
  /* Triangles pointing to the code */
  /* Background colour within the popup */
  /* For angle, this is the positive area (eg. 90deg will display one quadrant in this colour) */
  /* For time, this is the alternate colour */
  /* Stroke colours of the handle, direction point, and vector itself */
  /* Fill colour of the handle */
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
  background: hsl(220, 13%, 18%);
  color: hsl(220, 14%, 71%);
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: "Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-]::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-] *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(220, 13%, 28%);
  color: inherit;
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-] *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] *::selection {
  background: hsl(220, 13%, 28%);
  color: inherit;
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em 0.3em;
  border-radius: 0.3em;
  white-space: normal;
}
@media print {
  [data-code-block-theme=one-dark] div[data-type=codeBlock] code[class*=language-],
  [data-code-block-theme=one-dark] div[data-type=codeBlock] pre[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.cdata {
  color: hsl(220, 10%, 40%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.entity {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.atrule {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.keyword {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.important {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value > .token.punctuation {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.function {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.url {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.attr-value > .token.punctuation.attr-equals,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.selector {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.property {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.function,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.url > .token.function {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.url > .token.string.url {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.important,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-css .token.atrule .token.rule {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-javascript .token.operator {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(5, 48%, 51%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-json .token.operator {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-json .token.null.keyword {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.operator,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url-reference.url > .token.string {
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.content {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url > .token.url,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.url-reference.url {
  color: hsl(187, 47%, 55%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.blockquote.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.hr.punctuation {
  color: hsl(220, 10%, 40%);
  font-style: italic;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.code-snippet {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.bold .token.content {
  color: hsl(29, 54%, 61%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.italic .token.content {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.strike .token.content,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.strike .token.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.list.punctuation,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .language-markdown .token.title.important > .token.punctuation {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.namespace {
  opacity: 0.8;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.tab:not(:empty):before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.cr:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.lf:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .token.token.space:before {
  color: hsla(220, 14%, 71%, 0.15);
  text-shadow: none;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item {
  margin-right: 0.4em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span {
  background: hsl(220, 13%, 26%);
  color: hsl(220, 9%, 55%);
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover,
[data-code-block-theme=one-dark] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus {
  background: hsl(220, 13%, 28%);
  color: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: hsla(220, 100%, 80%, 0.04);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-highlight.line-highlight[data-end]:after {
  background: hsl(220, 13%, 26%);
  color: hsl(220, 14%, 71%);
  padding: 0.1em 0.6em;
  border-radius: 0.3em;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2); /* same as Toolbar plugin default */
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before {
  background-color: hsla(220, 100%, 80%, 0.04);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .command-line .command-line-prompt {
  border-right-color: hsla(220, 14%, 71%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .command-line .command-line-prompt > span:before {
  color: hsl(220, 14%, 45%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-1,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-5,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-9 {
  color: hsl(355, 65%, 65%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-2,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-6,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-10 {
  color: hsl(95, 38%, 62%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-3,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-7,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-11 {
  color: hsl(207, 82%, 66%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-4,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-8,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-12 {
  color: hsl(286, 60%, 67%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix),
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) {
  background-color: hsla(353, 100%, 66%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix),
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) {
  background-color: hsla(137, 100%, 55%, 0.15);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-dark] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer.prism-previewer:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div {
  border-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-color.prism-previewer-color:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing:before {
  border-radius: 0.3em;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer.prism-previewer:after {
  border-top-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-flipped.prism-previewer-flipped.after {
  border-bottom-color: hsl(224, 13%, 17%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time:before,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing {
  background: hsl(219, 13%, 22%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle circle,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time circle {
  stroke: hsl(220, 14%, 71%);
  stroke-opacity: 1;
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing path,
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing line {
  stroke: hsl(220, 14%, 71%);
}
[data-code-block-theme=one-dark] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle {
  fill: transparent;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] {
  /**
   * One Light theme for prism.js
   * Based on Atom's One Light theme: https://github.com/atom/atom/tree/master/packages/one-light-syntax
   */
  /**
   * One Light colours (accurate as of commit eb064bf on 19 Feb 2021)
   * From colors.less
   * --mono-1: hsl(230, 8%, 24%);
   * --mono-2: hsl(230, 6%, 44%);
   * --mono-3: hsl(230, 4%, 64%)
   * --hue-1: hsl(198, 99%, 37%);
   * --hue-2: hsl(221, 87%, 60%);
   * --hue-3: hsl(301, 63%, 40%);
   * --hue-4: hsl(119, 34%, 47%);
   * --hue-5: hsl(5, 74%, 59%);
   * --hue-5-2: hsl(344, 84%, 43%);
   * --hue-6: hsl(35, 99%, 36%);
   * --hue-6-2: hsl(35, 99%, 40%);
   * --syntax-fg: hsl(230, 8%, 24%);
   * --syntax-bg: hsl(230, 1%, 98%);
   * --syntax-gutter: hsl(230, 1%, 62%);
   * --syntax-guide: hsla(230, 8%, 24%, 0.2);
   * --syntax-accent: hsl(230, 100%, 66%);
   * From syntax-variables.less
   * --syntax-selection-color: hsl(230, 1%, 90%);
   * --syntax-gutter-background-color-selected: hsl(230, 1%, 90%);
   * --syntax-cursor-line: hsla(230, 8%, 24%, 0.05);
   */
  /* Selection */
  /* Code blocks */
  /* Inline code */
  /* HTML overrides */
  /* CSS overrides */
  /* JS overrides */
  /* JSON overrides */
  /* MD overrides */
  /* General */
  /* Plugin overrides */
  /* Selectors should have higher specificity than those in the plugins' default stylesheets */
  /* Show Invisibles plugin overrides */
  /* Toolbar plugin overrides */
  /* Space out all buttons and move them away from the right edge of the code block */
  /* Styling the buttons */
  /* Line Highlight plugin overrides */
  /* The highlighted line itself */
  /* Default line numbers in Line Highlight plugin */
  /* Hovering over a linkable line number (in the gutter area) */
  /* Requires Line Numbers plugin as well */
  /* Line Numbers and Command Line plugins overrides */
  /* Line separating gutter from coding area */
  /* Stuff in the gutter */
  /* Match Braces plugin overrides */
  /* Note: Outline colour is inherited from the braces */
  /* Diff Highlight plugin overrides */
  /* Taken from https://github.com/atom/github/blob/master/styles/variables.less */
  /* Previewers plugin overrides */
  /* Based on https://github.com/atom-community/atom-ide-datatip/blob/master/styles/atom-ide-datatips.less and https://github.com/atom/atom/blob/master/packages/one-light-ui */
  /* Border around popup */
  /* Angle and time should remain as circles and are hence not included */
  /* Triangles pointing to the code */
  /* Background colour within the popup */
  /* For angle, this is the positive area (eg. 90deg will display one quadrant in this colour) */
  /* For time, this is the alternate colour */
  /* Stroke colours of the handle, direction point, and vector itself */
  /* Fill colour of the handle */
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] {
  background: hsl(230, 1%, 98%);
  color: hsl(230, 8%, 24%);
  font-family: "Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 2;
  -o-tab-size: 2;
  tab-size: 2;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-] *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] *::-moz-selection {
  background: hsl(230, 1%, 90%);
  color: inherit;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] code[class*=language-] *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] *::selection {
  background: hsl(230, 1%, 90%);
  color: inherit;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em 0.3em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.cdata {
  color: hsl(230, 4%, 64%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.entity {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.atrule {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.keyword {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.important {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.string,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.char,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value > .token.punctuation {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.function {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.url {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.attr-value > .token.punctuation.attr-equals,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.special-attr > .token.attr-value > .token.value.css {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.selector {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.property {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.function,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.url > .token.function {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.url > .token.string.url {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.important,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-css .token.atrule .token.rule {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-javascript .token.operator {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-javascript .token.template-string > .token.interpolation > .token.interpolation-punctuation.punctuation {
  color: hsl(344, 84%, 43%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-json .token.operator {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-json .token.null.keyword {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.operator,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url-reference.url > .token.string {
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.content {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url > .token.url,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.url-reference.url {
  color: hsl(198, 99%, 37%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.blockquote.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.hr.punctuation {
  color: hsl(230, 4%, 64%);
  font-style: italic;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.code-snippet {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.bold .token.content {
  color: hsl(35, 99%, 36%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.italic .token.content {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.strike .token.content,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.strike .token.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.list.punctuation,
[data-code-block-theme=one-light] div[data-type=codeBlock] .language-markdown .token.title.important > .token.punctuation {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.8;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.tab:not(:empty):before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.cr:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.lf:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .token.token.space:before {
  color: hsla(230, 8%, 24%, 0.2);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item {
  margin-right: 0.4em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span {
  background: hsl(230, 1%, 90%);
  color: hsl(230, 6%, 44%);
  padding: 0.1em 0.4em;
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > button:focus,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > a:focus,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:hover,
[data-code-block-theme=one-light] div[data-type=codeBlock] div.code-toolbar > .toolbar.toolbar > .toolbar-item > span:focus {
  background: hsl(230, 1%, 78%); /* custom: darken(--syntax-bg, 20%) */
  color: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: hsla(230, 8%, 24%, 0.05);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-highlight.line-highlight[data-end]:after {
  background: hsl(230, 1%, 90%);
  color: hsl(230, 8%, 24%);
  padding: 0.1em 0.6em;
  border-radius: 0.3em;
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.2); /* same as Toolbar plugin default */
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre[id].linkable-line-numbers.linkable-line-numbers span.line-numbers-rows > span:hover:before {
  background-color: hsla(230, 8%, 24%, 0.05);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows,
[data-code-block-theme=one-light] div[data-type=codeBlock] .command-line .command-line-prompt {
  border-right-color: hsla(230, 8%, 24%, 0.2);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .command-line .command-line-prompt > span:before {
  color: hsl(230, 1%, 62%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-1,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-5,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-9 {
  color: hsl(5, 74%, 59%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-2,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-6,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-10 {
  color: hsl(119, 34%, 47%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-3,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-7,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-11 {
  color: hsl(221, 87%, 60%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-4,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-8,
[data-code-block-theme=one-light] div[data-type=codeBlock] .rainbow-braces .token.token.punctuation.brace-level-12 {
  color: hsl(301, 63%, 40%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix),
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) {
  background-color: hsla(353, 100%, 66%, 0.15);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::-moz-selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.deleted:not(.prefix) *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.deleted:not(.prefix) *::selection {
  background-color: hsla(353, 95%, 66%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix),
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) {
  background-color: hsla(137, 100%, 55%, 0.15);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::-moz-selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::-moz-selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre.diff-highlight > code .token.token.inserted:not(.prefix) *::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix)::selection,
[data-code-block-theme=one-light] div[data-type=codeBlock] pre > code.diff-highlight .token.token.inserted:not(.prefix) *::selection {
  background-color: hsla(135, 73%, 55%, 0.25);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer.prism-previewer:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div {
  border-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-color.prism-previewer-color:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-gradient.prism-previewer-gradient div,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing:before {
  border-radius: 0.3em;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer.prism-previewer:after {
  border-top-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-flipped.prism-previewer-flipped.after {
  border-bottom-color: rgb(242, 242, 242);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time:before,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing {
  background: hsl(0, 0%, 100%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-angle.prism-previewer-angle circle,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-time.prism-previewer-time circle {
  stroke: hsl(230, 8%, 24%);
  stroke-opacity: 1;
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing path,
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing line {
  stroke: hsl(230, 8%, 24%);
}
[data-code-block-theme=one-light] div[data-type=codeBlock] .prism-previewer-easing.prism-previewer-easing circle {
  fill: transparent;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] {
  /*********************************************************
  * Tokens
  */
  /*********************************************************
  * Language Specific
  */
  /*********************************************************
  * Line highlighting
  */
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] {
  color: #d4d4d4;
  font-size: 13px;
  text-shadow: none;
  font-family: Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-]::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-]::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] *::selection,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] *::selection {
  text-shadow: none;
  background: #264F78;
}
@media print {
  [data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-],
  [data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-] {
    text-shadow: none;
  }
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  background: #1e1e1e;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em 0.3em;
  border-radius: 0.3em;
  color: #db4c69;
  background: #1e1e1e;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .namespace {
  opacity: 0.7;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.doctype .token.doctype-tag {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.doctype .token.name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.prolog {
  color: #6a9955;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .language-css .token.punctuation,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .language-javascript .token.punctuation {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.number,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.inserted,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.unit {
  color: #b5cea8;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.string,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.char,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.deleted {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-css .token.string.url {
  text-decoration: underline;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.entity {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.operator.arrow {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.rule {
  color: #c586c0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url .token.function {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.atrule .token.url .token.punctuation {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword {
  color: #569CD6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword.module,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.keyword.control-flow {
  color: #c586c0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.function,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.function .token.maybe-class-name {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.regex {
  color: #d16969;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.important {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.constant {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.maybe-class-name {
  color: #4ec9b0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.console {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.parameter {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.interpolation {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.punctuation.interpolation-punctuation {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.boolean {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.imports .token.maybe-class-name,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.exports .token.maybe-class-name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.selector {
  color: #d7ba7d;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.escape {
  color: #d7ba7d;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.tag .token.punctuation {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.cdata {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-name {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value .token.punctuation {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.attr-value .token.punctuation.attr-equals {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.entity {
  color: #569cd6;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .token.namespace {
  color: #4ec9b0;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-javascript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-javascript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-jsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-jsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-typescript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-typescript],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-tsx],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-tsx] {
  color: #9cdcfe;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-css],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-css] {
  color: #ce9178;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-html],
[data-code-block-theme=vs-dark] div[data-type=codeBlock] code[class*=language-html] {
  color: #d4d4d4;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-regex .token.anchor {
  color: #dcdcaa;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .language-html .token.punctuation {
  color: #808080;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] pre[class*=language-] > code[class*=language-] {
  position: relative;
  z-index: 1;
}
[data-code-block-theme=vs-dark] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: #f7ebc6;
  box-shadow: inset 5px 0 0 #f7d87c;
  z-index: 0;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] {
  /**
   * VS theme by Andrew Lock (https://andrewlock.net)
   * Inspired by Visual Studio syntax coloring
   */
  /* Code blocks */
  /* Inline code */
  /* overrides color-values for the Line Numbers plugin
   * http://prismjs.com/plugins/line-numbers/
   */
  /* overrides color-values for the Line Highlight plugin
  * http://prismjs.com/plugins/line-highlight/
  */
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] {
  color: #393A34;
  font-family: "Consolas", "Bitstream Vera Sans Mono", "Courier New", Courier, monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  font-size: 0.9em;
  line-height: 1.2em;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre > code[class*=language-] {
  font-size: 1em;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #C1DEF1;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-] ::selection {
  background: #C1DEF1;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border: 1px solid #dddddd;
  background-color: white;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.2em;
  padding-top: 1px;
  padding-bottom: 1px;
  background: #f8f8f8;
  border: 1px solid #dddddd;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.cdata {
  color: #008000;
  font-style: italic;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.string {
  color: #A31515;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.punctuation,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.operator {
  color: #393A34; /* no highlight */
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.url,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.inserted {
  color: #36acaa;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.selector,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.boolean,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.number,
[data-code-block-theme=vs-light] div[data-type=codeBlock] code[class*=language-css] {
  color: #0000ff;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.function {
  color: #393A34;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.deleted,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.tag {
  color: #9a050f;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-autohotkey .token.keyword {
  color: #00009f;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.class-name,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .language-json .token.property {
  color: #2B91AF;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.selector {
  color: #800000;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.entity {
  color: #ff0000;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .token.directive.tag .tag {
  background: #ffff00;
  color: #393A34;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-numbers.line-numbers .line-numbers-rows {
  border-right-color: #a5a5a5;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-numbers .line-numbers-rows > span:before {
  color: #2B91AF;
}
[data-code-block-theme=vs-light] div[data-type=codeBlock] .line-highlight.line-highlight {
  background: rgba(193, 222, 241, 0.2);
  background: -webkit-linear-gradient(left, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0));
  background: linear-gradient(to right, rgba(193, 222, 241, 0.2) 70%, rgba(221, 222, 241, 0));
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-okaidia&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * okaidia theme for JavaScript, CSS and HTML
   * Loosely based on Monokai textmate theme by http://www.monokai.nl/
   * <AUTHOR>
   */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  color: #f8f8f2;
  background: none;
  text-shadow: 0 1px rgba(0, 0, 0, 0.3);
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=okaidia] div[data-type=codeBlock] pre[class*=language-] {
  background: #272822;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.cdata {
  color: #8292a2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.punctuation {
  color: #f8f8f2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.property,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.deleted {
  color: #f92672;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.number {
  color: #ae81ff;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.char,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.inserted {
  color: #a6e22e;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.url,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.variable {
  color: #f8f8f2;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.function,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.class-name {
  color: #e6db74;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.keyword {
  color: #66d9ef;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.important {
  color: #fd971f;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.important,
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=okaidia] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-twilight&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Twilight theme
   * Based (more or less) on the Twilight theme originally of Textmate fame.
   * <AUTHOR> Bach
   */
  /* Code blocks */
  /* Text Selection colour */
  /* Inline code */
  /* Markup */
  /* Make the tokens sit above the line highlight so the colours don't look faded. */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] {
  color: white;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  text-shadow: 0 -0.1em 0.2em black;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-],
[data-code-block-theme=twilight] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  background: hsl(0, 0%, 8%); /* #141414 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] {
  border-radius: 0.5em;
  border: 0.3em solid hsl(0, 0%, 33%); /* #282A2B */
  box-shadow: 1px 1px 0.5em black inset;
  margin: 0.5em 0;
  overflow: auto;
  padding: 1em;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::-moz-selection {
  /* Firefox */
  background: hsl(200, 4%, 16%); /* #282A2B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::selection {
  /* Safari */
  background: hsl(200, 4%, 16%); /* #282A2B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: hsla(0, 0%, 93%, 0.15); /* #EDEDED */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=twilight] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=twilight] div[data-type=codeBlock] code[class*=language-] ::selection {
  text-shadow: none;
  background: hsla(0, 0%, 93%, 0.15); /* #EDEDED */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  border-radius: 0.3em;
  border: 0.13em solid hsl(0, 0%, 33%); /* #545454 */
  box-shadow: 1px 1px 0.3em -0.1em black inset;
  padding: 0.15em 0.2em 0.05em;
  white-space: normal;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.cdata {
  color: hsl(0, 0%, 47%); /* #777777 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.punctuation {
  opacity: 0.7;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.number,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.deleted {
  color: hsl(14, 58%, 55%); /* #CF6A4C */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.property,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.builtin {
  color: hsl(53, 89%, 79%); /* #F9EE98 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.char,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.url,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .style .token.string,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.variable,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.inserted {
  color: hsl(76, 21%, 52%); /* #8F9D6A */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.atrule {
  color: hsl(218, 22%, 55%); /* #7587A6 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.important {
  color: hsl(42, 75%, 65%); /* #E9C062 */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.important,
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] pre[data-line] {
  padding: 1em 0 1em 3em;
  position: relative;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.tag,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.attr-name,
[data-code-block-theme=twilight] div[data-type=codeBlock] .language-markup .token.punctuation {
  color: hsl(33, 33%, 52%); /* #AC885B */
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .token {
  position: relative;
  z-index: 1;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight {
  background: hsla(0, 0%, 33%, 0.25); /* #545454 */
  background: linear-gradient(to right, hsla(0, 0%, 33%, 0.1) 70%, hsla(0, 0%, 33%, 0)); /* #545454 */
  border-bottom: 1px dashed hsl(0, 0%, 33%); /* #545454 */
  border-top: 1px dashed hsl(0, 0%, 33%); /* #545454 */
  left: 0;
  line-height: inherit;
  margin-top: 0.75em; /* Same as .prism’s padding-top */
  padding: inherit 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  white-space: pre;
  z-index: 0;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight:before,
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight[data-end]:after {
  background-color: hsl(215, 15%, 59%); /* #8794A6 */
  border-radius: 999px;
  box-shadow: 0 1px white;
  color: hsl(24, 20%, 95%); /* #F5F2F0 */
  content: attr(data-start);
  font: bold 65%/1.5 sans-serif;
  left: 0.6em;
  min-width: 1em;
  padding: 0 0.5em;
  position: absolute;
  text-align: center;
  text-shadow: none;
  top: 0.4em;
  vertical-align: 0.3em;
}
[data-code-block-theme=twilight] div[data-type=codeBlock] .line-highlight[data-end]:after {
  bottom: 0.4em;
  content: attr(data-end);
  top: auto;
}
[data-code-block-theme=coy] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-coy&languages=markup+css+clike+javascript+abap+abnf+actionscript+ada+agda+al+antlr4+apacheconf+apex+apl+applescript+aql+arduino+arff+asciidoc+aspnet+asm6502+autohotkey+autoit+bash+basic+batch+bbcode+birb+bison+bnf+brainfuck+brightscript+bro+bsl+c+csharp+cpp+cfscript+chaiscript+cil+clojure+cmake+cobol+coffeescript+concurnas+csp+coq+crystal+css-extras+csv+cypher+d+dart+dataweave+dax+dhall+diff+django+dns-zone-file+docker+dot+ebnf+editorconfig+eiffel+ejs+elixir+elm+etlua+erb+erlang+excel-formula+fsharp+factor+false+firestore-security-rules+flow+fortran+ftl+gml+gcode+gdscript+gedcom+gherkin+git+glsl+go+graphql+groovy+haml+handlebars+haskell+haxe+hcl+hlsl+http+hpkp+hsts+ichigojam+icon+icu-message-format+idris+ignore+inform7+ini+io+j+java+javadoc+javadoclike+javastacktrace+jexl+jolie+jq+jsdoc+js-extras+json+json5+jsonp+jsstacktrace+js-templates+julia+keyman+kotlin+kumir+latex+latte+less+lilypond+liquid+lisp+livescript+llvm+log+lolcode+lua+makefile+markdown+markup-templating+matlab+mel+mizar+mongodb+monkey+moonscript+n1ql+n4js+nand2tetris-hdl+naniscript+nasm+neon+nevod+nginx+nim+nix+nsis+objectivec+ocaml+opencl+openqasm+oz+parigp+parser+pascal+pascaligo+psl+pcaxis+peoplecode+perl+php+phpdoc+php-extras+plsql+powerquery+powershell+processing+prolog+promql+properties+protobuf+pug+puppet+pure+purebasic+purescript+python+qsharp+q+qml+qore+r+racket+jsx+tsx+reason+regex+rego+renpy+rest+rip+roboconf+robotframework+ruby+rust+sas+sass+scss+scala+scheme+shell-session+smali+smalltalk+smarty+sml+solidity+solution-file+soy+sparql+splunk-spl+sqf+sql+squirrel+stan+iecst+stylus+swift+t4-templating+t4-cs+t4-vb+tap+tcl+tt2+textile+toml+turtle+twig+typescript+typoscript+unrealscript+uri+v+vala+vbnet+velocity+verilog+vhdl+vim+visual-basic+warpscript+wasm+wiki+wolfram+xeora+xml-doc+xojo+xquery+yaml+yang+zig */
  /**
   * prism.js Coy theme for JavaScript, CoffeeScript, CSS and HTML
   * Based on https://github.com/tshedor/workshop-wp-theme (Example: http://workshop.kansan.com/category/sessions/basics or http://workshop.timshedor.com/category/sessions/basics);
   * <AUTHOR>  Shedor
   */
  /* Code blocks */
  /* Margin bottom to accommodate shadow */
  /* Inline code */
  /* Plugin styles: Line Numbers */
  /* Plugin styles: Line Highlight */
}
[data-code-block-theme=coy] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  color: black;
  background: none;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  position: relative;
  margin: 0.5em 0;
  overflow-y: hidden;
  padding: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] > code {
  position: relative;
  border-left: 10px solid #358ccb;
  box-shadow: -1px 0px 0px 0px #358ccb, 0px 0px 0px 1px #dfdfdf;
  background-color: #fdfdfd;
  background-image: linear-gradient(transparent 50%, rgba(69, 142, 209, 0.04) 50%);
  background-size: 3em 3em;
  background-origin: content-box;
  background-attachment: local;
}
[data-code-block-theme=coy] div[data-type=codeBlock] code[class*=language-] {
  max-height: inherit;
  height: inherit;
  padding: 0 1em;
  display: block;
}
[data-code-block-theme=coy] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-] {
  background-color: #fdfdfd;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 1em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  position: relative;
  padding: 0.2em;
  border-radius: 0.3em;
  color: #c92c2c;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: inline;
  white-space: normal;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:before,
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
  content: "";
  z-index: -2;
  display: block;
  position: absolute;
  bottom: 0.75em;
  left: 0.18em;
  width: 40%;
  height: 20%;
  max-height: 13em;
  box-shadow: 0px 13px 8px #979797;
  -webkit-transform: rotate(-2deg);
  -moz-transform: rotate(-2deg);
  -ms-transform: rotate(-2deg);
  -o-transform: rotate(-2deg);
  transform: rotate(-2deg);
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
  right: 0.75em;
  left: auto;
  -webkit-transform: rotate(2deg);
  -moz-transform: rotate(2deg);
  -ms-transform: rotate(2deg);
  -o-transform: rotate(2deg);
  transform: rotate(2deg);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.block-comment,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.cdata {
  color: #7D8B99;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.punctuation {
  color: #5F6364;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.property,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.number,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.function-name,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.deleted {
  color: #c92c2c;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.string,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.char,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.function,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.inserted {
  color: #2f9c0a;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.operator,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.entity,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.url,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.variable {
  color: #a67f59;
  background: rgba(255, 255, 255, 0.5);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.keyword,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.class-name {
  color: #1990b8;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=coy] div[data-type=codeBlock] .token.important {
  color: #e90;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .language-css .token.string,
[data-code-block-theme=coy] div[data-type=codeBlock] .style .token.string {
  color: #a67f59;
  background: rgba(255, 255, 255, 0.5);
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.important {
  font-weight: normal;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.entity {
  cursor: help;
}
[data-code-block-theme=coy] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
@media screen and (max-width: 767px) {
  [data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:before,
  [data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-]:after {
    bottom: 14px;
    box-shadow: none;
  }
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers {
  padding-left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers code {
  padding-left: 3.8em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-].line-numbers.line-numbers .line-numbers-rows {
  left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[class*=language-][data-line] {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre[data-line] code {
  position: relative;
  padding-left: 4em;
}
[data-code-block-theme=coy] div[data-type=codeBlock] pre .line-highlight {
  margin-top: 0;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] {
  /* PrismJS 1.23.0
  https://prismjs.com/download.html#themes=prism-solarizedlight&languages=markup+css+clike+javascript */
  /*
   Solarized Color Schemes originally by Ethan Schoonover
   http://ethanschoonover.com/solarized

   Ported for PrismJS by Hector Matos
   Website: https://krakendev.io
   Twitter Handle: https://twitter.com/allonsykraken)
  */
  /*
  SOLARIZED HEX
  --------- -------
  base03    #002b36
  base02    #073642
  base01    #586e75
  base00    #657b83
  base0     #839496
  base1     #93a1a1
  base2     #eee8d5
  base3     #fdf6e3
  yellow    #b58900
  orange    #cb4b16
  red       #dc322f
  magenta   #d33682
  violet    #6c71c4
  blue      #268bd2
  cyan      #2aa198
  green     #859900
  */
  /* Code blocks */
  /* Inline code */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-],
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  color: #657b83; /* base00 */
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] ::-moz-selection,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-]::-moz-selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-] ::-moz-selection {
  background: #073642; /* base02 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-]::selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] ::selection,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-]::selection, [data-code-block-theme=solarized-light] div[data-type=codeBlock] code[class*=language-] ::selection {
  background: #073642; /* base02 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.3em;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] :not(pre) > code[class*=language-],
[data-code-block-theme=solarized-light] div[data-type=codeBlock] pre[class*=language-] {
  background-color: #fdf6e3; /* base3 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] :not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.comment,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.prolog,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.doctype,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.cdata {
  color: #93a1a1; /* base1 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.punctuation {
  color: #586e75; /* base01 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.namespace {
  opacity: 0.7;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.property,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.tag,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.boolean,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.number,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.constant,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.symbol,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.deleted {
  color: #268bd2; /* blue */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.selector,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.attr-name,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.string,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.char,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.builtin,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.url,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.inserted {
  color: #2aa198; /* cyan */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.entity {
  color: #657b83; /* base00 */
  background: #eee8d5; /* base2 */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.atrule,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.attr-value,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.keyword {
  color: #859900; /* green */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.function,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.class-name {
  color: #b58900; /* yellow */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.regex,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.variable {
  color: #cb4b16; /* orange */
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.important,
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.bold {
  font-weight: bold;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.italic {
  font-style: italic;
}
[data-code-block-theme=solarized-light] div[data-type=codeBlock] .token.entity {
  cursor: help;
}

.cherry-detail details {
  background: rgba(248, 249, 250, 0.6666666667);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
}
.cherry-detail details summary {
  user-select: none;
  padding: 5px 10px;
  background-color: #6c757d;
  color: #fff;
  border-radius: 8px;
}
.cherry-detail details .cherry-detail-body {
  padding: 15px 25px 0 25px;
}

.cherry-detail__multiple {
  border-radius: 8px;
  overflow: hidden;
}
.cherry-detail__multiple details {
  margin-bottom: 1px;
  border-radius: 0;
  border: none;
}
.cherry-detail__multiple details summary {
  border-radius: 0;
}

.cherry-text-align__center table {
  margin-left: auto;
  margin-right: auto;
}

.cherry-text-align__right table {
  margin-left: auto;
}

.cherry-panel {
  margin: 10px 0;
  overflow: hidden;
  border-radius: 8px;
  box-sizing: border-box;
  border: 0.5px solid;
}
.cherry-panel .cherry-panel--title {
  color: #fff;
  padding: 5px 20px;
}
.cherry-panel .cherry-panel--title.cherry-panel--title__not-empty::before {
  font-family: "ch-icon";
  margin: 0 12px 0 -6px;
  vertical-align: bottom;
}
.cherry-panel .cherry-panel--body {
  padding: 5px 20px;
}

.cherry-panel__primary {
  background-color: #cfe2ff;
  color: #0a58ca;
}
.cherry-panel__primary .cherry-panel--title {
  background-color: #0d6dfe;
}
.cherry-panel__primary .cherry-panel--title.cherry-panel--title__not-empty::before {
  content: "\ea6a";
}

.cherry-panel__info {
  background-color: #cff4fc;
  color: #087990;
}
.cherry-panel__info .cherry-panel--title {
  background-color: #099cba;
}
.cherry-panel__info .cherry-panel--title.cherry-panel--title__not-empty::before {
  content: "\ea69";
}

.cherry-panel__warning {
  background-color: #fff3cd;
  color: #997404;
}
.cherry-panel__warning .cherry-panel--title {
  background-color: #b38806;
}
.cherry-panel__warning .cherry-panel--title.cherry-panel--title__not-empty::before {
  content: "\ea6b";
}

.cherry-panel__danger {
  background-color: #f8d7da;
  color: #b02a37;
}
.cherry-panel__danger .cherry-panel--title {
  background-color: #dc3545;
}
.cherry-panel__danger .cherry-panel--title.cherry-panel--title__not-empty::before {
  content: "\ea68";
}

.cherry-panel__success {
  background-color: #d1e7dd;
  color: #146c43;
}
.cherry-panel__success .cherry-panel--title {
  background-color: #198754;
}
.cherry-panel__success .cherry-panel--title.cherry-panel--title__not-empty::before {
  content: "\ea67";
}

.cherry .doing-resize-img {
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
}
.cherry .cherry-previewer img {
  transition: all 0.1s;
}
.cherry .cherry-previewer-img-size-handler {
  position: absolute;
  box-shadow: 0 1px 4px 0 rgba(20, 81, 154, 0.5);
  border: 1px solid #3582fb;
  box-sizing: content-box;
  pointer-events: none;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points {
  position: absolute;
  height: 10px;
  width: 10px;
  margin-top: -7px;
  margin-left: -7px;
  border-radius: 9px;
  background: #3582fb;
  border: 2px solid #fff;
  box-sizing: content-box;
  box-shadow: 0px 2px 2px 0px rgba(20, 81, 154, 0.5);
  pointer-events: all;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__background {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  opacity: 0.5;
  width: 100%;
  height: 100%;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftTop {
  cursor: nw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightTop {
  cursor: sw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftBottom {
  cursor: sw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightBottom {
  cursor: nw-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-middleTop {
  cursor: n-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-middleBottom {
  cursor: n-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-leftMiddle {
  cursor: e-resize;
}
.cherry .cherry-previewer-img-size-handler .cherry-previewer-img-size-handler__points-rightMiddle {
  cursor: e-resize;
}
.cherry .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input {
  position: absolute;
}
.cherry .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  width: 100%;
  height: 100%;
  border: 0;
  box-sizing: border-box;
  resize: none;
  outline: 1px solid #3582fb;
  word-break: break-all;
}
.cherry .cherry-previewer-codeBlock-content-handler .cherry-previewer-codeBlock-content-handler__input {
  position: absolute;
}
.cherry .cherry-previewer-table-hover-handler {
  position: absolute;
  pointer-events: none;
  z-index: 999;
}
.cherry .cherry-previewer-table-hover-handler-container, .cherry .cherry-previewer-table-hover-handler-sort-container, .cherry .cherry-previewer-table-hover-handler-delete-container {
  position: absolute;
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.cherry .cherry-previewer-table-hover-handler__symbol {
  pointer-events: auto;
  display: flex;
  justify-content: center;
  position: absolute;
  color: #3582fb;
  width: 12px;
  height: 12px;
  line-height: 12px;
  border: 1px solid rgba(53, 130, 251, 0);
  background-color: rgba(255, 255, 255, 0);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__symbol:hover {
  background-color: rgba(53, 130, 251, 0.5333333333);
  color: #FFF;
}
.cherry .cherry-previewer-table-hover-handler__sort {
  pointer-events: auto;
  display: flex;
  justify-content: center;
  position: absolute;
  color: #3582fb;
  width: 12px;
  height: 12px;
  padding: 5px 0;
  line-height: 12px;
  border: 1px solid rgba(53, 130, 251, 0);
  background-color: rgba(255, 255, 255, 0);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__sort:hover {
  background-color: rgba(53, 130, 251, 0.5333333333);
  border-color: rgba(53, 130, 251, 0.5333333333);
  color: #FFF;
}
.cherry .cherry-previewer-table-hover-handler__sort[data-type=ColUp], .cherry .cherry-previewer-table-hover-handler__sort[data-type=ColDown] {
  padding: 0 5px;
}
.cherry .cherry-previewer-table-hover-handler__delete {
  pointer-events: auto;
  position: absolute;
  color: #FFF;
  width: 25px;
  height: 15px;
  font-size: 12px;
  line-height: 12px;
  border: 1px solid rgba(255, 77, 79, 0);
  border-radius: 3px;
  background-color: rgba(255, 77, 79, 0);
  color: rgba(255, 77, 79, 0.5);
  cursor: pointer;
  transition: all 0.3s;
}
.cherry .cherry-previewer-table-hover-handler__delete:hover {
  background-color: rgb(255, 77, 79);
  border-color: rgb(255, 77, 79);
  color: #FFF;
}
.cherry .cherry-previewer-table-hover-handler__delete[data-type=left], .cherry .cherry-previewer-table-hover-handler__delete[data-type=right] {
  padding: 0;
  width: 18px;
  height: 18px;
}
@keyframes changeBgColor {
  0% {
    background-color: rgba(255, 255, 204, 0.5333333333);
  }
  60% {
    background-color: rgba(255, 255, 204, 0.5333333333);
  }
  100% {
    background-color: rgba(255, 255, 204, 0);
  }
}
.cherry .cherry-highlight-line {
  animation: changeBgColor 3s;
}

@media print {
  img, figure, pre, table {
    page-break-inside: avoid;
  }
  .cherry-previewer {
    width: 100% !important;
    max-height: none;
    border-left: none !important;
  }
  .cherry-toolbar, .cherry-sidebar, .cherry-editor, .cherry-drag {
    display: none !important;
  }
}
.cherry-insert-formula-wrappler {
  width: 610px !important;
  height: 300px !important;
  padding: 15px;
  display: flex;
  position: fixed !important;
  z-index: 9999999;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  border-radius: 10px;
  background-color: #FFF !important;
  overflow: hidden;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-more {
  position: absolute;
  bottom: 0;
  font-size: 12px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-tabs {
  width: 100px;
  height: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-right: 10px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-tabs .cherry-insert-formula-tab {
  width: 100%;
  height: 30px;
  text-align: center;
  border: 1px solid #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-tabs .cherry-insert-formula-tab > a {
  display: block;
  text-decoration: none;
  user-select: none;
  user-select: none;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-tabs .cherry-insert-formula-tab:not(:first-child) {
  margin-top: 10px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-tabs .cherry-insert-formula-tab.active {
  color: black;
  border: 1px solid #000;
  border-radius: 5px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select {
  height: 100%;
  flex: 1;
  display: none;
  overflow-y: scroll;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary {
  width: 130px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary:not(:first-child) {
  margin-top: 10px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func {
  min-width: 200px;
  height: 260px;
  position: absolute;
  left: 255px;
  top: 0;
  z-index: 100;
  padding: 20px 10px;
  background-color: #fff;
  border-left: 1px solid rgba(0, 0, 0, 0.15);
  display: none;
  overflow-y: scroll;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func .cherry-insert-formula-categary__func-categary {
  border-top: 1px solid rgb(233, 236, 239);
  border-bottom: 1px solid rgb(233, 236, 239);
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func .cherry-insert-formula-categary__func-item {
  cursor: pointer;
  border: 1px solid #fff;
  display: inline-block;
  text-align: center;
  background-color: #f8f9fa;
  margin: 2px;
  padding: 2px;
  vertical-align: middle;
  line-height: 30px;
  border-color: #f8f9fa;
  border-radius: 5px;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func .cherry-insert-formula-categary__func-item:hover {
  border-color: #dae0e5;
  background-color: #e2e6ea;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func .cherry-insert-formula-categary__func-item svg, .cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func .cherry-insert-formula-categary__func-item img {
  pointer-events: none;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__func:hover {
  display: block;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__btn {
  cursor: pointer;
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  width: 100%;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__btn > img {
  width: 100%;
  height: 60%;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__btn:hover {
  color: #3582fb;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .cherry-insert-formula-categary__btn:hover + .cherry-insert-formula-categary__func {
  float: left;
  display: block;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select .cherry-insert-formula-categary .btn-light {
  color: #212529;
  background-color: #ebecf2;
  border-color: #f8f9fa;
}
.cherry-insert-formula-wrappler .cherry-insert-formula-select.active {
  display: block;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.formula-utils-bubble-container {
  width: 350px;
  height: 40px;
  background-color: #fff;
  position: fixed;
  left: 0;
  top: 0;
  display: none;
  z-index: 1000;
  box-sizing: border-box;
}
.formula-utils-bubble-container .formula-utils-btn {
  flex: 1;
  position: relative;
}
.formula-utils-bubble-container .formula-utils-btn > button {
  width: 100%;
  height: 100%;
  border: 1px solid #fff;
  background-color: #ebecf2;
  cursor: pointer;
  border-radius: 5px;
}
.formula-utils-bubble-container .formula-utils-btn > button:hover {
  background-color: #eee;
}
.formula-utils-bubble-container .formula-utils-btn > button:focus {
  outline: none;
}
.formula-utils-bubble-container .formula-utils-btn > button:active {
  background-color: #ddd;
}
.formula-utils-bubble-container .formula-utils-btn > button:hover + .formula-utils-submenu {
  display: block;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu {
  position: absolute;
  display: none;
  width: 100%;
  background-color: #fff;
  border: 1px solid #f8f9fa;
  left: 0;
  top: 100%;
  box-shadow: 0 0 5px #f8f9fa;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu:hover {
  display: block;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div {
  width: 100%;
  height: 40px;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button {
  width: 100%;
  height: 100%;
  border: 1px solid #fff;
  background-color: #fff;
  cursor: pointer;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:hover {
  background-color: #eee;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:focus {
  outline: none;
}
.formula-utils-bubble-container .formula-utils-btn .formula-utils-submenu div button:active {
  background-color: #ddd;
}

.cherry-shortcut-key-config-panel-wrapper {
  width: 250px !important;
  height: 400px !important;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner::-webkit-scrollbar {
  display: none;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .shortcut-top {
  width: 100%;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item {
  display: flex;
  justify-content: space-between;
  height: auto;
  padding: 2px 15px;
  border-top: 1px solid #eee;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .input-shortcut-wrapper {
  width: 100px;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .input-shortcut-wrapper input {
  width: 100%;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-name {
  max-width: 120px;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-kbd {
  display: flex;
  gap: 10px;
  min-width: 120px;
  justify-content: right;
}
.cherry-shortcut-key-config-panel-wrapper .cherry-shortcut-key-config-panel-inner .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-kbd .keyboard-key {
  border-radius: 3px;
  border-style: solid;
  border-width: 1px;
  display: inline-block;
  font-size: 11px;
  margin: 0 2px;
  padding: 3px 5px;
  vertical-align: middle;
  line-height: 20px;
  margin: 4px;
  min-width: 16px;
  text-align: center;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-static .cherry-shortcut-key-config-panel-ul {
  gap: 0;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-static .cherry-shortcut-key-config-panel-ul .shortcut-key-item {
  cursor: default;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-static .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-static {
  display: flex;
  gap: 3px;
  min-width: 80px;
  justify-content: right;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-static .cherry-shortcut-key-config-panel-ul .shortcut-key-item .shortcut-key-config-panel-static .shortcut-split {
  color: #aaa;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-panel-title, .cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings {
  font-size: 14px;
  padding: 10px 15px;
  background-color: #eee;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings {
  padding: 10px 15px;
  font-size: 12px;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #aaa;
  justify-content: space-between;
  display: flex;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings .shortcut-settings-btn {
  height: auto;
  line-height: 1.2em;
}
.cherry-shortcut-key-config-panel-wrapper .shortcut-panel-settings .shortcut-settings-btn:hover {
  color: rgb(255, 77, 79);
}
.cherry-shortcut-key-config-panel-wrapper.disable .cherry-shortcut-key-config-panel-ul {
  opacity: 0.3;
}
.cherry-shortcut-key-config-panel-wrapper.disable .j-shortcut-settings-disable-btn {
  color: rgb(255, 77, 79);
}

.cherry {
  display: flex;
  flex-flow: row wrap;
  align-items: stretch;
  align-content: flex-start;
  height: 100%;
  min-height: 60px;
  position: relative;
}
.cherry .cherry-editor,
.cherry .cherry-previewer {
  max-height: calc(100% - 48px);
  min-height: calc(100% - 48px);
}
.cherry .CodeMirror {
  height: 100%;
}
.cherry.cherry--no-toolbar .cherry-toolbar,
.cherry.cherry--no-toolbar .cherry-sidebar {
  height: 0;
  display: none;
}
.cherry.cherry--no-toolbar .cherry-editor,
.cherry.cherry--no-toolbar .cherry-previewer {
  max-height: 100%;
  min-height: 100%;
}

.cherry {
  font-family: "Helvetica Neue", Arial, "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  font-size: 16px;
  line-height: 27px;
  color: #3f4a56;
  background: #FFF;
  /* box-shadow: 0 0 10px rgba(128, 145, 165, 0.2); */
}
.cherry .ch-icon {
  vertical-align: middle;
}
.cherry .clearfix {
  zoom: 1;
}
.cherry .clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
  overflow: hidden;
  font-size: 0;
}
.cherry.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}
.cherry .no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.cherry .cherry-insert-table-menu {
  display: block;
  position: fixed;
  top: 40px;
  left: 40px;
  border-collapse: separate;
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
  padding: 4px;
  border-radius: 3px;
  width: auto;
  height: auto;
}
.cherry .cherry-insert-table-menu-item {
  padding: 7px;
  border: 1px solid #dfe6ee;
}
.cherry .cherry-insert-table-menu-item.active {
  background-color: #ebf3ff;
}
.cherry[data-toolbar-theme=dark] .cherry-insert-table-menu-item {
  border-color: rgba(255, 255, 255, 0.2);
}
.cherry[data-toolbar-theme=dark] .cherry-insert-table-menu-item.active {
  background-color: #d7e6fe;
}

.cherry-dropdown {
  position: absolute;
  width: 130px;
  min-height: 40px;
  background: #fff;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.5);
  margin-left: -60px;
  z-index: 13;
}
.cherry-dropdown-item {
  width: 100%;
  padding: 0 15px;
  text-align: left;
  display: inline-block;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  font-style: normal;
  cursor: pointer;
  box-sizing: border-box;
}
.cherry-dropdown-item:hover {
  background: #ebf3ff;
  color: #5d9bfc;
}
.cherry-dropdown-item__selected {
  background: #ebf3ff;
  color: #5d9bfc;
}
.cherry-dropdown-item .ch-icon {
  margin-right: 10px;
}
[data-toolbar-theme=dark] .cherry-dropdown {
  background: #20304b;
}
[data-toolbar-theme=dark] .cherry-dropdown .cherry-dropdown-item {
  background: transparent;
  color: #d7e6fe;
}
[data-toolbar-theme=dark] .cherry-dropdown .cherry-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}
[data-toolbar-theme=dark] .cherry-dropdown .cherry-dropdown-item__selected {
  background: rgba(255, 255, 255, 0.1);
  color: #5d9bfc;
}

.cherry-toolbar {
  position: relative;
  display: flex;
  align-items: baseline;
  justify-content: space-between;
  padding: 0 20px;
  min-height: 48px;
  font-size: 14px;
  line-height: 2.8;
  flex-basis: 100%;
  box-sizing: border-box;
  z-index: 2;
  user-select: none;
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
  background: white;
  overflow: hidden;
}
.cherry-toolbar .icon-loading.loading {
  display: inline-block;
  width: 8px;
  height: 8px;
}
.cherry-toolbar .icon-loading.loading:after {
  content: " ";
  display: block;
  width: 8px;
  height: 8px;
  margin-left: 2px;
  margin-top: -2px;
  border-radius: 50%;
  border: 2px solid #000;
  border-color: #000 transparent #000 transparent;
  animation: loading 1.2s linear infinite;
}
[data-toolbar-theme=dark] .cherry-toolbar {
  background: #20304b;
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
[data-toolbar-theme=dark] .cherry-toolbar .cherry-toolbar-button {
  color: #d7e6fe;
  background: transparent;
}
[data-toolbar-theme=dark] .cherry-toolbar .cherry-toolbar-button:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}
.cherry-toolbar .toolbar-left,
.cherry-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  min-height: 48px;
  flex-wrap: wrap;
  flex: 1;
}
.cherry-toolbar .toolbar-left {
  margin-right: 20px;
}
.cherry-toolbar .toolbar-right {
  flex: 0 1 auto;
  flex-direction: row-reverse;
  margin-left: 10px;
  box-sizing: border-box;
  min-height: 0;
}
.cherry-toolbar.preview-only .cherry-toolbar-button {
  display: none;
}
.cherry-toolbar.preview-only .cherry-toolbar-switchPreview {
  display: inline;
}
.cherry-toolbar-button {
  float: left;
  padding: 0 12px;
  height: 38px;
  color: #3f4a56;
  background: transparent;
  border: 1px solid transparent;
  -webkit-transition: background-color ease-in-out 0.15s, color ease-in-out 0.15s, border-color ease-in-out 0.15s;
  transition: background-color ease-in-out 0.15s, color ease-in-out 0.15s, border-color ease-in-out 0.15s;
  cursor: pointer;
  font-style: normal;
}
.cherry-toolbar-button:hover {
  color: #5d9bfc;
  background: #ebf3ff;
}
.cherry-toolbar-button.cherry-toolbar-split {
  font-size: 0;
  height: 19px;
  padding: 0;
  margin: 9.5px 4px;
  border: 1px solid transparent;
  border-left: 1px solid #dfe6ee;
  pointer-events: none;
  overflow: hidden;
  opacity: 0.5;
}
.cherry-toolbar-button.disabled {
  color: #ccc;
}

.cherry .ace_search {
  background: #fff;
}

.cherry-sidebar {
  width: 30px;
  position: absolute;
  top: 48px;
  right: 7px;
  z-index: 11;
  bottom: 0;
  overflow: hidden;
}
.cherry-sidebar .cherry-toolbar-button {
  height: 30px;
  padding: 3px 12px 0 6px;
}
.cherry-sidebar .cherry-toolbar-button:hover {
  background: transparent;
}
.cherry-sidebar .cherry-toolbar-button .icon-loading.loading {
  display: inline-block;
  width: 8px;
  height: 8px;
}
.cherry-sidebar .cherry-toolbar-button .icon-loading.loading:after {
  content: " ";
  display: block;
  width: 8px;
  height: 8px;
  margin-left: 2px;
  margin-top: -2px;
  border-radius: 50%;
  border: 2px solid #000;
  border-color: #000 transparent #000 transparent;
  animation: loading 1.2s linear infinite;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.cherry-bubble {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  font-size: 14px;
  min-height: 35px;
  min-width: 50px;
  border: 1px solid #dfe6ee;
  background-color: #fff;
  box-shadow: 0 2px 15px -5px rgba(0, 0, 0, 0.5);
  border-radius: 3px;
  z-index: 8;
}
.cherry-bubble.cherry-bubble--centered {
  left: 50%;
  transform: translateX(-50%);
}
.cherry-bubble .cherry-bubble-top,
.cherry-bubble .cherry-bubble-bottom {
  position: absolute;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -8px;
  border-left: 8px solid rgba(0, 0, 0, 0);
  border-right: 8px solid rgba(0, 0, 0, 0);
}
.cherry-bubble .cherry-bubble-top {
  top: 0;
  transform: translateY(-100%);
  border-bottom: 8px solid #fff;
}
.cherry-bubble .cherry-bubble-bottom {
  bottom: 0;
  transform: translateY(100%);
  border-top: 8px solid #fff;
}
.cherry-bubble .cherry-toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  cursor: pointer;
  user-select: none;
}
.cherry-bubble .cherry-toolbar-button:hover {
  border-color: #dfe6ee;
  background-color: rgba(89, 128, 166, 0.05);
}
.cherry-bubble .cherry-toolbar-button.cherry-toolbar-split {
  height: 65%;
  min-height: 22.75px;
}
[data-toolbar-theme=dark] .cherry-bubble {
  border-color: #20304b;
  background: #20304b;
}
[data-toolbar-theme=dark] .cherry-bubble .cherry-toolbar-button {
  color: #d7e6fe;
  background: transparent;
}
[data-toolbar-theme=dark] .cherry-bubble .cherry-toolbar-button:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}
[data-toolbar-theme=dark] .cherry-bubble .cherry-bubble-top {
  border-bottom-color: #20304b;
}
[data-toolbar-theme=dark] .cherry-bubble .cherry-bubble-bottom {
  border-top-color: #20304b;
}
[data-toolbar-theme=dark] .cherry-bubble .cherry-toolbar-button:hover {
  border-color: #20304b;
}

.cherry-switch-paste .switch-btn--bg {
  position: absolute;
  width: 50%;
  height: 100%;
  box-sizing: border-box;
  z-index: -1;
  left: 0;
  top: 0;
  opacity: 0.3;
  background-color: #5d9bfc;
  border-radius: 2px;
  transition: all 0.3s;
}
.cherry-switch-paste .cherry-toolbar-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  text-align: center;
}
.cherry-switch-paste .cherry-toolbar-button:hover {
  border-color: transparent;
}
.cherry-switch-paste[data-type=text] .cherry-text-btn {
  color: #3f4a56;
}
.cherry-switch-paste[data-type=text] .cherry-md-btn {
  color: #5d9bfc;
}
.cherry-switch-paste[data-type=md] .cherry-md-btn {
  color: #3f4a56;
}
.cherry-switch-paste[data-type=md] .cherry-text-btn {
  color: #5d9bfc;
}
.cherry-switch-paste[data-type=md] .switch-btn--bg {
  left: 50%;
}
[data-toolbar-theme=dark] .cherry-switch-paste .switch-btn--bg {
  background-color: #fff;
}
[data-toolbar-theme=dark] .cherry-switch-paste[data-type=text] .cherry-text-btn {
  color: #d7e6fe;
}
[data-toolbar-theme=dark] .cherry-switch-paste[data-type=text] .cherry-md-btn {
  color: #fff;
}
[data-toolbar-theme=dark] .cherry-switch-paste[data-type=md] .cherry-md-btn {
  color: #d7e6fe;
}
[data-toolbar-theme=dark] .cherry-switch-paste[data-type=md] .cherry-text-btn {
  color: #fff;
}
[data-toolbar-theme=dark] .cherry-switch-paste[data-type=md] .switch-btn--bg {
  left: 50%;
}

.cherry-floatmenu {
  z-index: 4;
  display: none;
  position: absolute;
  left: 30px;
  margin-left: 60px;
  height: 27px;
  line-height: 27px;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.cherry-floatmenu .cherry-toolbar-button {
  float: left;
  padding: 0 9px;
  margin: 0;
  height: 27px;
  line-height: 27px;
  font-size: 14px;
  color: #3f4a56;
  overflow: hidden;
  vertical-align: middle;
  text-align: center;
  border: 0;
  cursor: pointer;
  font-style: normal;
}
.cherry-floatmenu .cherry-toolbar-button.cherry-toolbar-split {
  border-left: 1px solid #dfe6ee;
  width: 0;
  padding: 0;
  overflow: hidden;
  height: 25px;
}
.cherry-floatmenu .cherry-toolbar-button .ch-icon {
  color: #aaa;
  font-size: 12px;
}
.cherry-floatmenu .cherry-toolbar-button:hover {
  background: rgba(0, 0, 0, 0.05);
}
.cherry-floatmenu .cherry-toolbar-button:hover .ch-icon {
  color: #3f4a56;
}

.cherry-editor {
  position: relative;
  padding-top: 5px;
  padding-right: 5px;
  width: 50%;
  box-sizing: border-box;
  overflow: hidden;
}
.cherry-editor.cherry-editor--full {
  width: 100%;
  padding-right: 0;
}
.cherry-editor.cherry-editor--hidden {
  display: none;
}
.cherry-editor-writing-style--focus::before {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  top: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.0235294118), rgba(0, 0, 0, 0.2));
  pointer-events: none;
  z-index: 11;
}
.cherry-editor-writing-style--focus::after {
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.0235294118), rgba(0, 0, 0, 0.2));
  pointer-events: none;
  z-index: 11;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines {
  position: relative;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines::before {
  content: "";
  display: block;
}
.cherry-editor-writing-style--typewriter .CodeMirror-lines::after {
  content: "";
  display: block;
}
.cherry-editor .CodeMirror {
  font-family: "Helvetica Neue", Arial, "Hiragino Sans GB", "STHeiti", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  background: #FFF;
  color: #3f4a56;
}
.cherry-editor .CodeMirror textarea {
  font-size: 27px;
}
.cherry-editor .CodeMirror-lines {
  padding: 15px 34px;
}
.cherry-editor .CodeMirror-lines .long-text,
.cherry-editor .CodeMirror-lines .drawio,
.cherry-editor .CodeMirror-lines .base64 {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
  white-space: nowrap;
  vertical-align: bottom;
  color: darkmagenta !important;
  font-size: 12px !important;
}
.cherry-editor .cm-s-default .cm-header {
  color: #3f4a56;
}
.cherry-editor .cm-s-default .cm-string {
  color: #3f4a56;
}
.cherry-editor .cm-s-default .cm-comment {
  color: #3582fb;
  font-family: "Menlo", "Liberation Mono", "Consolas", "DejaVu Sans Mono", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-whitespace,
.cherry-editor .cm-tab {
  font-family: "Menlo", "Liberation Mono", "Consolas", "DejaVu Sans Mono", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-quote {
  color: #3582fb;
}
.cherry-editor .cm-s-default .cm-link {
  color: #3582fb;
}
.cherry-editor .cm-s-default .cm-url {
  background: #d7e6fe;
  font-family: "Menlo", "Liberation Mono", "Consolas", "DejaVu Sans Mono", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace;
  font-size: 0.9em;
}
.cherry-editor .cm-s-default .cm-variable-2 {
  color: #3f4a56;
}
.cherry-editor .cm-s-default .cm-variable-3 {
  color: #3f4a56;
}
.cherry-editor .cm-s-default .cm-keyword {
  color: #3f4a56;
}
.cherry-editor .cm-s-default .cm-fullWidth {
  color: #d71616;
  z-index: 3;
  cursor: pointer;
}

.cherry-drag {
  width: 15px;
  cursor: ew-resize;
  position: absolute;
  z-index: 12;
  background: transparent;
}
.cherry-drag.cherry-drag--show {
  width: 5px;
  display: block;
  background: #dfe6ee;
}
.cherry-drag.cherry-drag--hidden {
  display: none;
}

.cherry-editor-mask {
  z-index: 10;
  position: absolute;
  display: none;
  background: rgba(0, 0, 0, 0.2);
}
.cherry-editor-mask.cherry-editor-mask--show {
  display: block;
}

.cherry-previewer-mask {
  z-index: 10;
  position: absolute;
  display: none;
  background: rgba(0, 0, 0, 0.4);
}
.cherry-previewer-mask.cherry-previewer-mask--show {
  display: block;
}

.cherry-previewer-codeBlock-click-handler {
  position: absolute;
  z-index: 1;
}

.cherry-mask-code-block {
  width: 100%;
  padding-top: 50px;
  display: none;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), to(#fff));
  background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
  text-align: center;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0.5em;
  z-index: 10;
}
.cherry-mask-code-block .expand-btn {
  width: 25px;
  height: 25px;
  border: 1px solid rgba(255, 255, 255, 0);
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.3s;
  z-index: 12;
  color: #3582fb;
  background-color: #d7e6fe;
  display: inline-block;
}
.cherry-mask-code-block .expand-btn:hover {
  color: #eee;
  background-color: #3582fb;
  border-color: #eee;
}

.cherry-code-unExpand pre {
  height: 240px;
  overflow: hidden !important;
}
.cherry-code-unExpand .cherry-mask-code-block {
  display: inline-block;
}

.cherry-previewer-codeBlock-hover-handler {
  z-index: 0;
  position: absolute;
  pointer-events: none;
}
.cherry-previewer-codeBlock-hover-handler * {
  pointer-events: all;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block {
  position: relative;
  width: 25px;
  text-align: center;
  height: 25px;
  border: 1px solid #3582fb;
  cursor: pointer;
  float: right;
  top: 15px;
  border-radius: 5px;
  transition: all 0.3s;
  z-index: 2;
  color: #3582fb;
  background-color: #eee;
  border-color: #3582fb;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn {
  width: auto;
}
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block {
  position: absolute;
  width: 25px;
  text-align: center;
  height: 25px;
  border: 1px solid #3582fb;
  cursor: pointer;
  float: right;
  border-radius: 5px;
  margin-left: -27px;
  transition: all 0.3s;
  z-index: 2;
  color: #3582fb;
  background-color: #eee;
  border-color: #3582fb;
}
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block {
  top: 45px;
  right: 10px;
}
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block {
  z-index: 12;
}
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block.hidden {
  display: none;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-block-custom-btn:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-copy-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-expand-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-unExpand-code-block:hover,
.cherry-previewer-codeBlock-hover-handler .cherry-edit-code-block:hover {
  color: #eee;
  background-color: #3582fb;
  border-color: #eee;
}
.cherry-previewer-codeBlock-hover-handler .cherry-code-preview-lang-select {
  position: absolute;
  transform: translate(2px, -50%);
}

.float-previewer-wrap {
  position: fixed;
  right: 0;
  top: 0;
  z-index: 100;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.1);
  resize: both;
  min-width: 430px;
  min-height: 300px;
}
.float-previewer-wrap.float-previewer-dragging {
  box-shadow: 0 0 60px rgba(0, 0, 0, 0.3);
}
.float-previewer-wrap.float-previewer-dragging .float-previewer-header {
  cursor: grabbing;
  background: #ace4ff;
}
.float-previewer-wrap .float-previewer-header {
  z-index: 999999;
  height: 40px;
  border-bottom: 1px solid #ebedee;
  background: #caecfd;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  cursor: grab;
}
.float-previewer-wrap .float-previewer-header .float-previewer-title {
  user-select: none;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}
.float-previewer-wrap .cherry-previewer {
  border-left: none;
}

.cherry-previewer {
  /* padding: 20px 45px 20px 20px; */
  /* border-left: 2px solid #ebedee; */
  width: 50%;
  box-sizing: border-box;
  background-color: #FFF;
  min-height: auto;
  overflow-y: auto;
  -webkit-print-color-adjust: exact;
}
.cherry-previewer .cherry-mobile-previewer-content {
  width: 375px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  overflow-y: scroll;
  /* box-shadow: 0 0 60px rgba(0, 0, 0, 0.1); */
  box-sizing: border-box;
}
.cherry-previewer.cherry-previewer--hidden {
  width: 0;
  display: none;
}
.cherry-previewer.cherry-previewer--full {
  width: 100%;
}
.cherry-previewer .cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-previewer .cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-previewer .cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-previewer .cherry-list__circle {
  list-style: circle;
}
.cherry-previewer .cherry-list__square {
  list-style: square;
}
[data-code-block-theme=default] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=default] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=default] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=funky] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=funky] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=funky] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=solarized-light] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=solarized-light] .cherry-previewer .cherry-edit-code-block, [data-code-block-theme=coy] .cherry-previewer .cherry-code-block-custom-btn,
[data-code-block-theme=coy] .cherry-previewer .cherry-copy-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-expand-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-unExpand-code-block,
[data-code-block-theme=coy] .cherry-previewer .cherry-edit-code-block {
  background-color: #3582fb;
}
@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.cherry-previewer .cherry-flow-session-cursor {
  background-color: rgba(53, 130, 251, 0.5333333333);
  padding: 0 2.5px;
  animation: blink 1s infinite;
}

.cherry-color-wrap {
  display: none;
  position: fixed;
  width: auto;
  padding: 5px 10px;
  z-index: 6;
  background: #fff;
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
.cherry-color-wrap h3 {
  font-size: 12px;
  margin: 0px;
  font-weight: 400;
}
[data-toolbar-theme=dark] .cherry-color-wrap h3 {
  color: #d7e6fe;
}
.cherry-color-wrap .cherry-color-text {
  float: left;
  width: 128px;
  margin: 0 8px 0 5px;
}
.cherry-color-wrap .cherry-color-bg {
  float: left;
  width: 128px;
  margin-right: 5px;
}
.cherry-color-wrap .cherry-color-item {
  float: left;
  width: 14px;
  height: 14px;
  border: 1px solid #fff;
  cursor: pointer;
}
.cherry-color-wrap .cherry-color-item:hover {
  border: 1px solid #000;
}

.Cherry-Math svg {
  max-width: 100%;
}

.cherry-suggester-panel {
  display: none;
  position: absolute;
  left: 0;
  top: 0;
  background: #fff;
  border-radius: 2px;
  max-height: 500px;
  box-shadow: 0 2px 8px 1px rgba(0, 0, 0, 0.2);
  overflow-x: hidden;
  overflow-y: auto;
}
.cherry-suggester-panel .cherry-suggester-panel__item {
  border: none;
  white-space: nowrap;
  min-width: 50px;
  padding: 5px 13px;
  color: #333;
  display: block;
  cursor: pointer;
}
.cherry-suggester-panel .cherry-suggester-panel__item.cherry-suggester-panel__item--selected {
  background-color: #f2f2f5;
  text-decoration: none;
  color: #eb7350;
}
.cherry-suggester-panel .cherry-suggester-panel__item > i {
  display: inline-block;
  transform: translateY(2px);
  margin-right: 8px;
}

.cherry-suggestion {
  background-color: #ebf3ff;
  color: #3582fb;
  padding: 1px 4px;
  border-radius: 3px;
  cursor: pointer;
}

.cherry-flex-toc {
  z-index: 11;
  position: absolute;
  width: 160px;
  height: calc(100% - 220px);
  max-height: 600px;
  right: 0;
  top: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: rgba(255, 255, 255, 0.2);
  margin-right: 8px;
  box-sizing: border-box;
  user-select: none;
  box-shadow: 0px 5px 11px rgba(51, 51, 51, 0.2);
  border-radius: 10px;
  transition: all 0.3s;
}
.cherry-flex-toc.cherry-flex-toc__fixed {
  position: fixed;
}
.cherry-flex-toc:hover {
  background-color: #fff;
  width: 260px;
}
.cherry-flex-toc .cherry-toc-head {
  border-bottom: 1px dashed rgba(51, 51, 51, 0.2);
  padding: 5px;
}
.cherry-flex-toc .cherry-toc-head .cherry-toc-title {
  font-size: 16px;
  font-weight: bold;
  padding-left: 5px;
}
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsLeft {
  display: none;
}
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsRight,
.cherry-flex-toc .cherry-toc-head .ch-icon-chevronsLeft {
  padding: 5px;
  position: absolute;
  right: 0;
  top: 0;
}
.cherry-flex-toc .cherry-toc-head i {
  cursor: pointer;
  padding: 5px 5px 0;
}
.cherry-flex-toc .cherry-toc-head i:hover {
  color: #3582fb;
}
.cherry-flex-toc .cherry-toc-list {
  overflow-y: auto;
  height: calc(100% - 40px);
  overflow-x: hidden;
  width: 100%;
  padding-bottom: 10px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a {
  display: block;
  text-decoration: none;
  color: #000;
  border-left: 5px solid rgba(51, 51, 51, 0.2);
  height: 28px;
  line-height: 28px;
  transition: all 0.3s;
  padding-left: 10px;
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  cursor: pointer;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a.current {
  border-left-color: #3582fb;
  color: #3582fb;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a:hover {
  border-left-color: rgba(53, 130, 251, 0.6666666667);
  color: rgba(53, 130, 251, 0.6666666667);
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__1 {
  font-weight: bold;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__2 {
  padding-left: 20px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__3 {
  padding-left: 40px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__4 {
  padding-left: 60px;
}
.cherry-flex-toc .cherry-toc-list .cherry-toc-one-a__5 {
  padding-left: 80px;
}
.cherry-flex-toc.cherry-flex-toc__pure {
  width: 30px;
  height: calc(100% - 200px);
  max-height: 600px;
  background: rgba(255, 255, 255, 0);
  box-shadow: none;
  border-radius: 0;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head {
  height: 25px;
  border-bottom: 1px dashed rgba(51, 51, 51, 0);
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .cherry-toc-title {
  display: none;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsRight {
  display: none;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsLeft {
  display: inline;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list {
  padding-left: 7px;
}
.cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list .cherry-toc-one-a {
  overflow: hidden;
  width: 0;
  margin-bottom: 3px;
  height: 5px;
  border-left-width: 18px;
}
.cherry-flex-toc.auto-num .cherry-toc-list {
  counter-reset: toclevel1;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1 {
  counter-reset: toclevel2;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2 {
  counter-reset: toclevel3;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3 {
  counter-reset: toclevel4;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4 {
  counter-reset: toclevel5;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5 {
  counter-reset: toclevel6;
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1:before {
  counter-increment: toclevel1;
  content: counter(toclevel1) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2:before {
  counter-increment: toclevel2;
  content: counter(toclevel1) "." counter(toclevel2) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3:before {
  counter-increment: toclevel3;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4:before {
  counter-increment: toclevel4;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) ". ";
}
.cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__6:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) "." counter(toclevel6) ". ";
}

/** 引入自带的主题 */
/** 编辑器样式 */
.cherry.theme__default {
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
  /** 光标focus到空行时联想出的按钮 */
}
.cherry.theme__default .cherry-dropdown {
  /** 选择颜色的按钮 */
}
.cherry.theme__default .cherry-dropdown .cherry-dropdown-item {
  /** 图标 */
}
.cherry.theme__default .cherry-dropdown.cherry-color-wrap .cherry-color-text {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__default .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
/** 预览区域样式 */
.cherry-markdown.theme__default {
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__default h1, .cherry-markdown.theme__default h2, .cherry-markdown.theme__default h3, .cherry-markdown.theme__default h4, .cherry-markdown.theme__default h5, .cherry-markdown.theme__default h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__default ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__default ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__default ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__default ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__default ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__default ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__default ruby {
  /** 上部的拼音 */
}
/** 色值可以参考：https://yeun.github.io/open-color/ */
/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__think {
  color: #999999;
  border-left: 2px solid #ebedee;
  box-shadow: none;
}
.cherry.theme__think .cherry-previewer {
  padding: 0 0 0 20px;
}
.cherry.theme__dark {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
  /* 目录区域样式 */
}
.cherry.theme__dark .cherry-toolbar, .cherry.theme__dark .cherry-floatmenu, .cherry.theme__dark .cherry-bubble, .cherry.theme__dark .cherry-sidebar {
  background: rgb(60, 60, 60);
  border-color: rgb(60, 60, 60);
}
.cherry.theme__dark .cherry-toolbar .cherry-toolbar-button, .cherry.theme__dark .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__dark .cherry-bubble .cherry-toolbar-button, .cherry.theme__dark .cherry-sidebar .cherry-toolbar-button {
  color: #d7e6fe;
}
.cherry.theme__dark .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__dark .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__dark .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__dark .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: rgb(69, 70, 70);
  color: rgb(255, 255, 255) !important;
  border-color: rgb(60, 60, 60);
}
.cherry.theme__dark .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__dark .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__dark .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__dark .cherry-sidebar .cherry-toolbar-button:hover i {
  color: rgb(255, 255, 255) !important;
}
.cherry.theme__dark .cherry-dropdown {
  background: rgb(60, 60, 60);
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__dark .cherry-dropdown .cherry-dropdown-item {
  color: #d7e6fe;
}
.cherry.theme__dark .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: rgb(69, 70, 70);
  color: rgb(255, 255, 255);
}
.cherry.theme__dark .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: rgb(69, 70, 70);
  color: rgb(255, 255, 255);
}
.cherry.theme__dark .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__dark .cherry-dropdown.cherry-color-wrap h3 {
  color: #d7e6fe;
}
.cherry.theme__dark .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: rgb(69, 70, 70);
}
.cherry.theme__dark .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: rgb(247, 133, 83);
}
.cherry.theme__dark .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: rgb(69, 70, 70);
}
.cherry.theme__dark .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__dark .cherry-bubble .cherry-bubble-bottom {
  border-top-color: rgb(60, 60, 60);
}
.cherry.theme__dark .cherry-bubble .cherry-bubble-top {
  border-bottom-color: rgb(60, 60, 60);
}
.cherry.theme__dark .cherry-editor {
  background-color: rgb(37, 37, 38);
}
.cherry.theme__dark .cherry-editor .CodeMirror {
  background-color: rgb(37, 37, 38);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid rgb(255, 255, 255);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: rgb(200, 200, 200);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: rgb(247, 133, 83);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: rgb(81, 56, 56);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: rgb(255, 203, 107);
}
.cherry.theme__dark .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: rgb(69, 70, 70);
}
.cherry.theme__dark .cherry-sidebar {
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
.cherry.theme__dark .cherry-previewer {
  background-color: rgb(51, 51, 51);
}
.cherry.theme__dark .cherry-previewer .cherry-mobile-previewer-content {
  background-color: rgb(37, 37, 38);
}
.cherry.theme__dark .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: rgb(37, 37, 38);
  color: rgb(200, 200, 200);
  outline-color: rgb(247, 133, 83);
}
.cherry.theme__dark .cherry-flex-toc:hover {
  background-color: #d0cece;
  width: 260px;
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-head i:hover {
  color: rgb(247, 133, 83);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a {
  border-left-color: rgba(247, 133, 83, 0.3);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a.current {
  border-left-color: rgb(255, 100, 33);
  color: rgb(255, 100, 33);
}
.cherry.theme__dark .cherry-flex-toc .cherry-toc-list .cherry-toc-one-a:hover {
  border-left-color: rgb(247, 162, 14);
  color: rgb(255, 100, 33);
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure {
  width: 30px;
  height: calc(100% - 200px);
  max-height: 600px;
  background: rgba(255, 255, 255, 0);
  box-shadow: none;
  border-radius: 0;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head {
  height: 25px;
  border-bottom: 1px dashed rgba(51, 51, 51, 0);
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .cherry-toc-title {
  display: none;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsRight {
  display: none;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-head .ch-icon-chevronsLeft {
  display: inline;
  color: rgb(255, 100, 33);
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list {
  padding-left: 7px;
}
.cherry.theme__dark .cherry-flex-toc.cherry-flex-toc__pure .cherry-toc-list .cherry-toc-one-a {
  overflow: hidden;
  width: 0;
  margin-bottom: 3px;
  height: 5px;
  border-left-width: 18px;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list {
  counter-reset: toclevel1;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1 {
  counter-reset: toclevel2;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2 {
  counter-reset: toclevel3;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3 {
  counter-reset: toclevel4;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4 {
  counter-reset: toclevel5;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5 {
  counter-reset: toclevel6;
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__1:before {
  counter-increment: toclevel1;
  content: counter(toclevel1) ". ";
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__2:before {
  counter-increment: toclevel2;
  content: counter(toclevel1) "." counter(toclevel2) ". ";
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__3:before {
  counter-increment: toclevel3;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) ". ";
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__4:before {
  counter-increment: toclevel4;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) ". ";
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__5:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) ". ";
}
.cherry.theme__dark .cherry-flex-toc.auto-num .cherry-toc-list .cherry-toc-one-a__6:before {
  counter-increment: toclevel5;
  content: counter(toclevel1) "." counter(toclevel2) "." counter(toclevel3) "." counter(toclevel4) "." counter(toclevel5) "." counter(toclevel6) ". ";
}

/** 预览区域样式 */
.cherry-markdown.theme__dark {
  color: rgb(200, 200, 200);
  background-color: rgb(51, 51, 51);
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__dark h1, .cherry-markdown.theme__dark h2, .cherry-markdown.theme__dark h3, .cherry-markdown.theme__dark h4, .cherry-markdown.theme__dark h5 {
  color: rgb(247, 133, 83);
}
.cherry-markdown.theme__dark h1, .cherry-markdown.theme__dark h2, .cherry-markdown.theme__dark h3, .cherry-markdown.theme__dark h4, .cherry-markdown.theme__dark h5, .cherry-markdown.theme__dark h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__dark ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__dark ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__dark ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__dark ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__dark ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__dark ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__dark ul.cherry-list__default {
  background-color: rgb(51, 51, 51);
}
.cherry-markdown.theme__dark blockquote {
  color: rgb(200, 200, 200);
}
.cherry-markdown.theme__dark a {
  text-decoration: none;
  color: rgb(255, 203, 107);
}
.cherry-markdown.theme__dark a:hover {
  color: rgb(247, 133, 83);
}
.cherry-markdown.theme__dark hr {
  border-color: rgb(105, 105, 105);
}
.cherry-markdown.theme__dark p code, .cherry-markdown.theme__dark li code {
  background-color: rgb(81, 56, 56);
  color: rgb(255, 203, 107);
  border: 1px solid rgb(105, 105, 105);
}
.cherry-markdown.theme__dark table, .cherry-markdown.theme__dark .cherry-table {
  color: rgb(200, 200, 200);
}
.cherry-markdown.theme__dark table th, .cherry-markdown.theme__dark .cherry-table th {
  background-color: rgb(81, 56, 56);
}
.cherry-markdown.theme__dark table tr, .cherry-markdown.theme__dark table th, .cherry-markdown.theme__dark table td, .cherry-markdown.theme__dark .cherry-table tr, .cherry-markdown.theme__dark .cherry-table th, .cherry-markdown.theme__dark .cherry-table td {
  border-color: rgb(105, 105, 105);
}
.cherry-markdown.theme__dark ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__dark .footnote {
  border-color: rgb(105, 105, 105);
}
.cherry-markdown.theme__dark .footnote .footnote-title {
  background-color: rgb(81, 56, 56);
}
.cherry-markdown.theme__dark .footnote .one-footnote {
  color: rgb(200, 200, 200);
  border-color: rgb(105, 105, 105);
}
.cherry-markdown.theme__dark .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown.theme__dark .toc {
  border: 1px solid rgb(105, 105, 105);
  margin-top: 15px;
  margin-bottom: 15px;
  margin-right: 15px;
}
.cherry-markdown.theme__dark .toc .toc-title {
  padding: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgb(105, 105, 105);
}
.cherry-markdown.theme__dark .toc .toc-li {
  border: none;
  padding: 0 20px;
}
.cherry-markdown.theme__dark .toc .toc-li a {
  color: rgb(200, 200, 200);
}
.cherry-markdown.theme__dark .toc .toc-li a:hover {
  color: rgb(247, 133, 83);
}
.cherry-markdown.theme__dark figure svg path, .cherry-markdown.theme__dark figure svg rect, .cherry-markdown.theme__dark figure svg line {
  stroke: rgb(255, 203, 107) !important;
}
.cherry-markdown.theme__dark figure svg text {
  fill: rgb(250, 160, 0) !important;
  stroke: none !important;
}
.cherry-markdown.theme__dark figure svg tspan {
  fill: rgb(250, 160, 0) !important;
}
.cherry-markdown.theme__dark figure svg circle {
  fill: rgb(236, 236, 255) !important;
}
.cherry-markdown.theme__dark figure svg circle.state-start {
  fill: rgb(250, 160, 0) !important;
}
@keyframes changeBgColorDark {
  0% {
    background-color: rgb(78, 76, 76);
  }
  60% {
    background-color: rgb(78, 76, 76);
  }
  100% {
    background-color: rgb(51, 51, 51);
  }
}
.cherry-markdown.theme__dark .cherry-highlight-line {
  animation: changeBgColorDark 3s;
}

/** 色值可以参考：https://yeun.github.io/open-color/ */
/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__light {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
}
.cherry.theme__light .cherry-toolbar, .cherry.theme__light .cherry-floatmenu, .cherry.theme__light .cherry-bubble, .cherry.theme__light .cherry-sidebar {
  background: white;
  border-color: white;
}
.cherry.theme__light .cherry-toolbar .cherry-toolbar-button, .cherry.theme__light .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__light .cherry-bubble .cherry-toolbar-button, .cherry.theme__light .cherry-sidebar .cherry-toolbar-button {
  color: #3f4a56;
}
.cherry.theme__light .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__light .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__light .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__light .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: #ebf3ff;
  color: #5d9bfc !important;
  border-color: white;
}
.cherry.theme__light .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__light .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__light .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__light .cherry-sidebar .cherry-toolbar-button:hover i {
  color: #5d9bfc !important;
}
.cherry.theme__light .cherry-dropdown {
  background: white;
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__light .cherry-dropdown .cherry-dropdown-item {
  color: #3f4a56;
}
.cherry.theme__light .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: #ebf3ff;
  color: #5d9bfc;
}
.cherry.theme__light .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: #ebf3ff;
  color: #5d9bfc;
}
.cherry.theme__light .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__light .cherry-dropdown.cherry-color-wrap h3 {
  color: #3f4a56;
}
.cherry.theme__light .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: #ebf3ff;
}
.cherry.theme__light .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: rgb(247, 133, 83);
}
.cherry.theme__light .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: #ebf3ff;
}
.cherry.theme__light .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__light .cherry-bubble .cherry-bubble-bottom {
  border-top-color: white;
}
.cherry.theme__light .cherry-bubble .cherry-bubble-top {
  border-bottom-color: white;
}
.cherry.theme__light .cherry-editor {
  background-color: rgb(255, 255, 255);
}
.cherry.theme__light .cherry-editor .CodeMirror {
  background-color: rgb(255, 255, 255);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid rgb(0, 0, 0);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: rgb(63, 74, 86);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: rgb(34, 139, 230);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: rgb(215, 230, 254);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: rgb(77, 171, 247);
}
.cherry.theme__light .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: #ebf3ff;
}
.cherry.theme__light .cherry-sidebar {
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
.cherry.theme__light .cherry-previewer {
  background-color: rgb(255, 255, 255);
}
.cherry.theme__light .cherry-previewer .cherry-mobile-previewer-content {
  background-color: rgb(255, 255, 255);
}
.cherry.theme__light .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: rgb(255, 255, 255);
  color: rgb(63, 74, 86);
  outline-color: rgb(34, 139, 230);
}

/** 预览区域样式 */
.cherry-markdown.theme__light {
  color: rgb(63, 74, 86);
  background-color: rgb(255, 255, 255);
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__light h1, .cherry-markdown.theme__light h2, .cherry-markdown.theme__light h3, .cherry-markdown.theme__light h4, .cherry-markdown.theme__light h5 {
  color: rgb(34, 139, 230);
}
.cherry-markdown.theme__light h1, .cherry-markdown.theme__light h2, .cherry-markdown.theme__light h3, .cherry-markdown.theme__light h4, .cherry-markdown.theme__light h5, .cherry-markdown.theme__light h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__light ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__light ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__light ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__light ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__light ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__light ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__light blockquote {
  color: rgb(63, 74, 86);
  background-color: rgb(231, 245, 255);
  border-color: rgb(25, 113, 194);
}
.cherry-markdown.theme__light a {
  text-decoration: none;
  color: rgb(77, 171, 247);
}
.cherry-markdown.theme__light a:hover {
  text-decoration: underline;
  color: rgb(34, 139, 230);
}
.cherry-markdown.theme__light hr {
  border-color: rgb(25, 113, 194);
}
.cherry-markdown.theme__light p code, .cherry-markdown.theme__light li code {
  background-color: rgb(215, 230, 254);
  color: rgb(77, 171, 247);
  border: 1px solid rgb(25, 113, 194);
}
.cherry-markdown.theme__light table, .cherry-markdown.theme__light .cherry-table {
  color: rgb(63, 74, 86);
}
.cherry-markdown.theme__light table th, .cherry-markdown.theme__light .cherry-table th {
  background-color: rgb(215, 230, 254);
}
.cherry-markdown.theme__light table tr, .cherry-markdown.theme__light table th, .cherry-markdown.theme__light table td, .cherry-markdown.theme__light .cherry-table tr, .cherry-markdown.theme__light .cherry-table th, .cherry-markdown.theme__light .cherry-table td {
  border-color: rgb(25, 113, 194);
}
.cherry-markdown.theme__light ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__light .footnote {
  border-color: rgb(25, 113, 194);
}
.cherry-markdown.theme__light .footnote .footnote-title {
  background-color: rgb(215, 230, 254);
}
.cherry-markdown.theme__light .footnote .one-footnote {
  color: rgb(63, 74, 86);
  border-color: rgb(25, 113, 194);
}
.cherry-markdown.theme__light .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
/** 色值可以参考：https://yeun.github.io/open-color/ */
/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__green {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
}
.cherry.theme__green .cherry-toolbar, .cherry.theme__green .cherry-floatmenu, .cherry.theme__green .cherry-bubble, .cherry.theme__green .cherry-sidebar {
  background: #FFF;
  border-color: #FFF;
}
.cherry.theme__green .cherry-toolbar .cherry-toolbar-button, .cherry.theme__green .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__green .cherry-bubble .cherry-toolbar-button, .cherry.theme__green .cherry-sidebar .cherry-toolbar-button {
  color: #2b8a3e;
}
.cherry.theme__green .cherry-toolbar .cherry-toolbar-button i, .cherry.theme__green .cherry-floatmenu .cherry-toolbar-button i, .cherry.theme__green .cherry-bubble .cherry-toolbar-button i, .cherry.theme__green .cherry-sidebar .cherry-toolbar-button i {
  color: #2b8a3e;
}
.cherry.theme__green .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__green .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__green .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__green .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: #51cf66;
  color: #ebfbee !important;
  border-color: #FFF;
}
.cherry.theme__green .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__green .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__green .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__green .cherry-sidebar .cherry-toolbar-button:hover i {
  color: #ebfbee !important;
}
.cherry.theme__green .cherry-dropdown {
  background: #FFF;
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__green .cherry-dropdown .cherry-dropdown-item {
  color: #2b8a3e;
}
.cherry.theme__green .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: #51cf66;
  color: #ebfbee;
}
.cherry.theme__green .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: #51cf66;
  color: #ebfbee;
}
.cherry.theme__green .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__green .cherry-dropdown.cherry-color-wrap h3 {
  color: #2b8a3e;
}
.cherry.theme__green .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: #51cf66;
}
.cherry.theme__green .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: #2b8a3e;
}
.cherry.theme__green .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: #51cf66;
}
.cherry.theme__green .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__green .cherry-bubble .cherry-bubble-bottom {
  border-top-color: #FFF;
}
.cherry.theme__green .cherry-bubble .cherry-bubble-top {
  border-bottom-color: #FFF;
}
.cherry.theme__green .cherry-editor {
  background-color: #FFF;
}
.cherry.theme__green .cherry-editor .CodeMirror {
  background-color: #FFF;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid #2b8a3e;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: #2b8a3e;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: #37b24d;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: #ebfbee;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: #40c057;
}
.cherry.theme__green .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: #b2f2bb;
}
.cherry.theme__green .cherry-sidebar {
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
.cherry.theme__green .cherry-previewer {
  background-color: #ebfbee;
}
.cherry.theme__green .cherry-previewer .cherry-mobile-previewer-content {
  background-color: #FFF;
}
.cherry.theme__green .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: #FFF;
  color: #2b8a3e;
  outline-color: #37b24d;
}

/** 预览区域样式 */
.cherry-markdown.theme__green {
  color: #2b8a3e;
  background-color: #ebfbee;
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__green h1, .cherry-markdown.theme__green h2, .cherry-markdown.theme__green h3, .cherry-markdown.theme__green h4, .cherry-markdown.theme__green h5 {
  color: #37b24d;
  text-align: center;
  margin-bottom: 35px;
}
.cherry-markdown.theme__green h1, .cherry-markdown.theme__green h2, .cherry-markdown.theme__green h3, .cherry-markdown.theme__green h4, .cherry-markdown.theme__green h5, .cherry-markdown.theme__green h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__green ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__green ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__green ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__green ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__green ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__green ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__green blockquote {
  color: #2b8a3e;
  background-color: #d3f9d8;
  border-color: #2f9e44;
}
.cherry-markdown.theme__green a {
  text-decoration: none;
  color: #40c057;
}
.cherry-markdown.theme__green a:hover {
  text-decoration: underline;
  color: #37b24d;
}
.cherry-markdown.theme__green hr {
  border-color: #2f9e44;
}
.cherry-markdown.theme__green p code, .cherry-markdown.theme__green li code {
  background-color: #d3f9d8;
  color: #40c057;
  border: 1px solid #2f9e44;
}
.cherry-markdown.theme__green table, .cherry-markdown.theme__green .cherry-table {
  color: #2b8a3e;
}
.cherry-markdown.theme__green table th, .cherry-markdown.theme__green .cherry-table th {
  background-color: #d3f9d8;
}
.cherry-markdown.theme__green table tr, .cherry-markdown.theme__green table th, .cherry-markdown.theme__green table td, .cherry-markdown.theme__green .cherry-table tr, .cherry-markdown.theme__green .cherry-table th, .cherry-markdown.theme__green .cherry-table td {
  border-color: #2f9e44;
}
.cherry-markdown.theme__green ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__green .footnote {
  border-color: #2f9e44;
}
.cherry-markdown.theme__green .footnote .footnote-title {
  background-color: #d3f9d8;
}
.cherry-markdown.theme__green .footnote .one-footnote {
  color: #2b8a3e;
  border-color: #2f9e44;
}
.cherry-markdown.theme__green .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown.theme__green .toc {
  border-bottom: 1px solid #2f9e44;
  padding-bottom: 15px;
  margin-bottom: 30px;
}
.cherry-markdown.theme__green .toc .toc-title {
  text-align: center;
  padding-bottom: 15px;
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 1px solid #2f9e44;
}
.cherry-markdown.theme__green .toc .toc-li {
  border: none;
}
.cherry-markdown.theme__green .toc .toc-li a {
  color: #2b8a3e;
}
.cherry-markdown.theme__green .toc .toc-li a:hover {
  color: #37b24d;
}

/** 色值可以参考：https://yeun.github.io/open-color/ */
/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__red {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
}
.cherry.theme__red .cherry-toolbar, .cherry.theme__red .cherry-floatmenu, .cherry.theme__red .cherry-bubble, .cherry.theme__red .cherry-sidebar {
  background: #ffdeeb;
  border-color: #ffdeeb;
}
.cherry.theme__red .cherry-toolbar .cherry-toolbar-button, .cherry.theme__red .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__red .cherry-bubble .cherry-toolbar-button, .cherry.theme__red .cherry-sidebar .cherry-toolbar-button {
  color: #c2255c;
}
.cherry.theme__red .cherry-toolbar .cherry-toolbar-button i, .cherry.theme__red .cherry-floatmenu .cherry-toolbar-button i, .cherry.theme__red .cherry-bubble .cherry-toolbar-button i, .cherry.theme__red .cherry-sidebar .cherry-toolbar-button i {
  color: #c2255c;
}
.cherry.theme__red .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__red .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__red .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__red .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: #f06595;
  color: #fff0f6 !important;
  border-color: #ffdeeb;
}
.cherry.theme__red .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__red .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__red .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__red .cherry-sidebar .cherry-toolbar-button:hover i {
  color: #fff0f6 !important;
}
.cherry.theme__red .cherry-dropdown {
  background: #ffdeeb;
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__red .cherry-dropdown .cherry-dropdown-item {
  color: #c2255c;
}
.cherry.theme__red .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: #f06595;
  color: #fff0f6;
}
.cherry.theme__red .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: #f06595;
  color: #fff0f6;
}
.cherry.theme__red .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__red .cherry-dropdown.cherry-color-wrap h3 {
  color: #c2255c;
}
.cherry.theme__red .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: #f06595;
}
.cherry.theme__red .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: #a61e4d;
}
.cherry.theme__red .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: #f06595;
}
.cherry.theme__red .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__red .cherry-bubble .cherry-bubble-bottom {
  border-top-color: #ffdeeb;
}
.cherry.theme__red .cherry-bubble .cherry-bubble-top {
  border-bottom-color: #ffdeeb;
}
.cherry.theme__red .cherry-editor {
  background-color: #fff0f6;
}
.cherry.theme__red .cherry-editor .CodeMirror {
  background-color: #fff0f6;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid #a61e4d;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: #a61e4d;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: #d6336c;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: #ffdeeb;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: #f06595;
}
.cherry.theme__red .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: #fcc2d7;
}
.cherry.theme__red .cherry-sidebar {
  box-shadow: 0 0 10px #fcc2d7;
}
.cherry.theme__red .cherry-previewer {
  background-color: #fff0f6;
}
.cherry.theme__red .cherry-previewer .cherry-mobile-previewer-content {
  background-color: #fff0f6;
}
.cherry.theme__red .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: #fff0f6;
  color: #a61e4d;
  outline-color: #d6336c;
}

/** 预览区域样式 */
.cherry-markdown.theme__red {
  color: #a61e4d;
  background-color: #fff0f6;
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__red h1, .cherry-markdown.theme__red h2, .cherry-markdown.theme__red h3, .cherry-markdown.theme__red h4, .cherry-markdown.theme__red h5 {
  color: #d6336c;
  text-align: center;
  border-bottom: 1px dashed #c2255c;
  padding-bottom: 15px;
  margin-bottom: 25px;
}
.cherry-markdown.theme__red h1, .cherry-markdown.theme__red h2, .cherry-markdown.theme__red h3, .cherry-markdown.theme__red h4, .cherry-markdown.theme__red h5, .cherry-markdown.theme__red h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__red ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__red ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__red ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__red ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__red ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__red ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__red blockquote {
  color: #a61e4d;
  background-color: #ffdeeb;
  border-color: #c2255c;
}
.cherry-markdown.theme__red a {
  text-decoration: none;
  color: #f06595;
}
.cherry-markdown.theme__red a:hover {
  text-decoration: underline;
  color: #d6336c;
}
.cherry-markdown.theme__red hr {
  border-color: #c2255c;
}
.cherry-markdown.theme__red p code, .cherry-markdown.theme__red li code {
  background-color: #ffdeeb;
  color: #f06595;
  border: 1px solid #c2255c;
}
.cherry-markdown.theme__red table, .cherry-markdown.theme__red .cherry-table {
  color: #a61e4d;
}
.cherry-markdown.theme__red table th, .cherry-markdown.theme__red .cherry-table th {
  background-color: #ffdeeb;
}
.cherry-markdown.theme__red table tr, .cherry-markdown.theme__red table th, .cherry-markdown.theme__red table td, .cherry-markdown.theme__red .cherry-table tr, .cherry-markdown.theme__red .cherry-table th, .cherry-markdown.theme__red .cherry-table td {
  border-color: #c2255c;
}
.cherry-markdown.theme__red ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__red .footnote {
  border-color: #c2255c;
}
.cherry-markdown.theme__red .footnote .footnote-title {
  background-color: #ffdeeb;
}
.cherry-markdown.theme__red .footnote .one-footnote {
  color: #a61e4d;
  border-color: #c2255c;
}
.cherry-markdown.theme__red .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown.theme__red .toc {
  border-bottom: 1px solid #c2255c;
  padding-bottom: 15px;
  margin-bottom: 30px;
}
.cherry-markdown.theme__red .toc .toc-title {
  text-align: center;
  padding-bottom: 15px;
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 1px solid #c2255c;
}
.cherry-markdown.theme__red .toc .toc-li {
  border: none;
}
.cherry-markdown.theme__red .toc .toc-li a {
  color: #a61e4d;
}
.cherry-markdown.theme__red .toc .toc-li a:hover {
  color: #d6336c;
}

/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__violet {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
  /** 光标focus到空行时联想出的按钮 */
}
.cherry.theme__violet .cherry-toolbar, .cherry.theme__violet .cherry-floatmenu, .cherry.theme__violet .cherry-bubble, .cherry.theme__violet .cherry-sidebar {
  background: #FFF;
  border-color: #FFF;
}
.cherry.theme__violet .cherry-toolbar .cherry-toolbar-button, .cherry.theme__violet .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__violet .cherry-bubble .cherry-toolbar-button, .cherry.theme__violet .cherry-sidebar .cherry-toolbar-button {
  color: #5f3dc4;
}
.cherry.theme__violet .cherry-toolbar .cherry-toolbar-button i, .cherry.theme__violet .cherry-floatmenu .cherry-toolbar-button i, .cherry.theme__violet .cherry-bubble .cherry-toolbar-button i, .cherry.theme__violet .cherry-sidebar .cherry-toolbar-button i {
  color: #5f3dc4;
}
.cherry.theme__violet .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__violet .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__violet .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__violet .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: #845ef7;
  color: #f3f0ff !important;
  border-color: #FFF;
}
.cherry.theme__violet .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__violet .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__violet .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__violet .cherry-sidebar .cherry-toolbar-button:hover i {
  color: #f3f0ff !important;
}
.cherry.theme__violet .cherry-dropdown {
  background: #FFF;
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__violet .cherry-dropdown .cherry-dropdown-item {
  color: #5f3dc4;
}
.cherry.theme__violet .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: #845ef7;
  color: #f3f0ff;
}
.cherry.theme__violet .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: #845ef7;
  color: #f3f0ff;
}
.cherry.theme__violet .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__violet .cherry-dropdown.cherry-color-wrap h3 {
  color: #5f3dc4;
}
.cherry.theme__violet .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: #845ef7;
}
.cherry.theme__violet .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: #5f3dc4;
}
.cherry.theme__violet .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: #845ef7;
}
.cherry.theme__violet .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__violet .cherry-bubble .cherry-bubble-bottom {
  border-top-color: #FFF;
}
.cherry.theme__violet .cherry-bubble .cherry-bubble-top {
  border-bottom-color: #FFF;
}
.cherry.theme__violet .cherry-editor {
  background-color: #FFF;
}
.cherry.theme__violet .cherry-editor .CodeMirror {
  background-color: #FFF;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid #5f3dc4;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: #5f3dc4;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: #7048e8;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: #f3f0ff;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: #7950f2;
}
.cherry.theme__violet .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: #d0bfff;
}
.cherry.theme__violet .cherry-sidebar {
  box-shadow: 0 0 10px rgba(128, 145, 165, 0.2);
}
.cherry.theme__violet .cherry-previewer {
  background-color: #FFF;
}
.cherry.theme__violet .cherry-previewer .cherry-mobile-previewer-content {
  background-color: #FFF;
}
.cherry.theme__violet .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: #FFF;
  color: #5f3dc4;
  outline-color: #7048e8;
}

/** 预览区域样式 */
.cherry-markdown.theme__violet {
  color: #5f3dc4;
  background-color: #FFF;
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__violet h1, .cherry-markdown.theme__violet h2, .cherry-markdown.theme__violet h3, .cherry-markdown.theme__violet h4, .cherry-markdown.theme__violet h5 {
  color: #7048e8;
  text-align: center;
  margin-bottom: 35px;
}
.cherry-markdown.theme__violet h1, .cherry-markdown.theme__violet h2, .cherry-markdown.theme__violet h3, .cherry-markdown.theme__violet h4, .cherry-markdown.theme__violet h5, .cherry-markdown.theme__violet h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__violet ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__violet ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__violet ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__violet ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__violet ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__violet ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__violet blockquote {
  color: #5f3dc4;
  background-color: #e5dbff;
  border-color: #6741d9;
}
.cherry-markdown.theme__violet a {
  text-decoration: none;
  color: #7950f2;
}
.cherry-markdown.theme__violet a:hover {
  text-decoration: underline;
  color: #7048e8;
}
.cherry-markdown.theme__violet hr {
  border-color: #6741d9;
}
.cherry-markdown.theme__violet p code, .cherry-markdown.theme__violet li code {
  background-color: #e5dbff;
  color: #7950f2;
  border: 1px solid #6741d9;
}
.cherry-markdown.theme__violet table, .cherry-markdown.theme__violet .cherry-table {
  color: #5f3dc4;
}
.cherry-markdown.theme__violet table th, .cherry-markdown.theme__violet .cherry-table th {
  background-color: #e5dbff;
}
.cherry-markdown.theme__violet table tr, .cherry-markdown.theme__violet table th, .cherry-markdown.theme__violet table td, .cherry-markdown.theme__violet .cherry-table tr, .cherry-markdown.theme__violet .cherry-table th, .cherry-markdown.theme__violet .cherry-table td {
  border-color: #6741d9;
}
.cherry-markdown.theme__violet ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__violet .footnote {
  border-color: #6741d9;
}
.cherry-markdown.theme__violet .footnote .footnote-title {
  background-color: #e5dbff;
}
.cherry-markdown.theme__violet .footnote .one-footnote {
  color: #5f3dc4;
  border-color: #6741d9;
}
.cherry-markdown.theme__violet .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown.theme__violet .toc {
  border-bottom: 1px solid #6741d9;
  padding-bottom: 15px;
  margin-bottom: 30px;
}
.cherry-markdown.theme__violet .toc .toc-title {
  text-align: center;
  padding-bottom: 15px;
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 1px solid #6741d9;
}
.cherry-markdown.theme__violet .toc .toc-li {
  border: none;
}
.cherry-markdown.theme__violet .toc .toc-li a {
  color: #5f3dc4;
}
.cherry-markdown.theme__violet .toc .toc-li a:hover {
  color: #7048e8;
}

/** 色值可以参考：https://yeun.github.io/open-color/ */
/** 工具栏样式 */
/** 编辑区域样式 */
/** 预览区域样式 */
/** markdown样式 */
/** 编辑器样式 */
.cherry.theme__blue {
  /** 顶部按钮, 选中文字时弹出的按钮, 光标focus到空行时联想出的按钮, 侧边栏按钮 */
  /** 二级菜单 */
  /** 选中文字时弹出的按钮 */
}
.cherry.theme__blue .cherry-toolbar, .cherry.theme__blue .cherry-floatmenu, .cherry.theme__blue .cherry-bubble, .cherry.theme__blue .cherry-sidebar {
  background: #EDE7F6;
  border-color: #EDE7F6;
  box-shadow: 0 0 12px #C5CAE9;
}
.cherry.theme__blue .cherry-toolbar .cherry-toolbar-button, .cherry.theme__blue .cherry-floatmenu .cherry-toolbar-button, .cherry.theme__blue .cherry-bubble .cherry-toolbar-button, .cherry.theme__blue .cherry-sidebar .cherry-toolbar-button {
  color: #3949AB;
}
.cherry.theme__blue .cherry-toolbar .cherry-toolbar-button i, .cherry.theme__blue .cherry-floatmenu .cherry-toolbar-button i, .cherry.theme__blue .cherry-bubble .cherry-toolbar-button i, .cherry.theme__blue .cherry-sidebar .cherry-toolbar-button i {
  color: #3949AB;
}
.cherry.theme__blue .cherry-toolbar .cherry-toolbar-button:hover, .cherry.theme__blue .cherry-floatmenu .cherry-toolbar-button:hover, .cherry.theme__blue .cherry-bubble .cherry-toolbar-button:hover, .cherry.theme__blue .cherry-sidebar .cherry-toolbar-button:hover {
  background-color: #B39DDB;
  color: #edf2ff !important;
  border-color: #EDE7F6;
}
.cherry.theme__blue .cherry-toolbar .cherry-toolbar-button:hover i, .cherry.theme__blue .cherry-floatmenu .cherry-toolbar-button:hover i, .cherry.theme__blue .cherry-bubble .cherry-toolbar-button:hover i, .cherry.theme__blue .cherry-sidebar .cherry-toolbar-button:hover i {
  color: #edf2ff !important;
}
.cherry.theme__blue .cherry-dropdown {
  background: #EDE7F6;
  /** 选择颜色的按钮 */
  /** 选择表格的按钮 */
}
.cherry.theme__blue .cherry-dropdown .cherry-dropdown-item {
  color: #3949AB;
}
.cherry.theme__blue .cherry-dropdown .cherry-dropdown-item__selected {
  background-color: #B39DDB;
  color: #edf2ff;
}
.cherry.theme__blue .cherry-dropdown .cherry-dropdown-item:hover {
  background-color: #B39DDB;
  color: #edf2ff;
}
.cherry.theme__blue .cherry-dropdown.cherry-color-wrap {
  /** 色盘的标题 */
  /** 色盘里的每一个色块 */
}
.cherry.theme__blue .cherry-dropdown.cherry-color-wrap h3 {
  color: #3949AB;
}
.cherry.theme__blue .cherry-dropdown.cherry-color-wrap .cherry-color-item {
  border-color: #B39DDB;
}
.cherry.theme__blue .cherry-dropdown.cherry-color-wrap .cherry-color-item:hover {
  border-color: #283593;
}
.cherry.theme__blue .cherry-dropdown .cherry-insert-table-menu-item {
  border-color: #B39DDB;
}
.cherry.theme__blue .cherry-bubble {
  /** 粘贴HTML内容时弹出的选择按钮 */
}
.cherry.theme__blue .cherry-bubble .cherry-bubble-bottom {
  border-top-color: #EDE7F6;
}
.cherry.theme__blue .cherry-bubble .cherry-bubble-top {
  border-bottom-color: #EDE7F6;
}
.cherry.theme__blue .cherry-editor {
  background-color: rgba(243, 240, 255, 0.862745098);
}
.cherry.theme__blue .cherry-editor .CodeMirror {
  background-color: rgba(243, 240, 255, 0.862745098);
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-cursor {
  border-left: 1px solid #283593;
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll span, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-variable-2, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-string, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-strong, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-em, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-meta {
  color: #283593;
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-image-marker, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-quote, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-header, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-atom {
  color: #303F9F;
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  background-color: #EDE7F6;
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-comment, .cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-scroll .cm-url {
  color: #8C9EFF;
}
.cherry.theme__blue .cherry-editor .CodeMirror .CodeMirror-selected {
  background-color: #D1C4E9;
}
.cherry.theme__blue .cherry-sidebar {
  box-shadow: 0 0 12px #C5CAE9;
}
.cherry.theme__blue .cherry-previewer {
  background-color: rgba(243, 240, 255, 0.862745098);
}
.cherry.theme__blue .cherry-previewer .cherry-mobile-previewer-content {
  background-color: rgba(243, 240, 255, 0.862745098);
}
.cherry.theme__blue .cherry-previewer-table-content-handler .cherry-previewer-table-content-handler__input textarea {
  background-color: rgba(243, 240, 255, 0.862745098);
  color: #283593;
  outline-color: #303F9F;
}

/** 预览区域样式 */
.cherry-markdown.theme__blue {
  color: #283593;
  background-color: rgba(243, 240, 255, 0.862745098);
  /** 行内代码 */
  /** 
  * 代码块
  */
  /** 
  * 表格
  */
  /** 可以理解为上下结构的音标，下部是文字，上部是对应的拼音 */
  /** 脚注 */
  /** 行间公式 */
  /** 段落公式 */
  /** 目录 */
}
.cherry-markdown.theme__blue h1, .cherry-markdown.theme__blue h2, .cherry-markdown.theme__blue h3, .cherry-markdown.theme__blue h4, .cherry-markdown.theme__blue h5 {
  color: #303F9F;
  text-align: center;
  border-bottom: 1px dashed #3949AB;
  padding-bottom: 15px;
  margin-bottom: 25px;
}
.cherry-markdown.theme__blue h1, .cherry-markdown.theme__blue h2, .cherry-markdown.theme__blue h3, .cherry-markdown.theme__blue h4, .cherry-markdown.theme__blue h5, .cherry-markdown.theme__blue h6 {
  /** 标题前面的锚点或序号 */
}
.cherry-markdown.theme__blue ul {
  /** checklist 模式，未勾选时 */
  /** checklist 模式，勾选时 */
}
.cherry-markdown.theme__blue ul.cherry-list__upper-roman {
  list-style: upper-roman;
}
.cherry-markdown.theme__blue ul.cherry-list__lower-greek {
  list-style: lower-greek;
}
.cherry-markdown.theme__blue ul.cherry-list__cjk-ideographic {
  list-style: cjk-ideographic;
}
.cherry-markdown.theme__blue ul.cherry-list__circle {
  list-style: circle;
}
.cherry-markdown.theme__blue ul.cherry-list__square {
  list-style: square;
}
.cherry-markdown.theme__blue blockquote {
  color: #283593;
  background-color: #EDE7F6;
  border-color: #3949AB;
}
.cherry-markdown.theme__blue a {
  text-decoration: none;
  color: #8C9EFF;
}
.cherry-markdown.theme__blue a:hover {
  text-decoration: underline;
  color: #303F9F;
}
.cherry-markdown.theme__blue hr {
  border-color: #3949AB;
}
.cherry-markdown.theme__blue p code, .cherry-markdown.theme__blue li code {
  background-color: #EDE7F6;
  color: #8C9EFF;
  border: 1px solid #3949AB;
}
.cherry-markdown.theme__blue table, .cherry-markdown.theme__blue .cherry-table {
  color: #283593;
}
.cherry-markdown.theme__blue table th, .cherry-markdown.theme__blue .cherry-table th {
  background-color: #EDE7F6;
}
.cherry-markdown.theme__blue table tr, .cherry-markdown.theme__blue table th, .cherry-markdown.theme__blue table td, .cherry-markdown.theme__blue .cherry-table tr, .cherry-markdown.theme__blue .cherry-table th, .cherry-markdown.theme__blue .cherry-table td {
  border-color: #3949AB;
}
.cherry-markdown.theme__blue ruby {
  /** 上部的拼音 */
}
.cherry-markdown.theme__blue .footnote {
  border-color: #3949AB;
}
.cherry-markdown.theme__blue .footnote .footnote-title {
  background-color: #EDE7F6;
}
.cherry-markdown.theme__blue .footnote .one-footnote {
  color: #283593;
  border-color: #3949AB;
}
.cherry-markdown.theme__blue .footnote .one-footnote a.footnote-ref {
  padding: 5px;
}
.cherry-markdown.theme__blue .toc {
  border-bottom: 1px solid #3949AB;
  padding-bottom: 15px;
  margin-bottom: 30px;
}
.cherry-markdown.theme__blue .toc .toc-title {
  text-align: center;
  padding-bottom: 15px;
  margin-top: 30px;
  margin-bottom: 15px;
  border-bottom: 1px solid #3949AB;
}
.cherry-markdown.theme__blue .toc .toc-li {
  border: none;
}
.cherry-markdown.theme__blue .toc .toc-li a {
  color: #283593;
}
.cherry-markdown.theme__blue .toc .toc-li a:hover {
  color: #303F9F;
}
