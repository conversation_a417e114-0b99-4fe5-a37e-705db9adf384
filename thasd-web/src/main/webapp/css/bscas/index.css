@IMPORT url("../rs/full.css");
@IMPORT url("./scrollbar.css");

html,body{
    width:100%;height:100%;max-height:100%;
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select:none;
    -khtml-user-select:none;
    -webkit-user-select:none;
    -ms-user-select:none;
    user-select:none;
}
body{
    background-size: cover;
    background-image: url("./images/53bg.png");
    /*background-image: url("./images/bg.jpg");*/
    /*background-image: url("./images/blue.jpg");*/
}
@font-face {
    font-family: 'bigsfont';
    src: url('./icon-font/iconfont.woff2?t=1686290785576') format('woff2'),
    url('./icon-font/iconfont.woff?t=1686290785576') format('woff'),
    url('./icon-font/iconfont.ttf?t=1686290785576') format('truetype');
}
.bigsfont {
    font-family: "bigsfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
@font-face{font-family:electronicFont;src:url(./font/DS-DIGIT.TTF)}
@font-face {
    font-family: "阿里妈妈数黑体 Bold";font-weight: 700;src: url("./webfont/HyUeJEaGq3KX.woff2") format("woff2"), url("./webfont/HyUeJEaGq3KX.woff") format("woff");
    font-display: swap;
}
@font-face {
    font-family: "思源宋体 SemiBold";font-weight: 700;src: url("./simsun/aEkEnPsCVLwi.woff2") format("woff2"), url("./simsun/aEkEnPsCVLwi.woff") format("woff");
    font-display: swap;
}
/*三高共管六病同防三级协同信息化管理平台之家基地中心登录管理更科学更便捷更高效*/
.wfont {
    font-family: "阿里妈妈数黑体 Bold";
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.wfont2 {
    font-family: "思源宋体 SemiBold";
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


.flex-row{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
}
.flex-col{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.big-main-box{
    height: 100vh;
    overflow:hidden;
}
.big-head-box{
    flex-shrink: 2;
    flex-basis: 90px;
    background-size: cover;
    background-image: url("./images/head_bg.png");
}
@media screen and (max-height: 1080px){
    .big-main-box{
        overflow:auto;
    }
}
.big-content-box{
    -ms-flex: 1;
    flex: 1;
    height: 0;
    flex-shrink: 0;
    flex-basis: 90px;
}
.big-head-box-text{
    width: 100%;height:90px;
    background-size: cover;
    background-image: url("./images/53titlebg.png");
}
.big-head-box-text-left{
    flex-shrink: 2;
    flex-basis: 23%;
}
.big-head-box-text-center{
    -ms-flex: 1;
    flex: 1;
    width: 0;
    flex-shrink: 0;
    flex-basis: 23%;
    justify-content: center;
    align-items: center;
}
.big-head-box-text-right{
    flex-shrink: 2;
    flex-basis: 23%;
    justify-content: end;
    align-items: start;
}

.big-content-box-left{
    flex-shrink: 2;
    flex-basis: 25%;
}
.big-content-box-center{
    -ms-flex: 1;
    flex: 1;
    width: 0;
    flex-shrink: 0;
    flex-basis: 25%;
}
.big-content-box-right{
    flex-shrink: 2;
    flex-basis: 25%;
}

.count-box{
    -ms-flex: 1;
    flex: 1;
}
.layui-card-header{
    position: relative;
    height: 42px;
    line-height: 42px;
    padding: 0;
    border-bottom: none;
    color: #333;
    border-radius: 2px 2px 0 0;
    font-family: "微软雅黑";
    font-size: 18px;
    font-weight: 300;
    color: #ffffff;
}
.layui-card-body{
    padding: 0 0;
}
.bottom-border{
    border-bottom: 2px solid rgba(69,186,192,.9);
}


.card-bg{background: rgba(32,81,111,0.57);}
.card-bg2{background: rgba(32,81,111,0.85);}
.card-bgn{background: none}
.space{margin:8px 8px;}
.pading0{ padding: 0!important;}
.pading10{ padding:0 10px!important;}
.border{border: 1px solid rgba(8,235 ,227,.3);}
.numBox{
    width: 100%;
    height: auto;
    display: grid;
    grid-template-columns: 25% 25% 25% 25% ;
    justify-content: space-around;
    align-items: center;
}
.center-card-body1{
    padding: 10px 25px
}
.numBox .numBoxItem{
    margin:10px 10px;
    white-space: nowrap;
    justify-content: center;
    align-items: center;
}
.numBoxItem .item{flex: 1;}
.numBoxItem .item-text{
    font-family: "微软雅黑";
    font-size: 16px;
    font-weight:400;
    color: #bce3e3;
    justify-content: center;
    align-items: center;
    flex: 1;
}
.numBoxItem .item-text p{
    padding-top: 8px;
}
.numBoxItem em {
    background: linear-gradient( 0deg,#45d3fd, #45d3fd, #61ddb1,#61ddb1);
    font-style: normal;
    background-size: cover;
    font-family: electronicFont;
    font-size: 32px;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}
.numBoxItem i {
    padding-left: 10px;
}
.icon {
    background-image: url(./images/iconbg.png);
    background-size: cover;
    width: 85px;
    height: 85px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.c-text{font-size: 42px;color: #08ebe3;letter-spacing:3px;padding-left: 5px;}
.hosname{
    margin-left: 2.15rem;
    line-height: 3.25rem;
    color: rgba(255,255,255,.8);
    font-size: 18px;
}
.time {
    margin-right: 2.15rem;
    line-height: 3.25rem;
    color: rgba(255,255,255,.8);
    font-size: .3rem;
    font-family: electronicFont,"黑体";
    font-size: 24px;
}
.fbgfont{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    font-family: "bigsfont" !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient( 0deg,#45d3fd, #45d3fd, #61ddb1,#61ddb1);
    font-style: normal;
    background-size: cover;
    font-size: 48px;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-fill-color: transparent;
}

.tit01 {
    background: linear-gradient(to right, #077d88, #20516f, #45bac0, #45bac0);
    padding: .03rem .1rem;
    text-indent:15px;
}
.header-d{text-indent: 20px;}
.header-d:before {
    position: absolute;
    height: 16px;
    width: 4px;
    background: #40adb4;
    border-radius: 5px;
    content: "";
    left: 10px;
    top: 13px;
}

.layui-table-view {
    margin: 0 !important;
}
.layui-table th, .layui-table td, .layui-table[lay-skin="line"], .layui-table[lay-skin="row"], .layui-table-view, .layui-table-tool, .layui-table-header, .layui-table-col-set, .layui-table-total, .layui-table-page, .layui-table-fixed-r, .layui-table-tips-main, .layui-table-grid-down {
    border-width: 0;
    border-style: solid;
    border-color: #e6e6e6;
}
.layui-table thead tr, .layui-table-header, .layui-table-tool, .layui-table-total, .layui-table-total tr, .layui-table-patch, .layui-table-mend, .layui-table tbody tr:hover, .layui-table-hover, .layui-table-click{
    background-color:rgba(255,255,255,0);
}
.layui-table{
    background-color:rgba(255,255,255,0);
}
.layui-table-header {
    background: #40adb41f !important;
}
.layui-table-header .layui-table{
    color: #ffffff;
}
.layui-table-main .layui-table{
    color: #9da8b8;
}
.layui-table[lay-even] tr:nth-child(even){
    background: #40adb41f !important;
}
.layui-table-cell{
    padding: 0!important;
}
.layui-table-main  .layui-table th, .layui-table-main .layui-table td{
    padding: 1px 0 !important;
}
.layui-table-view .layui-table{
    width: 100%;
}
.layui-table-body{overflow: hidden!important;}

.sycm{
    margin-left:-.5rem;margin-right:-.5rem;  padding: .16rem 0;
}
.sycm ul{
    display: inline-block;
    width: 100%;
    height: 100%;
}
.sycm li{ float: left; width:50%;height: 100%; text-align: center; position: relative;
    display: grid;
    justify-content: center;
    align-items: center;
}
.sycm li:before{ position:absolute; content: ""; height:65%; width: 1px; background: rgba(33 85 114); right: 0; top: 15%;}
.sycm li:last-child:before{ width: 0;}

.sycm li h2{ font-size:46px; padding-top: 10px; color: #08ebe3; font-weight: 700; font-family: Gotham, "Helvetica Neue", Helvetica, Arial, "sans-serif";}
.sycm li span{ font-size:20px; color: #9da8b8;}