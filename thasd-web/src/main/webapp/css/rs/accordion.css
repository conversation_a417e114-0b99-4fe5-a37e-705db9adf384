#accordion {
	-webkit-touch-callout: none;
	-webkit-user-select: none; 
	-khtml-user-select: none; 
	-moz-user-select: none; 
	-ms-user-select: none;
	user-select: none; 
	-ms-flex: 1 1;
	flex: 1 1;
	background: rgba(240, 240, 240, 0.65);
	/*padding: 16px 0px;*/
	overflow:auto;
	padding: 0 0 0 0;
	-ms-scroll-chaining: chained;
    -ms-overflow-style: none;
    -ms-content-zooming: zoom;
    -ms-scroll-rails: none;
    -ms-content-zoom-limit-min: 100%;
    -ms-content-zoom-limit-max: 500%;
    -ms-scroll-snap-type: proximity;
    -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
    -ms-overflow-style: none;
}
/*滚动条宽度*/  
#accordion::-webkit-scrollbar {  
    width:0;  
    height:0;
}  
/* 轨道样式 */  
#accordion::-webkit-scrollbar-track {
	background: rgba(0,0,0,0.1);
}  
/* Handle样式 */  
#accordion::-webkit-scrollbar-thumb {  
    /*border-radius: 10px;  */
    background: rgba(0,0,0,0.4);   
}  
/*当前窗口未激活的情况下*/  
#accordion::-webkit-scrollbar-thumb:window-inactive {  
   background: rgba(0,0,0,0.4);   
}  
/*hover到滚动条上*/  
#accordion::-webkit-scrollbar-thumb:vertical:hover{  
    background: rgba(0,0,0,0.5);   
}  
/*滚动条按下*/  
#accordion::-webkit-scrollbar-thumb:vertical:active{
  background: rgba(0,0,0,0.6); 
}
#accordion .menu-title{line-height:40px;width:100%;cursor:pointer;
	
	/*margin-top: 4px;
    margin-bottom: 4px;
    */
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
}
#accordion .menu-title a{
	padding-left:24px;
	line-height:40px;
	/*
	color: rgba(255, 255, 255, 0.65);
	background: rgb(0, 21, 41);
	*/
	color: #000000;
    background: rgb(245, 245, 245);
	font-size: 14px;
	flex:1;
	display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
     -webkit-box-shadow: 0 0 1px rgba(0,21,41,.35);
    box-shadow: 0 0 1px rgba(0,21,41,.35);
    
}


#accordion .menu-title a span{flex:1;}

#accordion .menu-title a:HOVER{
	color: rgb(0, 0, 0);
}
#accordion .menu-title a:ACTIVE{
	color: rgb(0, 0, 0);
}


#accordion a{
	text-decoration:none;font-weight:normal;color:#fff;
}
#accordion a i.menu-icon{margin-right:4px;font-size:1.4em;}
#accordion a i.arrow-menu{
	padding-right:10px;
    width:10px;
}

#accordion .menu-title.sel a{
	-webkit-box-shadow: 0 1px 8px  rgba(0,21,41,.35);
    box-shadow: 0 1px 8px  rgba(0,21,41,.35);
    background: #13a387;
    color: #ffffff;
}
#accordion .menu-title.sel a.header_h{
	-webkit-box-shadow: 0 1px 8px  rgba(0,21,41,.35);
    box-shadow: 0 1px 8px  rgba(0,21,41,.35);
    background: #13a387;
    color: #ffffff;
}
#accordion .menu-title.sel a:HOVER{
	 background: #13a387;
    color: #ffffff;
}
#accordion h3:FIRST-CHILD .menu-title a{
	
}

#accordion h3 .header{}
#accordion h3 .header_h{}

#accordion h3 ul {
width:100%;margin:0;padding:0;overflow:auto;
display:none;
/*
box-shadow: rgba(0, 0, 0, 0.45) 0px 2px 8px inset;
background: rgb(0, 12, 23);
*/
background: #ffffff;

}
#accordion h3 ul li a{
	display:inline-block;width:100%;height:36px;line-height:36px;
    /*color: rgba(255, 255, 255, 0.65);*/
	color: rgb(0, 0, 0);
    text-indent:48px;
    border-bottom:solid 1px #efefef;
}

#accordion h3 ul li a.menusel{  
	color: #13a387;
    background: rgb(241, 250, 255);
	
}

#accordion h3 ul li a:HOVER{
 background: rgb(240, 242, 245);
 color:#000;
}
#accordion h3 ul li a.sel{}
	
#accordion h3 a.sel{}	