@IMPORT url("scrollbar.css");
@IMPORT url("ant-icon.css");
@IMPORT url("mfont/mfont.css");
html,
body,
.layui-layout {
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select:none;
    -khtml-user-select:none;
    -webkit-user-select:none;
    -ms-user-select:none;
    user-select:none;
    height: 100%;
}
.menu___2IuJy .anticon {
    margin-right: 8px;
}
.layui-nav.layui-layout-right{padding: 0 5px 0 10px}
.pear-admin .layui-header,
.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header .layui-layout-left {
    transition: all .3s;
}

.pear-admin .layui-logo .title {
    font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
    border: 1px solid whitesmoke;
    border-radius: 6px;
    width: 150px;
}

.pear-admin .layui-header {
    left: 230px;
    width: calc(100% - 230px);
    background-color: white;
    border-bottom: 1px solid whitesmoke;
}

.pear-admin .layui-header .layui-nav-img {
    width: 26px;
    height: 26px;
    margin-right:5px;
}

.pear-admin .layui-layout-control {
    left: 120px;
    position: absolute;
}

.pear-admin .layui-logo {
    text-align: left!important;
    width: 230px;
    height: 60px;
    line-height: 60px;
    position: relative;
    /*background-color: #13a387;*/
    background-color: rgb(19 163 135 / 90%);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.pear-admin .layui-logo img {
    width: 28px;
    height: 28px;
    padding: 0 4px 0 0;
}

.pear-admin .layui-logo .title {
    font-size: 2.1vh;
    font-weight: normal;
    line-height: 1;
    color: #FFFFFF;
    position: relative;
    top: 0;
    white-space: pre-wrap;
}

.pear-admin .layui-logo .logo {
    /*display: none;*/
}

.pear-admin .layui-side {
    top: 0px;
    width: 230px;
    box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
    z-index: 9999;
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

.pear-admin .layui-side-scroll {
    height: calc(100% - 60px) !important;
    background-color: #13a387;
    width: 247px;

}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
    color: black;
    font-size: 15px;
}

.pear-admin .layui-body {
    left: 230px;
    bottom: 0px;
}

.pear-admin .layui-layout-left {
    left: 0px;

}

/** 隐 藏 布 局 样 式 */
.pear-mini .layui-logo .title {
    display: none;
}

.pear-mini .layui-logo .logo {
    display: inline-block;
}
.pear-mini .layui-side {
    width: 60px;
}

.pear-mini .layui-header {
    left: 60px;
    width: calc(100% - 60px);
}

.pear-mini .layui-body {
    left: 60px;
}

.pear-mini .layui-logo {
    width: 60px;
    justify-content: center;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
    display: none;
}

.pear-mini .bottom-nav li {
    width: 100% !important;
}

.pear-mini .layui-side-scroll {
    height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
    top: 0px !important;
    height: 2px !important;
    background-color: #5FB878;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
    display: none;
}

.pear-admin .layui-header .layui-nav-more {
    display: none;
}

.pear-collasped-pe {
    display: none;
    width: 50px;
    position: absolute;
    z-index: 400000;
    bottom: 30px;
    right: 30px;
    background-color: #5FB878 !important;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-radius: 50px;
    box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.pear-collasped-pe a {
    color: white !important;
}

@media screen and (min-width: 768px) {
    .layui-hide-sm {
        display: inline-block !important;
    }
}

@media screen and (min-width: 769px) {
    .layui-hide-sm {
        display: none !important;
    }
}

/** 新增兼容 */
@media screen and (max-width:768px) {
    .collaspe {
        display: none !important;
    }

    .pear-collasped-pe {
        display: inline-block !important;
    }

    .layui-layout-control {
        left: 45px !important;
    }

    .layui-layout-left {
        padding-left: 10px;
        padding-right: 10px;
    }

    .pear-mini .bottom-nav {
        display: none;
    }

    .pear-mini .layui-side-scroll {
        height: calc(100% - 62px);
    }

    /** 隐 藏 布 局 样 式 */
    .pear-mini .layui-side {
        width: 0px;
    }

    .pear-mini .layui-header {
        left: 0px;
        width: 100%;
    }

    .pear-mini .layui-body {
        left: 0px;
    }

    .pear-mini .layui-logo {
        width: 0px;
    }

    .pear-admin .layui-body {
        left: 0px;
    }

    .pear-admin .layui-header {
        left: 0px;
        width: 100%;
    }

    .pear-admin .pear-cover {
        width: 100%;
        height: 100%;
        background-color: #1E1E1E;
        display: block;
        position: absolute;
        z-index: 1000;
        opacity: 0;
        margin-top: -60px;
    }

    .pear-mini .pear-cover {
        display: none;
    }
}

@-webkit-keyframes am-horizontal-roll_show {
    0% {
        opacity: 1;
        -webkit-transform: translateX(2000px);
        transform: translateX(2000px)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes am-horizontal-roll_show {
    0% {
        opacity: 1;
        -webkit-transform: translateX(800px);
        -ms-transform: translateX(800px);
        transform: translateX(800px)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0)
    }
}

.layer-anim-right {
    -webkit-animation: am-horizontal-roll_show .6s ease-out;
    animation: am-horizontal-roll_show .6s ease-out;

}

/** 亮色侧边风格 */
.light-theme .layui-logo {
    background-color: white !important;
    color: black !important;
}

.light-theme .layui-side-scroll {
    background-color: white !important;
    color: black !important;
}

.light-theme .layui-side {
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

/** 主 题 选 择 界 面 样 式 */
.pearone-color .color-title {
    padding: 15px 0 0px 20px;
    margin-bottom: 4px;
}

.pearone-color .color-content {
    padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
    list-style: none;
    padding: 0px;
}

.pearone-color .color-content ul li {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 70px;
    height: 50px;
    margin: 0 20px 20px 0;
    padding: 2px 2px 2px 2px;
    background-color: #f2f2f2;
    cursor: pointer;
    font-size: 12px;
    color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
    width: 100%;
    height: 100%;
    padding: 4px;
    top: -5px;
    left: -5px;
    border: #5FB878 2px solid;
    opacity: 1;
    border-radius: 4px;
}

.pearone-color .color-content li:after {
    content: '';
    position: absolute;
    z-index: 20;
    top: 50%;
    left: 50%;
    width: 1px;
    height: 0;
    border: 2px solid #F2F2F2;
    transition: all .3s;
    -webkit-transition: all .3s;
    opacity: 0;
}

.select-color {
    margin-bottom: 30px;
}

.select-color .select-color-title {
    padding: 15px 0 0px 20px;
    margin-bottom: 4px;
}

.select-color .select-color-content {
    padding: 20px 0 0px 0px;
    margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
    background-color: gray;
    width: 30px;
    height: 30px;
    border-radius: 3px;
    float: left;
    margin-left: 20px;
    color: white;
    font-size: 18px;
    text-align: center;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
    line-height: 30px;
}

/** 友情链接 */
.more-setting {
    margin-top: 45px;
}

.more-setting form {
    margin-top: 30px;
}

.more-setting-title {
    padding: 15px 0 0px 20px;
    margin-bottom: 4px;
}

.more-setting .layui-form-label {
    width: 60px;
}

.more-menu-list {
    width: 100%;
    margin-top: 80px;
}

.more-menu-item:first-child {
    border-top: 1px solid #e8e8e8;
}

.more-menu-item .layui-icon {
    font-size: 18px;
    padding-right: 10px;
}

.more-menu-item {
    color: #595959;
    height: 50px;
    line-height: 50px;
    font-size: 16px;
    padding: 0 25px;
    border-bottom: 1px solid #e8e8e8;
    font-style: normal;
    display: block;
}

.more-menu-item:hover {
    background-color: whitesmoke;
}

.more-menu-item:after {
    color: #8c8c8c;
    right: 16px;
    content: "\e602";
    position: absolute;
    font-family: layui-icon !important;
}

.loader-main{
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: whitesmoke;
    z-index: 9999999;
    margin-top: -61px;
}
.loader {
    width: 50px;
    height: 50px;
    margin: 30px auto 40px;
    margin-top: 20%;
    position: relative;
    z-index: 999999;
    background-color: whitesmoke;
}
.loader:before {
    content: "";
    width: 50px;
    height: 7px;
    border-radius: 50%;
    background: #000;
    opacity: 0.1;
    position: absolute;
    top: 59px;
    left: 0;
    animation: shadow .5s linear infinite;
}
.loader:after {
    content: "";
    width: 50px;
    height: 50px;
    border-radius: 3px;
    background-color: #5FB878;
    position: absolute;
    top: 0;
    left: 0;
    animation: loading .5s linear infinite;
}
@-webkit-keyframes loading {
    17% {
        border-bottom-right-radius: 3px;
    }

    25% {
        transform: translateY(9px) rotate(22.5deg);
    }

    50% {
        transform: translateY(18px) scale(1, 0.9) rotate(45deg);
        border-bottom-right-radius: 40px;
    }

    75% {
        transform: translateY(9px) rotate(67.5deg);
    }

    100% {
        transform: translateY(0) rotate(90deg);
    }
}
@keyframes loading {
    17% {
        border-bottom-right-radius: 3px;
    }

    25% {
        transform: translateY(9px) rotate(22.5deg);
    }

    50% {
        transform: translateY(18px) scale(1, 0.9) rotate(45deg);
        border-bottom-right-radius: 40px;
    }

    75% {
        transform: translateY(9px) rotate(67.5deg);
    }

    100% {
        transform: translateY(0) rotate(90deg);
    }
}
@-webkit-keyframes shadow {

    0%,
    100% {
        transform: scale(1, 1);
    }

    50% {
        transform: scale(1.2, 1);
    }
}
@keyframes shadow {

    0%,
    100% {
        transform: scale(1, 1);
    }

    50% {
        transform: scale(1.2, 1);
    }
}
.admin-menu{display: none}
.admin-menu .layui-input-block{
    margin-left: 0;
}
.admin-menu .layui-input-block button{
    width: 100%;
    text-align:left;
}

.admin-menu .layui-card{box-shadow:none}
.admin-menu .layui-card-header{font-weight: bold;border-bottom-color: #E3E3E3}
.admin-menu .layui-card-body{font-size:12px;color:#ff2222;}