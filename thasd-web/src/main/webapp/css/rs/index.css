@IMPORT url("full.css");
@IMPORT url("accordionp.css");
@IMPORT url("ant-icon.css");
@IMPORT url("mfont/mfont.css");
html,body{width:100%;height:100%;max-height:100%;
moz-user-select: -moz-none;
-moz-user-select: none;
-o-user-select:none;
-khtml-user-select:none;
-webkit-user-select:none;
-ms-user-select:none;
user-select:none;
}
body{
	background-size:100% 100%;
}
.fullbox{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    height: 100vh;
    overflow:hidden;
}
.menubox{
	width:256px;
	min-width:256px;
    -webkit-box-shadow: 2px 0 6px rgba(0,21,41,.35);
    box-shadow: 2px 0 6px rgba(0,21,41,.35);
    position: relative;
    z-index: 10;
    background: #13a387;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction:column;
    flex-direction:column;
}
.logo___2J9hf {
    height: 64px;
    position:relative;
    line-height:64px;
    padding-left: 8px;
    background: #13a387;
    overflow: hidden;
    /*
    background:url("logo-bg.png") 50% 50% no-repeat #002140;
    background-size:227px 42px;
    */
}
.logo___2J9hf h1, .logo___2J9hf img {
    display: inline-block;
    vertical-align: middle;
}
.logo___2J9hf img {
	/*
	background:url("logo.png") no-repeat 50% 50%;
	background-size:24px 24px;
	*/
    height: 32px;
}
.logo___2J9hf h1 {
    color: #fff;
    font-size: 19px;
    margin: 0 0 0 2px;
    font-family: "webfont","Myriad Pro","Helvetica Neue",Arial,Helvetica,sans-serif;
    font-weight: 400;
    letter-spacing:1px;
    word-break:break-all;
    word-wrap:break-word;
}
.logo___2J9hf h1, .logo___2J9hf img {
    display: inline-block;
    vertical-align: middle;
}
.ant-layout-header {
    background: #001529;
    height: 65px;
    line-height: 65px;
    flex:0 0 auto;
    /*background:url("top_bg.jpg") #001529 50% 50% repeat-x;*/
}
.header___1L3tU {
    height: 65px;
    padding: 0 12px 0 0;
    /*
    background: #fff;
    -webkit-box-shadow: 0 1px 4px rgba(0,21,41,.08);
    box-shadow: 0 1px 4px rgba(0,21,41,.08);*/
    position: relative;
    
   background:#f5f6f8;
    -webkit-box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
    box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
}
.header___1L3tU span.titlename-bg{
	line-height: 68px;
	padding: 10px 0 10px 256px;
	background:url("bt.png") 24px 50% no-repeat;
	background-size: 177px 27px;
}
.header___1L3tU span.titlename {
    line-height: 68px;
    padding:0 0 0 24px;
    font-size: 28px;
    font-family: "webfont","Myriad Pro","Helvetica Neue",Arial,Helvetica,sans-serif;
    font-weight: 600;
    letter-spacing: 3px;
}
.right___3kEl5 {
    float: right;
    height: 100%;
}
.right___3kEl5 .action___119rd.ant-popover-open, .right___3kEl5 .action___119rd:hover {
    background: #e6f7ff;
    color:#333333;
}
.right___3kEl5 .action___119rd {
    cursor: pointer;
    padding: 0 12px;
    display: inline-block;
    -webkit-transition: all .3s;
    transition: all .3s;
    height: 100%;
}
.right___3kEl5 .account___39VBq .avatar___HsSLb {
    margin: 17px 8px 20px 0;
    color: #1890ff;
    background: hsla(0,0%,100%,.85);
    vertical-align: middle;
}
.ant-avatar-sm, .ant-avatar-sm>* {
    line-height: 24px;
}
.ant-avatar-sm {
    width: 24px;
    height: 24px;
    border-radius: 12px;
}
.ant-avatar-image {
    background: transparent;
}
.ant-avatar {
    font-family: "Monospaced Number","Chinese Quote",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: rgba(0,0,0,.65);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    display: inline-block;
    text-align: center;
    background: #ccc;
    color: #fff;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    vertical-align: middle;
    /*
    width: 32px;
    height: 32px;
    */
    line-height: 32px;
    border-radius: 16px;
}
.ant-avatar>img {
    width: 100%;
    height: 100%;
    display: block;
}
.ant-avatar-sm, .ant-avatar-sm>* {
    line-height: 24px;
}
.ant-avatar>* {
    line-height: 32px;
}
.contentbox{
	-ms-flex: 1;
    flex: 1;
    background: #f0f2f5;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction:column;
    flex-direction:column;
    overflow: hidden;
}
.pagebox{
    -ms-flex: 1;
    flex: 1;
}
.pagebox iframe{
    width: 100%;
    height: 100%;
}
.user-menu{
	position:absolute;
	margin-top:-2px;
	right:12px;
	display:none;
	/*
	border:solid 1px #ccc;
	width:200px;
	height:200px;
	background:#fff;
	border-radius:4px;*/
	
}
.user-menu-show{display:block;}
/*
.right___3kEl5 .action___119rd:hover .user-menu{
	display:block;
}
*/
.ant-dropdown {
    font-family: "Monospaced Number","Chinese Quote",-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: rgba(0,0,0,.65);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    list-style: none;
    z-index: 1050;
    display: block;
}
.ant-dropdown-menu {
    outline: none;
    position: relative;
    list-style-type: none;
    padding: 4px 0;
    margin: 0;
    text-align: left;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0 2px 8px rgba(0,0,0,.15);
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    background-clip: padding-box;
}
.menu___2IuJy .ant-dropdown-menu-item {
    width: 108px;
}
.ant-dropdown-menu-item-disabled:hover, .ant-dropdown-menu-submenu-title-disabled:hover {
    color: rgba(0,0,0,.25);
    background-color: #fff;
    cursor: not-allowed;
}
.ant-dropdown-menu-item:hover, .ant-dropdown-menu-submenu-title:hover {
    background-color: #e6f7ff;
}
.ant-dropdown-menu-item-disabled, .ant-dropdown-menu-submenu-title-disabled {
    color: rgba(0,0,0,.25);
    cursor: not-allowed;
}
.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {
    padding: 5px 0;
    margin: 0;
    clear: both;
    font-size: 14px;
    font-weight: normal;
    color: rgba(0,0,0,.65);
    white-space: nowrap;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
    line-height: 22px;
    text-indent:6px;
}
.menu___2IuJy .anticon {
    margin-right: 8px;
}
.menu___2IuJy .anticon.anticon-logout{font-size:12px;}
li {
    display: list-item;
    text-align: -webkit-match-parent;
}