@IMPORT url("mfont/mfont.css");
@IMPORT url("scrollbar.css");
@IMPORT url("treeicon.css");

html,body{width:100%;height:100%;}
body{
	background-size:100% 100%;
}
.fulllistbox{
	display: -ms-flexbox;
   display: flex;
   -ms-flex-direction: column;
   flex-direction: column;
   height: 100vh;
   overflow: auto;
}
.initbox{
    position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
}
.initbox > span[class*="-icon"] {
    width: 45px;
    height: 45px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}
.titlebox{
    background: #fff;
    padding: 16px 32px 0;
    border-bottom: 1px solid #e8e8e8;
}
.titlebox .title-name{
	padding:16px 0 0 0;
    font-size: 20px;
    font-weight: 500;
    color: rgba(0,0,0,.85);
}
.listbox{
	-ms-flex: 1;
    flex: 1;
    overflow-x: hidden;
    overflow-x: hidden;
    background: #f0f2f5;
    padding:15px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.listbox .listbox-body{
	background:#ffffff;
	-ms-flex: 1;
    flex: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 0;
}


.listbox .listbox-body-menu{
	background:#ffffff;
	-ms-flex: 1;
    flex: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
}
.listbox .listbox-body-menu .leftMenu{
	min-width:256px;
	background:#f0f2f5;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-right:12px;
    flex-shrink:2;
}
.listbox .listbox-body-menu .leftMenu .leftMenu-title{
	height:42px;line-height:42px;
	padding: 0 0 0 20px;;
    -webkit-box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
    box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
    font-size:14px;
    font-family: "Myriad Pro","Helvetica Neue",Arial,Helvetica,sans-serif;
    font-weight:540;
    flex-shrink: 2;
    flex-basis: 42px;
}
.listbox .listbox-body-menu .leftMenu .treeMe{
	-ms-flex: 1;
    flex: 1;
    overflow:auto;
    -webkit-box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
    box-shadow: 0 1px 4px rgba(1, 10, 19, 0.28);
    height: 0;
    flex-shrink: 0;
    flex-basis: 42px;
    background: #ffffff;
}
.tree-content {
    background: #ffffff;
    height:auto;
    width:100%;
}
.listbox .listbox-body-menu .menuTabel{
	background:#ffffff;
	-ms-flex: 1;
    flex: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width:0;
    flex-shrink: 0;
}
.layui-card-header.layuiadmin-card-header-auto {
    padding-top: 5px;
    padding-bottom: 0;
    height:auto;
    box-shadow: 0 1px 4px rgb(207 207 207 / 70%);
    border-bottom-color:#efefef;
}
.layui-form-label{
font-size:12px;
width:60px;
text-align:left;
}
.layui-form-item{margin-bottom:0;}

/********/
.layui-card-header{line-height:normal;height:30px}
.layui-form-item .layui-inline .layui-form-label{padding:4px 1px}
.layui-form-item .layui-input{height:28px;line-height:28px;font-size:12px;}
.layui-form-item .layui-btn{height:28px;line-height:28px}
.layui-form-item .layui-btn.user-search{
	height: 27px;
    line-height: 27px;
    margin-bottom: 5px;
}
.layui-form-item .layui-inline{margin-right:0;}
.layui-form-item .layui-input-inline{width:160px;}
/********/
.layui-input-block {
    float: left;
    margin-left:0;
}
.layui-card-body{
	-ms-flex:1;
    flex: 1;
    padding: 5px 15px 0 15px;
    overflow:auto;
    height: 0;
}
.layui-card-body.body-tree-table{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-card-body.body-tree-table .ew-tree-table{
    -ms-flex:1;
    flex: 1;
    height: 0;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-tool{
    flex-shrink: 0;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-head{
    flex-shrink: 0;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-box{
    -ms-flex:1;
    flex: 1;
    height: 0;
}


.layui-card.search-box{margin:0;
    /*background:#f0f2f5 ;*/
}
.layui-card.opt-box{margin:0;}
.opt{padding-top: 15px;padding-left:15px;}

.layui-card.opt-box.opt-box-flex{
    display: flex;
    display: -ms-flexbox;
    -ms-flex-direction: row;
    flex-direction: row;
}
.layui-card.opt-box.opt-box-flex .opt{
    -ms-flex:1;
    flex: 1;
    width: 0;
    flex-shrink: 0;
    flex-basis: 42px;
}

.layui-card.opt-box.opt-box-flex .extBox{
    padding-top: 15px;
    padding-right: 15px;
    flex-shrink: 2;
    flex-basis: 280px;

    display: flex;
    display: -ms-flexbox;
    -ms-flex-direction: row;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}
.layui-card.opt-box.opt-box-flex .extBox i{
    display: flex;
    display: -ms-flexbox;
    -ms-flex-direction: row;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width:42px;
    font-size:12px;font-style: normal;

}


.list-opt-box{
	padding:10px 24px 10px 0;
	border-top:solid 1px #e8e8e8;
	text-align:right;
}
.layui-btn.list-opt-btn{line-height:32px;height:32px;padding: 0 22px;}

/**/
.layui-btn.icon{padding:0 18px 0 8px;}
.layui-btn.icon i.layui-icon{margin-right:10px;}
/*.layui-table-page{padding: 12px 7px 0;}*/

.tree-content-select {
    display: none;
    position: absolute;
    max-height:210px;
    left: 0 !important;
    top: 38px !important;
    background: #ffffff;
    z-index: 9999999;
    border: 1px solid #C9C9C9 !important;
    overflow-y: auto;
}

.layui-form-select .clearInpt {
	display:none;
    position: absolute;
    right: 7px;
    top: 26%;
    margin-top: -3px;
    cursor: pointer;
    z-index: 9999;
}
.layui-form-select .clearInpt i{    
	font-size:1.4em
}

.search-box .layui-form-item .layui-form-checkbox[lay-skin="primary"] {
    margin-top: 4px!important;
}

.search-box .layui-form-checkbox span{font-size:12px}

/* 查询表单容器样式 */
.search-form-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    min-height: 60px;
}

.search-fields {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex: 1;
}

.search-button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    flex-shrink: 0;
    height: 100%;
}

/* 确保查询按钮在小屏幕上也保持在右侧 */
@media (max-width: 768px) {
    .search-form-container {
        flex-direction: column;
        align-items: stretch;
    }

    .search-button-container {
        margin-left: 0;
        margin-top: 10px;
        justify-content: flex-end;
    }
}