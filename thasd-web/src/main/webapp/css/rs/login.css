@IMPORT url("full.css");
@IMPORT url("scrollbar.css");
@IMPORT url("ant-input.css");
@IMPORT url("ant-icon.css");
@IMPORT url("mfont/mfont.css");
html,body{width:100%;height:100%;
moz-user-select: -moz-none;
-moz-user-select: none;
-o-user-select:none;
-khtml-user-select:none;
-webkit-user-select:none;
-ms-user-select:none;
user-select:none;
}
body{
}
body:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin-left: -48%;
    background-image: url(login-bg.b9f5c736.svg);
    background-position: 100%;
    background-repeat: no-repeat;
    background-size: auto 100%;
    content: "";
}
.mainbox{

    /*background: rgba(0,0,0,0.65);*/
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100vh;
    overflow: auto;
    
}
.logo-info{
	-ms-flex: 1 1;
    flex: 1 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction:row;
    flex-direction: row;
    align-items:center;
    justify-content:center;
}
.top{
	
    text-align:center;
}
.top .header{    
	height: 66px;
    line-height: 66x;
    border-top-left-radius:10px;
	border-top-right-radius:10px;
	background: #87e0fd; /* Old browsers */
	background: -moz-linear-gradient(top, #37446b 0%, #000c17 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, #37446b 0%,#000c17 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, #37446b 0%,#000c17 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#37446b', endColorstr='#000c17',GradientType=0 ); /* IE6-9 */
}
.top .header .logo-box{
	height: 66px;
	display: -ms-flexbox;
    display: flex;
    -ms-flex-direction:row;
    flex-direction: row;
    align-items:center;
    justify-content:center;
    /*background:url("logo-text.png") 50% 50% no-repeat;*/
}
	
.top .header .logo{
	width:36px;
    height: 36px;
    margin-right: 6px;
    /*
    border:solid 2px #ffffff;
    border-radius:20px;
    background:url(logo.png) #ffffff 50% 50% no-repeat;background-size:36px 35px; 
    */
    background:url(logo.png)  50% 50% no-repeat;background-size:36px 36px; 
}
.top .header .title{
	font-size:28px;
    color: #ffffff;
    font-family: "webfont","Myriad Pro","Helvetica Neue",Arial,Helvetica,sans-serif;
    font-weight: 400;
    position: relative;
    letter-spacing:1px;
}
.main{
    overflow: auto;
    background-color: transparent;
    webkit-box-shadow: 0 -1px 53px 0 rgb(255 255 255/.43);
    -webkit-box-shadow: 0 -1px 53px 0 rgb(255 255 255/.43);
    /*box-shadow: 0 20px 80px 0 rgba(0,0,0,.3);*/
    box-shadow: 0 -1px 53px 0 rgb(255 255 255/.43);
    /*background: url(./left-1-e6bd44245f3950a6f5787218eeeed90b.png) no-repeat #1acc7b;*/
    width: 100%;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-direction: row;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.left_box {
    height: 100%;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
}
.left_box_info{
    margin-left: 20%;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
}
.left_box_info div{
    flex-basis: 72px;
    -webkit-flex-basis: 72px;
}
.top_logo{
    width:100%;
    -ms-flex: 1;
    flex: 1;
    z-index: 9999;
    flex-grow: 2;
}
.center_img{
    width:100%;
    height:420px;
    background:url("login-icon1.svg") no-repeat 0 50% ;
    /*background: url(./left_box_pic.png) 30px 50% no-repeat;*/
    /*background-size: 322px 236px;*/
    z-index: 9999;
    flex-grow: 6;
}
.bottom_text{
    -ms-flex: 1;
    flex: 1;
    width:100%;
    z-index: 99999;
    flex-grow:3;

}
.bottom_text p.one{
    font-size:1.4vw;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Liberation Sans", "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", SimHei, SimSun, "WenQuanYi Zen Hei Sharp", sans-serif;
    line-height: 1.33em;
    font-weight:normal;
    padding: 32px 0 0 32px;
    letter-spacing:2px;
    color: #ffffff;
}
.bottom_text p.two{
    font-size:1.5vw;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Liberation Sans", "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", SimHei, SimSun, "WenQuanYi Zen Hei Sharp", sans-serif;
    line-height: 1.33em;
    font-weight: normal;
    padding: 26px 0 0 32px;
    letter-spacing:2px;
    color: #ffffff;
}
.login_txt_t2{
    font-size:21px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Liberation Sans", "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", "Wenquanyi Micro Hei", "WenQuanYi Zen Hei", "ST Heiti", SimHei, SimSun, "WenQuanYi Zen Hei Sharp", sans-serif;
    line-height: 1.33em;
    font-weight: bold;
    padding: 0em;
    letter-spacing:2px;
    position: absolute;
    margin-top: 76px;
    margin-left: 67px;
    color: #ffffff;
}
.mainbox img.logo {
    position: absolute;
    /*margin-top: 36px;*/
    /*margin-left: 36px;*/
    margin-top: 98px;
    margin-left: 30px;
}
.mainbox img.logo:after, :before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.left_box img {
    position: absolute;
    /*margin-top: 36px;*/
    /*margin-left: 36px;*/
    margin-top: 48px;
    margin-left: 30px;
}
.left_box img:after, :before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.right_box {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 60px;
    padding-right: 60px;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-direction: column;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: #ffffff;
}
.globalFooter{
	padding: 0 16px;
    margin: 48px 0 24px;
    text-align: center;
    color:#000;
}
.info{
	border-radius:10px;
	background: rgba(250, 250, 250, 0.61);
	 -webkit-box-shadow: 1px 0 22px  rgba(10, 10, 10, 0.62);
    box-shadow: 1px 0 22px  rgba(10, 10, 10, 0.62);
}
.form-box{
    width:360px;
    display: -ms-flexbox;
    display: -webkit-box;
    display: flex;
    -ms-flex-direction: row;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    flex: 1;
}
.login-icon{
	width:180px;
	background:url("login-icon.png") no-repeat 50% 50%;
	background-size:90px 120px;
}
.login-sline{
	width:4px;
	display: -ms-flexbox;
    display: flex;
    -ms-flex-direction:row;
    flex-direction: row;
    align-items:center;
}
.login-sline .lines{
	width:1px;
	height:110px;
	border-left:solid 1px #efe7e7;
}
.form-box-in{
	flex:1 1;
	-ms-flex: 1 1;
	/*padding:50px 70px 50px 70px;*/
}
.prefixIcon {
    font-size: 16px;
    color: rgba(0,0,0,.45);
}
.submit___22B-v {
    width: 100%;
    /*margin-top: 24px;*/
}
.ant-form-item{margin-bottom:22px}
.copyright2{
    height:56px;
}
.login_txt_t{
    color:#000000;
    font-size:32px;
    font-family: "webfont" !important;
    font-style: normal;
    font-weight: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding: 0px 0 20px 0;
    text-align: center;
    animation: fadein 1s;
    -moz-animation: fadein 1s; /* Firefox */
    -webkit-animation: fadein 1s; /* Safari and Chrome */
    -o-animation: fadein 1s; /* Opera */
    animation-delay:.1s;
    -moz-animation-delay:.1s;
    -webkit-delay:.1s;
    -o-delay:.1s;
    opacity:0.8;
}
.login_txt_t:before{
    content: "登录";
}
@keyframes fadein {
    from {
        opacity:1;
    }
    to {
        opacity:1;
    }
}
@-moz-keyframes fadein { /* Firefox */
    from {
        opacity:1;
    }
    to {
        opacity:1;
    }
}
@-webkit-keyframes fadein { /* Safari and Chrome */
    from {
        opacity:1;
    }
    to {
        opacity:1;
    }
}
@-o-keyframes fadein { /* Opera */
    from {
        opacity:1;
    }
    to {
        opacity: 1;
    }
}
