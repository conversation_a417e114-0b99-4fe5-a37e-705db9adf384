@media print {
    body {
        background-color: white;
    }

    a:after{
        content:""!important;
    }
}

@page {
    size: A4 portrait;
    margin: 0.26cm 0.26cm 0.49cm 0.26cm; /* 3.7cm 2.6cm 3.5cm; 国家标准公文页边距 GB/T 9704-2012 */
    /* 页面内容区域底部添加一条 1px 的灰线 */
    @bottom-left, @bottom-center, @bottom-right {
    border-top: 1px solid gray;
    }

    /* 页脚中间显示格式如 "第 3 页" 的页码 */
    @bottom-center {
        content: "第" counter(page) "页";
    }
}

body{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.search-box{
    flex-shrink: 2;
    flex-basis: 42px;
}
.ptable{
    -ms-flex:1;
    flex: 1;
    height: 0;
    flex-shrink: 0;
    flex-basis: 42px;
    overflow: auto;
    padding: 15px 0 ;
}

.link-c,.link-c:hover{text-decoration:none;color:#000000;}

.ptable a.link-c{text-decoration:underline;color:#333333;}
.ptable a.link-c:hover{
    color:#1e9fff;
}

