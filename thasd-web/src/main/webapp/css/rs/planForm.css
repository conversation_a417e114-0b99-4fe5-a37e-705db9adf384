@IMPORT url("mfont/mfont.css");
@IMPORT url("scrollbar.css");
@IMPORT url("treeicon.css");
html,body{width:100%;height:100%;}
body{
}
.initbox{
    position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
}
.initbox > span[class*="-icon"] {
    width: 45px;
    height: 45px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.planTab{
    height: 100%;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}


.planTab .layui-tab-title{
    flex-shrink: 2;
    flex-basis: 40px;
}

.layui-tab-content.planTabContent{
    -ms-flex: 1;
    flex: 1;
    height: 0;
    flex-shrink: 0;
    flex-basis: 40px;
    overflow: auto;
}

.layui-tab-content.planTabContent .layui-tab-item{
    height: 100%;
    overflow: auto;
}

.layui-form.pgbg .layui-input, .layui-textarea, .layui-select{
}

.formbox.pgbg .layui-input{
    padding-top: 7px;
}
.layui-form-item .layui-form-label.ex-label-min{
    width: 50px;
    padding: 9px 15px 9px 0;
}
.layui-form-item .layui-input-inline.ex-input-min {
    width: 96px;
    margin-right: 10px;
}
.layui-form-item .layui-form-label.pr0{padding-right: 5px;}


.form-opt-box-center{
    border-top:solid 1px #e8e8e8;
    padding:10px 0 10px 0;
    text-align:center;
    flex-shrink: 0;
}
.layui-form-checkbox span{
    font-size: 13px;
}
.layui-form-radio * {
    font-size: 13px;
    font-weight: normal;

}
.layui-form-radio{
    margin-top:5px !important;
    color: #666;
}

.layui-form-radio>i{
    font-size:17px;
    margin-right:2px;
}
.layui-form-checkbox[lay-skin="primary"] i {
    width: 13px;height: 13px;
    margin-top: 2px;
    margin-right:2px;
}
.layui-form-checkbox span{
    padding:0 2px 0 2px;
}

.top-panel {
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 1px 2px 0 rgb(138 144 147);
    background-color: rgb(242 251 250 / 33%);
}
.top-panel2{
    border-radius: 0;
    text-align: left;
    box-shadow: none;
    border:solid 1px #dedede;
    background:#ffffff;
}
.top-panel>.layui-card-body {
    height: 60px;
}

.layui-card-body.fzjcbox{
    height: 120px;
    padding: 0!important;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    align-items:start;
    justify-content: center;
}
.layui-card-body.fzjcbox div >span{
    padding: 0 5px;
    font-weight: bold;
    font-size: 16px;
}
.top-panel-number {
    line-height: 60px;
    font-size: 20px;
    border-right: 1px solid #eceff9;
}

.top-panel-tips {
    padding-left: 8px;
    padding-top: 16px;
    line-height: 30px;
    font-size: 12px;
    border-right: 1px solid #eceff9;
}
.top-panel-tips1{
    padding-left: 0;
    padding-top: 0;
    border-right:none;
}

.top-panel-tips i {
    font-size: 33px;
}

.top-panel-tips svg {
    margin-top: -12px;
    width: 50px;
    height: 50px;
}

.pgbgpage .top-panel-tips svg.title-icon{
    margin-top: 7px;
    width: 28px;
    height: 28px;
}

.pgbgpage .top-panel-tips svg.icon{
    margin-top: 0;
    width: 37px;
    height: 37px;
}

.pgbgpage .top-panel-tips svg.bmi{
    margin-top: 0px;
    width: 34px;
    height: 34px;
}
.pgbgpage .top-panel-tips svg.wateline{
    margin-top: 0px;
    width: 36px;
    height: 36px;
}
.top-panel .layui-card-header1{
    border-bottom: 1px solid #d5e5e3;
}
.shfs-tb {
    width: 100%;
}
.shfs-tb .layui-form-checkbox[lay-skin="primary"] i{
    margin-top: 1px!important;
}
.shfs-tb tr{
    border-width: 0;
}

.shfs-tb tr td{
    height: 56px;
    line-height: 56px;
    background: #ffffff;
    border:solid 1px #cdcdcd;
    text-align: left;
    font-size: 16px;
    padding-left:15px;
    border-right-width: 0;
    border-bottom-width: 0;
}
.shfs-tb tr td:first-child{
    font-weight: bold;
    width:100px;
    text-align: center;
    padding-left:0;
}

.shfs-tb tr td.th{
    width:33.33%!important;
    text-align: center!important;
    font-size: 16px!important;
    font-weight: bold!important;
    padding: 0!important;
    background: #f6f6f6;
}

.jg-tb tr td:first-child{
    width:40px;
}

.jkzd-tb tr td:first-child{
    width:120px;
    background: #f6f6f6;
}

.shfs-tb tr td:last-child{
    border-right-width: 1px;
}

.shfs-tb tr:last-child td{
    border-bottom-width: 1px;
}
.shfs-tb .layui-disabled{background:none!important;border: none !important}
.shfs-tb td span.bb {
    padding: 0 5px;
    font-weight: bold;
    font-size: 16px;
}
.yj-radio{font-size: 14px}
.yj-radio .layui-form-radio{
    margin-top: 0px !important;
}
.panel-result {

}
.shfs-tb tr td.ttt{padding:10px 0; height:24px!important;line-height: 1!important;text-align:center;vertical-align: baseline}
.tv-box{font-size:14px!important;font-weight: normal!important;}
.tv-box .tle{font-weight: bold;line-height:26px;text-align: left;text-indent:15px;}
.tv-box .vle{font-weight: normal;line-height:20px;text-align: left;text-indent:15px;}

p.jkzd-content{
    font-size: 13px;
    font-weight: normal;
    line-height: 24px;
    white-space: pre-line;
}