@IMPORT url("mfont/mfont.css");
@IMPORT url("scrollbar.css");
@IMPORT url("treeicon.css");
@IMPORT url("add.css");
iframe.size-watch{visibility: hidden}
html,body{width:100%;height:100%;}
body{
	background-size:100% 100%;
}
.fulllistbox{
	display: -ms-flexbox;
   display: flex;
   -ms-flex-direction: column;
   flex-direction: column;
   height: 100vh;
   overflow: auto;
}
.initbox{
    position:absolute;width:100%;height:100%;background:#ffffff;z-index: 1000;background: rgba(255, 255, 255, 1); animation-duration: 300ms; font-family: Quicksand, sans-serif;
}
.initbox > span[class*="-icon"] {
    width: 45px;
    height: 45px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}
.titlebox{
    background: #fff;
    padding: 16px 32px 0;
    border-bottom: 1px solid #e8e8e8;
}
.titlebox .title-name{
	padding:16px 0 0 0;
    font-size: 20px;
    font-weight: 500;
    color: rgba(0,0,0,.85);
}
.listbox{
	-ms-flex: 1;
    flex: 1;
    overflow-x: hidden;
    overflow-x: hidden;
    background: #f0f2f5;
    padding:15px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.listbox .listbox-body{
	background:#ffffff;
	-ms-flex: 1;
    flex: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 0;
}

.layui-card-body{
	-ms-flex:1;
    flex: 1;
    padding: 0;
    overflow:auto;
    height: 0;
}
.layui-card-body.body-tree-table{
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-card-body.body-tree-table .ew-tree-table{
    -ms-flex:1;
    flex: 1;
    height: 0;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-tool{
    flex-shrink: 0;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-head{
    flex-shrink: 0;
}
.layui-card-body.body-tree-table .ew-tree-table .ew-tree-table-box{
    -ms-flex:1;
    flex: 1;
    height: 0;
}

.layui-card.search-box{margin:0;
    /*background:#f0f2f5 ;*/
}
.layui-card.opt-box{margin:0;}
.opt{padding-top: 15px;padding-left:15px;}

.list-opt-box{
	padding:10px 24px 10px 0;
	border-top:solid 1px #e8e8e8;
	text-align:right;
}
.layui-tab{
    margin: 0;
    -ms-flex:1;
    flex: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-tab-title{
    padding: 5px 0 0 0;
    box-shadow: 0 1px 4px rgb(207 207 207 / 70%);
    flex-shrink: 2;
    flex-basis: 40px;
}
.layui-tab-content{
    -ms-flex: 1;
    flex: 1;
    height: 0;
    flex-shrink: 0;
    flex-basis: 40px;

    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-tab-content .layui-tab-item.layui-show{
    -ms-flex: 1;
    flex: 1;
    display: -ms-flexbox!important;
    display: flex!important;
    -ms-flex-direction: column;
    flex-direction: column;
}
.layui-tab-content .layui-tab-item .layui-card.opt-box{
    height: auto;
    flex-shrink: 2;
    flex-basis: 52px;
}

.layui-tab-content .layui-tab-item.layui-show .layui-row-cus{
    -ms-flex: 1;
    flex: 1;
    height: 0;
    flex-shrink: 0;
    flex-basis: 52px;
    overflow: auto;
    border:solid 1px #dedede;
}

.layui-tab-content .layui-tab-item.layui-show .layui-row-title{
    border:solid 1px #dedede;
    height: 39px;
    flex-shrink: 2;
    flex-basis: 39px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    background: #f0f2f5;
}
.layui-tab-content .layui-tab-item.layui-show .layui-row-title .layui-form-mid{font-weight: bolder}
.layui-tab-content .layui-tab-item.layui-show .layui-row-title .layui-col-cus{padding: 0!important;}
.layui-tab-content .layui-tab-item.layui-show .layui-row-cus .tplbox{
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    -ms-flex-direction: row;
    flex-direction: row;
    align-items: center;
    justify-content: start;
}

.layui-tab-content .tab-06Content.layui-show .layui-row-title,
.layui-tab-content .tab-07Content.layui-show .layui-row-title,
.layui-tab-content .tab-08Content.layui-show .layui-row-title {
    height: 78px!important;
    flex-basis: 78px!important;
}
.layui-tab-content .tab-06Content.layui-show .layui-row-title .layui-col-cus,
.layui-tab-content .tab-07Content.layui-show .layui-row-title .layui-col-cus,
.layui-tab-content .tab-08Content.layui-show .layui-row-title .layui-col-cus {
    flex: 1;
}
.layui-tab-content .tab-06Content.layui-show .layui-row-title .layui-form-mid,
.layui-tab-content .tab-07Content.layui-show .layui-row-title .layui-form-mid,
.layui-tab-content .tab-08Content.layui-show .layui-row-title .layui-form-mid {
    height: 78px;
    line-height: 78px;
    padding: 0!important;
    margin: 0!important;
    border-left: solid 1px #dedede;
}
.layui-tab-content .tab-06Content.layui-show .layui-row-title .layui-form-mid.th-row,
.layui-tab-content .tab-07Content.layui-show .layui-row-title .layui-form-mid.th-row,
.layui-tab-content .tab-08Content.layui-show .layui-row-title .layui-form-mid.th-row {
    height: 39px;
    line-height: 39px;
    padding: 0!important;
    margin: 0!important;
    border-left-width: 0
}
.layui-tab-content .tab-06Content.layui-show .layui-row-title .layui-form-mid.td-col,
.layui-tab-content .tab-07Content.layui-show .layui-row-title .layui-form-mid.td-col,
.layui-tab-content .tab-08Content.layui-show .layui-row-title .layui-form-mid.td-col {
    height: 39px;
    line-height: 39px;
    padding: 0!important;
    margin: 0!important;
    border-left-width: 0;
    border-top: solid 1px #dedede;
}
.layui-tab-item .opt {
    padding: 10px 10px 10px 10px!important;
    isplay: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    align-items: center;
    justify-content: end;
}

.layui-tab-title .layui-this{
    background: #13a387!important;
    color: #FFFFFF!important;
    border-radius: 6px 6px 0 0!important;
}

.layui-tab-title li:first-child{margin-left: 15px;}

.layui-tab-title .layui-this:after{
    border-color: #13a387!important;
    border-radius: 6px 6px 0 0!important;
}

.layui-col-space10{margin: 0!important;}
.layui-col-space10>*{padding: 0!important;}
.layui-card-body .layui-form{margin: 0!important;}

.layui-form-item {
    margin-bottom: 0;
}

.layui-form-item .layui-inline {
    margin:0!important;
}
.layui-form-item .layui-input-inline {
    margin-right: 0!important;
}
.layui-col-cus{

    width:566px;
    flex-shrink: 2;
    flex-basis: 566px;
    padding: 6px 0 6px 0 !important;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: row;
    flex-direction: row;
    align-items: center;
    justify-content: start;

    border-bottom:solid 1px #dedede;
    border-right:solid 1px #dedede;
    margin-left:-1px;
    margin-bottom:-1px;
}
.tab-04Content .layui-col-cus{
    width:1018px;
    flex-shrink: 2;
    flex-basis: 1018px;
}
.tab-06Content .layui-col-cus,
.tab-07Content .layui-col-cus,
.tab-08Content .layui-col-cus{
    flex: 1;
    width: 100%;
    flex-basis: 100%;
}
.layui-show .layui-row-cus .tplbox .layui-col-cus .layui-btn-xs{
    padding: 0 4px 0 3px!important;
}
.layui-show .layui-row-cus .tplbox .layui-col-cus .layui-btn .layui-icon{
    margin-right: 0!important;
}
.layui-show .layui-row-cus .tplbox .layui-col-cus .layui-btn+.layui-btn{
    margin-left:5px!important;
}
.layui-show .layui-row-cus .tplbox .layui-col-cus:nth-of-type(even){
}

.layui-form-mid{
    text-align: center;
}
.layui-form-mid.content{
    cursor: pointer;
}
.layui-form-mid.content.show .content-copy{
    position: absolute;
    width:276px;
    height:auto;
    max-height:200px;
    overflow: auto;
    margin-top:-10px;
    background: #ffffff;
    border: solid 1px #dedede;
    white-space: pre-line;
    z-index: 2222;
    padding: 10px;
}
.th-col,.layui-form-mid.th-col,.layui-form-mid.th-col .layui-form-mid.th-col {
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    align-items: center;
    justify-content: center;

}
.th-row, .layui-form-mid.th-row{
    flex: 1!important;
}

.content-copy{
    display: block;
    /* 1.先强制一行内显示文本 */
    white-space: nowrap;
    /* 2.超出部分隐藏 */
    overflow: hidden;
    /* 3.文字用省略号替代超出的部分 */
    text-overflow: ellipsis;
}

.layui-form-mid.content .close{
    display:none;
}
.layui-form-mid.content.show .close{
    display:block;
    position: absolute;
    background: #ff5555;
    margin-left:280px;
    margin-top:-14px;
    z-index: 55555;
    border-radius: 24px;
    width: 24px;
    height: 24px;
    line-height:24px;
    padding:0;
}

.layui-form-mid.content.show .close i{
    color: #FFFFFF;
    font-size:16px!important;
}

.layui-layer.layui-layer-page .layui-layer-content{
    padding: 10px 15px;
}
.layui-layer.layui-layer-page .layui-layer-content .layui-form-item{

}
.layui-form-pane.fwqd-pane  .layui-form-label{
    width:146px!important;
}
.layui-form-pane.fwqd-pane .layui-input-block {
    margin-left: 146px!important;
}
