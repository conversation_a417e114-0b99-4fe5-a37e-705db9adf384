/*滚动条宽度*/  
::-webkit-scrollbar {  
    width:10px;  
    height:10px;
}  
/* 轨道样式 */
::-webkit-scrollbar-track {
    background: rgb(13 21 21 / 12%) !important;
}
/* Handle样式 */  
::-webkit-scrollbar-thumb {  
    /*border-radius: 10px;  */
    background: rgba(0,0,0,0.5)!important;
}  
/*当前窗口未激活的情况下*/  
::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(0,0,0,0.5)!important;
}  
/*hover到滚动条上*/  
::-webkit-scrollbar-thumb:vertical:hover{  
    background: rgba(0,0,0,0.5)!important;
}  
/*滚动条按下*/  
::-webkit-scrollbar-thumb:vertical:active{
  background: rgba(0,0,0,0.6)!important;
}