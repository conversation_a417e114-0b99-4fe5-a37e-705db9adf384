.hidden {
    display: none !important;
}

.selectPickerWrapper * {
    box-sizing: border-box;
}

.selectPickerWrapper {
    position: relative;
    margin: 0;
    display: inline-block;
    width: 100%;
    height: 100%;
}

.select-picker-search {
    width: 100%;
    height: 100%;
    line-height: 32px;
    color: #666;
    /*border-radius: 3px;*/
    border: 1px solid #999;
    padding: 0 0;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    background: #FFFFFF;
}

.select-picker-search-checked {
    position: relative;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    padding-right: 5px;
    text-indent:5px;
    -moz-user-select: none; /*火狐*/
    -webkit-user-select: none; /*webkit浏览器*/
    -ms-user-select: none; /*IE10*/
    -khtml-user-select: none; /*早期浏览器*/
    user-select: none;
}

.select-picker-search-checked:after {
    border: 5px solid transparent;
    content: ' ';
    height: 0;
    position: absolute;
    width: 0;
    top: 12.2px;
    border-top: 8px solid #666;
    right: 5px;
}

.select-picker-options-wrp {
    display: none;
    width: 100%;
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .2);
    background-color: #fff;
    /*top: 0;*/
    /*left: 0;*/
    position: absolute;
    z-index: 999999999999999999999;
    height: 345px;
    overflow: auto;
    border: solid 1px #999;
}

.select-picker-options-serch input {
    width: 160px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin: 12px 0 0 10px;
    padding-left: 8px;
}

.select-picker-options-serch input:focus {
    border-color: rgba(118, 234, 210, 1);
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(118, 234, 210, .6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(118, 234, 210, .6)
}

.select-picker-options-list {
    width: 100%;
    padding: 8px 0;
}

.select-picker-options-list-item {
    width: 100%;
    line-height: 36px;
    cursor: pointer;
    padding: 0 12px;
    text-align: left;
}

.select-picker-options-list-item:hover {
    background: rgba(118, 234, 210, 1);
}

.duihao {
    display: inline-block;
    width: 18px;
    height: 18px;
    border-radius: 3px;
    transform: translateY(4px);
    margin-right: 6px;
}

.duihao-checked {
    background: #13a387;
    position: relative;
}

.duihao-checked:before, .duihao-checked::after {
    content: "";
    height: 10px;
    width: 2px;
    border-radius: 10px;
    display: block;
    background: white;
    position: absolute;
    top: 4px;
    left: 10px;
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
}

.duihao-checked::before {
    height: 6px;
    transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    position: absolute;
    top: 7px;
    left: 5px;
}

.duihao-nocheck {
    border: 1px solid #333;
}