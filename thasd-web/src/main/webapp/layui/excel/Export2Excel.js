function generateArray(table) {
    var out = [];
    // $(table.querySelectorAll('td')).attr("style",'mso-number-format:\\@')
    // style='mso-number-format:\\@'"

    var rows = table.querySelectorAll('tr');
    var ranges = [];
    for (var R = 0; R < rows.length; ++R) {
        var outRow = [];
        var row = rows[R];
        var columns = row.querySelectorAll('td');
        for (var C = 0; C < columns.length; ++C) {
            var cell = columns[C];
            var colspan = cell.getAttribute('colspan');
            var rowspan = cell.getAttribute('rowspan');
            var cellValue = cell.innerText;
            //console.log(cellValue,1)
            // if(cellValue !== "" && cellValue == +cellValue) cellValue = +cellValue;
            //console.log(cellValue,2)
            //Skip ranges
            ranges.forEach(function(range) {
                if(R >= range.s.r && R <= range.e.r && outRow.length >= range.s.c && outRow.length <= range.e.c) {
                    for(var i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null);
                }
            });
            //console.log(cellValue,3)

            //Handle Row Span
            if (rowspan || colspan) {
                rowspan = rowspan || 1;
                colspan = colspan || 1;
                ranges.push({s:{r:R, c:outRow.length},e:{r:R+rowspan-1, c:outRow.length+colspan-1}});
            };
           // console.log(cellValue,4)

            //Handle Value
            outRow.push(cellValue !== "" ? cellValue :"");

            //Handle Colspan
            if (colspan) for (var k = 0; k < colspan - 1; ++k) outRow.push(null);
        }
        out.push(outRow);
    }
    return [out, ranges];
};

function datenum(v, date1904) {
    if(date1904) v+=1462;
    var epoch = Date.parse(v);
    return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

var borderAll = {
		  top:{
		    style:'thin'
		    , color:{ rgb:"FF000000" }
		  },
		  bottom:{
		    style:'thin'
		    , color:{ rgb:"FF000000" }
		  },
		  left:{
		    style:'thin'
		    , color:{ rgb:"FF000000" }
		  },
		  right:{
		    style:'thin'
		   , color:{ rgb:"FF000000" }
		  }
};
function sheet_from_array_of_arrays(data, opts) {
    var ws = {};
    var range = {s:{c:10000000, r:10000000}, e:{c:0, r:0 }};

    for(var R = 0; R != data.length; ++R) {
        for(var C = 0; C != data[R].length; ++C) {
            if(range.s.r > R) range.s.r = R;
            if(range.s.c > C) range.s.c = C;
            if(range.e.r < R) range.e.r = R;
            if(range.e.c < C) range.e.c = C;
            var cell = {v:data[R][C] };

            var cell_ref = XLSX.utils.encode_cell({c:C,r:R});
            // console.log(cell,cell_ref)
            if(cell.v == null){
            	delete cell["v"];
            }
            cell.t = 's';
            // else if(typeof cell.v === 'number') cell.t = 's';
            // else if(typeof cell.v === 'boolean') cell.t = 'b';
            // else if(cell.v instanceof Date) {
            //     cell.t = 'n'; cell.z = XLSX.SSF._table[14];
            //     cell.v = datenum(cell.v);
            // }
            // else{
            // 	cell.t = 's';
            // }
            ws[cell_ref] = cell;
            ws[cell_ref].s = {numFmt:"@",fill:{patternType:'none'}, border:borderAll, alignment:{vertical:'center',horizontal:'center' },font:{ sz:13, bold:false, color:{rgb:"FF000000" } }};
            // console.log(cell,cell_ref,ws[cell_ref])

        }
    }
    
    if(range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
    return ws;
}


function s2ab(s) {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i=0; i!=s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}
function Workbook(ws) {
    return { SheetNames: [], Sheets: {} };
    // if(!(this instanceof Workbook)) return new Workbook();
    // this.SheetNames = [];
    // this.Sheets = {};

}
function export_table_to_excel(id,fileName,type) {
    var theTable = document.getElementById(id);
    var oo = generateArray(theTable);
    var ranges = oo[1];
    //console.log(oo)
    /* original data */
    var data = oo[0];
    var ws_name = "sheet1";
    //var wb = XLSX.utils.table_to_book(theTable.querySelector("table"),{ raw: true })
    var ws = sheet_from_array_of_arrays(data);
    var wb = new Workbook(ws);
    /* add ranges to worksheet */
    ws['!merges'] = ranges;

    if(type=="countDj"){
    	ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 110}, {wpx: 110}];
    }
    if(type=="countGzlPgbfz"){
        ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 160}, {wpx: 240}];
    }
    if(type=="countGzlPg"){
        ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}];
    }

    if(type=="countGlrs"){
        ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 100}, {wpx: 100}, {wpx: 100}, {wpx: 120}, {wpx: 120}, {wpx: 110}, {wpx: 110}];
    }

    if(type=="countGlfa"){
        ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 110}, {wpx: 110}, {wpx: 110}, {wpx: 110}];
    }

    if(type=="countNrgl-dept"){
        ws['!cols'] = [{wpx: 185},  {wpx: 110}];
    }
    if(type=="countNrgl-person"){
        ws['!cols'] = [{wpx: 185}, {wpx: 100}, {wpx: 110}, {wpx: 110}];
    }

    if(type=="fabwzmc"){
        ws['!cols'] = [{wpx: 100}, {wpx: 130}, {wpx: 110}, {wpx: 110}];
    }
    if(type=="fawpg"){
        ws['!cols'] = [{wpx: 100}, {wpx: 130}, {wpx: 110}, {wpx: 110}];
    }

    if(type=="fwjl"){
        ws['!cols'] = [{wpx: 120}, {wpx: 150}, {wpx: 170},{wpx: 130}, {wpx: 120}, {wpx: 120}];
    }
    if(type=="fwjlwcl"){
        ws['!cols'] = [{wpx: 120}, {wpx: 150}, {wpx: 140}, {wpx: 170},{wpx: 130}, {wpx: 120}, {wpx: 120}, {wpx: 120}, {wpx: 120}, {wpx: 120}];
    }

    if(type=="2monthpatient"){
        ws['!cols'] = [{wpx: 185}, {wpx: 170}, {wpx: 150},{wpx: 150}, {wpx: 150}];
    }

    /*----------------手动添加----------------*/
    //var style = {fill:{patternType:'none'}, border:borderAll, alignment:{vertical:'center',horizontal:'center' },font:{ sz:13, bold:false, color:{rgb:"FF000000" } }};
    //ws['B'+data.length] = {"s":style}
    //ws['B1'] = {"s":style}
    //ws['C1'] = {"s":style}
    //ws['D1'] = {"s":style}
    /*--------------------------------------*/
    
    /* add worksheet to workbook */
    wb.SheetNames.push(ws_name);
    wb.Sheets[ws_name] = ws;
    //console.log(wb)
    var wbout = XLSX.write(wb, { bookType:'xlsx', bookSST:false, type:'binary'});

    saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), fileName+".xlsx")
}
