layui.define(['layer', 'jquery'], function(exports){
	"use strict";
	var jQuery = layui.jquery;
	(function ($) {
		'use strict';
		$.fn.Accordion = function (opts) {
			var options = jQuery.extend({
				// normal, fast, slow,or integer for duration in milliseconds
				speed: 'normal',
				header: 'h3',
				activeItem:0,
				classActive: 'active'
			}, opts);
			// Current hover status
			var container = this;
			var i = 0;
			var height = $(this).outerHeight(true);
			var width = $(this).outerWidth(true);
			var meunlist = $(container).find(options.header);
			var headsize = meunlist.length;
			var headerHeight = 30;
			//如果就只有一个菜单那么就为0,高于一个菜单设为1
			var addHeight = 0;
			if(headsize > 1)addHeight = 1;
			meunlist.each(function(){
				//$(this).children('a').css({width:width-2});
				//判断header是否有子项 len > 0 or len = 0
				var len = $(this).children('ul').length;
				//设置标记
				$(this).children('.menu-title').children('a').attr('mflg','menu_a_'+Math.ceil(Math.random()*999+3000));
				$(this).children('.menu-title').children('a').addClass("header");
				//显示第几个header  并且展开这一header
				if(i == options.activeItem){
					//加入图标
					if(len>0){
						var that = this;
						$(that).children('.menu-title').children('a').find('i.arrow-menu').remove();
						$("<i class='arrow-menu mfont'>&#xe7eb;</i>").appendTo($(that).children('.menu-title').children('a'));
						setTimeout(function(){
							$(that).children('.menu-title').children('a').find('i.arrow-menu').remove();
							$(that).children('.menu-title').addClass("sel");
							$("<i class='arrow-menu mfont'>&#xe7ec;</i>").appendTo($(that).children('.menu-title').children('a'));
							$(that).children('.menu-title').children('a').addClass('header_h');
							$(that).children('.menu-title').children('a').css("border-top","none");
							$(that).children('ul').show();
							$(that).children('ul').show(options.speed);
						},500);
					}
				}else{
					//加入图标
					if(len>0){
						$("<i class='arrow-menu mfont'>&#xe7eb;</i>").appendTo($(this).children('.menu-title').children('a'));
					}
					$(this).children('ul').hide();
				}
				//设置最后一个h3 a bottom
				if((i+1)==headsize){
					//$(this).find(".header").css("border-bottom","none");
				}

				/*点击隐藏展示菜单*/
				var menu = $(this);
				$(this).children('.menu-title').click(function () {
					doClick(menu);
				});
				i++;
			});
			$(container).show();

			$(".submenu").click(function () {
				$(".submenu").removeClass("menusel");
				$(".menu-title a.header").removeClass("menusel");
				$(this).addClass("menusel");
			});

			//显示隐藏菜单
			var doClick = function (thisHeader) {
				var len = thisHeader.children('ul').length;
				if(len>0){
					if(thisHeader.children('ul').is(':visible')){
						thisHeader.children('ul').hide();
						thisHeader.children('.menu-title').removeClass("sel");
						thisHeader.children('.menu-title').children('a').find('i.arrow-menu').remove();
						$("<i class='arrow-menu mfont'>&#xe7eb;</i>").appendTo(thisHeader.children('.menu-title').children('a'));

					}else{
						meunlist.each(function(){
							//判断header是否有子项 len > 0 or len = 0
							var len = $(this).children('ul').length;
							if(len>0){
								$(this).children('.menu-title').removeClass("sel");
								$(this).children('.menu-title').children('a').find('i.arrow-menu').remove();
								$("<i class='arrow-menu mfont'>&#xe7eb;</i>").appendTo($(this).children('.menu-title').children('a'));
							}
						});
						$("#accordion h3 ul").hide();
						thisHeader.children('ul').show();
						thisHeader.children('ul').show(options.speed);
						thisHeader.children('.menu-title').addClass("sel");
						thisHeader.children('.menu-title').children('a').find('i.arrow-menu').remove();
						$("<i class='arrow-menu mfont'>&#xe7ec;</i>").appendTo(thisHeader.children('.menu-title').children('a'));
					}
				}else{
					meunlist.each(function(){
						//判断header是否有子项 len > 0 or len = 0
						var len = $(this).children('ul').length;
						if(len>0){
							$(this).children('.menu-title').removeClass("sel");
							$(this).children('.menu-title').children('a').find('i.arrow-menu').remove();
							$("<i class='arrow-menu mfont'>&#xe7eb;</i>").appendTo($(this).children('.menu-title').children('a'));
						}
					});
					$("#accordion h3 ul").hide();
					$(".submenu").removeClass("menusel");
					$(this).addClass("menusel");
					thisHeader.children('.menu-title').children('a.header').addClass("menusel");
				}
			};
		};
	})(jQuery);
	exports('accordion', function(){
	});
});