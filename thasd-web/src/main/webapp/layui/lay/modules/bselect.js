layui.define(['layer', 'jquery'], function(exports){
    "use strict";
    var jQuery = layui.jquery;
    (function ($) {
        'use strict';
        /*##############################################################################################*/
        $.fn.mySelect = function (options) {
            var $this = $(this);      //当然响应事件对象
            // $this.live(options.Event, function (e) {   //功能代码部分，绑定事件
            //     alert(options.msg);
            // });
            //生成option-item并追加展示

            let proid = $this.attr("data-proid");
            let id = $this.attr("data-id");
            let idn = $this.attr("data-idn");

            var html = '';
            html += '<div class="select-picker-search">';
            html += '<div class="select-picker-search-checked">请选择对照的服务项目</div>';
            html += '</div>';
            html += '<div class="select-picker-options-wrp">';

            html += '<div class="select-picker-options-list">';

            var checkedNArr = [];
            layui.each(options.dicData, function(index, item){

                if (proid.indexOf(item.id) >= 0) {
                    checkedNArr.push(item.name);
                }

                html += '<div class="select-picker-options-list-item" dataid = "'+item.id+'" value = "'+item.val+'" itemid="'+ idn +'" >';
                html += '<b class="duihao '+ (proid.indexOf(item.id) >= 0  ? "duihao-checked" : "duihao-nocheck") +' " ></b>';
                html += '<span>'+item.name+'</span>';
                html += '</div>';

            });

            html += '</div>';
            html += '</div>';
            $this.append(html);

            if (checkedNArr.length > 0) {
                $this.find('.select-picker-search-checked').text(checkedNArr.join(',')).css('color', '#01746a');
            } else {
                $this.find('.select-picker-search-checked').text('请选择对照的服务项目').css('color', '#757575');
            }

            // 下拉显示隐藏
            $this.on('click',".select-picker-search", function (e) {   //功能代码部分，绑定事件
                $(this).next('.select-picker-options-wrp').toggle();
            });


            function postForm(itemSelectList){
                var items = JSON.stringify(itemSelectList).replace("[","@~").replace("]","~@").replaceAll("{","$~").replaceAll("}","~$");

                var poststr = "id=" + id + "&idn="+idn + "&items="+items;
                layer.msg("正在保存数据...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                $.ajaxSetup({
                    error:function(x,e){
                        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
                        return false;
                    }
                });
                $.getJSON(options.postUrl,poststr,function(jsondata){
                    if(jsondata.code=='200'){
                        layer.msg('已更新',{time:500},function(){ });
                    }else{
                        layer.msg(jsondata.msg,{time:2000});
                    }
                });
            }
            // 点击选中或不选
            $this.on('click',".select-picker-options-list-item",function(){
                let _this = $(this);
                if(_this.find('.duihao-nocheck').length > 0){
                    _this.find('.duihao').removeClass('duihao-nocheck').addClass('duihao-checked');
                }else{
                    _this.find('.duihao').addClass('duihao-nocheck').removeClass('duihao-checked');
                }
                // 循环遍历options中选中的项添加到选项栏中
                var checkedArr = [];
                var itemSelectList = [];
                $this.find(".select-picker-options-list-item").each(function(){
                    let _this = $(this);
                    if(_this.find('.duihao-checked').length > 0){
                        checkedArr.push($.trim(_this.text()))
                        itemSelectList.push({proid:_this.attr("dataid"),proname:_this.text()});
                    }
                })
                if (checkedArr.length > 0) {
                    $this.find('.select-picker-search-checked').text(checkedArr.join(',')).css('color', '#01746a');
                } else {
                    $this.find('.select-picker-search-checked').text('请选择对照的服务项目').css('color', '#757575');
                }
                console.log(itemSelectList,JSON.stringify(itemSelectList))
                postForm(itemSelectList);
            })
        }
        /*##############################################################################################*/
    })(jQuery);
    exports('bselect', function(){
    });
});