/**
 @Name：检测评估 工具集
 @Author：泰安翼云软件
 @License：MIT
 */
layui.define(['jquery', 'echarts'], function (exports) {
    "use strict";
    var $ = layui.$;
    var form = layui.form;
    var echarts = layui.echarts;

    var ascvdChart = echarts.init(document.getElementById('echarts-ascvd'));

    String.prototype.toFloat = function(fix) {
        let tofix = 0;
        if(fix){
            tofix = fix;
        }
        var val = this;
        if(val.length > 0){
            return  parseFloat(val).toFixed(tofix)
        }else{
            return 0;
        }
    };
    String.prototype.toInt = function() {
        var val = this;
        if(val.length > 0){
            return  parseInt(val);
        }else{
            return 0;
        }
    };

    String.prototype.eq = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Map.prototype.eq = function (key) {
        return this.hasOwnProperty(key);
    }
    Array.prototype.of = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Array.prototype.removeByVal = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }

    //高血压分层危险因素
    var riskFactor = {};
    //糖尿病分层危险因素
    var diabetesRiskFactor = {};
    //血脂分层危险因素
    var lipidemiaRiskFactor = {};
    //饮食习惯
    var eatHabit = new Array();
    var eatHabitName = new Array();
    //体重指数-评估结果字典
    var bmiDic = {0: "正常", 1: "过轻", 2: "超重", 3: "肥胖"};
    //腰围-评估结果字典
    var waistLineDic = {0: "正常", 1: "过大"};
    //血压-评估结果字典
    var bpDic = {0: "正常", 1: "高血压", 2: "低血压"};
    var bpLevelDic = {0: "正常", 1: "1级高血压", 2: "2级高血压", 3: "3级高血压"};
    var bpGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
    //血糖-评估结果字典
    var xtDic = {0: "正常", 1: "空腹血糖受损", 2: "糖耐量异常", 3: "糖尿病"};
    var xtLevelDic = {0: "正常", 1: "1型糖尿病", 2: "2型糖尿病", 3: "妊娠糖尿病", 4: "其他糖尿病"};
    var xtGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
    //血脂-评估结果字典
    var xzDic = {0: "正常", 1: "高血脂症"};
    var xzGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
    //饮食习惯-评估结果字典
    var ysDic = {0: "正常", 1: "高盐饮食", 2: "高糖饮食", 3: "食用油摄入量偏高"};
    //饮酒-评估结果字典
    var yjDic = {0: "正常饮酒", 1: "危险饮酒", 2: "有害饮酒"};

    //男性 cavsd评估对照 nn <= -1 | >= 17 = 52.6
    var menCavsd = {nn:0.3 ,0:0.5 ,1:0.6 ,2:0.8 ,3:1.1 ,4:1.5 ,5:2.1 ,6:2.9 ,7:3.9 ,8:5.4 ,9:7.3 ,10:9.7 ,11:12.8 ,12:16.8 ,13:21.7 ,14:27.7 ,15:35.3 ,16:44.3 ,17:52.6};
    //女性 cavsd评估对照 nn <= -2 = 0.0 , nn2 = -2 = 0.1 , nn1 = -1 = 0.2
    var womenCavsd = {nn:0 ,nn2:0.1,nn1:0.2 ,0:0.2 ,1:0.2 ,2:0.3 ,3:0.5 ,4:1.5 ,5:2.1 ,6:2.9 ,7:3.9 ,8:5.4 ,9:7.3 ,10:9.7 ,11:12.8 ,12:16.8 ,13:21.7 ,14:27.7 ,15:35.3 ,16:44.3 ,17:52.6};

    var detevalUtil = {//外部接口
        regexConfig: {
            positive: "^[0-9]\\d*$"//正整数 不带小数点
            , number: "^[0-9][0-9]*([\\.][0-9]{1,2})?$"//整数 包含小数点
            , number1: "^[1-9][0-9]*([\\.][0-9]{1,2})?$"//整数 包含小数点
        },
        buildRegex: function (regexPath) {
            var that = this;
            var op = "g";//全局搜索
            //op = op + "i";//忽略大小写
            return new RegExp(that.regexConfig[regexPath], op);
        }
        ,IdCard: function(IdCard, type) {
            if (type === 1) {
                //获取出生日期
                let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
                return birthday;
            }
            if (type === 2) {
                //获取性别
                if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
                    return "男"
                } else {
                    return "女"
                }
            }
            if (type === 3) {
                //获取年龄
                var ageDate = new Date()
                var month = ageDate.getMonth() + 1
                var day = ageDate.getDate()
                var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
                if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
                    age++
                }
                if (age <= 0) {
                    age = 1
                }
                return age;
            }
        }
        ,setCheckLis: function(id,name,val,checked){
            if(val == "0"){
                if(checked){
                    $('input:checkbox[name="'+name+'"]').attr("disabled","");
                    $("[name='"+name+"']").each(function(){
                        console.log($(this).val(),$(this).val() == 0,id)
                        if($(this).val() == "0"){
                            $(this)[0].checked = true;
                            $(this)[0].removeAttribute("disabled");
                        }else{
                            $(this)[0].checked = false;
                        }
                    });
                    form.render(null, 'evalForm');
                }else{
                    $('input:checkbox[name="'+name+'"]').removeAttr("disabled");
                    form.render(null, 'evalForm'); 
                }
            }
        }
        ,setCheckRiskData:function (id,name,val,title,checked,level){
            var that = this;
            console.log(id,name,val,title,checked,level);
            if(val == 0){
                clearFactor();
                return;
            }
            let key;
            //危险因素
            if(name.eq("wxys-chk")){  key = "risk"; }
            //靶器官损害
            if(name.eq("bqgsh-chk")){ key = "bqgsh"; }
            //伴临床疾患
            if(name.eq("lcjh-chk")){  key = "lcjh"; }

            if(level.eq("bp")){
                let factor = riskFactor;
                let valId="#bprisk";
                setFactor(key , factor , valId , checked);
            }
            if(level.eq("dp")){
                let factor = diabetesRiskFactor;
                let valId="#dprisk";
                setFactor(key , factor , valId , checked);
            }
            if(level.eq("lp")){
                let factor = lipidemiaRiskFactor;
                let valId="#lprisk";
                setFactor(key , factor , valId , checked);
            }

            function setFactor(key , factor , valId , checked){
                if(key && factor && valId){
                    if(checked){
                        addFactor(factor,key,title,valId);
                    }else {
                        removeFactor(factor,key,title,valId);
                    }
                }
            }
            function clearFactor(){

                $("#bprisk").val("");
                $("#dprisk").val("");
                $("#lprisk").val("");

                delRiskFactor("risk");
                deldbRiskFactor("risk");
                dellpRiskFactor("risk");

                delRiskFactor("bqgsh");
                deldbRiskFactor("bqgsh");
                dellpRiskFactor("bqgsh");

                delRiskFactor("lcjh");
                deldbRiskFactor("lcjh");
                dellpRiskFactor("lcjh");
            }
            function delRiskFactor(key){
                if( riskFactor[key] ){delete(riskFactor[key]);}
            }
            function deldbRiskFactor(key){
                if( diabetesRiskFactor[key] ){delete(diabetesRiskFactor[key]);}
            }
            function dellpRiskFactor(key){
                if( lipidemiaRiskFactor[key] ){delete(lipidemiaRiskFactor[key]);}
            }
            function addFactor(factor,key,title,valId){
                if( !factor[key] ){
                    let arrFactor = new Array();
                    arrFactor.push(title);
                    factor[key] = arrFactor;
                    setInputValue(valId,arrFactor);
                }else{
                    let arrFactor = factor[key];
                    arrFactor.push(title);
                    factor[key] = arrFactor;
                    setInputValue(valId,arrFactor);
                }
            }

            function removeFactor(factor,key,title,valId){
                if( factor[key] ){
                    let arrFactor = factor[key];
                    arrFactor.removeByVal(title);
                    factor[key] = arrFactor;
                    setInputValue(valId,arrFactor);
                }
            }

            function setInputValue(valId,arrFactor){//启用
                //$(valId).val(arrFactor.join((arrFactor.length > 1 ? "," : "")));
            }
        }
        ,genRisk(){

            console.log("---------------------------------------------")
            console.log(riskFactor)
            console.log(diabetesRiskFactor)
            console.log(lipidemiaRiskFactor)
            console.log("---------------------------------------------")

            let bpResult = this.genRisk_calc(riskFactor);
            let dpResult = this.genRisk_calc(diabetesRiskFactor);
            let lpResult = this.genRisk_calc(lipidemiaRiskFactor);
            $("#bprisk").val(bpResult);
            $("#dprisk").val(dpResult);
            $("#lprisk").val(lpResult);
        }
        ,genRisk_calc(factor){
            let bpRisk = new Array(),bpRisk2 = new Array(),bpBqg = new Array(),bpLcjh = new Array();
            for(var key in factor) {
                if(!key.eq("bqgsh") && !key.eq("lcjh") ){
                    if(key.eq("risk")){
                        bpRisk2 = factor[key];
                    }else{
                        bpRisk.push(factor[key]);
                    }
                }else{
                    if(key.eq("bqgsh")){
                        bpBqg = factor[key];
                    }
                    if(key.eq("lcjh")){
                        bpLcjh = factor[key];
                    }
                }
            }
            let resultStr = "";

            let resultRiskSpStr = "#危险因素:";
            let resultRiskStr = "";
            let bpRiskStr = bpRisk.join((bpRisk.length > 1 ? "," : ""));
            let bpRisk2Str = bpRisk2.join((bpRisk2.length > 1 ? "," : ""));
            resultRiskStr += bpRiskStr;
            resultRiskStr += ( bpRiskStr == "" ? bpRisk2Str : ( resultRiskStr.endsWith(",") ? bpRisk2Str : ("," + bpRisk2Str) ) );

            if(resultRiskStr.length > 0){
                resultStr += resultRiskSpStr;
                resultStr += resultRiskStr;
            }

            let resultBqgSpStr = "#靶器官损害:";
            let bpBqgStr = bpBqg.join((bpBqg.length > 1 ? "," : ""));
            if(bpBqgStr.length > 0){
                resultStr += resultBqgSpStr;
                resultStr += bpBqgStr;
            }

            let resultLcjhSpStr = "#伴临床疾病:";
            let bpLcjhStr = bpLcjh.join((bpLcjh.length > 1 ? "," : ""));
            if(bpLcjhStr.length > 0){
                resultStr += resultLcjhSpStr;
                resultStr += bpLcjhStr;
            }

            return resultStr;

        }
        ,initCheckBox: function (formId){
            var that = this;
            form.verify({
                formChk: function(value, item){ //value：表单的值、item：表单的DOM对象
                    //console.log(item)
                    let name = item.name;
                    let chkLength = $(":input[name='"+name+"']:checked").length;
                    if(chkLength == 0){
                        return '请至少选择一个选项!';
                    }
                }
                ,formRadio: function(value, item){ //value：表单的值、item：表单的DOM对象
                    //console.log(item)
                    let name = item.name;
                    let chkLength = $(":input[name='"+name+"']:checked").length;
                    if(chkLength == 0){
                        return '请至少选择一个选项!';
                    }
                }
            });
            form.on('checkbox(chk-special-filter)', function(data){
                let name = data.elem.name;
                let title = data.elem.title;
                let value = data.value;
                let id = "#"+name.replace("-chk","");
                var valId = "#" + name.replace("chk","val");
                var valName = "#" + name.replace("chk","name");
                that.setCheckLis(id,name,data.value,data.elem.checked);
                if(data.elem.checked){
                    data.elem.checked = true;
                    data.elem.removeAttribute("disabled");
                    form.render(null, 'evalForm'); 
                    if(data.value == "0"){
                        $(valId).val("0");
                        $(valName).val(title);
                    }else{
                        let arr = $(valId).val().Split(",");
                        arr.push(data.value);
                        $(valId).val(arr.join((arr.length > 1 ? "," : "")));
                        let arr2 = $(valName).val().Split(",");
                        arr2.push(data.elem.title);
                        $(valName).val(arr2.join((arr2.length > 1 ? "," : "")));

                        that.setCheckRiskData(id,name,data.value,data.elem.title,true,$(data.elem).attr("level"));
                    }
                }else{
                    let arr = $(valId).val().Split(",");
                    arr.removeArr(data.value);
                    $(valId).val(arr.join((arr.length > 1 ? "," : "")));

                    let arr2 = $(valName).val().Split(",");
                    arr2.removeArr(data.elem.title);
                    $(valName).val(arr2.join((arr2.length > 1 ? "," : "")));

                    that.setCheckRiskData(id,name,data.value,data.elem.title,false,$(data.elem).attr("level"));
                }

            });
            form.on('radio(chk-radio-filter)', function(data){
                let name = data.elem.name;
                let value = data.value;
                //console.log(name,value,data)
                if(name == "xyzk"){//吸烟,加入风险因素
                    $("#pgsmockcode").val(value);
                    $("#pgsmockname").val(data.elem.title);
                    if(value == "3"){//吸烟,加入风险因素
                        that.setRiskFactor("smcok","吸烟");
                    }else{
                        if(riskFactor["smcok"]){
                            delete(riskFactor["smcok"]);
                        }

                    }
                }
                if(name == "tlld"){//体力劳动
                    $("#pgtlldcode").val(value);
                    $("#pgtlldname").val(data.elem.title);
                    if(value == "0"){//轻劳动,加入风险因素
                        that.setDbtRiskFactor("tlld","静坐生活方式");
                    }else{
                        if(diabetesRiskFactor["tlld"]){
                            delete(diabetesRiskFactor["tlld"]);
                        }
                    }
                }
            });

        }
        ,setHBPRiskFactor: function (patientData) {
            var that = this;

            //console.log(JSON.stringify(patientData))

            let age = patientData.age;
            let gender = patientData.gendercode;

            let jzsmq = patientData.jzsmq;
            let jzsfq = patientData.jzsfq;
            let jzsxdjm = patientData.jzsxdjm;
            let jzszn = patientData.jzszn;

            if(gender == "1" && parseInt(age) >= 55){
                that.setRiskFactor("hbpAge","年龄大于55岁");
            }
            if(gender == "2" && parseInt(age) >= 65){
                that.setRiskFactor("hbpAge","年龄大于65岁");
            }
            if(jzsmq.eq("2") || jzsfq.eq("2") || jzsxdjm.eq("2") || jzszn.eq("2")){
                // that.setRiskFactor("hbpJzs","高血压家族史");
            }
        }
        ,setDBRiskFactor: function (patientData) {
            var that = this;

            let age = patientData.age;
            let gender = patientData.gendercode;

            let jzsmq = patientData.jzsmq;
            let jzsfq = patientData.jzsfq;
            let jzsxdjm = patientData.jzsxdjm;
            let jzszn = patientData.jzszn;

            //心血管疾病既往史?

            if(parseInt(age) >= 40){
                that.setDbtRiskFactor("hbpAge","年龄≥40岁");
            }
            if(jzsmq.eq("3") || jzsfq.eq("3") || jzsxdjm.eq("3") || jzszn.eq("3")){
                that.setDbtRiskFactor("dbpJzs","糖尿病家族史");
            }
        }
        ,setLPRiskFactor: function (patientData) {
            var that = this;

            let age = patientData.age;
            let gender = patientData.gendercode;

            if(gender == "1" && parseInt(age) >= 45){
                that.setLpdRiskFactor("hbpAge","年龄大于45岁");
            }
            if(gender == "2" && parseInt(age) >= 55){
                that.setLpdRiskFactor("hbpAge","年龄大于55岁");
            }

        }
        ,setRiskFactor: function (key,value) {
            if( !riskFactor[key] ){
                riskFactor[key] = value;
            }
        }
        ,setDbtRiskFactor: function (key,value) {
            if( !diabetesRiskFactor[key] ){
                diabetesRiskFactor[key] = value;
            }
        }
        ,setLpdRiskFactor: function (key,value) {
            if( !lipidemiaRiskFactor[key] ){
                lipidemiaRiskFactor[key] = value;
            }
        }
        ,onBlurByInput: function (formId) {
            var that = this;
            $("#" + formId + " :input[type='number']").blur(function () {
                var id = $(this).attr("id");
                var value = $(this).val();
                var regex = $(this).attr("regex");
                if (value.length > 0  && regex && !that.buildRegex(regex).test(value)) {
                    $(this).val("");
                    return;
                }

                let gender = $("#gendercode").val();
                let age = $("#age").val();

                let weight = $("#weight").val();
                let height = $("#height").val();
                let waistline = $("#waistline").val();

                let ssy = $("#ssy").val();
                let szy = $("#szy").val();

                let kfxt = $("#kfxt").val();
                let ckxt = $("#ckxt").val();

                let zdgc = $("#zdgc").val();
                let gysz = $("#gysz").val();
                let dmdzdb = $("#dmdzdb").val();

                let yssysr = $("#yssysr").val();
                let ystsr = $("#ystsr").val();
                let ysysr = $("#ysysr").val();

                let yj1cs = $("#yj1cs").val();
                let yj1ks = $("#yj1ks").val();
                let yj2cs = $("#yj2cs").val();
                let yj2ks = $("#yj2ks").val();
                let yj3cs = $("#yj3cs").val();
                let yj3ks = $("#yj3ks").val();
                let yj4cs = $("#yj4cs").val();
                let yj4ks = $("#yj4ks").val();

                switch (id) {
                    case "weight"://体重指数
                        that.verifyBmi(weight, height);
                        break;
                    case "height"://体重指数
                        that.verifyBmi(weight, height);
                        break;
                    case "waistline"://腰围
                        that.verifyWaistline(waistline, gender);
                        break;
                    case "ssy"://血压
                        that.verifyBp(ssy, szy);
                        break;
                    case "szy"://血压
                        that.verifyBp(ssy, szy);
                        break;
                    case "kfxt"://血糖
                        that.verifyXt(kfxt, ckxt);
                        break;
                    case "ckxt"://血糖
                        that.verifyXt(kfxt, ckxt);
                        break;
                    case "zdgc"://血脂
                        that.verifyXz(zdgc.toFloat(2), gysz.toFloat(2),dmdzdb.toFloat(2));
                        break;
                    case "gysz"://血脂
                        that.verifyXz(zdgc.toFloat(2), gysz.toFloat(2),dmdzdb.toFloat(2));
                        break;
                    case "dmdzdb"://血脂
                        that.verifyXz(zdgc.toFloat(2), gysz.toFloat(2),dmdzdb.toFloat(2));
                        break;
                    case "yssysr"://饮食-食盐摄入
                        that.verifyYsSy(yssysr);
                        break;
                    case "ystsr"://饮食-糖摄入
                        that.verifyYsTang(ystsr);
                        break;
                    case "ysysr"://饮食-食用油摄入
                        that.verifyYsYou(ysysr);
                        break;
                    case "yj1cs"://饮酒
                    case "yj1ks":
                    case "yj2cs":
                    case "yj2ks":
                    case "yj3cs":
                    case "yj3ks":
                    case "yj4cs":
                    case "yj4ks":
                        that.verifyYj(gender);
                        break;
                }
                console.log(riskFactor)
            });
            $("#" + formId + " :input[type='number']").each(function () {
                //console.log($(this))
            });
        }
        ,ascvdChart: function(result) {
            let color3Arr = ["#2CBD32","#F5DD0C","#FF8D02","#FF2222"];
            let value = result["value"];
            let zfb = result["zfb"];
            let color = result["color"];
            console.log(result,value,zfb,color)
            let option = {
                series: [{
                    type: 'liquidFill',
                    data: [value],
                    color: [color],
                    center: ['50%', '50%'],
                    radius: '50%',
                    amplitude: '8%',
                    waveLength: '80%',
                    phase: 'auto',
                    period: 'auto',
                    direction: 'right',
                    shape: 'circle',
                    waveAnimation: false,
                    animationEasing: 'linear',
                    animationEasingUpdate: 'linear',
                    animationDuration: 2000,
                    animationDurationUpdate: 1000,
                    outline: {
                        show: true,
                        borderDistance: 0,
                        itemStyle: {
                            color: 'none',
                            borderColor:"#3faade",
                            borderWidth: 8,
                            shadowBlur: 20,
                            shadowColor: 'rgba(0, 0, 0, 0.25)'
                        }
                    },

                    backgroundStyle: {
                        color: 'rgba(96,189,232,0.95)'
                    },

                    itemStyle: {
                        opacity: 0.75,
                        shadowBlur: 50,
                        shadowColor: 'rgba(0, 0, 0, 0.4)'
                    },
                    label: {
                        formatter: "" + zfb + "%",
                        show: true,
                        color: '#fff',
                        insideColor: '#fff',
                        fontSize: 24,
                        fontWeight: 'bold',
                        align: 'center',
                        baseline: 'middle',
                        position: 'inside'
                    },

                    emphasis: {
                        itemStyle: {
                            opacity: 1
                        }
                    }
                }]
            };
            ascvdChart.setOption(option);
        }
        ,genEvalResultTable: function (evalResult){
            let step = 1;
            var evalResultLength  = Object.keys(evalResult).length;
            var html = "";
            if(evalResultLength > 0){
                html += '<table class="layui-form shfs-tb jg-tb" border="0" cellSpacing="0" cellPadding="0" border="0">';
                for(var key in evalResult) {
                    let kVal = evalResult[key];
                    if(kVal && kVal.length > 0){
                        html += '<tr>';
                        html += '<td>'+step+'.</td>';
                        html += '<td>'+kVal+'</td>';
                        html += '</tr>';
                        step++;
                    }
                }
                html += '</table>';
            }
            return html;
        }
        ,genAscvdColor: function (ascvd){
            let ascvdColorArr = ["#2CBD32","#F5DD0C","#FF8D02","#FF2222"];
            let ascvdColor = ascvdColorArr[0];
            if(ascvd < 5 )   {ascvdColor=ascvdColorArr[1];}
            if(ascvd >= 5 && ascvd <= 10 )  {ascvdColor=ascvdColorArr[2];}
            if(ascvd > 10 ) {ascvdColor=ascvdColorArr[3];}
            return ascvdColor;
        }
        ,getAgeScore(gender,age){
            if( age <= 39 ){
                return 0;
            }
            if( age <= 44 ){
                return 1;
            }
            if( age <= 49 ){
                return 2;
            }
            if( age <= 54 ){
                return 3;
            }
            if( age <= 59 ){
                return 4;
            }
            if( age >= 60 ){//每增加5岁加1分
                return 4 + parseInt((age-60) / 5);
            }
            return 0;
        }
        ,getSmockScore(gender,xyzk){
            if(xyzk == "3"){
                if(gender == "1"){//男性
                    return 2;
                }else{//女性及其他
                    return 1;
                }
            }else {
                return 0;
            }
        }
        ,getSsyScore(gender,ssy){
            if(gender == "1"){//男性
                if(ssy < 120){
                    return -2;
                }
                if(ssy < 129){
                    return 0;
                }
                if(ssy < 139){
                    return 1;
                }
                if(ssy < 159){
                    return 2;
                }
                if(ssy < 179){
                    return 5;
                }
                if(ssy >= 180){
                    return 8;
                }
            }else{//女性及其他
                if(ssy < 120){
                    return -2;
                }
                if(ssy < 129){
                    return 0;
                }
                if(ssy < 139){
                    return 1;
                }
                if(ssy < 159){
                    return 2;
                }
                if(ssy < 179){
                    return 3;
                }
                if(ssy >= 180){
                    return 4;
                }
            }
            return 0;
        }
        ,getBmiScore(gender,bmi){
            if(bmi < 24){
                return 0;
            }
            if(bmi < 27.9){
                return 1;
            }
            if(bmi >= 28 ){
                return 2;
            }
            return 0;
        }
        ,getDbScore(gender,pgxtcode){
            if(pgxtcode == "3"){
                if(gender == "1"){//男性
                    return 1;
                }else{//女性及其他
                    return 2;
                }
            }
            return 0;
        }
        ,getLpScore(gender,zdgc){
            if(zdgc < 5.2){
                return 0;
            }
            if(zdgc >= 5.2){
                return 1;
            }
            return 0;
        }
        ,calcAscvd: function (formData){

            var that = this;

            let ageScore = that.getAgeScore(formData.gendercode,parseInt(formData.age));
            let smockScore = that.getSmockScore(formData.gendercode,formData.pgsmockcode);
            let ssyScore = that.getSsyScore(formData.gendercode,parseInt(formData.ssy));
            let bmiScore = that.getBmiScore(formData.gendercode,parseFloat(formData.bmi));
            let dbScore = that.getDbScore(formData.gendercode,formData.pgxtcode);
            let lpScore = that.getLpScore(formData.gendercode,parseFloat(formData.zdgc));

            let totalScore = ageScore + smockScore + ssyScore + bmiScore + dbScore + lpScore;
            let ascvd = 0;
            if(formData.gendercode == "1"){
                if(totalScore <= -1) {
                    return menCavsd["nn"];
                }else if(totalScore >= 17) {
                    return menCavsd["17"];
                }else{
                    return menCavsd[totalScore];
                }
            }else{
                if(totalScore <= -2) {
                    return womenCavsd["nn2"];
                }else if(totalScore <= 1) {
                    return womenCavsd["nn1"];
                }else if(totalScore >= 17) {
                    return womenCavsd["17"];
                }else{
                    return womenCavsd[totalScore];
                }
            }

            return 0;

        }
        ,genAscvd: function (formData){
            var that = this;

            let ascvd = that.calcAscvd(formData);
            let valBfb = 0;
            if(ascvd < 5) valBfb = 0.2;
            if(ascvd <= 0) valBfb = 0;
            if(ascvd >= 5 && ascvd <= 10) valBfb = 0.4;
            if(ascvd > 10) valBfb = 0.6;

            let ascvdColor = that.genAscvdColor(ascvd);

            let ascvdRst = "正常";
            let ascvdCode = "0";
            if(ascvd < 5 )   {ascvdRst = "低危";ascvdCode = "1";}
            if(ascvd <= 0 )  {ascvdRst = "正常";ascvdCode = "0";}
            if(ascvd >= 5 && ascvd <= 10)  {ascvdRst = "中危";ascvdCode = "2";}
            if(ascvd > 10 ) {ascvdRst = "高危";ascvdCode = "3";}

            $("#ascvd").val(ascvd);
            $("#adgradecode").val(ascvdCode);
            $("#adgradename").val(ascvdRst);
            $("#adgradebfb").val(valBfb);

            let ascvdResult = {zfb: ascvd,value: valBfb,color:ascvdColor,name:ascvdRst};

            return ascvdResult;
        }
        ,print: function(){

        }
        ,resetAscvdChart(formData){
            // var that = this;
            // let ascvd = formData.ascvd;
            // let valBfb = formData.adgradebfb;
            // let ascvdColor = that.genAscvdColor(ascvd);
            // let ascvdResult = {zfb: ascvd,value: valBfb,color:ascvdColor};
            if(ascvdChart)ascvdChart.resize();
        }
        , setEvalPage: function (formData) {
            var that = this;
            var evalResult = {};
            for(var key in formData) {

                var value = formData[key];
                var valId = "#"+key + "-num";
                var valName = key + "-num";

                let node = $(`input[type="checkbox"][name^="${valName}"][value="${value}"]`);
                if (node && node.length) {
                    node[0].checked = true;
                }

                let node2 = $(`input[type="radio"][name^="${valName}"][value="${value}"]`);
                if (node2 && node2.length) {
                    node2[0].checked = true;
                }

                if($(valId).length > 0 ){
                    let unit = $(valId).attr("unit");
                    $(valId).html(value + (unit ? unit : ""));
                }
                if(key.eq("bpresult") || key.eq("dbresult") || key.eq("lpresult")){
                    evalResult[key] = value;
                }
                var evalResultHtml = that.genEvalResultTable(evalResult);
                if(evalResultHtml.length > 0){
                    $("#evalResult").html(evalResultHtml);
                }
            }

            form.render(null, 'bgForm');
            var radioNodes = $('.pgbgpage input:radio:checked');
            var checkboxNodes = $('.pgbgpage input:checkbox:checked');
            radioNodes.each(function(){
                let chkBoxx = $(this).next();
                if(chkBoxx && chkBoxx.length){
                    chkBoxx.removeClass("layui-checkbox-disbaled");
                    chkBoxx.removeClass("layui-radio-disbaled");
                    chkBoxx.removeClass("layui-disabled");
                }
            });
            checkboxNodes.each(function(){
                let chkBoxx = $(this).next();
                if(chkBoxx && chkBoxx.length){
                    chkBoxx.removeClass("layui-checkbox-disbaled");
                    chkBoxx.removeClass("layui-radio-disbaled");
                    chkBoxx.removeClass("layui-disabled");
                }
            });

            let colorArr = ["#2CBD32","#FFC107","#FF2222"];

            let tzColor = colorArr[0];
            if(formData.pgtzcode.eq("2"))tzColor=colorArr[1];
            if(formData.pgtzcode.eq("3"))tzColor=colorArr[2];
            $("#bmiResult").html('<span class="panel-result " style="color:'+tzColor+'">'+formData.pgtzname+'</span>');

            let ywColor = colorArr[0];
            if(formData.pgywcode.eq("1"))ywColor=colorArr[2];
            $("#waistlineResult").html('<span class="panel-result " style="color:'+ywColor+'">'+formData.pgywname+'</span>');

            let xyColor = colorArr[0];
            if(formData.pgxycode.eq("2"))xyColor=colorArr[1];
            if(formData.pgxycode.eq("1"))xyColor=colorArr[2];
            $("#xyResult").html('<span class="panel-result " style="color:'+xyColor+'">'+formData.pgxyname+'</span>');

            let xtColor = colorArr[0];
            if(formData.pgxtcode.eq("1"))xtColor=colorArr[1];
            if(formData.pgxtcode.eq("2"))xtColor=colorArr[1];
            if(formData.pgxtcode.eq("3"))xtColor=colorArr[2];
            $("#xtResult").html('<span class="panel-result " style="color:'+xtColor+'">'+formData.pgxtname+'</span>');

            let xzColor = colorArr[0];
            if(formData.pgxzcode.eq("1"))xzColor=colorArr[2];
            $("#xzResult").html('<span class="panel-result " style="color:'+xzColor+'">'+formData.pgxzname+'</span>');

            /**
             * bplevelcode  bpgradecode  dbgradecode lpgradecode
             *
             *   血压-评估结果字典
                 var bpLevelDic = {0: "正常", 1: "1级高血压", 2: "2级高血压", 3: "3级高血压"};
                 var bpGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
                 //血糖-评估结果字典
                 var xtGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
                 //血脂-评估结果字典
                 var xzGradeDic = {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"};
             */

            let color2Arr = ["#2CBD32","#07B0FF","#FFC107","#FF2222"];
            let color3Arr = ["#2CBD32","#07B0FF","#F5DD0C","#FF8D02","#FF2222"];
            let xyfxlvColor = color2Arr[0];
            $("#xy-fx-level").html(formData.bplevelname);
            if(formData.bplevelcode.eq("1"))xyfxlvColor = color2Arr[1];
            if(formData.bplevelcode.eq("2"))xyfxlvColor = color2Arr[2];
            if(formData.bplevelcode.eq("3"))xyfxlvColor = color2Arr[3];
            $("#xy-fx-level").css({"color":xyfxlvColor});

            let xyfxgrColor = color3Arr[0];
            $("#xy-fx-grade").html(formData.bpgradename);
            if(formData.bpgradecode.eq("1"))xyfxgrColor = color3Arr[2];
            if(formData.bpgradecode.eq("2"))xyfxgrColor = color3Arr[3];
            if(formData.bpgradecode.eq("3"))xyfxgrColor = color3Arr[4];
            if(formData.bpgradecode.eq("6"))xyfxgrColor = color3Arr[1];
            $("#xy-fx-grade").css({"color":xyfxgrColor});

            let xtfxgrColor = color3Arr[0];
            $("#xt-fx-grade").html(formData.dbgradename);
            if(formData.dbgradecode.eq("1"))xtfxgrColor = color3Arr[2];
            if(formData.dbgradecode.eq("2"))xtfxgrColor = color3Arr[3];
            if(formData.dbgradecode.eq("3"))xtfxgrColor = color3Arr[4];
            if(formData.dbgradecode.eq("6"))xtfxgrColor = color3Arr[1];
            $("#xt-fx-grade").css({"color":xtfxgrColor});

            let xzfxgrColor = color3Arr[0];
            $("#xz-fx-grade").html(formData.lpgradename);
            if(formData.lpgradecode.eq("1"))xzfxgrColor = color3Arr[2];
            if(formData.lpgradecode.eq("2"))xzfxgrColor = color3Arr[3];
            if(formData.lpgradecode.eq("3"))xzfxgrColor = color3Arr[4];
            if(formData.lpgradecode.eq("6"))xzfxgrColor = color3Arr[1];
            $("#xz-fx-grade").css({"color":xzfxgrColor});

            let ascvd = formData.ascvd;
            let valBfb = formData.adgradebfb;
            let ascvdColor = that.genAscvdColor(ascvd);

            $("#ascvdResult .bb").html(formData.adgradename);

            let ascvdResult = {zfb: ascvd,value: valBfb,color:ascvdColor};

            setTimeout(function (){
                that.ascvdChart(ascvdResult);
            },300);

            var bpRiskArr = formData.bprisk ? formData.bprisk.Split("#") : [];
            var dpRiskArr = formData.dprisk ? formData.dprisk.Split("#") : [];
            var lpRiskArr = formData.lprisk ? formData.lprisk.Split("#") : [];

            if(bpRiskArr && bpRiskArr.length > 0){
                $("#bprisk-box").html(that.genFxHtml(bpRiskArr));
            }
            if(dpRiskArr && dpRiskArr.length > 0){
                $("#dprisk-box").html(that.genFxHtml(dpRiskArr));
            }
            if(lpRiskArr && lpRiskArr.length > 0){
                $("#lprisk-box").html(that.genFxHtml(lpRiskArr));
            }



        }
        ,genFxHtml(riskArr){
            let html = "<div class='tv-box'>"
            riskArr.forEach((item,index) => {
                let itemArr = item.Split(":");
                let tle = itemArr[0];
                let vle = itemArr[1];
                let vleArr = vle.Split(",");
                html += '<div class="tle">'+tle+'</div>';
                vleArr.forEach((vleItem,i) => {
                    html += '<div class="vle"><span>'+(i+1)+'.</span>'+vleItem+'</div>';
                });
                console.log(tle,index,vle)
            });
            html+="</div>"
            return html;
        }
        , verifyBmi: function (weight, height) {//需要与腰围联动
            var that = this;
            if ((weight.length > 0 && height.length > 0) || (weight > 0 && height > 0)) {
                let result = that.getBmi(parseFloat(weight), parseFloat(height).toFixed(2));
                $("#bmi").val(result[0]);
                $("#pgtzcode").val(result[1]);
                $("#pgtzname").val(bmiDic[result[1]]);
                if (result[1] == "3") {//添加危险因素
                    if(! riskFactor["bmi"]){
                        riskFactor["bmi"] = "肥胖";
                    }
                }
            }else{
                let ywpgjg = $("#pgywcode").val();
                if(ywpgjg != 1 && riskFactor["bmi"]){
                    delete(riskFactor["bmi"]);
                }
            }
        }
        , verifyWaistline: function (waistline, gender) {
            let result = this.getWaistlineEval(waistline,gender);
            $("#pgywcode").val(result);
            $("#pgywname").val(waistLineDic[result]);
            if (result == "1") {//添加危险因素
                if(! riskFactor["bmi"]){
                    riskFactor["bmi"] = "腹型肥胖";
                }
            }else{
                let tzpgjg = $("#pgtzcode").val();
                if(tzpgjg != 3 && riskFactor["bmi"]){
                    delete(riskFactor["bmi"]);
                }
            }
        }
        , verifyBp: function (ssy, szy) {
            var that = this;
            if ((ssy.length > 0 && szy.length > 0) || (ssy > 0 && szy > 0)) {
                let result = that.getBpEval(ssy,szy);
                console.log(result,bpDic[result])
                $("#pgxycode").val(result);
                $("#pgxyname").val(bpDic[result]);
                let levelResult = that.getBpLevel(ssy,szy);
                $("#bplevelcode").val(levelResult);
                $("#bplevelname").val(bpLevelDic[levelResult]);
            }
        }
        , verifyXt: function (kfxt, ckxt) {
            var that = this;

            if(kfxt.length == 0) kfxt = 0;
            if(ckxt.length == 0) ckxt = 0;

            let result = that.getXtEval(kfxt,ckxt);
            $("#pgxtcode").val(result);
            $("#pgxtname").val(xtDic[result]);
            if (result != "0") {//添加危险因素
                if(! riskFactor["xt"]){
                    riskFactor["xt"] = xtDic[result];
                }
            }else{
                if(riskFactor["xt"]){
                    delete(riskFactor["xt"]);
                }
            }
        }
        , verifyXz: function (tc, tg, ldlc) {
            var that = this;
            if ( (tc.length > 0 || tg.length > 0 || ldlc.length > 0) || (tc > 0 || tg > 0 || ldlc > 0)) {
                let result = that.getXzEval(parseFloat(tc).toFixed(2), parseFloat(tg).toFixed(2), parseFloat(ldlc).toFixed(2));
                $("#pgxzcode").val(result);
                $("#pgxzname").val(xzDic[result]);
                if (result != "0") {//添加危险因素
                    if(! riskFactor["xz"]){
                        riskFactor["xz"] = xzDic[result];
                    }
                }
            }else{
                if(riskFactor["xz"]){
                    delete(riskFactor["xz"]);
                }
            }
        }
        ,verifyYs: function (){
            var lname = "正常饮食";
            if(eatHabit.length == 0){
                $("#pgysxgcode").val("0");
                $("#pgysxgname").val(lname);
            }
        }
        , verifyYsSy: function (ks) {
            var that = this;
            var lname = "高盐饮食";
            let result = that.getYssyEval(ks);
            if(result == "1"){
                if(!eatHabit.includes("1")){
                    eatHabit.push("1");
                    eatHabitName.push(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
                if(! riskFactor["yssysr"]){//添加危险因素
                    riskFactor["yssysr"] = "长期膳食高盐";
                }
            }else{
                if(eatHabit.includes("1")){
                    eatHabit.removeByVal("1");
                    eatHabitName.removeByVal(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
                if(riskFactor["yssysr"]){
                    delete(riskFactor["yssysr"]);
                }
            }
            that.verifyYs();
        }
        , verifyYsTang: function (ks) {
            var that = this;
            var lname = "高糖饮食";
            let result = that.getYstangEval(ks);
            if(result == "1"){
                if(!eatHabit.includes("2")){
                    eatHabit.push("2");
                    eatHabitName.push(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
            }else{
                if(eatHabit.includes("2")){
                    eatHabit.removeByVal("2");
                    eatHabitName.removeByVal(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
            }
            that.verifyYs();
        }
        , verifyYsYou: function (ks) {
            var that = this;
            var lname = "食用油摄入量偏高";
            let result = that.getYsyouEval(ks);
            if(result == "1"){
                if(!eatHabit.includes("3")){
                    eatHabit.push("3");
                    eatHabitName.push(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
            }else{
                if(eatHabit.includes("3")){
                    eatHabit.removeByVal("3");
                    eatHabitName.removeByVal(lname);
                    $("#pgysxgcode").val(eatHabit.join((eatHabit.length > 1 ? "," : "")));
                    $("#pgysxgname").val(eatHabitName.join((eatHabitName.length > 1 ? "," : "")));
                }
            }
            that.verifyYs();
        }
        , verifyYj: function (gender) {
            var that = this;
            let result = that.getYjEval(that.getYjFormData(),gender);
            let rjyjl = result.get("rjyjl");
            let evalResult = result.get("eval");
            if(evalResult != "0"){//添加危险因素
                if(! riskFactor["yj"]){
                    riskFactor["yj"] = yjDic[evalResult];
                }
            }else{
                if(riskFactor["yj"]){
                    delete(riskFactor["yj"]);
                }
            }
            $("#pgyjcode").val(evalResult);
            $("#pgyjname").val(yjDic[evalResult]);
            $("#rjyjks").val(parseInt(rjyjl));
        }
        ,getYjFormData: function (){
            var that = this;
            let yj1cs = $("#yj1cs").val().length > 0 ? $("#yj1cs").val() : 0;
            let yj1ks = $("#yj1ks").val().length > 0 ? $("#yj1ks").val() : 0;
            let yj2cs = $("#yj2cs").val().length > 0 ? $("#yj2cs").val() : 0;
            let yj2ks = $("#yj2ks").val().length > 0 ? $("#yj2ks").val() : 0;
            let yj3cs = $("#yj3cs").val().length > 0 ? $("#yj3cs").val() : 0;
            let yj3ks = $("#yj3ks").val().length > 0 ? $("#yj3ks").val() : 0;
            let yj4cs = $("#yj4cs").val().length > 0 ? $("#yj4cs").val() : 0;
            let yj4ks = $("#yj4ks").val().length > 0 ? $("#yj4ks").val() : 0;
            let data = new Array();
            data.push(that.genYjParam(yj1cs, yj1ks, 0.4));
            data.push(that.genYjParam(yj2cs, yj2ks, 0.3));
            data.push(that.genYjParam(yj3cs, yj3ks, 0.04));
            data.push(that.genYjParam(yj4cs, yj4ks, 0.1));
            return data;
        }
        /**
         * 获取bmi指数及评估结果
         * @param weight 体重
         * @param height 身高
         * @returns {[string, *]}
         */
        , getBmi: function (weight, _height) {
            var that = this;
            let height = parseFloat(_height/100);
            let bmiValue = weight / (height * height);
            return [bmiValue.toFixed(1), that.getBmiEval(bmiValue.toFixed(1))];
        }
        , getBmiEval: function (bmi) {
            if (bmi < 18.5) {
                return "1"; //过轻
            }
            if (bmi >= 28) {
                return "3"; //肥胖
            }
            if (bmi >= 18.5 && bmi < 24) {
                return "0"; //正常
            }
            if (bmi >= 24 && bmi < 28) {
                return "2"; //超重
            }
            return "0";
        }
        , getWaistlineEval: function (waistline, gender) {
            if (gender == "1" || gender == "2") {
                if (gender === "1" && waistline >= 90) { //男性
                    return "1"; //过大
                }
                if (gender === "2" && waistline >= 85) { //女性
                    return "1"; //过大
                }
                return "0"; //正常
            }
            if (waistline >= 85) {
                return "1"; //过大
            }
            return "0"; //正常
        }

        , getBpEval: function (sbp, dbp) {
            var that = this;
            // if ((sbp > 90 && sbp < 140) && (dbp > 60 && dbp < 90)) {
            //     return "0"; //正常
            // }
            // if (sbp >= 140 && dbp >= 90) {
            //     return "1"; //高血压
            // }
            // if (sbp <= 90 && dbp <= 60) {
            //     return "2"; //低血压
            // }
            if(that.getBpLevel(sbp, dbp) == "0"){
                return "0"; //正常
            }else{
                return "1"; //高血压
            }
        }
        /**
         * 高血压分级
         * @param sbp
         * @param dbp
         */
        , getBpLevel: function (sbp, dbp) {
            if ((sbp >= 140 && sbp < 160)) {
                return "1"; //1级高血压
            }else if ((sbp >= 160 && sbp < 180) ) {
                return "2"; //2级高血压
            }else if (sbp >= 180) {
                return "3"; //3级高血压
            }else if ( (dbp >= 90 && dbp < 100) ) {
                return "1"; //1级高血压
            }else if ((dbp >= 100 && dbp < 110)) {
                return "2"; //2级高血压
            }else if (dbp >= 110) {
                return "3"; //3级高血压
            }
            return "0"; //正常
        }
        , setValById: function (id, value) {
            $("#"+id).val(value);
        }
        /**
         * 高血压分层
         */
        , genBpGrade: function (data) {
            console.log("==========",data)
            var that = this;
            let bpLevel = data.bplevelcode;
            var riskFactorLength  = Object.keys(riskFactor).length;
            let keyId,kValue;
            switch (bpLevel){
                case "0"://正常：判断是否易患
                    keyId = that.getBpGrade(riskFactorLength);
                    kValue = bpGradeDic[keyId];
                    break;
                case "1"://1及高血压
                    keyId = that.getBpGrade1(riskFactorLength);
                    kValue = bpGradeDic[keyId];
                    break;
                case "2"://2及高血压
                    keyId = that.getBpGrade2(riskFactorLength);
                    kValue = bpGradeDic[keyId];
                    break;
                case "3"://3及高血压
                    keyId = that.getBpGrade3(riskFactorLength);
                    kValue = bpGradeDic[keyId];
                    break;
            }
            that.setValById("bpgradecode",keyId);
            that.setValById("bpgradename",kValue);
            let result = "";
            let tmplet = "该患者属于高血压"+kValue+",请定期进行检查并治疗";
            if(!keyId.eq("0"))result = tmplet;
            that.setValById("bpresult",result);
        }
        , getBpGrade: function (riskFactorLength) {
            var that = this;
            if(riskFactorLength > 0){
                return "6";//易患
            }
            // if(riskFactor.eq("hbpAge") || riskFactor.eq("hbpJzs") || riskFactor.eq("bmi") || riskFactor.eq("smcok") || riskFactor.eq("yj") || riskFactor.eq("yssysr")){}
            return "0";//正常
        }
        , getBpGrade1: function (riskFactorLength) {
            var that = this;
            if(riskFactorLength == 0){
                return "1";//低危
            }
            if(riskFactorLength > 0 && riskFactorLength <=2){
                return "2";//中危
            }
            if(riskFactorLength >= 3){
                return "3";//高危
            }
            return "1";
        }
        , getBpGrade2: function (riskFactorLength) {
            var that = this;
            if(riskFactorLength == 0){
                return "2";//中危
            }
            if(riskFactorLength > 0 && riskFactorLength <=2){
                return "2";//中危
            }
            if(riskFactorLength >= 3){
                return "3";//高危
            }
            return "2";
        }
        , getBpGrade3: function (riskFactorLength) {
            return "3";
        }
        , getYssyEval: function (sy) {
            if (sy > 6) {
                return "1";//偏高
            }
            return "0"; //正常
        }
        /**
         * 糖尿病分层
         * {0: "正常", 1: "空腹血糖受损", 2: "糖耐量异常", 3: "糖尿病"};
         * {0: "正常", 1: "1型糖尿病", 2: "2型糖尿病", 3: "妊娠糖尿病", 4: "其他糖尿病"};
         * 暂时定义为 糖尿病 为2型糖尿病  缺少判断依据
         */
        , genDbLevel: function (data) {
            var that = this;
            let pgxtcode = data.pgxtcode;
            if(pgxtcode == "0"){
                return "0";
            }else if(pgxtcode == "3"){
                return "2"; //2型糖尿病
            }else {
                return "1"; //1型糖尿病
            }
            return "0"; //正常
        }
        /**
         * 糖尿病分层
         * {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"}
         */
        , genDbGrade: function (data) {
            var that = this;
            let keyId,kValue;
            let dbLevel = that.genDbLevel(data);
            let pgxtcode = data.pgxtcode;
            let pgtlldcode = data.pgtlldcode;
            let _newMap = new Map();
            _newMap.set(riskFactor);
            for(var key in riskFactor) {
                //心血管疾病既往史
                if( !key.eq("hbpAge") && !key.eq("hbpJzs") && !key.eq("yssysr") && !key.eq("yj") && !key.eq("risk") && !key.eq("bqgsh") && !key.eq("lcjh") && !key.eq("xt")){
                    diabetesRiskFactor[key] = riskFactor[key];
                }
            }
            // if(pgxtcode.eq("1") || pgxtcode.eq("2")){
            //     that.setDbtRiskFactor("IGR","糖尿病前期");
            // }
            if(pgtlldcode.eq("0") ){//静坐生活方式
                that.setDbtRiskFactor("tlld","静坐生活方式");
            }
            var diabetesRiskFactorLength  = Object.keys(diabetesRiskFactor).length;
            switch (dbLevel){
                case "0"://正常：判断是否易患
                    keyId = that.getDbGrade(diabetesRiskFactorLength);
                    kValue = xtGradeDic[keyId];
                    break;
                case "1"://空腹血糖受损, 糖耐量异常：判断是否易患
                    keyId = that.getDbGrade(diabetesRiskFactorLength);
                    kValue = xtGradeDic[keyId];
                    break;
                default://糖尿病
                    keyId = that.getDbGrade1(diabetesRiskFactorLength);
                    kValue = xtGradeDic[keyId];
                    break;
            }
            that.setValById("dblevelcode",dbLevel);
            that.setValById("dblevelname",xtLevelDic[dbLevel]);
            that.setValById("dbgradecode",keyId);
            that.setValById("dbgradename",kValue);

            let result = "";
            let tmplet = "该患者属于糖尿病"+kValue+",请定期进行检查并治疗";
            if(!keyId.eq("0"))result = tmplet;
            that.setValById("dbresult",result);
        }
        , getDbGrade: function (diabetesRiskFactorLength) {
            var that = this;
            if(diabetesRiskFactorLength > 0){
                return "6";//易患
            }
            return "0";//正常
        }
        , getDbGrade1: function (diabetesRiskFactorLength) {
            var that = this;
            let tnbbc = $("#tnbbc").val().length > 0 ? parseInt($("#tnbbc").val()) : 0;
            let bqgshLen = diabetesRiskFactor["bqgsh"] ? diabetesRiskFactor["bqgsh"].length : 0;
            if(diabetesRiskFactorLength >= 1 || tnbbc >= 10){//不伴有靶器官损害，且病程>=10 合并1个及以上危险因素
                return "2";//中危    ->高危
            }
            if(diabetesRiskFactorLength >= 3 || bqgshLen >=3 || tnbbc >= 20){
                return "3";//高危  ->极高危
            }
            return "1";
        }
        /**
         * 高血脂分层
         * {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"}
         */
        , genLpGrade: function (data) {
            var that = this;

            let pgxycode = data.pgxycode;//1 高血压
            let pgxzcode = data.pgxzcode;//1 高血脂

            let tc = data.zdgc;//总胆固醇
            let ldlc = data.dmdzdb;//总胆固醇
            let keyId = "0";
            let kValue = xzGradeDic[keyId];
            let xzLevel = "0";
            let isGxbWz = data.wxyscode.eq("8");
            if(parseFloat(tc) >=6.2  || parseFloat(ldlc) >= 4.1){
                xzLevel = "2";
            }
            if(parseFloat(tc) >=5.2  || parseFloat(ldlc) >= 3.4){
                xzLevel = "1";
            }
            let _newMap = new Map();
            _newMap.set(riskFactor);
            for(var key in riskFactor) {
                if( !key.eq("hbpAge") && !key.eq("hbpJzs") && !key.eq("yssysr") && !key.eq("yj")  && !key.eq("risk") && !key.eq("bqgsh")&& !key.eq("lcjh") && !key.eq("xt")  && !key.eq("xz")){
                    lipidemiaRiskFactor[key] = riskFactor[key];
                }
            }
            var lipidemiaRiskFactorLength  = Object.keys(lipidemiaRiskFactor).length;

            //pgxzcode = 0 {0:"正常",1:"低危",2:"中危",3:"高危",6:"易患"}

            if(pgxzcode.eq("1")){
                keyId = "6";
                kValue = xzGradeDic[keyId];
            }
            if(xzLevel.eq("1")){//1档
                if(!pgxycode.eq("1") && lipidemiaRiskFactorLength < 3){//无高血压并且危险因素<3
                    keyId = "1";
                    kValue = xzGradeDic[keyId];
                }
                if(pgxycode.eq("1") || lipidemiaRiskFactorLength >= 3){//高血压并或危险因素>=3
                    keyId = "1";
                    kValue = xzGradeDic[keyId];
                }
                if(pgxycode.eq("1") && lipidemiaRiskFactorLength >= 1){//高血压并且危险因素>=3
                    keyId = "2";
                    kValue = xzGradeDic[keyId];
                }
                //冠心病及其他危症=高危
                if(isGxbWz){
                    keyId = "3";
                    kValue = xzGradeDic[keyId];
                }
            }

            if(xzLevel.eq("2")){//2档
                if(!pgxycode.eq("1") && lipidemiaRiskFactorLength < 3){//无高血压并且危险因素<3
                    keyId = "1";
                    kValue = xzGradeDic[keyId];
                }
                if(pgxycode.eq("1") || lipidemiaRiskFactorLength >= 3){//高血压并或危险因素>=3
                    keyId = "2";
                    kValue = xzGradeDic[keyId];
                }
                if(pgxycode.eq("1") && lipidemiaRiskFactorLength >= 1){//高血压并且危险因素>=3
                    keyId = "3";
                    kValue = xzGradeDic[keyId];
                }
                //冠心病及其他危症=高危
                if(isGxbWz){
                    keyId = "3";
                    kValue = xzGradeDic[keyId];
                }
            }

            that.setValById("lpgradecode",keyId);
            that.setValById("lpgradename",kValue);

            let result = "";
            let tmplet = "该患者属于高血脂"+kValue+",请定期进行检查并治疗";
            if(!keyId.eq("0"))result = tmplet;
            that.setValById("lpresult",result);
        }
        , getYstangEval: function (tang) {
            if (tang > 50) {
                return "1";//偏高
            }
            return "0"; //正常
        }
        , getYsyouEval: function (you) {
            if (you > 25) {
                return "1";//偏高
            }
            return "0"; //正常
        }
        , getYjEval: function (_data, _gender) {
            let result = new Map();
            let data = _data;
            let yjl = 0;
            data.forEach(map => {
                let cs = map.get("cs");
                let ks = map.get("ks");
                let bz = map.get("bz");
                yjl += (cs * ks) * bz;
            });
            let rjyjl = yjl / 30;
            let evalVal = "0";
            if (_gender == "1") {
                if (rjyjl < 41) { //正常饮酒 0
                    evalVal = "0";
                }
                if (rjyjl >= 41 && rjyjl < 61) { //危险饮酒 1
                    evalVal = "1";
                }
                if (rjyjl >= 61) { //有害饮酒 2
                    evalVal = "2";
                }
            } else {
                if (rjyjl < 21) { //正常饮酒 0
                    evalVal = "0";
                }
                if (rjyjl >= 21 && rjyjl < 41) { //危险饮酒 1
                    evalVal = "1";
                }
                if (rjyjl >= 41) { //有害饮酒 2
                    evalVal = "2";
                }
            }
            result.set("rjyjl", rjyjl);
            result.set("eval", evalVal);
            return result;
        }
        , genYjItem: function (cs, ks, bz) {
            return (cs * ks) * bz;
        }
        , genYjParam: function (cs, ks, bz) {
            var data = new Map();
            data.set("cs", cs);
            data.set("ks", ks);
            data.set("bz", bz);
            return data;
        }
        /**
         * @param kfXt 空腹血糖
         * @param ck2Xt 餐后2小时血糖
         * @returns {string}
         */
        , getXtEval: function (kfXt, ck2Xt) {
            if ((kfXt < 6.1) && (ck2Xt < 7.8)) { //3.9~6.1 - 3.9~7.8
                return "0"; //正常
            }
            if (kfXt >= 6.1 && kfXt < 7) {
                return "1"; //空腹血糖受损
            }
            if (ck2Xt >= 7.8 && ck2Xt < 11.1) {
                return "2"; //糖耐量异常
            }
            if (kfXt >= 7 || ck2Xt >= 11.1) {
                return "3"; //糖尿病
            }
            return "0"; //正常
        }
        /**
         * @param tc 总胆固醇
         * @param tg 甘油三酯
         * @param ldlc 低密度脂蛋白
         */
        , getXzEval: function (tc, tg, ldlc) {
            console.log("tc",tc, tc>=5.17)
            console.log("tg",tg, tg>=1.7)
            console.log("ldlc",ldlc, ldlc>=3.1)
            if (tc >= 5.17 || tg >= 1.7 || ldlc >= 3.1) {
                return "1"; //高血脂症
            }
            return "0"; //正常
        }
    };
    //暴露接口
    exports('detevalUtil', detevalUtil);
});