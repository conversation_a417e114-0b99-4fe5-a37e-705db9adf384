/**
 @Name：检测评估 工具集
 @Author：泰安翼云软件
 @License：MIT
 */
layui.define(['jquery', 'echarts'], function (exports) {
    "use strict";
    var $ = layui.$;
    var form = layui.form;

    String.prototype.toFloat = function(fix) {
        let tofix = 0;
        if(fix){
            tofix = fix;
        }
        var val = this;
        if(val.length > 0){
            return  parseFloat(val).toFixed(tofix)
        }else{
            return 0;
        }
    };
    String.prototype.toInt = function() {
        var val = this;
        if(val.length > 0){
            return  parseInt(val);
        }else{
            return 0;
        }
    };

    String.prototype.eq = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Map.prototype.eq = function (key) {
        return this.hasOwnProperty(key);
    }
    Array.prototype.of = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            return true;
        }
        return false;
    };
    Array.prototype.removeByVal = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }

    var itemDic = {"01": "饮食指导", "02": "运动指导", "03": "教育指导", "04": "用药指导", "05": "监测指导"};

    var planUtil = {//外部接口

    };
    //暴露接口
    exports('planUtil', planUtil);
});