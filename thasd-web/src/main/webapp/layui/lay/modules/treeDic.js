layui.define(['layer', 'jquery'], function(exports){
 "use strict";
  var $ = layui.jquery;
  var layer = layui.layer;
  var zTreeIds;
  var treeDic = {
	 mod : ""
	,sTipMsg : "只能选择子项目"
	,initSelectTree: function(zNodes,id,isParent,sMsg,sMod,isMultiple,chkboxType,onClickCallback){
		  zTreeIds = "#" + id + "Tree";
		  var type = typeof onClickCallback === 'function';
		  var setting = {
		   mod : sMod
		  ,sTipMsg :( sMsg == "" ? "只能选择子项目"	 : sMsg)
          ,view: {
              dblClickExpand: treeDic.dblClickExpand
             ,showLine: true
          }
          ,edit: {
        	  showRemoveBtn: treeDic.showRemoveBtn
        	 ,showRenameBtn: treeDic.showRenameBtn
          }
          ,data: {
              simpleData: {
                  enable: true
              }
          },
          check: {
              enable: false,
              chkboxType: {"Y": "ps", "N": "s"}
          },
          callback: {
              onClick: type ? onClickCallback : treeDic.onClick,
              onCheck: treeDic.onCheck
          }
	  };
	  if(sMod=='dicTable'){
          setting.view.addDiyDom = treeDic.addDiyDom;
	  }
	  if (isParent) {
          setting.callback.beforeClick = treeDic.beforeClick;
	  }
	  if (isMultiple) {
          setting.check.enable = isMultiple;
	  }
      if (chkboxType !== undefined && chkboxType != null) {
          setting.check.chkboxType = chkboxType;
      }
      $("#" + id).html('<div class="tree-content scrollbar">' 
				          +'<ul id="' + id + 'Tree" class="ztree scrollbar" style="margin-top:0;"></ul>' 
				         +'</div>');

      $.fn.zTree.init($("#" + id + "Tree"), setting, zNodes);
    }
  	,dblClickExpand : function(treeId, treeNode) {
		return treeNode.level > 0;
	}
  	,beforeClick : function(treeId, treeNode) {
		  var treeObj = $.fn.zTree.getZTreeObj(treeId);
  		  if(treeObj.setting.mod == 'dicTable'){
  			  if(treeNode.id=='000000000000000000000000000000000000'){
    			//layer.msg(treeObj.setting.sTipMsg,{time:2000});
  				return false;
  			  }else{
  				return true;
  			  }
  		  }else if(treeObj.setting.mod == 'menuTable'){
  			  var checkId = '000000000000000000000000000000000000';
	  		  if(treeNode.id == checkId || treeNode.pId == checkId){
	  			  //layer.msg(treeObj.setting.sTipMsg,{time:2000});
	  			  return false;
	  		  }else{
	  			return true;
	  		  }
    	  } else if(treeObj.setting.mod == 'deptTable'){
  			  var checkId = '000000000000000000000000000000000000';
	  		  if(!treeNode.canclick){
	  			  	//layer.msg(treeObj.setting.sTipMsg,{time:2000});
	  			  return false;
	  		  }else{
	  			return true;
	  		  }
    	  } else {
  			return true;
  		  }
	}
  	,onClick : function (event, treeId, treeNode) {
	      var zTree = $.fn.zTree.getZTreeObj(treeId);
	      if(zTree.setting.mod == 'dicTable'){
			  selectDicNode = treeNode;
			  loadListFunction();
	  	  }else if(zTree.setting.mod == 'menuTable'){
	  		 selectMenuNode = treeNode;
	  	     loadListFunction();
	  	  }else if(zTree.setting.mod == 'deptTable'){
		  	  selectMenuNode = treeNode;
		  	  loadListFunction();
		  }
	      
	      if (zTree.setting.check.enable == true) {
	          zTree.checkNode(treeNode, !treeNode.checked, false)
	          treeDic.assignment(treeId, zTree.getCheckedNodes());
	      } else {
	    	  treeDic.assignment(treeId, zTree.getSelectedNodes());
	      }
	}
  	,onCheck : function(event, treeId, treeNode) {
	      var zTree = $.fn.zTree.getZTreeObj(treeId);
	      treeDic.assignment(treeId, zTree.getCheckedNodes());
	}
  	,showRemoveBtn : function (treeId, treeNode) {
		return !treeNode.isFirstNode;
	}
	,showRenameBtn : function (treeId, treeNode) {
		return !treeNode.isFirstNode;
	}
	,addDiyDom :function(treeId, treeNode) {
		if (treeNode.id=="000000000000000000000000000000000000") return;
		var IDMark_Switch = "_switch",
		IDMark_Icon = "_ico",
		IDMark_Span = "_span",
		IDMark_Input = "_input",
		IDMark_Check = "_check",
		IDMark_Edit = "_edit",
		IDMark_Remove = "_remove",
		IDMark_Ul = "_ul",
		IDMark_A = "_a";
		var aObj = $("#" + treeNode.tId + IDMark_Span);
		//id='diyBtn_" +treeNode.id+ "'
		var editStr = "<span >["+treeNode.code+"]</span>";
		aObj.after(editStr);
	}
  	,assignment : function(treeId, nodes) {
	      var names = "";
	      var ids = "";
	      for (var i = 0, l = nodes.length; i < l; i++) {
	          names += nodes[i].name + ",";
	          ids += nodes[i].id + ",";
	      }
	      if (names.length > 0) {
	          names = names.substring(0, names.length - 1);
	          ids = ids.substring(0, ids.length - 1);
	      }
	      treeId = treeId.substring(0, treeId.length - 4);
	      $("#" + treeId + "Show").attr("value", names);
	      $("#" + treeId + "Show").attr("title", names);
	      $("#" + treeId + "Hide").attr("value", ids);
	}
  };
  exports('treeDic',treeDic);
});