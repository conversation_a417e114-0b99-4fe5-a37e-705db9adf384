layui.define(['layer', 'jquery'], function(exports){
 "use strict";
  var $ = layui.jquery;
  var layer = layui.layer;
  var treeSelect = {
	 mod : ""
	,sTipMsg : "只能选择子项目"
	,initSelectTree: function(zNodes,placeholder,id,isRequired,isParent,sMsg,sMod,isMultiple,chkboxType){
		  console.log(zNodes,placeholder,id,isRequired,isParent,sMsg,sMod,isMultiple,chkboxType)

		var requiredStr = 'lay-verify="required"';
        if(sMod == "roleDept"){
        	requiredStr = 'lay-verify="roledept"';
        }
        if(sMod == "roleOrg"){
        	requiredStr = 'lay-verify="roleorg"';
        }
        if(sMod == "roleMenurs"){
        	requiredStr = 'lay-verify="rolemenu"';
        }
        if(sMod == "deptzl2"){
        	requiredStr = 'lay-verify="required|zldwCk"';
        }
		var disNoCss = "";
		var setting = {
		   mod : sMod
		  ,sTipMsg :( sMsg == "" ? "只能选择叶子节点"	 : sMsg)
          ,view: {
              dblClickExpand: false,
              showLine: false
          },
          data: {
              simpleData: {
                  enable: true
              }
          },
          check: {
              enable: false,
              chkboxType: { "Y" : "ps", "N" : "ps" }
          },
          callback: {
              onClick: treeSelect.onClick,
              onCheck: treeSelect.onCheck
          }
	  };
	  if (isParent) {
          setting.callback.beforeClick = treeSelect.beforeClick;
	  }
	  if (isMultiple) {
          setting.check.enable = isMultiple;
	  }
	  /*
      if (chkboxType !== undefined && chkboxType != null) {
          setting.check.chkboxType = chkboxType;
      }*/
      if(sMod == "roleDept" || sMod == "roleMenurs" || sMod == "roleOrg"){
    	  disNoCss = ' style="display:none;" '
    	  //setting.callback.onNodeCreated = treeSelect.onNodeCreatedCheck; 
      }
      
      var html = '<div class = "layui-select-title" '+disNoCss+' >' +
          '<input name = "' + $("#" + id).attr("rname") +'"  id="' + id + 'Show"' + 'type = "text" placeholder = "' + placeholder +'" '+(isRequired ? requiredStr: "")+' value = "' + $("#" + id).attr("nval") +'" class = "layui-input" readonly>' +
          '<i class= "layui-edge" ></i>' +
          '</div>';
      $("#" + id).append(html);
      $("#" + id).parent().append('<div class="tree-content scrollbar">' +
          '<input hidden id="' + id + 'Hide" ' +
          'value = "' + $("#" + id).attr("ival") +'"' +
          'name="' + $("#" + id).attr("rid") + '">' +
          '<ul id="' + id + 'Tree" class="ztree scrollbar" style="margin-top:0;"></ul>' +
          '</div>');
      $("#" + id).bind("click", function () {
          if ($(this).parent().find(".tree-content").css("display") !== "none") {
        	  treeSelect.hideMenu()
          } else {
              $(this).addClass("layui-form-selected");
              var Offset = $(this).offset();
              var width = $(this).width() - 2;
              if(sMod == "sbfl"){
            	  width = $(this).width()*2;
              }else if(sMod == "cascdeptsb" || sMod == "deptzl"){
            	  width = $(this).width()*1.4;
              }

              $(this).parent().find(".tree-content").css({
                  left: Offset.left + "px",
                  top: Offset.top + $(this).height() + "px"
              }).slideDown("fast");
              
              
              $(this).parent().find(".tree-content").css({
                  width: width
              });
              $("body").bind("mousedown", treeSelect.onBodyDown);
          }
      });
        var initTreeObj = $.fn.zTree.init($("#" + id + "Tree"), setting, zNodes);
        var nodes = initTreeObj.getCheckedNodes(true);
        if(sMod == "roleMenurs"){
        	for(var i = 0; i < nodes.length;i++){
           	 var node = nodes[i];
           	 if(node.checked && node.type=="resource"){
           		 initTreeObj.checkNode(node, true, true);
           	 }
           	 /*
           	 var parentNode = node.getParentNode();
           	 if(parentNode){
           		 initTreeObj.checkNode(parentNode, true, true);
           	 }
           	 */
           }
        }else{
        	for(var i = 0; i < nodes.length;i++){
              	 var node = nodes[i];
          		 initTreeObj.checkNode(node, true, true);
              	 /*
              	 var parentNode = node.getParentNode();
              	 if(parentNode){
              		 initTreeObj.checkNode(parentNode, true, true);
              	 }
              	 */
              }
        }
        
        if(sMod == "roleMenurs"){
        	var names = "";
        	var menuids = "";
        	var resourceids = "";
        	var nodeMenu = initTreeObj.getNodesByParam("type", 'menu', null);
    	   	for(var i = 0; i < nodeMenu.length;i++){
    	     	 var node = nodeMenu[i];
    	     	 if(node.checked){
    	     	   menuids += node.id + ",";
    	     	   names += node.name + ",";
    	     	 }
    	    }
    	   	$("#sjcdHide").val(menuids.substring(0, menuids.length - 1));
    	    var nodeResource = initTreeObj.getNodesByParam("type", 'resource', null);
    	   	for(var i = 0; i < nodeResource.length;i++){
    	     	 var node = nodeResource[i];
    	     	 if(node.checked){
    	     		resourceids += node.id + ",";
        	     	names += node.name + ",";
    	     	 }
    	    }
    	   	$("#resourceids").val(resourceids.substring(0, resourceids.length - 1));
    	   	//$("#sjcdShow").val(names.substring(0, names.length - 1));

        }
        if(sMod == "roleDept"){
        	var ids = "";
        	var names = "";
        	var nodeMenu = initTreeObj.getCheckedNodes(true);
    	   	for(var i = 0; i < nodeMenu.length;i++){
    	     	 var node = nodeMenu[i];
    	     	ids += node.id + ",";
    	     	names += node.name + ",";
    	    }
    	   	//$("#sjcdShow").val(names.substring(0, names.length - 1));
    	   	$("#sjcdHide").val(ids.substring(0, ids.length - 1));
        }
        if(sMod == "roleOrg"){
        	var ids = "";
        	var names = "";
        	var nodeMenu = initTreeObj.getCheckedNodes(true);
    	   	for(var i = 0; i < nodeMenu.length;i++){
    	     	 var node = nodeMenu[i];
    	     	ids += node.id + ",";
    	     	names += node.name + ",";
    	    }
    	   	$("#sjcdShow").val(names.substring(0, names.length - 1));
    	   	$("#sjcdHide").val(ids.substring(0, ids.length - 1));
        }
    }
  	,beforeClick : function(treeId, treeNode) {
		  var treeObj = $.fn.zTree.getZTreeObj(treeId);
		  if(treeObj.setting.mod == 'xyyorg'){
			  //console.log(treeNode,treeNode.grade)
			  if(treeNode.grade == "3"){
				  return true;
			  }else{
				  layer.msg(treeObj.setting.sTipMsg,{time:2000});
				  return false;
			  }
		  }else if(treeObj.setting.mod == 'psarea'){
			  //console.log(treeNode,treeNode.grade)
			  if(treeNode.grade == "3"){
				  return true;
			  }else{
				  layer.msg(treeObj.setting.sTipMsg,{time:2000});
				  return false;
			  }
		  }else if(treeObj.setting.mod == 'resource'){
  			  var checkId = '000000000000000000000000000000000000';
    		  if(treeNode.id == checkId || treeNode.pId == checkId){
    			  layer.msg(treeObj.setting.sTipMsg,{time:2000});
    			  return false;
    		  }else{
    			return true;
    		  }
  		  }else if(treeObj.setting.mod == 'deptarea'  || treeObj.setting.mod == 'deptsb'  || treeObj.setting.mod == 'sydeptsb' || treeObj.setting.mod == 'deptzl' || treeObj.setting.mod == 'deptzl2'){
    		  var checkId = '000000000000000000000000000000000000';
      		  if(treeNode.id == checkId || treeNode.isParent){
      			  layer.msg(treeObj.setting.sTipMsg,{time:2000});
      			  return false;
      		  }else{
      			return true;
      		  }
    	  }else if(treeObj.setting.mod == 'userdept'){
    		  if(!treeNode.isOrg){
				  return true;
    		  }else{
				  layer.msg(treeObj.setting.sTipMsg,{time:2000});
				  return false;
    		  }
    	  }else if(treeObj.setting.mod == 'sbfl'){
			  if(treeNode.isParent){
				  layer.msg(treeObj.setting.sTipMsg,{time:2000});
				  return false;
			  }else{
				return true;
			  }
		  }else if(treeObj.setting.mod == 'cascareasb'){
			  if(treeNode.isParent){
				  layer.msg(treeObj.setting.sTipMsg,{time:2000});
				  return false;
			  }else{
				return true;
			  }
		  }else {
  			return true;
  		  }
	}
  	,onClick : function (event, treeId, treeNode) {
		  var treeObj = $.fn.zTree.getZTreeObj(treeId);
	      var zTree = $.fn.zTree.getZTreeObj(treeId);
	      if (zTree.setting.check.enable == true) {
	          zTree.checkNode(treeNode, !treeNode.checked, true)
	          treeSelect.assignment(treeId, zTree.getCheckedNodes());
	      } else {
	    	  if(treeObj.setting.mod == 'sbflowner' || treeObj.setting.mod == 'calkid' || treeObj.setting.mod == 'calkidfln' ){
	    		  $("#parentid").val(treeNode.id);
	    	  }else if(treeObj.setting.mod == 'sydeptsb'){
		    	  _getCascDept(treeNode.id);
	    	  }else if(treeObj.setting.mod == 'deptzl'){
	    		  $("#tenanttype").val(treeNode.type);
	    		  if(treeNode.type=="2"){
		    		  $("#tenantcode").val(treeNode.code);
		    		  $("#tenantorgid").val(treeNode.id);

	    		  }else{
		    		  $("#tenantcode").val(treeNode.getParentNode().code);
		    		  $("#tenantorgid").val(treeNode.id);
	    		  }
	    	  }else if(treeObj.setting.mod == 'deptzl2'){
	    		  $("#rentalunittype").val(treeNode.type);
	    		  if(treeNode.type=="2"){
		    		  $("#rentalunitcode").val(treeNode.code);
		    		  $("#rentalunitorgid").val(treeNode.id);

	    		  }else{
		    		  $("#rentalunitcode").val(treeNode.getParentNode().code);
		    		  $("#rentalunitorgid").val(treeNode.id);
	    		  }
		    	  _getCascDept(treeNode.id);
	    	  }else if(treeObj.setting.mod == 'org'){
	    		  $("#parentcode").val(treeNode.code);
	    	  }else if(treeObj.setting.mod == 'deptarea'){
	    		  $("#orgcode").val(treeNode.code);
		    	  _getCascDept(treeNode.id);
	    	  }else if(treeObj.setting.mod == 'dept'){
	    		  $("#orgcode").val(treeNode.code);
		    	  _getCascDept(treeNode.id);
	    	  }else if(treeObj.setting.mod == 'xyyorg'){
				  $("#orgcode").val(treeNode.code);
				  $("#areaid").val(treeNode.areaid);
				  $("#areaname").val(treeNode.areaname);
			  }else if(treeObj.setting.mod == 'cascdept'){
	    		  $("#parentcode").val(treeNode.code);
	    	  }else if(treeObj.setting.mod == 'userdept'){
	    		  $("#deptcode").val(treeNode.code);
		    	  $("#orgname").val(treeNode.orgname);
		    	  $("#orgid").val(treeNode.orgid);
		    	  $("#orgcode").val(treeNode.orgcode);
	    	  }
	    	  treeSelect.assignment(treeId, zTree.getSelectedNodes());
	          treeSelect.hideMenu();
	      }
	}
  	,onCheck : function(event, treeId, treeNode) {
	      var zTree = $.fn.zTree.getZTreeObj(treeId);
	      treeSelect.assignment(treeId, zTree.getCheckedNodes());
	      
	      
	}
  	,onNodeCreatedCheck :function(event, treeId, treeNode) {
  		
	}
  	,hideMenu : function() {
  		  $(".select-tree").removeClass("layui-form-selected");
	      $(".tree-content").fadeOut("fast");
	      $("body").unbind("mousedown", treeSelect.onBodyDown);
	}
  	,assignment : function(treeId, nodes) {
		  var treeObj = $.fn.zTree.getZTreeObj(treeId);
	      var names = "";
	      var ids = "";
	      var resourceids = "";

	      for (var i = 0, l = nodes.length; i < l; i++) {
	    	  if(treeObj.setting.mod == 'roleMenurs'){
	    		  if( nodes[i].type=="resource" ){
		    		  resourceids += nodes[i].id + ",";
	    		  }
	    		  //names += nodes[i].name + ",";
		          ids += nodes[i].id + ",";
	    	  }else if(treeObj.setting.mod == 'sbfl'){
		          names += nodes[i].name + ",";
		          ids += nodes[i].code + ",";
	    	  }else if(treeObj.setting.mod == 'sbflowner' || treeObj.setting.mod == 'calkidfln' ){
	    		  var allname = "";
	    		  var nNode = "";
	    		  for(var j = 0;j < 10;j++){
	    			  if(nNode==""){
	    				  if(nodes[i].parentTId == null){
		    				  break; 
		    			  }
	    				  nNode = treeObj.getNodeByTId(nodes[i].parentTId);
	    			  }else{
	    				  var tId = nNode.parentTId;
	    				  if(tId == null){
		    				  break; 
		    			  }
	    				  nNode = treeObj.getNodeByTId(tId);
	    			  }
    				  allname =  nNode.name+"/"+allname
    				  if(nNode.pId == null){
	    				  break; 
	    			  }
	    		  }
		          names += allname + nodes[i].name + ",";
		          ids += nodes[i].code + ",";
	    	  }else if(treeObj.setting.mod == 'cascareasb'){
	    		  var allname = "";
	    		  var nNode = "";
	    		  for(var j = 0;j < 10;j++){
	    			  if(nNode==""){
	    				  if(nodes[i].parentTId == null){
		    				  break; 
		    			  }
	    				  nNode = treeObj.getNodeByTId(nodes[i].parentTId);
	    			  }else{
	    				  var tId = nNode.parentTId;
	    				  if(tId == null){
		    				  break; 
		    			  }
	    				  nNode = treeObj.getNodeByTId(tId);
	    			  }
    				  allname =  nNode.name+"/"+allname
    				  if(nNode.pId == null){
	    				  break; 
	    			  }
	    		  }
		          names += allname+nodes[i].name + ",";
		          ids += nodes[i].id + ",";
	    	  }else{
		          names += nodes[i].name + ",";
		          ids += nodes[i].id + ",";
	    	  }
	      }
	      if (names.length > 0) {
	          names = names.substring(0, names.length - 1);
	          ids = ids.substring(0, ids.length - 1);
	      }
	      treeId = treeId.substring(0, treeId.length - 4);
	      $("#" + treeId + "Show").attr("title", names);
	      $("#" + treeId + "Show").val(names.replace("顶级节点/",""));
	      $("#" + treeId + "Hide").val(ids);
	      
	      if(treeObj.setting.mod == 'roleMenurs'){
	    	  if (resourceids.length > 0) {
	    		  resourceids = resourceids.substring(0, resourceids.length - 1);
		      }
	    	  $("#resourceids").val(resourceids);
	      }
	      //$("#" + treeId + "Show").attr("value", names);
	      //$("#" + treeId + "Hide").attr("value", ids);
	}
  	,onBodyDown : function(event) {
  		// console.log($(event.target).html())
  		if ($(event.target).parents(".tree-content").html() == null && $(event.target).find(".ztree").html()==null) {
    	   treeSelect.hideMenu();
        }
	} 
  };
  exports('treeselect',treeSelect);
});