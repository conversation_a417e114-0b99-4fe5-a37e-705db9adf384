layui.define(['laypage', 'form'], function (exports) {
    "use strict";

    var IconPicker =function () {
        this.v = '1.1';
    }, _MOD = 'menuIconPicker',
        _this = this,
        $ = layui.jquery,
        laypage = layui.laypage,
        form = layui.form,
        BODY = 'body',
        TIPS = '请选择图标';

    IconPicker.prototype.render = function(options){
        var opts = options,
            // DOM选择器
            elem = opts.elem,
            // 数据类型：fontClass/unicode
            type = opts.type == null ? 'fontClass' : opts.type,
            // 是否分页：true/false
            page = opts.page == null ? true : opts.page,
            // 每页显示数量
            limit = opts.limit == null ? 12 : opts.limit,
            // 是否开启搜索：true/false
            search = opts.search == null ? true : opts.search,
            // 每个图标格子的宽度：'43px'或'20%'
            cellWidth = opts.cellWidth,
            // 点击回调
            click = opts.click,
            // 显示或隐藏
            showHide = opts.showHide,
            // 渲染成功后的回调
            success = opts.success,
            // json数据
            data = {},
            // 唯一标识
            tmp = new Date().getTime(),
            // 是否使用的class数据
            isFontClass = opts.type === 'fontClass',
            // 初始化时input的值
            ORIGINAL_ELEM_VALUE = $(elem).val(),
            TITLE = 'layui-select-title',
            TITLE_ID = 'layui-select-title-' + tmp,
            ICON_BODY = 'layui-iconpicker-' + tmp,
            PICKER_BODY = 'layui-iconpicker-body-' + tmp,
            PAGE_ID = 'layui-iconpicker-page-' + tmp,
            LIST_BOX = 'layui-iconpicker-list-box',
            selected = 'layui-form-selected',
            unselect = 'layui-unselect';

        var a = {
            init: function () {
                data = common.getData[type]();

                a.hideElem().createSelect().createBody().toggleSelect();
                a.preventEvent().inputListen();
                common.loadCss();
                
                if (success) {
                    success(this.successHandle());
                }

                return a;
            },
            successHandle: function(){
                var d = {
                    options: opts,
                    data: data,
                    id: tmp,
                    elem: $('#' + ICON_BODY)
                };
                return d;
            },
            /**
             * 隐藏elem
             */
            hideElem: function () {
                $(elem).hide();
                return a;
            },
            /**
             * 绘制select下拉选择框
             */
            createSelect: function () {
                var oriIcon = '<i class="pro-menu-icon">';
                
                // 默认图标
                if(ORIGINAL_ELEM_VALUE === '') {
                    if(isFontClass) {
                        ORIGINAL_ELEM_VALUE = 'pro-menu-icon-shouye2';
                    } else {
                        ORIGINAL_ELEM_VALUE = '&#xe9c5;';
                    }
                }

                if (isFontClass) {
                    oriIcon = '<i class="pro-menu-icon '+ ORIGINAL_ELEM_VALUE +'">';
                } else {
                    oriIcon += ORIGINAL_ELEM_VALUE; 
                }
                oriIcon += '</i>';

                var selectHtml = '<div class="layui-iconpicker layui-unselect layui-form-select" id="'+ ICON_BODY +'">' +
                    '<div class="'+ TITLE +'" id="'+ TITLE_ID +'">' +
                        '<div class="layui-iconpicker-item">'+
                            '<span class="layui-iconpicker-icon layui-unselect">' +
                                oriIcon +
                            '</span>'+
                            '<i class="layui-edge"></i>' +
                        '</div>'+
                    '</div>' +
                    '<div class="layui-anim layui-anim-upbit" style="">' +
                        '123' +
                    '</div>';
                $(elem).after(selectHtml);
                return a;
            },
            /**
             * 展开/折叠下拉框
             */
            toggleSelect: function () {
                var item = '#' + TITLE_ID + ' .layui-iconpicker-item,#' + TITLE_ID + ' .layui-iconpicker-item .layui-edge';
                a.event('click', item, function (e) {
                    var $icon = $('#' + ICON_BODY);
                    if ($icon.hasClass(selected)) {
                        $icon.removeClass(selected).addClass(unselect);
                        if(showHide){
                            showHide(false)
                        }
                    } else {
                        // 隐藏其他picker
                        $('.layui-form-select').removeClass(selected);
                        // 显示当前picker
                        $icon.addClass(selected).removeClass(unselect);
                        if(showHide){
                            showHide(true)
                        }
                    }
                    e.stopPropagation();
                });
                return a;
            },
            /**
             * 绘制主体部分
             */
            createBody: function () {
                // 获取数据
                var searchHtml = '';

                if (search) {
                    searchHtml = '<div class="layui-iconpicker-search">' +
                        '<input class="layui-input">' +
                        '<i class="layui-icon">&#xe615;</i>' +
                        '</div>';
                }

                // 组合dom
                var bodyHtml = '<div class="layui-iconpicker-body" id="'+ PICKER_BODY +'">' +
                    searchHtml +
                        '<div class="'+ LIST_BOX +'"></div> '+
                     '</div>';
                $('#' + ICON_BODY).find('.layui-anim').eq(0).html(bodyHtml);
                a.search().createList().check().page();

                return a;
            },
            /**
             * 绘制图标列表
             * @param text 模糊查询关键字
             * @returns {string}
             */
            createList: function (text) {
                var d = data,
                    l = d.length,
                    pageHtml = '',
                    listHtml = $('<div class="layui-iconpicker-list">')//'<div class="layui-iconpicker-list">';

                // 计算分页数据
                var _limit = limit, // 每页显示数量
                    _pages = l % _limit === 0 ? l / _limit : parseInt(l / _limit + 1), // 总计多少页
                    _id = PAGE_ID;

                // 图标列表
                var icons = [];

                for (var i = 0; i < l; i++) {
                    var obj = d[i];

                    // 判断是否模糊查询
                    if (text && obj.indexOf(text) === -1) {
                        continue;
                    }

                    // 是否自定义格子宽度
                    var style = '';
                    if (cellWidth != null) {
                        style += ' style="width:' + cellWidth + '"';
                    }

                    // 每个图标dom
                    var icon = '<div class="layui-iconpicker-icon-item" title="'+ obj +'" '+ style +'>';
                    if (isFontClass){
                        icon += '<i class="pro-menu-icon '+ obj +'"></i>';
                    } else {
                        icon += '<i class="pro-menu-icon">'+ obj.replace('amp;', '') +'</i>';
                    }
                    icon += '</div>';

                    icons.push(icon);
                }

                // 查询出图标后再分页
                l = icons.length;
                _pages = l % _limit === 0 ? l / _limit : parseInt(l / _limit + 1);
                for (var i = 0; i < _pages; i++) {
                    // 按limit分块
                    var lm = $('<div class="layui-iconpicker-icon-limit" id="layui-iconpicker-icon-limit-' + tmp + (i+1) +'">');

                    for (var j = i * _limit; j < (i+1) * _limit && j < l; j++) {
                        lm.append(icons[j]);
                    }

                    listHtml.append(lm);
                }

                // 无数据
                if (l === 0) {
                    listHtml.append('<p class="layui-iconpicker-tips">无数据</p>');
                }

                // 判断是否分页
                if (page){
                    $('#' + PICKER_BODY).addClass('layui-iconpicker-body-page');
                    pageHtml = '<div class="layui-iconpicker-page" id="'+ PAGE_ID +'">' +
                        '<div class="layui-iconpicker-page-count">' +
                        '<span id="'+ PAGE_ID +'-current">1</span>/' +
                        '<span id="'+ PAGE_ID +'-pages">'+ _pages +'</span>' +
                        ' (<span id="'+ PAGE_ID +'-length">'+ l +'</span>)' +
                        '</div>' +
                        '<div class="layui-iconpicker-page-operate">' +
                        '<i class="layui-icon" id="'+ PAGE_ID +'-prev" data-index="0" prev>&#xe603;</i> ' +
                        '<i class="layui-icon" id="'+ PAGE_ID +'-next" data-index="2" next>&#xe602;</i> ' +
                        '</div>' +
                        '</div>';
                }

                $('#' + ICON_BODY).find('.layui-anim').find('.' + LIST_BOX).html('').append(listHtml).append(pageHtml);
                return a;
            },
            preventEvent: function() {
                var item = '#' + ICON_BODY + ' .layui-anim';
                a.event('click', item, function (e) {
                    e.stopPropagation();
                });
                return a;
            },
            page: function () {
                var icon = '#' + PAGE_ID + ' .layui-iconpicker-page-operate .layui-icon';

                $(icon).unbind('click');
                a.event('click', icon, function (e) {
                   var elem = e.currentTarget,
                       total = parseInt($('#' +PAGE_ID + '-pages').html()),
                       isPrev = $(elem).attr('prev') !== undefined,
                       // 按钮上标的页码
                       index = parseInt($(elem).attr('data-index')),
                       $cur = $('#' +PAGE_ID + '-current'),
                       // 点击时正在显示的页码
                       current = parseInt($cur.html());

                    // 分页数据
                    if (isPrev && current > 1) {
                        current=current-1;
                        $(icon + '[prev]').attr('data-index', current);
                    } else if (!isPrev && current < total){
                        current=current+1;
                        $(icon + '[next]').attr('data-index', current);
                    }
                    $cur.html(current);

                    // 图标数据
                    $('#'+ ICON_BODY + ' .layui-iconpicker-icon-limit').hide();
                    $('#layui-iconpicker-icon-limit-' + tmp + current).show();
                    e.stopPropagation();
                });
                return a;
            },
            /**
             * 搜索
             */
            search: function () {
                var item = '#' + PICKER_BODY + ' .layui-iconpicker-search .layui-input';
                a.event('input propertychange', item, function (e) {
                    var elem = e.target,
                        t = $(elem).val();
                    a.createList(t);
                });
                return a;
            },
            /**
             * 点击选中图标
             */
            check: function () {
                var item = '#' + PICKER_BODY + ' .layui-iconpicker-icon-item';
                a.event('click', item, function (e) {
                    var el = $(e.currentTarget).find('.pro-menu-icon'),
                        icon = '';
                    if (isFontClass) {
                        var clsArr = el.attr('class').split(/[\s\n]/),
                            cls = clsArr[1],
                            icon = cls;
                        $('#' + TITLE_ID).find('.layui-iconpicker-item .pro-menu-icon').html('').attr('class', clsArr.join(' '));
                    } else {
                        var cls = el.html(),
                            icon = cls;
                        $('#' + TITLE_ID).find('.layui-iconpicker-item .pro-menu-icon').html(icon);
                    }

                    $('#' + ICON_BODY).removeClass(selected).addClass(unselect);
                    $(elem).val(icon).attr('value', icon);
                    // 回调
                    if (click) {
                        click({
                            icon: icon
                        });
                    }

                });
                return a;
            },
            // 监听原始input数值改变
            inputListen: function(){
                var el = $(elem);
                a.event('change', elem, function(){
                    var value = el.val();
                })
                // el.change(function(){
                    
                // });
                return a;
            },
            event: function (evt, el, fn) {
                $(BODY).on(evt, el, fn);
            }
        };

        var common = {
            /**
             * 加载样式表
             */
            loadCss: function () {
                var css = '.layui-iconpicker {max-width: 280px;}.layui-iconpicker .layui-anim{display:none;width:280px;position:absolute;left:0;top:42px;padding:5px 0;z-index:899;min-width:100%;border:1px solid #d2d2d2;max-height:300px;overflow-y:auto;background-color:#fff;border-radius:2px;box-shadow:0 2px 4px rgba(0,0,0,.12);box-sizing:border-box;}.layui-iconpicker-item{border:1px solid #e6e6e6;width:90px;height:38px;border-radius:4px;cursor:pointer;position:relative;}.layui-iconpicker-icon{border-right:1px solid #e6e6e6;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;width:60px;height:100%;float:left;text-align:center;background:#fff;transition:all .3s;}.layui-iconpicker-icon i{line-height:38px;font-size:18px;}.layui-iconpicker-item > .layui-edge{left:70px;}.layui-iconpicker-item:hover{border-color:#D2D2D2!important;}.layui-iconpicker-item:hover .layui-iconpicker-icon{border-color:#D2D2D2!important;}.layui-iconpicker.layui-form-selected .layui-anim{display:block;}.layui-iconpicker-body{padding:6px;}.layui-iconpicker .layui-iconpicker-list{background-color:#fff;border:1px solid #ccc;border-radius:4px;}.layui-iconpicker .layui-iconpicker-icon-item{display:inline-block;width:21.1%;line-height:36px;text-align:center;cursor:pointer;vertical-align:top;height:36px;margin:4px;border:1px solid #ddd;border-radius:2px;transition:300ms;}.layui-iconpicker .layui-iconpicker-icon-item i.layui-icon{font-size:17px;}.layui-iconpicker .layui-iconpicker-icon-item:hover{background-color:#eee;border-color:#ccc;-webkit-box-shadow:0 0 2px #aaa,0 0 2px #fff inset;-moz-box-shadow:0 0 2px #aaa,0 0 2px #fff inset;box-shadow:0 0 2px #aaa,0 0 2px #fff inset;text-shadow:0 0 1px #fff;}.layui-iconpicker-search{position:relative;margin:0 0 6px 0;border:1px solid #e6e6e6;border-radius:2px;transition:300ms;}.layui-iconpicker-search:hover{border-color:#D2D2D2!important;}.layui-iconpicker-search .layui-input{cursor:text;display:inline-block;width:86%;border:none;padding-right:0;margin-top:1px;}.layui-iconpicker-search .layui-icon{position:absolute;top:11px;right:4%;}.layui-iconpicker-tips{text-align:center;padding:8px 0;cursor:not-allowed;}.layui-iconpicker-page{margin-top:6px;margin-bottom:-6px;font-size:12px;padding:0 2px;}.layui-iconpicker-page-count{display:inline-block;}.layui-iconpicker-page-operate{display:inline-block;float:right;cursor:default;}.layui-iconpicker-page-operate .layui-icon{font-size:12px;cursor:pointer;}.layui-iconpicker-body-page .layui-iconpicker-icon-limit{display:none;}.layui-iconpicker-body-page .layui-iconpicker-icon-limit:first-child{display:block;}';
                var $style = $('head').find('style[iconpicker]');
                if ($style.length === 0) {
                    $('head').append('<style rel="stylesheet" iconpicker>'+css+'</style>');
                }
            },
            /**
             * 获取数据
             */
            getData: {
                fontClass: function () {
                    var arr = ["pro-menu-icon-yongyao","pro-menu-icon-dangan","pro-menu-icon-baogao","pro-menu-icon-zixun","pro-menu-icon-shijian","pro-menu-icon-yongfa","pro-menu-icon-tixing","pro-menu-icon-zhuyishixiang","pro-menu-icon-xiaoxi","pro-menu-icon-huanzhe","pro-menu-icon-gongzuotai","pro-menu-icon-xiaoquguanli","pro-menu-icon-zuzhijigou","pro-menu-icon-shujuzidian","pro-menu-icon-gongdanguanli","pro-menu-icon-gongzuotai1","pro-menu-icon-dingdanguanli","pro-menu-icon-shujutongji","pro-menu-icon-kehuguanli","pro-menu-icon-iconku-1","pro-menu-icon-huanzheyilan","pro-menu-icon-wenjuantiaocha","pro-menu-icon-quanyuanxuanjiao","pro-menu-icon-bingquxuanjiao","pro-menu-icon-xitongpeizhi","pro-menu-icon-chafangjilu","pro-menu-icon-hulidanyuanguanli","pro-menu-icon-caozuojilu","pro-menu-icon-yonghuguanli","pro-menu-icon-iconku-","pro-menu-icon-Tubedrug","pro-menu-icon-Pill","pro-menu-icon-DNAhelix","pro-menu-icon-Pillsdrugs","pro-menu-icon-Heartpulse","pro-menu-icon-Heartbroken","pro-menu-icon-Eyesight","pro-menu-icon-Ear","pro-menu-icon-Hospital","pro-menu-icon-Heart","pro-menu-icon-Hearts","pro-menu-icon-Patch","pro-menu-icon-Patchcross","pro-menu-icon-Ear1","pro-menu-icon-Heart1","pro-menu-icon-Heartpulse1","pro-menu-icon-DNAhelix1","pro-menu-icon-Eyesight1","pro-menu-icon-Hospital1","pro-menu-icon-Hearts1","pro-menu-icon-Heartbroken1","pro-menu-icon-Tubedrug1","pro-menu-icon-Pill1","pro-menu-icon-Pilldrug","pro-menu-icon-Pillsdrugs1","pro-menu-icon-Patch1","pro-menu-icon-Patchcross1","pro-menu-icon-Wheelchair","pro-menu-icon-sharpicons_nurse","pro-menu-icon-sharpicons_plaster-","pro-menu-icon-sharpicons_pulse-histogram","pro-menu-icon-sharpicons_ribbon","pro-menu-icon-sharpicons_sterthooscope","pro-menu-icon-sharpicons_pills","pro-menu-icon-sharpicons_cross-","pro-menu-icon-sharpicons_ambulance","pro-menu-icon-sharpicons_plaster","pro-menu-icon-sharpicons_blood-drop-","pro-menu-icon-sharpicons_blood-drop","pro-menu-icon-sharpicons_pulse","pro-menu-icon-sharpicons_cross","pro-menu-icon-sharpicons_patient-wheelchair","pro-menu-icon-sharpicons_cross-1","pro-menu-icon-sharpicons_doctor-bag","pro-menu-icon-sharpicons_nurse-hat","pro-menu-icon-sharpicons_drug","pro-menu-icon-sharpicons_temperature","pro-menu-icon-sharpicons_doctor-notes","pro-menu-icon-sharpicons_blood-drop-1","pro-menu-icon-sharpicons_syringe","pro-menu-icon-sharpicons_diagnose-heart","pro-menu-icon-sharpicons_heart","pro-menu-icon-sharpicons_pill","pro-menu-icon-sharpicons_heart-cross","pro-menu-icon-sharpicons_hospital-point","pro-menu-icon-sharpicons_medical-note","pro-menu-icon-sharpicons_home-care","pro-menu-icon-sharpicons_helicopter-passageway","pro-menu-icon-sharpicons_hospital-bed-","pro-menu-icon-sharpicons_gauze","pro-menu-icon-sharpicons_medicine-drug","pro-menu-icon-sharpicons_medical-sign","pro-menu-icon-sharpicons_hospital-bed","pro-menu-icon-huayanke","pro-menu-icon-yanke---","pro-menu-icon-mazuike--","pro-menu-icon-zhongyike","pro-menu-icon-quanke-","pro-menu-icon-neike-","pro-menu-icon-tubiao_-","pro-menu-icon-erbihou-","pro-menu-icon-binglike","pro-menu-icon-yingxiangke-","pro-menu-icon-chuanran--","pro-menu-icon-kangfuke","pro-menu-icon-qitake-","pro-menu-icon-tubiao_-1","pro-menu-icon-tubiao_-2","pro-menu-icon-tubiao_-3","pro-menu-icon-tubiao_-4","pro-menu-icon-tubiao_-5","pro-menu-icon-tubiao_-6","pro-menu-icon-tubiao_-7","pro-menu-icon-tubiao_-8","pro-menu-icon-tubiao_-9","pro-menu-icon-tubiao_-10","pro-menu-icon-tubiao_-11","pro-menu-icon-tubiao_-12","pro-menu-icon-tubiao_-13","pro-menu-icon-tubiao_-14","pro-menu-icon-tubiao_-15","pro-menu-icon-tubiao_-16","pro-menu-icon-tubiao_-17","pro-menu-icon-tubiao_-18","pro-menu-icon-tubiao_-19","pro-menu-icon-waike","pro-menu-icon-zhuyuan","pro-menu-icon-zhuyuan1","pro-menu-icon-zonghechaxun","pro-menu-icon-zhuyuan2","pro-menu-icon-menzhen","pro-menu-icon-zonghechaxun1","pro-menu-icon-patient","pro-menu-icon-dayin","pro-menu-icon-dayin1","pro-menu-icon-daochu1","pro-menu-icon-dayin2","pro-menu-icon-guanli3","pro-menu-icon-jichuguanli","pro-menu-icon-shouye2"];
                    return arr;
                },
                unicode: function () {
                    return [ "&amp;#xe62a","&amp;#xe62c","&amp;#xe62d","&amp;#xe62f","&amp;#xe630","&amp;#xe631","&amp;#xe632","&amp;#xe635","&amp;#xe64b","&amp;#xe6c2","&amp;#xe6c4","&amp;#xe6c7","&amp;#xe6cf","&amp;#xe6d0","&amp;#xe6d5","&amp;#xe6e4","&amp;#xe6e7","&amp;#xe6ef","&amp;#xe6f0","&amp;#xe612","&amp;#xe601","&amp;#xe602","&amp;#xe603","&amp;#xe605","&amp;#xe607","&amp;#xe608","&amp;#xe60b","&amp;#xe60c","&amp;#xe60d","&amp;#xe611","&amp;#xe68a","&amp;#xe689","&amp;#xe68b","&amp;#xe68c","&amp;#xe68d","&amp;#xe68e","&amp;#xe68f","&amp;#xe690","&amp;#xe691","&amp;#xe692","&amp;#xe693","&amp;#xe694","&amp;#xe695","&amp;#xe696","&amp;#xe697","&amp;#xe698","&amp;#xe699","&amp;#xe69a","&amp;#xe69b","&amp;#xe69c","&amp;#xe69d","&amp;#xe69e","&amp;#xe69f","&amp;#xe6a0","&amp;#xe6a1","&amp;#xe6a2","&amp;#xe6a3","&amp;#xe6a4","&amp;#xe80a","&amp;#xe80b","&amp;#xe80c","&amp;#xe80d","&amp;#xe80e","&amp;#xe80f","&amp;#xe810","&amp;#xe811","&amp;#xe812","&amp;#xe813","&amp;#xe814","&amp;#xe815","&amp;#xe816","&amp;#xe817","&amp;#xe818","&amp;#xe819","&amp;#xe81a","&amp;#xe81b","&amp;#xe81c","&amp;#xe81d","&amp;#xe81e","&amp;#xe81f","&amp;#xe820","&amp;#xe821","&amp;#xe822","&amp;#xe823","&amp;#xe824","&amp;#xe825","&amp;#xe826","&amp;#xe827","&amp;#xe828","&amp;#xe829","&amp;#xe82a","&amp;#xe82b","&amp;#xe82c","&amp;#xe6ca","&amp;#xe6cd","&amp;#xe6d1","&amp;#xe6d4","&amp;#xe6db","&amp;#xe6dc","&amp;#xe6eb","&amp;#xe6ee","&amp;#xe6f3","&amp;#xe6f6","&amp;#xe6f7","&amp;#xe6f8","&amp;#xe6f9","&amp;#xe6fa","&amp;#xe6fc","&amp;#xe6fe","&amp;#xe6ff","&amp;#xe700","&amp;#xe701","&amp;#xe702","&amp;#xe703","&amp;#xe704","&amp;#xe705","&amp;#xe706","&amp;#xe707","&amp;#xe708","&amp;#xe709","&amp;#xe70a","&amp;#xe70b","&amp;#xe70c","&amp;#xe710","&amp;#xe713","&amp;#xe716","&amp;#xe65e","&amp;#xe65f","&amp;#xe63f","&amp;#xe618","&amp;#xe6f5","&amp;#xe604","&amp;#xe628","&amp;#xe650","&amp;#xe621","&amp;#xe636","&amp;#xe60a","&amp;#xe6ad","&amp;#xe609","&amp;#xe9c5"];
                }
            }
        };

        a.init();
        return new IconPicker();
    };

    /**
     * 选中图标
     * @param filter lay-filter
     * @param iconName 图标名称，自动识别fontClass/unicode
     */
    IconPicker.prototype.checkIcon = function (filter, iconName){
        var el = $('*[lay-filter='+ filter +']'),
            p = el.next().find('.layui-iconpicker-item .pro-menu-icon'),
            c = iconName;

        if (c.indexOf('#xe') > 0){
            p.html(c);
        } else {
            p.html('').attr('class', 'pro-menu-icon ' + c);
        }
        el.attr('value', c).val(c);
    };

    var iconPicker = new IconPicker();
    exports(_MOD, iconPicker);
});