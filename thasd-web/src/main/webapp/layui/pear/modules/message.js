layui.define(['table', 'jquery', 'element'], function(exports) {
	"use strict";

	var MOD_NAME = 'message',
		$ = layui.jquery,
	 		table = layui.table,
			form = layui.form,
			element = layui.element;

	var message = function(opt) {
		this.option = opt;
	};

	message.prototype.render = function(opt) {
		//默认配置值
		var option = {
			elem: opt.elem,
			url: opt.url ? opt.url : false,
			height: opt.height,
			data: opt.data
		}
		if (option.url != false) {
			option.data = getData(option.url);
			var notice = createTable(option);
			console.log(option.elem)
			$(option.elem).html(notice);

			var noticeData = JSON.parse(option.data);
			$(".layui-nav-item.message").hover(function() {
				if($(".layui-nav-item.message .layui-nav-child.pear-notice").attr("request") == "0"){
					$(".layui-nav-item.message .layui-nav-child.pear-notice").attr("request","1");
					setTableData(option.url)
				}
			});

			$(".layui-nav-item.message .layui-icon-notice").mouseenter(function() {
				if( $(".layui-nav-item.message .layui-nav-child.pear-notice").attr("request") == "1" && $(".layui-nav-item.message .layui-nav-child.pear-notice").is(":hidden") ){
					table.reloadData("noticeResult",{
						where: {
							idcard: $("#idcard").val()
							,name: $("#pname").val()
							,gljyusername: $("#gljyusername").val()
						}
					});
				}
			});

			// element.on('nav(message)', function(elem){
			// 	console.log(elem)
			// });
		}
		return new message(option);
	}
	
	message.prototype.click = function(callback){
		$("*[notice-id]").click(function(event) {
			event.preventDefault();
			var id = $(this).attr("notice-id");
			var title = $(this).attr("notice-title");
			var context = $(this).attr("notice-context");
			var form = $(this).attr("notice-form");
			callback(id, title, context, form);
		})
	}
	

	/** 同 步 请 求 获 取 数 据 */
	function getData(url) {

		$.ajaxSettings.async = false;
		var data = null;

		$.get(url, function(result) {
			data = result;
		});

		$.ajaxSettings.async = true;
		return data;
	}

	function createHtml(option) {
		console.log(option)
		var noticeData = JSON.parse(option.data);
		console.log(noticeData,noticeData.length)
		option.height = "420px";
		var notice = '<li class="layui-nav-item" lay-unselect="">';

			if(noticeData.length > 0 && noticeData[0].children.length > 0){
				notice += '<a href="#" class="notice layui-icon layui-icon-notice"><span class="layui-badge-dot"></span></a>';
			}

			notice += '<div class="layui-nav-child pear-notice" style="left: -425px;">';

			var nHtml = '<div class="layui-card">';
			nHtml += '<div class="layui-card-body" style="height:' + option.height + ';overflow-x: hidden;">';
			nHtml += '<ul class="list">';
			// 根据 data 便利数据
			$.each(noticeData, function(i, item) {
				$.each(item.children, function(i, note) {
					nHtml += '<li class="list-item"><span class="title">'+note.name+'('+note.idcard+')患者于'+ note.executetime+'至'+ note.endtime +'应进行'+note.proname+'</span></li>';
				})
			})
			nHtml += '</ul>';
			nHtml += '</div>';
			nHtml += '</div>';
		notice += nHtml;
		notice += '</div></li>';

		return notice;

	}

	function createTable(option) {
		console.log(option)
		var noticeData = JSON.parse(option.data);
		console.log(noticeData)
		option.height = "490px";
		var notice = '<li class="layui-nav-item" lay-unselect="">';
		if(noticeData.data.length > 0  &&  noticeData.code == '0'  ){
			notice += '<a href="#" class="notice layui-icon layui-icon-notice"><span class="layui-badge-dot"></span></a>';
		}

		notice += '<div  class="layui-nav-child pear-notice" request="0" style="left: -942px;">';

		var nHtml = '<div  class="layui-card">' +
			'<div class="layui-card-header">' +
				'<div class="layui-form layui-col-xs8 layui-col-md8 " style="color: #28333E;">' +
					'<div class="layui-form-item">' +
					'    <div class="layui-inline">' +
					'        <label class="layui-form-label">患者姓名</label>' +
					'        <div class="layui-input-inline">' +
					'            <input type="text" name="pname" id="pname" placeholder="请输入" autocomplete="off" class="layui-input">' +
					'        </div>' +
					'    </div>' +
					'    <div class="layui-inline">' +
					'        <label class="layui-form-label">身份证号</label>' +
					'        <div class="layui-input-inline" style="margin-right: 0;width: 160px;">' +
					'            <input type="text" name="idcard" id="idcard" placeholder="请输入" autocomplete="off" class="layui-input">' +
					'        </div>' +
					'        <div class="layui-form-mid layui-word-aux" style="margin:0;padding: 0 0 0 0!important;position: absolute;margin-left: 192px;"><button class="layui-btn layui-btn-xs user-search" style="padding: 0 0 0 0!important;width:28px;height: 23px;line-height: 23px;margin-top: 2px;color:#ffffff;" id="readcard" data-method="readcard"><i class="layui-icon layui-icon-friends"></i></button></div>' +
					'    </div>' +

					'    <div class="layui-inline">' +
					'        <label class="layui-form-label">管理医生</label>' +
					'        <div class="layui-input-inline">' +
					'            <input type="text" name="gljyusername" id="gljyusername" placeholder="请输入" autocomplete="off" class="layui-input">' +
					'        </div>' +
					'    </div>' +

					'    <div class="layui-inline"><button class="layui-btn layui-btn-normal user-search" style="margin-top: 2px;padding: 0 0 0 0!important;width:43px;height: 26px;color:#ffffff;" id="reloadMessage" data-method="reloadMessage" >' +
					'        <i class="layui-icon layui-icon-search "></i>' +
					'    </button></div>' +
					'</div>' +
				'</div>'+
				'<div class="layui-col-xs4 layui-col-md4 " style="text-align: right;">'+
					'<button class="layui-btn layui-btn-normal downFwjh" id="downFwjh" style="color: #ffffff!important;" data-type="downFwjh"><i class="layui-icon layui-icon-export "></i>导出</button>'+
				'</div>'+
			'</div>';
				nHtml += '<div class="layui-card-body" style="height:' + option.height + ';">';
					nHtml += '<table id="noticeResult"  lay-filter="noticeResult">';
					nHtml += '</table>';
				nHtml += '</div>';
		nHtml += '</div>';

		notice += nHtml;

		notice += '</div></li>';

		return notice;

	}

	function setTableData(url){
		table.render({
			done: function(res, curr, count){
				$(".layui-laypage-limits").hide();
				setTimeout(function (){
					table.resize('noticeResult');
				},500)
			}
			,toolbar:  ''
			,defaultToolbar: []
			,elem: '#noticeResult'
			,id: 'noticeResult'
			,url:url
			,where: {
				idcard: $("#idcard").val()
				,name: $("#pname").val()
				,gljyusername: $("#gljyusername").val()
			}
			,cols: [[ //标题栏
				{align:'center',field: 'name', title: '患者姓名',width:110}
				,{align:'center',field: 'idcard', title: '身份证号',width:174}
				,{align:'center',field: 'lxdh', title: '联系方式',width:120}
				,{align:'center',title: '服务时间',templet: function(d){
					return  d.executetime + '至' + d.endtime;
				},width:213}
				,{align:'center',field: 'proname', title: '服务项目',width:140}
				,{align:'center',field: 'gljyusername', title: '当前管理医生',width:134}
				,{align:'center',field: 'gljyuser', title: '医生账号',width:110}
				,{align:'center', title: '操作',templet: function(d){
						return  '<button class="layui-btn layui-btn-xs" lay-event="addPlanSfjl"><i style="margin-right:0;color: #ffffff;" class="layui-icon layui-icon-add-circle"></i></button>';
				 },width:70}

			]]
			,even: true
			,page: true
			,height: 490
			,limit: 10
		});
	}

	exports(MOD_NAME, new message());
})