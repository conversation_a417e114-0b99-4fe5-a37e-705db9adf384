layui.use(['layer','laytpl','element','form','table','ztree','laydate','treeselectTable'], function(){//独立版的layer无需执行这一句
    var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
    var jQuery = layui.jquery;
    var element = layui.element;
    var table = layui.table;
    var laydate = layui.laydate;
    var laytpl = layui.laytpl;
    var orgname = "";
    Date.prototype.FormatNow = function (fmt) { //author: meizz
        var o = {
            "M+": this.getMonth(), //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    function convertDateFromString(dateString) {
        if (dateString) {
            var date = new Date(dateString.replace(/-/,"/"))
            return date;
        }
    }
    function  DateAdd(interval,number,date)
    {
        /*
         *---------------  DateAdd(interval,number,date)  -----------------
         *  DateAdd(interval,number,date) 
         *  功能:实现VBScript的DateAdd功能.
         *  参数:interval,字符串表达式，表示要添加的时间间隔.
         *  参数:number,数值表达式，表示要添加的时间间隔的个数.
         *  参数:date,时间对象.
         *  返回:新的时间对象.
         *  var  now  =  new  Date();
         *  var  newDate  =  DateAdd( "d ",5,now);
         *  author:wanghr100(灰豆宝宝.net)
         *  update:2004-5-28  11:46
         *---------------  DateAdd(interval,number,date)  -----------------
         */
        switch(interval)
        {
            case  "y "  :  {
                date.setFullYear(date.getFullYear()+number);
                return  date;
                break;
            }
            case  "q "  :  {
                date.setMonth(date.getMonth()+number*3);
                return  date;
                break;
            }
            case  "m "  :  {
                date.setMonth(date.getMonth()+number);
                return  date;
                break;
            }
            case  "w "  :  {
                date.setDate(date.getDate()+number*7);
                return  date;
                break;
            }
            case  "d "  :  {
                date.setDate(date.getDate()+number);
                return  date;
                break;
            }
            case  "h "  :  {
                date.setHours(date.getHours()+number);
                return  date;
                break;
            }
            case  "m "  :  {
                date.setMinutes(date.getMinutes()+number);
                return  date;
                break;
            }
            case  "s "  :  {
                date.setSeconds(date.getSeconds()+number);
                return  date;
                break;
            }
            default  :  {
                date.setDate(d.getDate()+number);
                return  date;
                break;
            }
        }
    }
    var dateNow = new Date();
    var maxDate = dateNow.FormatNow("yyyy");
    //年月范围
    /*
    var end;
    var start =  laydate.render({
        elem: '#sttime'
        ,type: 'date'
        ,done:function(value, date, endDate){
            if(value.length>0){
                end.config.min = {
                    year:date.year,
                    month:date.month-1,//关键
                    date:date.date,
                    hours:date.hours,
                    minutes:date.minutes,
                    seconds:date.seconds
                };
                $("#ettime").val("");
                $("#ettime").attr("disabled",null);
                $("#ettime").removeClass("layui-input-disabled");
                $("#ettime").removeClass("layui-disabled");
            }else{
                $("#ettime").val("");
                $("#ettime").attr("disabled","disabled");
                $("#ettime").addClass("layui-input-disabled");
                $("#ettime").addClass("layui-disabled");
            }
        }
    });
    end =  laydate.render({
        elem: '#ettime'
        ,type: 'date'
        ,done:function(value, date, endDate){
            if(value==""){
                if($("#sttime").val().length==0){
                    $("#ettime").attr("disabled","disabled");
                    $("#ettime").addClass("layui-input-disabled");
                    $("#ettime").addClass("layui-disabled");
                }
            }
        }
    });
    */
    var windowWidth = '100%';
    var windowHeight = '100%';
    var zFun =layui.treeselectTable;

    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }
    var ctxPath = getUParam("ctxPath","listjs");
    var glorgid = getUParam("glorgid","listjs");
    var glorgname = getUParam("glorgname","listjs");
    var countType = getUParam("countType","listjs");
    function reView(isp){
        if(isp){
            $(".layui-btn").hide()
            $("#chartBox").removeClass("ptable");
            setTimeout(function(){
                setTimeout(function(){
                    layer.closeAll("dialog");
                    $(".layui-layer-dialog").hide();
                    setTimeout(function(){
                        window.print();
                        reView(false);
                    },100);
                }, 500);
            }, 1000);
        }else{
            $(".layui-btn").show()
            $("#chartBox").addClass("ptable");
        }
    }

    function getUrl(){
        var url = "";
        url += ("countType="+countType);
        url += ("&glorgid="+glorgid);
        return url;
    }

    //触发事件
    var active = {
        reload: function(){
            var that = this;

            var url = getUrl();
            _getChart(url);
        },back: function(){
            window.history.go(-1);
        },print: function(){
            layer.msg("请稍等，正在准备数据...", {icon: 16,anim:-1,time: 0,shade: [1, '#FFFFFF']},function(){});
            reView(true);
        }
        ,exportd:function(){
            var orgname = glorgname;
            var title = '方案' +(countType == "0" ? "未" : "已")+ '评估人员名册';
            export_table_to_excel('chartBox',orgname + title,'fawpg');
        }
    };
    $('.layui-btn.user-search').on('click', function(){
        var othis = $(this), method = othis.data('method');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });

    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '数据请求中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }
    function _getChart(paramUrl){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+"/v/tjfx/countWpgry",paramUrl,function(jsondata){
            $(".initbox").remove();
            if(jsondata.code=='200'){
                console.log(JSON.stringify(jsondata));
                jsondata.rd["glorgname"] = glorgname;
                var getTpl = document.getElementById('chart-table').innerHTML
                    ,view = document.getElementById('chartBox');
                laytpl(getTpl).render(jsondata.rd, function(html){
                    view.innerHTML = html;
                });
                $
                $(".initbox").remove();
                layer.closeAll("dialog");
            }else{
                layer.msg(jsondata.msg,{time:2000},function(){
                    $(".initbox").remove();
                });
            }
        });
    }

    _getChart(getUrl());

    var vHieght = $(".listbox-body").height()-$(".search-box").height() - 50;

});