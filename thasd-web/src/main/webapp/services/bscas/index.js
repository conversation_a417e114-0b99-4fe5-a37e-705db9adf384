function desensitizeName(name) {
    if (typeof name !== 'string' || name === '') return '';
    const len = name.length;
    if (len === 1) return name;
    if (len === 2) return name.charAt(0) + '*'; // 两字姓名单独处理‌:ml-citation{ref="1,2" data="citationList"}
    const firstChar = name.charAt(0);
    const lastChar = name.slice(-1);
    const stars = '*'.repeat(len - 2); // 动态生成星号‌:ml-citation{ref="1,2" data="citationList"}
    return firstChar + stars + lastChar;
}
var dataZoom = [//给x轴设置滚动条
        {
            type: 'slider',//slider表示有滑动块的，inside表示内置的
            // startValue: 8,//可用于设置开始显示的柱子的长度
            // endValue: 1,//可用于设置结束显示的柱子的长度
            start: 0,//默认为0  可设置滚动条从在后进行展示
            end: 36,//默认为100
            show: true,
            xAxisIndex: [0],
            handleSize: 0,//滑动条的 左右2个滑动条的大小
            height: 12,//组件高度
            left: '1%', //左边的距离
            right: '1%',//右边的距离
            bottom: 2,//右边的距离
            borderColor: "rgba(8,235 ,227,.3)",
            fillerColor: 'rgba(8,235 ,227,.5)',
            backgroundColor: 'none',//两边未选中的滑动条区域的颜色
            showDataShadow: false,//是否显示数据阴影 默认auto
            showDetail: false,//即拖拽时候是否显示详细数值信息 默认true
            realtime: true, //是否实时更新
            filterMode: 'filter',
            handleStyle: {
                borderRadius: '20',
            },
        },
        //下面这个属性是里面拖到
        {
            type: 'inside',
            show: true,
            xAxisIndex: [0],
            start: 0,//默认为1
            end: 100,//默认为100
            moveOnMouseWheel: false,
            preventDefaultMouseMove: false,
        },
];
var dataZoom1 = [//给x轴设置滚动条
    {
        type: 'slider',//slider表示有滑动块的，inside表示内置的
        // startValue: 8,//可用于设置开始显示的柱子的长度
        // endValue: 1,//可用于设置结束显示的柱子的长度
        start: 0,//默认为0  可设置滚动条从在后进行展示
        end: 36,//默认为100
        show: true,
        xAxisIndex: [0],
        handleSize: 0,//滑动条的 左右2个滑动条的大小
        height: 12,//组件高度
        left: '1%', //左边的距离
        right: '1%',//右边的距离
        bottom: 2,//右边的距离
        borderColor: "rgba(8,235 ,227,.3)",
        fillerColor: 'rgba(8,235 ,227,.5)',
        backgroundColor: 'none',//两边未选中的滑动条区域的颜色
        showDataShadow: false,//是否显示数据阴影 默认auto
        showDetail: false,//即拖拽时候是否显示详细数值信息 默认true
        realtime: true, //是否实时更新
        filterMode: 'filter',
        handleStyle: {
            borderRadius: '20',
        },
    },
    //下面这个属性是里面拖到
    {
        type: 'inside',
        show: true,
        xAxisIndex: [0],
        start: 0,//默认为1
        end: 100,//默认为100
        moveOnMouseWheel: false,
        preventDefaultMouseMove: false,
    },
];
// 表格滚动器
function TableScroller(tableContainer, interval) {
    // 响应鼠标事件
    let that = this;
    tableContainer.on('mouseover', function () {
        that.pause();
    });
    tableContainer.on('mouseleave', function () {
        that.resume();
    });

    // 隐藏表格滚动条
    let bodyContainer = tableContainer.find('.layui-table-body');
    bodyContainer.css('overflow-x', 'hidden');
    bodyContainer.css('overflow-y', 'hidden');

    this.timerID = null;
    this.interval = interval;
    this._bodyTable = bodyContainer.find('table');
    this._tbody = this._bodyTable.find('tbody');

    this.start = function () {
        let that = this;
        that.timerID = setInterval(function () {
            that._scroll(that._bodyTable, that._tbody, that.interval);
        }, that.interval);
    };
    this.pause = function () {
        let that = this;
        if (that.timerID === null) {
            return;
        }

        clearInterval(that.timerID);
        that.timerID = null;
    };
    this.resume = function () {
        let that = this;
        if (that.timerID !== null || that.callback === null || that.interval === null) {
            return;
        }

        that.timerID = setInterval(function () {
            that._scroll(that._bodyTable, that._tbody, that.interval);
        }, that.interval);
    };
    this.stop = function () {
        let that = this;
        if (this.timerID === null) {
            return;
        }

        clearInterval(that.timerID);
        that.callback = null;
        that.interval = null;
        that.timerID = null;
    };
    this._scroll = function (bodyTable, tbody, interval) {
        let firstRow = tbody.find('tr:first');
        let rowHeight = firstRow.height();
        bodyTable.animate({top: '-' + rowHeight + 'px'}, interval * 0.5, function () {
            tbody.append(firstRow.prop("outerHTML"));
            bodyTable.css('top', '0px');
            firstRow.remove();
        });
    }
};
layui.use(['layer', 'loading', 'echarts', 'element', 'count', 'laytpl', 'form', 'table'], function () {
    var $ = layui.jquery, layer = layui.layer;
    var laytpl = layui.laytpl;
    var count = layui.count,
        table = layui.table,
        echarts = layui.echarts;
    var loading = layui.loading;

    loading.block({
        type: 3,
        elem: '.loading-chart',
        msg: ''
    })

    function getUParam(name, id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = decodeURIComponent($("#" + id).attr("src").substr($("#" + id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]);
        return "";
    }

    var ctxPath = getUParam("ctxPath", "indexjs");
    var orgid = getUParam("orgid", "indexjs");
    var orgcode = getUParam("orgcode", "indexjs");
    var orgname = getUParam("orgname", "indexjs");

    function dfzHzList() {
        var queryCountUrl = ctxPath + "/bscas/dfzHzList";
        var cols = [[
            {align:'center',field:'name',  title: '患者姓名',width: 100,templet: function(d){
                    // return d.executetime + " 至 " + d.endtime;
                    return desensitizeName(d.name);
            }}
            ,{field: 'proname', title: '服务项目', width:137}
            ,{field: 'executetime', title: '截止时间', width: 100,templet: function(d){
                    // return d.executetime + " 至 " + d.endtime;
                    return d.endtime;
                }}
            ,{align:'center',field: 'gljyusername', title: '管理医生',width:100}
        ]];
        table.render({
            done: function(res, curr, count){
                setTimeout(function (){
                    loading.blockRemove(".loading-chart-dfz", 0);
                    if (res.data.length > 6) {
                        let scroller = new TableScroller($(".loading-chart-dfz"), 2500);
                        scroller.start();
                    }

                },0);
            }
            ,toolbar:  ''
            ,defaultToolbar: []
            ,loading:false
            ,even:true
            ,elem: '#dfzTable'
            ,id: 'dfzTable'
            ,url: queryCountUrl
            ,where: {
                orgid: orgid
                ,grade: 2
            }
            ,cols: cols
            ,page: false
            ,height: dfzHeight
            ,cellMinWidth:100
            ,limit:1000000
        });
    }
    function fwjlList() {
        var queryCountUrl = ctxPath + "/bscas/fwjlList";
        var cols = [[
            {align:'center',field:'name',  title: '患者姓名',width: 100,templet: function(d){
                    // return d.executetime + " 至 " + d.endtime;
                    return desensitizeName(d.name);
            }}
            ,{align:'left',field:'proname',title: '服务项目',width:142}
            ,{field:'fwsj', width:100, title: '服务时间'}
            ,{field:'createname',  title: '服务医生',width: 100}
        ]];
        table.render({
            done: function(res, curr, count){
                setTimeout(function (){
                    loading.blockRemove(".loading-chart-fwjl", 0);
                    if (res.data.length > 6) {
                        let scroller = new TableScroller($(".loading-chart-fwjl"), 3000);
                        scroller.start();
                    }
                },0);
            }
            ,toolbar:  ''
            ,defaultToolbar: []
            ,loading:false
            ,even:true
            ,elem: '#fwjlTable'
            ,id: 'fwjlTable'
            ,url: queryCountUrl
            ,where: {
                orgid: orgid
                ,grade: 2
            }
            ,cols: cols
            ,page: false
            ,height: fwjlHeight
            ,cellMinWidth:100
            ,limit:1000000
        });
    }
    function getPatientsCount() {
        var queryCountUrl = ctxPath + "/bscas/countMainPatients";
        var queryCountParams = "";
        $.getJSON(queryCountUrl, "orgid=" + orgid + "&grade=2", function (jsondata) {

            if (jsondata.code == '200') {

                count.up("yglrs", {
                    time: 4000,
                    num: jsondata.rd.yglrs,
                    regulator: 100
                });

                count.up("djrs", {
                    time: 4000,
                    num: jsondata.rd.djrs,
                    regulator: 100
                });

                count.up("sfrs", {
                    time: 4000,
                    num: jsondata.rd.sfrs,
                    regulator: 100
                });

                count.up("gxy", {
                    time: 4000,
                    num: jsondata.rd.gxyrs,
                    regulator: 100
                });

                count.up("tnb", {
                    time: 4000,
                    num: jsondata.rd.tnbrs,
                    regulator: 100
                });

                count.up("gxz", {
                    time: 4000,
                    num: jsondata.rd.gxzrs,
                    regulator: 100
                });

                count.up("ypg", {
                    time: 4000,
                    num: jsondata.rd.ypgrs || 0,
                    regulator: 100
                });

                count.up("bfz", {
                    time: 4000,
                    num: jsondata.rd.bfzrs  || 0,
                    regulator: 100
                });

                count.up("dfzCount", {
                    time: 4000,
                    num: jsondata.rd.dfzCount,
                    regulator: 100
                });
                count.up("fwjlCount", {
                    time: 4000,
                    num: jsondata.rd.fwjlCount,
                    regulator: 100
                });

                echarts_1(jsondata.rd);
                initChart1(jsondata.rd);
                initChartPieSg(jsondata.rd);
                initChart2(jsondata.rd);
                initChartPieSgGroup(jsondata.rd);

                resizeFun();

            } else {

            }
        });
    }

    var column1 = echarts.init(document.getElementById('echarts-fb'));
    var myChart = echarts.init(document.getElementById('echarts-xxgjb'));
    var columnBfz = echarts.init(document.getElementById('echarts-bfz'));
    var columnPieSg = echarts.init(document.getElementById('echarts-pieSg'));
    var columnPieSgGroup = echarts.init(document.getElementById('echarts-pieSgGroup'));

    function echarts_1(dataItem) {

        let option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {type: 'shadow'},
            }
            ,grid: {
                "top": "25px",
                "right": "5",
                "bottom": "25",
                "left": "50",
            }
            ,legend: {
                data: ['易患', '低危', '中危', '高危'],
                align: 'left',
                top:4,
                center: 0,
                textStyle: {
                    color: "#ffffff"
                },
                itemWidth: 12,
                itemHeight: 10,
            }
            ,xAxis: [
                {
                    "type": "category",
                    data: ['高血压 ', '糖尿病', '高血脂'],
                    axisLine: {lineStyle: {color: "rgba(8,235,227,0.17)"}},
                    axisLabel: {
                        textStyle: {color: "rgba(255,255,255,0.87)", fontSize: '13',},
                    }
                }
            ]
            ,yAxis: [
                {
                    "type": "value",
                    "name": "",//单位(人)
                    nameTextStyle:{color: "rgba(255,255,255,0.87)",padding:[0,0,0,50] },
                    axisTick: {show: false},
                    axisLabel: {
                        show: true,
                        fontSize: 14,
                        color: "rgba(255,255,255,0.87)"
                    },
                    axisLine: {
                        min: 0,
                        max: 10,
                        lineStyle: {color: 'rgba(8,235,227,0.17)'}
                    }
                    ,splitLine: {show:true,lineStyle: {color:'rgba(8,235,227,0.17)'}}//x轴线
                }
                ,{
                    "type": "value",
                    "name": "",
                    "show": true,
                    "axisLabel": {
                        "show": false,
                    },
                    axisTick: {show: false},
                    axisLine: {lineStyle: {color: 'rgba(8,235,227,0.17)'}},//右线色
                    splitLine: {show:true,lineStyle: {color:'rgba(8,235,227,0.17)'}},//x轴线
                }
            ]
            ,series: [
                {
                    "name": "易患",
                    "type": "bar",
                    "data": dataItem.yhFcList,
                    "barWidth": "10%",
                    "itemStyle": {
                        "normal": {
                            barBorderRadius: 10,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#8bd46e'//
                            }, {
                                offset: 1,
                                color: '#09bcb7'//
                            }]),
                        }
                    },
                    "barGap": "0.2"
                }
                ,{
                    "name": "低危",
                    "type": "bar",
                    "data": dataItem.dwFcList,
                    "barWidth": "10%",
                    "itemStyle": {
                        "normal": {
                            barBorderRadius: 10,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#d4d26e'
                            }, {
                                offset: 1,
                                color: '#bcb909'
                            }]),
                        }
                    },
                    "barGap": "0.2"
                }
                ,{
                    "name": "中危",
                    "type": "bar",
                    "data": dataItem.zwFcList,
                    "barWidth": "10%",
                    "itemStyle": {
                        "normal": {
                            barBorderRadius: 10,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#d4956e'
                            }, {
                                offset: 1,
                                color: '#bc4e09'
                            }]),
                        }
                    },
                    "barGap": "0.2"
                }
                ,{
                    "name": "高危",
                    "type": "bar",
                    "data": dataItem.gwFcList,
                    "barWidth": "10%",
                    "itemStyle": {
                        "normal": {
                            barBorderRadius: 10,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: '#f12525'
                            }, {
                                offset: 1,
                                color: '#a6182d'
                            }]),
                        }
                    },
                    "barGap": "0.2"
                }

            ]
        };

        myChart.setOption(option);
        loading.blockRemove(".loading-chart-xxgjb", 0);
    }
    function initChart1(result){
        var glen = result.groupLabel.length;
        let option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: ['高血压', '糖尿病', '高血脂'],
                align: 'left',
                top:10,
                right: 0,
                textStyle: {
                    color: "#FFFFFF",
                    fontSize: 12,
                    fontWeight: 400
                },
                itemWidth: 12,
                itemHeight: 12,
                itemGap: 26
            },
            grid: {
                left: 15,
                right: 15,
                bottom: -20,
                top: 40,
                containLabel: true
            },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                offset: glen,
                data: result.groupLabel,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    textStyle: {
                        color: "#ffffff",
                        fontSize: 11,
                        fontWeight: 400
                    },
                    show: true,
                    interval: 0,
                    margin:0,
                    formatter: function (params) {
                        if(params.indexOf("卫生院")>=0){
                        }else{
                            params = params.replace("满庄镇","").replace("满庄","").replace("社区卫生服务中心","").replace("卫生室","").replace("卫生服务站","");
                        }
                        if (params.length > 10) {
                            return `${params.slice(0, 4)}...`.split("").join("\n");
                        }
                        //console.log(params.split("").join("\n"))
                        return params.split("").join("\n") + "\n\n\n\n\n\n";
                    },
                }
            }],
            yAxis: [
                    {
                        type: 'value',
                        axisTick: {show: false},
                        splitNumber: 4,
                        axisLine: {
                            show: true,
                            lineStyle: {
                            color: 'rgba(255,255,255,.1)'
                            }
                        },
                        axisLabel:  {
                            show: false,
                            textStyle: {
                            color: "rgba(255,255,255,1)",
                            //fontSize:10
                            },
                        },
                        splitLine: {
                            lineStyle: {
                            color: 'rgba(255,255,255,.1)',
                            type: 'dotted',
                            }
                        }
                    }
            ],
            series: [{
                name: '高血压',
                type: 'line',
                stack: 'Total',
                smooth: true,
                symbol: 'circle',
                symbolSize: 5,
                lineStyle: {
                    width: 0
                },
                showSymbol: false,
                data: result.groupBarGxy.map(item => (item === 0 ? 0 : item)) ,
                areaStyle: {
                    opacity: 0.4,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(243,37,146)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(245,36,102)'
                        }
                    ])
                },
                lineStyle: {
                    normal: {
                        color: 'rgba(245,36,102, 1)',
                        width: 2
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(255,0,81,1)',
                        borderColor: 'rgba(255,0,81, .1)',
                        borderWidth: 5
                    }
                },
                emphasis: {
                    focus: 'series'
                },
            }, {
                name: '糖尿病',
                type: 'line',
                stack: 'Total',
                smooth: true,
                symbol: 'circle',
                symbolSize: 5,
                lineStyle: {
                    width: 0
                },
                showSymbol: false,
                data: result.groupBarTnb.map(item => (item === 0 ? 0 : item)) ,
                areaStyle: {
                    opacity: 0.4,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(0, 221, 255)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(8,235,227)'
                        }
                    ])
                },
                lineStyle: {
                    normal: {
                        color: 'rgba(8,235,227, 1)',
                        width: 2
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(8,235,227,1)',
                        borderColor: 'rgba(8,235,227, .1)',
                        borderWidth: 5
                    }
                },
                emphasis: {
                    focus: 'series'
                },
            }, {
                name: '高血脂',
                type: 'line',
                stack: 'Total',
                smooth: true,
                symbol: 'circle',
                symbolSize: 5,
                lineStyle: {
                    width: 0
                },
                showSymbol: false,
                data: result.groupBarGxz.map(item => (item === 0 ? 0 : item)) ,
                areaStyle: {
                    opacity: 0.4,
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(255, 191, 0)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(182,137,2)'
                        }
                    ])
                },
                lineStyle: {
                    normal: {
                        color: 'rgba(255, 191, 0, 1)',
                        width: 2
                    }
                },
                itemStyle: {
                    normal: {
                        color: 'rgba(255, 191, 0)',
                        borderColor: 'rgba(255, 191, 0, .1)',
                        borderWidth: 5
                    }
                },
                emphasis: {
                    focus: 'series'
                },
            }]
        };

        column1.setOption(option);
        loading.blockRemove(".loading-chart-fb", 0);
    }
    function initChartPieSg(result){
        var colorList = ['#FE5050','#FBFE27','rgb(11,228,96)'];
        option = {
            color: colorList,
            legend: {
                top: 5,
                left: 'center',
                show:false,
                textStyle: {
                    color: "#FFFFFF",
                    fontSize: 12,
                    fontWeight: 400
                },
            },
            series: [
                {
                    type: 'pie',
                    radius: ['50%', '70%'],
                    center: ['50%', '58%'],
                    roseType: 'radius',
                    label: {
                        formatter: ['{c|{c}人}', '{b|{b}}'].join('\n'),
                        rich: {
                            c: {
                                color: 'rgb(8,235,227)',
                                fontSize: 20,
                                fontWeight:'bold',
                                lineHeight: 5
                            },
                            b: {
                                color: 'rgb(255,255,255)',
                                fontSize: 14,
                                height: 44
                            },
                        },
                    },
                    labelLine: {
                        normal: {
                            lineStyle: {
                                color: 'rgb(255,255,255)',
                            },
                            smooth: 0.2,
                            length: 10,
                            length2: 20,
                        }
                    },
                    data: result.pieSgPatients
                }
            ]
        };
        columnPieSg.setOption(option);
        loading.blockRemove(".loading-chart-pieSg", 0);
    }
    function initChartPieSgGroup(result){
        var colorList = ['#FE5050','#FBFE27','rgb(11,228,96)'];
        option = {
            color: colorList,
            legend: {
                top: 5,
                left: 'center',
                show:false,
                textStyle: {
                    color: "#FFFFFF",
                    fontSize: 12,
                    fontWeight: 400
                },
            },
            series: [
                {
                    type: 'pie',
                    radius: ['50%', '70%'],
                    center: ['50%', '58%'],
                    roseType: 'radius',
                    label: {
                        formatter: ['{c|{c}人}', '{b|{b}}'].join('\n'),
                        rich: {
                            c: {
                                color: 'rgb(8,235,227)',
                                fontSize: 20,
                                fontWeight:'bold',
                                lineHeight: 5
                            },
                            b: {
                                color: 'rgb(255,255,255)',
                                fontSize: 14,
                                height: 44
                            },
                        },
                    },
                    labelLine: {
                        normal: {
                            lineStyle: {
                                color: 'rgb(255,255,255)',
                            },
                            smooth: 0.2,
                            length: 10,
                            length2: 20,
                        }
                    },
                    data: result.lPie
                }
            ]
        };
        columnPieSgGroup.setOption(option);
        loading.blockRemove(".loading-chart-pieSgGroup", 0);
    }
    function initChart2(result){
        let option = {
            grid: {
                left: '10px',
                top:'0',
                right: '10px',
                bottom: '0',
                containLabel: true
            },
            xAxis: [{
                show: false,
            }],
            yAxis: [{
                axisTick:'none',
                axisLine:'none',
                offset:'6',
                axisLabel: {
                    textStyle: {
                        color: 'rgba(255,255,255,1)',
                        fontSize:'14',
                    }
                },
                data:result.bfzGroupLabel

            }, {
                axisTick:'none',
                axisLine:'none',
                axisLabel: {
                    textStyle: {
                        color: 'rgba(255,255,255,1)',
                        fontSize:'14',
                    }
                },
                data: result.bfzValues

            }],
            series: [{
                name: '',
                type: 'bar',
                yAxisIndex: 0,
                data: result.bfzValues,
                label:{
                    normal:{
                        show:false,
                        position:'right',
                        formatter:function(param){
                            return parseFloat((param.value / result.bfzrs) * 100).toFixed(1)   + '%';
                        },
                        textStyle:{
                            color: 'rgba(255,255,255,1)',
                            fontSize:'12',
                        }
                    }
                },
                barWidth: 15,
                itemStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [{
                            offset: 0,
                            color: '#03c893'
                        },
                            {
                                offset: 1,
                                color: '#0091ff'
                            }
                        ]),
                        barBorderRadius: 15,
                    }
                },
                z: 2
            }
            // , {
            //     name: '白框',
            //     type: 'bar',
            //     yAxisIndex: 1,
            //     barGap: '-100%',
            //     data: [100,100,100,100,100,100],
            //     barWidth: 15,
            //     itemStyle: {
            //         normal: {
            //             color:'rgba(255,255,255,.2)',
            //             barBorderRadius:15,
            //         }
            //     },
            //     z: 1
            // }
            ]
        };
        // 使用刚指定的配置项和数据显示图表。
        columnBfz.setOption(option);
        loading.blockRemove(".loading-chart-bfz", 0);

    }

    function resizeFun(){
        window.addEventListener("resize", function () {
            column1.resize();
            myChart.resize();
            columnBfz.resize();
            columnPieSg.resize();
            columnPieSgGroup.resize();
        });
    }

    var refSys = setTimeout(refFun, 0);
    function refFun (){
        clearTimeout(refSys);//清除定时器
        getPatientsCount();
        dfzHzList();
        fwjlList();
        refSys = setTimeout(refFun, 1000*60*60);//1000*60*60 1小时刷新一次
    }

});

window.onload = function () {
    var t = null;
    t = setTimeout(time, 100);

    function time() {
        clearTimeout(t);//清除定时器
        dt = new Date();
        var y = dt.getFullYear();
        var mt = dt.getMonth() + 1;
        var day = dt.getDate();
        var h = dt.getHours();//获取时
        var m = dt.getMinutes();//获取分
        var s = dt.getSeconds();//获取秒
        var t = null;
        document.getElementById("showTime").innerHTML = y + "年" + Appendzero(mt) + "月" + Appendzero(day) + "日 " + Appendzero(h) + ":" + Appendzero(m) + ":" + Appendzero(s) + "";

        function Appendzero(obj) {
            if (obj < 10) return "0" + "" + obj;
            else return obj;
        }

        t = setTimeout(time, 1000); //设定定时器，循环运行
    }
}