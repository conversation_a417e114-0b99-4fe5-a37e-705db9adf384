function loadHisYyTable(tabId,tabContentId){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","consolejs");
        var cols = [[
            {unresize:true,type:'numbers',align:'center',width:60,title:'序号'}
            ,{field: 'yytype', title: '用药类型',width: 86}
            ,{field: 'ywmc', title: '药品名称'}
            ,{field: 'yypl',title: '频率',width: 94}
            ,{field: 'yyjl',title: '用量'}
            ,{field: 'yyff', title: '用发'}
            ,{field: 'orgname', title: '指导单位'}
            ,{field: 'createname', title: '指导医生',width: 87}

        ]];
        var whereQurl = {patientid: window.parent.patientid,ishis:1};
        var queryUrl = ctxPath+'/v/plan/yyzdList';

        var elemId =  "#"+tabContentId;
        var tableId = tabContentId+'Tbl';
        if(tabId == "yyzd"){
            whereQurl = {patientid: window.parent.patientid};
            elemId =  "#yyzdResult .listtable";
            tableId = 'yyzdResult';
            cols =  [[ //标题栏
                {fixed:'left', field: 'yytype', title: '用药类型'}
                ,{fixed:'left',field: 'ywmc', title: '药品名称'}
                ,{field: 'yypl', title: '频率'}
                ,{field: 'yyjl', title: '用量'}
                ,{fixed:'right', title: '用法',field: 'yyff'}]];
        }

        var thisTable = table.render({
            done: function(res, curr, count){
                setTimeout(function (){
                },500)
            },
             toolbar:  ''
            ,defaultToolbar: []
            ,elem: elemId
            ,id: tableId
            ,url: queryUrl
            ,where: whereQurl
            ,cols: cols
            ,page: false
            ,height:400
            ,limit:100000000000
        });
    });
}

function loadFzTable(tabId,tabContentId){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","consolejs");
        // var queryParams = "patientid="+window.parent.patient.id;
        var queryParams = "idcard="+window.parent.patient.idcard;
        var cols = [[
            {unresize:true,type:'numbers',title: '序号', width: 50}
            ,{field: 'proname', title: '服务项目'}
            ,{field: 'executetime', title: '有效时间', width: 196,templet: function(d){
                    return d.executetime + " 至 " + d.endtime;
                }}
            ,{field: 'prorate', title: '服务频率', width: 87}
            ,{align:'center',field: 'gljyusername', title: '当前管理医生',width:134}
            ,{align:'center', title: '操作',templet: function(d){
                    return  '<button class="layui-btn layui-btn-xs" lay-event="addPlanSfjl"><i style="margin-right:0;color: #ffffff;" class="layui-icon layui-icon-add-circle"></i></button>';
                },width:70}

        ]];
        var whereQurl = {idcard: window.parent.patient.idcard};
        var queryUrl = ctxPath+"/v/patients/getNotice";
        if(tabId == "fz"){
            queryUrl = ctxPath+"/v/plan/fzjhDetailList";
            whereQurl = {planid:window.parent.patient.planid};
            cols = [[
                {unresize:true,type:'numbers',title: '序号', width: 50}
                ,{field: 'proname', title: '服务项目'}
                ,{field: 'executetime', title: '有效时间', width: 196,templet: function(d){
                        return d.executetime + " 至 " + d.endtime;
                    }}
                ,{field: 'prorate', title: '服务频率', width: 87}
            ]]
        }
        var thisTable = table.render({
            done: function(res, curr, count){
                setTimeout(function (){
                },500)
            },
            toolbar:  ''
            ,defaultToolbar: []
            ,elem: "#"+tabContentId
            ,id: tabContentId+'Tbl'
            ,url: queryUrl
            ,where: whereQurl
            ,cols: cols
            ,page: false
            ,height:400
            ,limit:100000000000
        });
    });
}

layui.use(['layer', 'loading', 'echarts', 'element', 'count','laytpl','table'], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        element = layui.element,
        count = layui.count,
        laytpl = layui.laytpl,
        echarts = layui.echarts;
    var loading = layui.loading;
    var table = layui.table;
    var ctxPath = layui.getContextPath("ctxPath","consolejs");

    $(".hzlx").text(window.parent.hzlx)
    $(".pmtype").text(window.parent.patient.pmtype)
    $(".fzInfo").text(window.parent.fzInfo)

    laytpl(document.getElementById("bpgrade").innerHTML).render(window.parent.patient, function(html){
        $(".tpstatus-li.gxy .tpstatus-color")[0].innerHTML = html;
    });

    laytpl(document.getElementById("dpgrade").innerHTML).render(window.parent.patient, function(html){
        $(".tpstatus-li.tnb .tpstatus-color")[0].innerHTML = html;
    });

    laytpl(document.getElementById("lpgrade").innerHTML).render(window.parent.patient, function(html){
        $(".tpstatus-li.gxz .tpstatus-color")[0].innerHTML = html;
    });

    laytpl(document.getElementById("ascvd").innerHTML).render(window.parent.patient, function(html){
        $(".tpstatus-li.ascvd .tpstatus-color")[0].innerHTML = html;
    });

    laytpl(document.getElementById("pgsj").innerHTML).render(window.parent.patient, function(html){
        $(".tpstatus-li.pgsj .tpstatus-color")[0].innerHTML = html;
    });

    loading.block({
        type: 3,
        elem: '.loading-chart1',
        msg: ''
    })
    loading.block({
        type: 3,
        elem: '.loading-chart2',
        msg: ''
    })
    loading.block({
        type: 3,
        elem: '#tab-card-jkda',
        msg: ''
    });

    element.on('tab(fzjhTab)', function(data){
        // console.log(data);
        // console.log(this); //当前Tab标题所在的原始DOM元素
        // console.log(data.index); //得到当前Tab的所在下标
        // console.log(data.elem); //得到当前的Tab大容器
        var tabId = $(".fzjhTab li.layui-this").attr("val");
        var loadStetup = $(".fzjhTab li.layui-this").attr("load-stetup");
        var tabContentId = tabId + "Table";
        if(loadStetup == "0"){
            loadFzTable(tabId,tabContentId);
        }
    });
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }

    function getJkda(){
        var tempData = JSON.parse('{"rd":{"yyzd":[],"jkzd":[{"list":[],"item":"饮食指导","itemcode":"01"},{"list":[],"item":"运动指导","itemcode":"02"},{"list":[],"item":"教育指导","itemcode":"03"},{"list":[],"item":"监测指导","itemcode":"05"}]}}');
        var fwqdTpl = document.getElementById("fwqdTemplet").innerHTML
            ,fwqdView  = $("#planTab-title")[0];

        var fwqdnrTpl = document.getElementById("fwqdnrTemplet").innerHTML
            ,fwqdnrView  = $("#planTab-content")[0];
        if(window.parent.patient.planid){
            ajaxValForm();
            var queryUrl = ctxPath+"/v/pcenter/getPlan";
            var queryParams = "planid="+window.parent.patient.planid;
            $.getJSON(queryUrl,queryParams,function(jsondata){
                if(jsondata.code=='200'){
                    laytpl(fwqdTpl).render(jsondata.rd, function(html){
                        fwqdView.innerHTML = html;
                    });
                    laytpl(fwqdnrTpl).render(jsondata.rd, function(html){
                        fwqdnrView.innerHTML = html;
                    });

                    if(jsondata.rd.yyzd) {
                        table.render({
                            id: "yyzdResult"
                            ,elem: '#yyzdResult .listtable'
                            ,cols: [[ //标题栏
                                 {fixed:'left', field: 'yytype', title: '用药类型'}
                                ,{fixed:'left',field: 'ywmc', title: '药品名称'}
                                ,{field: 'yypl', title: '频率'}
                                ,{field: 'yyjl', title: '用量'}
                                ,{fixed:'right', title: '用法',field: 'yyff'}
                            ]]
                            ,data: jsondata.rd.yyzd
                            ,page: false
                            ,cellMinWidth:70
                            ,height: 400
                            ,limit:100000000000
                        });
                    }

                }else{
                    layer.msg(jsondata.msg,{time:2000},function(){});
                }
                loading.blockRemove('#tab-card-jkda', 0);
            });
        }else{
            laytpl(fwqdTpl).render(tempData.rd, function(html){
                fwqdView.innerHTML = html;
            });
            laytpl(fwqdnrTpl).render(tempData.rd, function(html){
                fwqdnrView.innerHTML = html;
            });
            loading.blockRemove('#tab-card-jkda', 0);
        }
    }

    function getChart(){
        ajaxValForm();
        var queryUrl = ctxPath+"/v/sf/listChart";
        // var queryParams = "patientid="+window.parent.patient.id;
        var queryParams = "idcard="+window.parent.patient.idcard;
        $.getJSON(queryUrl,queryParams,function(jsondata){
            if(jsondata.code=='200'){
                initChart1(jsondata)
                initChart2(jsondata)
            }else{
                layer.msg(jsondata.msg,{time:2000},function(){});
            }
        });
    }

    var column1 = echarts.init(document.getElementById('echarts-gxy'), 'walden');
    var column2 = echarts.init(document.getElementById('echarts-tnb'), 'walden');
    // var column3 = echarts.init(document.getElementById('echarts-gxz'), 'walden');

    getJkda();
    getChart();
    loadFzTable("dfz","dfzTable");
    loadHisYyTable("his","hisTable");

    function initChart1(result){
        let bgColor = "#fff";
        let color = [
            "#0090FF",
            "#36CE9E",
            "#FFC005",
            "#FF515A",
            "#8B5CFF",
            "#00CA69"
        ];

        let xAxisData = result.rd.gxy.map(function(v){return v.sfrq});
        let yAxisData1 = result.rd.gxy.map(function(v){return v.ssy});
        let yAxisData2 = result.rd.gxy.map(function(v){return v.szy});

        var hexToRgba = function(hex, opacity) {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor =
                    `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                        "0x" + hex.slice(3, 5)
                    )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }
        option = {
            backgroundColor: bgColor,
            color: color,
            legend: {
                right: 0,
                top: 0
            },
            tooltip: {
                trigger: "axis",
                formatter: function(params) {
                    let html = '<b>'+params[0].name+'</b>';
                    params.forEach(function(v) {
                        html +=
                            `<div style="color: #666;font-size: 14px;line-height: 24px">
					                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
					                ${v.seriesName}
					                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
					                mmhg`;
                    })



                    return html
                },
                extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: '#ffffff',
                        shadowColor: 'rgba(225,225,225,1)',
                        shadowBlur: 5
                    }
                }
            },
            grid: {
                top: 50,
                containLabel: true
            },
            xAxis: [{
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    // formatter: '{value}月',
                    formatter: '{value}',
                    textStyle: {
                        color: "#333"
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: "#D9D9D9"
                    }
                },
                data: xAxisData
            }],
            yAxis: [{
                type: "value",
                name: '单位：mmhg',
                axisLabel: {
                    textStyle: {
                        color: "#666"
                    }
                },
                nameTextStyle: {
                    color: "#666",
                    fontSize: 12,
                    lineHeight: 40
                },
                splitLine: {
                    lineStyle: {
                        type: "dashed",
                        color: "#E9E9E9"
                    }
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            }],
            series: [{
                name: "收缩压",
                type: "line",
                smooth: true,
                // showSymbol: false,/
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[0],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[0], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[0], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[0], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[0], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData1
            }, {
                name: "舒张压",
                type: "line",
                smooth: true,
                // showSymbol: false,
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[1],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[1], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[1], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[1], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[1], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData2
            }]
        };
        column1.setOption(option);
        loading.blockRemove('.loading-chart1', 0);
    }

    function initChart2(result){
        let bgColor = "#fff";
        let color = [
            "#0090FF",
            "#36CE9E",
            "#FFC005",
            "#FF515A",
            "#8B5CFF",
            "#00CA69"
        ];

        let xAxisData = result.rd.tnb.map(function(v) {return v.sfrq});
        let yAxisData1 = result.rd.tnb.map(function(v) {return v.kfxt});
        let yAxisData2 = result.rd.tnb.map(function(v) {return v.thxhdb});

        var hexToRgba = function (hex, opacity) {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor =
                    `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                        "0x" + hex.slice(3, 5)
                    )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }
        option = {
            backgroundColor: bgColor,
            color: color,
            legend: {
                right: 0,
                top: 0
            },
            tooltip: {
                trigger: "axis",
                formatter: function(params) {
                    let html = '<b>'+params[0].name+'</b>';
                    params.forEach(function(v)  {
                        html +=
                            `<div style="color: #666;font-size: 14px;line-height: 24px">
					                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
					                ${v.seriesName}
					                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
					                mmol/l`;
                    })



                    return html
                },
                extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: '#ffffff',
                        shadowColor: 'rgba(225,225,225,1)',
                        shadowBlur: 5
                    }
                }
            },
            grid: {
                top: 50,
                containLabel: true
            },
            xAxis: [{
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    // formatter: '{value}月',
                    formatter: '{value}',
                    textStyle: {
                        color: "#333"
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: "#D9D9D9"
                    }
                },
                data: xAxisData
            }],
            yAxis: [{
                type: "value",
                name: '单位：mmol/l',
                axisLabel: {
                    textStyle: {
                        color: "#666"
                    }
                },
                nameTextStyle: {
                    color: "#666",
                    fontSize: 12,
                    lineHeight: 40
                },
                splitLine: {
                    lineStyle: {
                        type: "dashed",
                        color: "#E9E9E9"
                    }
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            }],
            series: [{
                name: "空腹血糖",
                type: "line",
                smooth: true,
                // showSymbol: false,/
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[0],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[0], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[0], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[0], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[0], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData1
            }, {
                name: "糖化血红蛋白",
                type: "line",
                smooth: true,
                // showSymbol: false,
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[1],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[1], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[1], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[1], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[1], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData2
            }]
        };
        column2.setOption(option);
        loading.blockRemove('.loading-chart2', 0);
    }
    $('#editYyzd').bind("click",function(){
        editYyzd();
    });
    var editYyzd = function (){
        var that = this;
        //执行重载
        parent.tzyyIndex = parent.layer.open({
            title:['调整用药']
            ,type: 2
            ,area: ['100%','100%']
            ,shade: [0.7, '#000000']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: [ctxPath+'/v/plan/tzyy?planid='+window.parent.patient.planid + '&patientid='+window.parent.patientid, 'no']
            ,end:function(){
                loadHisYyTable("yyzd","");
                loadHisYyTable("his","hisTable");
            }
        });
    }
    window.onresize = function() {
        column1.resize();
        column2.resize();
    }
});