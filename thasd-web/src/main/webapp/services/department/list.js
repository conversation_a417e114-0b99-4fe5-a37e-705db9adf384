layui.use(['layer','element','form','treeTable', 'util'], function() {//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var jQuery = layui.jquery;
	var element = layui.element;
	var table = layui.treeTable;
	var windowWidth = '500px';
	var windowHeight = '460px';
	var frist = true;
	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");
	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '数据请求中...';
		if(frist){
			layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		}else{
			//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
	}
	_loadMkInfo();
	let optionConfig = {
		done: function(res, curr, count){
			frist = false;
			setTimeout(function(){
				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
				$(".initbox").remove();
				layer.closeAll("dialog");
			},300);
		}
		,elem: '#listtable'
		,url:ctxPath+'/v/department/treeList'
		,where: {
			code: $("#code").val()
			,name: $("#name").val()
		},
		tree: {
			iconIndex: 2,// 折叠图标显示在第几列
			isPidData: true,
			idName: 'id',  // id字段名称
			pidName: 'parentid' // pid字段名称
		},
		cols: [
			{type:'checkbox',disKey:'isorg'}
			,{width:190,field:'code', title: '编码'}
			,{minWidth:240,field:'name', title: '名称'}
			//,{field:'parentname', title: '所属单位'}
			,{width:80,field:'sortcode',  title: '排序码', templet: '#listtable-sortcode'}
			,{width:140, align:'center', templet: '#listtable-opt' ,title: '操作'}
		]
		,toolbar: ['del','add']
		,defaultToolbar: ['filter']
		,page: false
		,height:'full-64'
		,cellMinWidth:100
		//,limit:20
	};
	var insTb = table.render(optionConfig);

	table.on('toolbar(listtable)', function(obj){
		//console.log(obj)
		switch(obj.event) {
			case 'add':
				active['add'].call(this, obj)
				break;
			case 'batchdel':
				active['batchdel'].call(this, obj)
				break;
		}
	});

	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'del'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				_delForm("id="+data.id);
			});
		} else if(obj.event === 'edit'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['修改部门信息']
				,type: 2
				,area: [windowWidth,windowHeight]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/department/editIndex?id='+data.id+'&noparent=noparent'+'&orgid='+data.orgid, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		reload: function(){
			var that = this;
			loadListFunction();
		}
		,batchdel: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var data = insTb.checkStatus(true);
			var len = data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+data[i].id
				}else{
					idDatas += "&id="+data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择您将要删除的记录',{time:2000});
				return false;
			} else{
				var info = '些';
				if(len==1)info='条';
				layer.confirm('你确认删除这'+info+'记录吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
					_delForm(idDatas);
				}, function(){
				});
			}
		}
		,add: function(){
			var that = this;
			//执行重载
			layer.open({
				title:['新增部门']
				,type: 2
				,area: [windowWidth,windowHeight]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/department/addIndex?noparent=noparent', 'no']
				,end:function(){
				}
			});
		}

	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});

	var loadListFunction = function(){
		var code = $("#code").val();
		var name = $("#name").val()
		optionConfig.where =  {
			code: code
			,name: name
		};
		insTb.reload(optionConfig);
	}
	window.reloadList = function(){
		layer.closeAll();
		loadListFunction();
	}

	window.resetSwClose = function(isClose){
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	}

	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/department/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

});




var loadListFunction = function(frist){
	layui.use(['layer','treeGrid'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.treeGrid;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/v/department/treeList'
		    ,treeId:'id'//树形id字段名称
            ,treeUpId:'parentid'//树形父id字段名称
            ,treeShowName:'name'//以树形式显示的字段
		    ,where: {
		    	code: $("#code").val()
		       ,name: $("#name").val()
		    }
		    ,cols: [[
			  {type:'checkbox'}
		      ,{width:190,field:'code', title: '编码'}
		      ,{minWidth:240,field:'name', title: '名称'}
		      //,{field:'parentname', title: '所属单位'}
		      ,{width:80,field:'sortcode',  title: '排序码', templet: '#listtable-sortcode'}
		      ,{width:140, align:'center', templet: '#listtable-opt' ,title: '操作'}
		    ]]
		    ,page: false
		    ,height:'full-174'
		    ,cellMinWidth:100
		    //,limit:20
		});
	});
}
