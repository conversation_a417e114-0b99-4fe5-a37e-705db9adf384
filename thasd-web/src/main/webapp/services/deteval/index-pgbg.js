var reloadList = function(){
}
layui.use(['layer', 'loading', 'form', 'step','element',"detevalUtil"], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        detevalUtil = layui.detevalUtil,
        element = layui.element,
        form = layui.form,
        step = layui.step;
    var loading = layui.loading;

    var windowWidth = function (){

        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        //console.log(seaWidth,seaHeight)

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowWidth2;
    }

    var windowHeight = function (){
        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowHeight2;
    }

    var ctxPath = layui.getContextPath("ctxPath","detevaljs");
    var mainid = layui.getContextPath("mainid","detevaljs");
    var patientid = "";
    var postData = {id:mainid};
    var postUrl = ctxPath+"/v/deteval/get";
    $.ajaxSetup({
        error:function(x,e){
            $(".initbox").remove();
            layer.msg('连接服务器失败,请稍后再试...',{time:2000});
            return false;
        }
    });
    $.getJSON(postUrl,postData,function(jsondata){
        if(jsondata.code=='200'){
            patientid = jsondata.rd.patientid;
            detevalUtil.setEvalPage(jsondata.rd);//生成评估页面
        }else{
            layer.msg(jsondata.msg,{time:2000});
        }
        $(".initbox").remove();
    });
    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    //触发事件
    var active = {
        edit: function() {
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            //执行重载
            layer.open({
                title:['修改三高患者基本信息']
                ,type: 2
                ,area: [windowWidth(),windowHeight()]
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/v/patients/editIndex?id='+patientid, 'no']
                ,end:function(){
                }
            });
        },
        printBg: function() {
            window.print();
            // window.jsPDF = window.jspdf.jsPDF;
            // html2canvas(document.querySelector("#pagetb"),{
            //     dpi: window.devicePixelRatio * 4, //将分辨率提高到特定的DPI 提高四倍
            //     scale: 4
            // }).then(canvas =>{
            //     var pdf = new jsPDF('p', 'mm', 'a4');    //A4纸，纵向
            //     var ctx = canvas.getContext('2d'),
            //         a4w = 190, a4h = 277,    //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
            //         imgHeight = Math.floor(a4h * canvas.width / a4w),    //按A4显示比例换算一页图像的像素高度
            //         renderedHeight = 0;
            //
            //     while(renderedHeight < canvas.height) {
            //         var page = document.createElement("canvas");
            //         page.width = canvas.width;
            //         page.height = Math.min(imgHeight, canvas.height - renderedHeight);//可能内容不足一页
            //
            //         //用getImageData剪裁指定区域，并画到前面建立的canvas对象中
            //         page.getContext('2d').putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0);
            //         pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(a4h, a4w * page.height / page.width));    //添加图像到页面，保留10mm边距
            //
            //         renderedHeight += imgHeight;
            //         if(renderedHeight < canvas.height){
            //             pdf.addPage();//若是后面还有内容，添加一个空页
            //         }
            //         delete page;
            //     }
            //     var filename = 'report_pdf_' + new Date().getTime() + '.pdf';
            //     // 导出pdf文件命名
            //     pdf.save(filename);
            //     var link = window.URL.createObjectURL(toBlob(pdf.output('datauristring')));
            //     var myWindow = window.open(link);
            //     myWindow.print();
            // });
            // .then(canvas => {
            //     var contentWidth = canvas.width
            //     var contentHeight = canvas.height
            //     // 一页pdf显示html页面生成的canvas高度;
            //     var pageHeight = contentWidth / 592.28 * 841.89
            //     // 未生成pdf的html页面高度
            //     var leftHeight = contentHeight
            //     // pdf页面偏移
            //     var position = 0
            //     // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
            //     var imgWidth = 595.28
            //     var imgHeight = 592.28 / contentWidth * contentHeight
            //     var pageData = canvas.toDataURL('image/jpeg', 1.0)
            //     // eslint-disable-next-line
            //     var pdf = new jsPDF('', 'pt', 'a4')
            //     // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
            //     // 当内容未超过pdf一页显示的范围，无需分页
            //     if (leftHeight < pageHeight) {
            //         pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
            //     } else {
            //         while (leftHeight > 0) {
            //             pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
            //             leftHeight -= pageHeight
            //             position -= 841.89
            //             // 避免添加空白页
            //             if (leftHeight > 0) {
            //                 pdf.addPage()
            //             }
            //         }
            //     }
            //     var filename = 'report_pdf_' + new Date().getTime() + '.pdf';
            //     // 导出pdf文件命名
            //     pdf.save(filename);
            //     // $(".prtbg-canvas")[0].appendChild(canvas);
            //     // $("#bgForm").hide();
            // });
        }
    };

    function toBlob(base64Data) {
        let byteString = base64Data
        if (base64Data.split(',')[0].indexOf('base64') >= 0) {
            byteString = atob(base64Data.split(',')[1]); // base64 解码
        } else {
            byteString = unescape(base64Data.split(',')[1]);
        }
        // 获取文件类型
        var mimeString = base64Data.split(';')[0].split(":")[1]; // mime类型

        // ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区
        // let arrayBuffer = new ArrayBuffer(byteString.length) // 创建缓冲数组
        // let uintArr = new Uint8Array(arrayBuffer) // 创建视图

        var uintArr = new Uint8Array(byteString.length); // 创建视图

        for (let i = 0; i < byteString.length; i += 1) {
            uintArr[i] = byteString.charCodeAt(i);
        }
        // 生成blob
        var blob = new Blob([uintArr], {
            type: mimeString
        })
        // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
        return blob;
    };

});