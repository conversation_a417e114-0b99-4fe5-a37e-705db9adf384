var layIndex1;
var detPatientData;
window.addEventListener("message", function(e){
    console.log(e,e.data,window.location.pathname);
    detPatientData = e.data.data;
    layui.use(['layer', 'loading', 'form', 'step','element',"detevalUtil"], function() {
        var $ = layui.jquery,
            layer = layui.layer,
            detevalUtil = layui.detevalUtil,
            element = layui.element,
            form = layui.form,
            step = layui.step;
        var loading = layui.loading;
        var ctxPath = layui.getContextPath("ctxPath","detevaljs");
        if(detPatientData){
            form.val('patientForm', JSON.parse(JSON.stringify(detPatientData)));
            form.val('evalForm', JSON.parse(JSON.stringify(detPatientData)));
            $("#patientid").val(detPatientData.id);
            $("#lxdh").removeClass("layui-disabled");
            $("#jtzz").removeClass("layui-disabled");
            $("#lxdh").prop("disabled",null);
            $("#jtzz").prop("disabled",null);
            detevalUtil.setHBPRiskFactor(detPatientData);
            detevalUtil.setDBRiskFactor(detPatientData);
            detevalUtil.setLPRiskFactor(detPatientData);
            //step.next('#stepForm');
            $(".initbox").remove();
        }
    });
}, false);


layui.use(['layer', 'loading', 'form', 'step','element',"detevalUtil"], function() {

    var isDateValid = (...val) => !Number.isNaN(new Date(...val).valueOf());
    var isAfterDate = (dateA, dateB) => dateA > dateB;


    var $ = layui.jquery,
        layer = layui.layer,
        detevalUtil = layui.detevalUtil,
        element = layui.element,
        form = layui.form,
        step = layui.step;

    var loading = layui.loading;

    var ctxPath = layui.getContextPath("ctxPath","detevaljs");
    var source = layui.getContextPath("source","detevaljs");
    if(!source || source != "out"){
        $(".initbox").remove();
    }

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });

    step.render({
        elem: '#stepForm',
        filter: 'stepForm',
        width: '100%',
        stepWidth: '600px',
        height: '100%',
        isLayer: true,
        stepItems: [{
            title: '请选择患者'
        }, {
            title: '填写评估指标'
        }, {
            title: '评估结果'
        }]
    });

    detevalUtil.onBlurByInput("evalForm");
    detevalUtil.initCheckBox("evalForm");

    form.on('submit(formStep)', function(data) {
        loading.Load(1, "正在读取数据,请稍后...");
        console.log(data)
        $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
        var postUrl = ctxPath+"/v/patients/getJktjb";
        $.ajaxSetup({
            error:function(x,e){
                loading.loadRemove(0 ,function (){
                    layer.msg('连接服务器失败,请稍后再试...',{time:3000});
                    $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                });
                return false;
            }
        });
        function checkResultData(jsondata,form){

            var bp = JSON.parse(jsondata.bp);
            var lab = JSON.parse(jsondata.lab);

            if(bp.code=='200') {
                console.log(bp.rd)
                if (bp.rd) {
                    var ghdate = bp.rd.ghdate || '';
                    if (isDateValid(ghdate)) {

                        var ssy = bp.rd.ssy || '';
                        var szy = bp.rd.szy || '';

                        form.val('evalForm', JSON.parse(JSON.stringify({ssy: ssy, szy: szy})));

                        detevalUtil.verifyBp(ssy, szy);
                    }
                }
            }
            if(lab.code=='200') {
                console.log(lab.rd)
                if (lab.rd) {
                    var kfxt = '', ckxt = '', zdgc = '', gysz = '', dmdzdb = '';

                    var ltime1 = lab.rd.GLU ? lab.rd.GLU.time || '' : '';
                    var ltime2 = lab.rd.TC ? lab.rd.TC.time || '' : '';
                    var ltime3 = lab.rd.TG ? lab.rd.TG.time || '' : '';
                    var ltime4 = lab.rd.LDL ? lab.rd.LDL.time || '' : '';

                    if (isDateValid(ltime1)) {
                        ckxt = lab.rd.GLU ? lab.rd.GLU.result || '' : '';
                    }
                    if (isDateValid(ltime2)) {
                        zdgc = lab.rd.TC ? lab.rd.TC.result || '' : '';
                    }
                    if (isDateValid(ltime3)) {
                        gysz = lab.rd.TG ? lab.rd.TG.result || '' : '';
                    }
                    if (isDateValid(ltime4)) {
                        dmdzdb = lab.rd.LDL ? lab.rd.LDL.result || '' : '';
                    }

                    form.val('evalForm', JSON.parse(JSON.stringify({ckxt: ckxt, zdgc: zdgc, gysz: gysz, dmdzdb: dmdzdb})));

                    detevalUtil.verifyXt(kfxt, ckxt);
                    detevalUtil.verifyXz(zdgc, gysz, dmdzdb);
                }
            }
        }
        $.getJSON(postUrl,"patientid="+detPatientData.id + "&idcard=" + detPatientData.idcard ,function(jsondata){
            console.log(jsondata)
            loading.loadRemove(500 ,function (){
                if(jsondata.code=='200' || jsondata.code==200){
                    var msgInfo = "";

                    checkResultData(jsondata,form);
                    /**暂时注释
                    // var jktj = JSON.parse(jsondata.jktj);
                    // if(jktj.code=='200') {
                    //
                    //     var bp = JSON.parse(jsondata.bp);
                    //     var lab = JSON.parse(jsondata.lab);
                    //
                    //     if(jktj.rd){
                    //         let gender = $("#gendercode").val();
                    //
                    //         form.val('evalForm', JSON.parse(JSON.stringify(jktj.rd)));
                    //
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>1")
                    //         console.log(jktj)
                    //         console.log(bp)
                    //         console.log(lab)
                    //
                    //         var sfrq =  jktj.rd.sfrq || '';
                    //         var ghdate = '',ltime1='',ltime2='',ltime3='',ltime4='';
                    //
                    //         if (bp.rd) {
                    //             ghdate = bp.rd.ghdate || '';
                    //         }
                    //         if (lab.rd) {
                    //              ltime1 = lab.rd.GLU ? lab.rd.GLU.time || '' : '';
                    //              ltime2 = lab.rd.TC ? lab.rd.TC.time || '' : '';
                    //              ltime3 = lab.rd.TG ? lab.rd.TG.time || '' : '';
                    //              ltime4 = lab.rd.LDL ? lab.rd.LDL.time || '' : '';
                    //         }
                    //
                    //         var weight = jktj.rd.weight ||'';
                    //         var height = jktj.rd.height ||'';
                    //         var waistline = jktj.rd.waistline ||'';
                    //
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>2")
                    //
                    //         detevalUtil.verifyBmi(weight, height);
                    //         detevalUtil.verifyWaistline(waistline, gender);
                    //
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>3")
                    //
                    //
                    //         var ssy = jktj.rd.ssy ||'';
                    //         var szy = jktj.rd.szy ||'';
                    //
                    //         var kfxt = jktj.rd.kfxt ||'';
                    //         var ckxt = jktj.rd.ckxt ||'';
                    //
                    //         var zdgc = jktj.rd.zdgc ||'';
                    //         var gysz = jktj.rd.gysz ||'';
                    //         var dmdzdb = jktj.rd.dmdzdb ||'';
                    //
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>4")
                    //
                    //         if(sfrq == ''){//为空取门诊血压数据...
                    //
                    //             if(isDateValid(ghdate)){//合法
                    //
                    //                 ssy = bp.rd.ssy || '';
                    //                 szy = bp.rd.szy || '';
                    //             }
                    //
                    //             if(isDateValid(ltime1)){
                    //                 ckxt = lab.rd.GLU ? lab.rd.GLU.result || ckxt : ckxt;
                    //             }
                    //             if(isDateValid(ltime2)){
                    //                 zdgc = lab.rd.TC ? lab.rd.TC.result || zdgc : zdgc;
                    //             }
                    //             if(isDateValid(ltime3)){
                    //                 gysz = lab.rd.TG ? lab.rd.TG.result || gysz : gysz;
                    //             }
                    //             if(isDateValid(ltime4)){
                    //                 dmdzdb = lab.rd.LDL ? lab.rd.LDL.result || dmdzdb : dmdzdb;
                    //             }
                    //
                    //         }else{//否,比对时间 去时间最新的
                    //             console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>4.1")
                    //             if( isDateValid(ghdate) && isAfterDate( new Date(ghdate),new Date(sfrq) ) ){//合法
                    //                 ssy = bp.rd.ssy || ssy;
                    //                 szy = bp.rd.szy || szy;
                    //             }
                    //
                    //             console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>4.2")
                    //
                    //             if(isDateValid(ltime1) && isAfterDate( new Date(ltime1),new Date(sfrq) )){
                    //                 ckxt = lab.rd.GLU ? lab.rd.GLU.result || ckxt : ckxt;
                    //             }
                    //             if(isDateValid(ltime2) && isAfterDate( new Date(ltime2),new Date(sfrq) )){
                    //                 zdgc = lab.rd.TC ? lab.rd.TC.result || zdgc : zdgc;
                    //             }
                    //             if(isDateValid(ltime3) && isAfterDate( new Date(ltime3),new Date(sfrq) )){
                    //                 gysz = lab.rd.TG ? lab.rd.TG.result || gysz : gysz;
                    //             }
                    //             if(isDateValid(ltime4) && isAfterDate( new Date(ltime4),new Date(sfrq) )){
                    //                 dmdzdb = lab.rd.LDL ? lab.rd.LDL.result || dmdzdb : dmdzdb;
                    //             }
                    //
                    //             console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>4.3")
                    //
                    //         }
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>5")
                    //
                    //         var showObj = {ckxt: ckxt, zdgc: zdgc, gysz: gysz, dmdzdb: dmdzdb};
                    //         form.val('evalForm', JSON.parse(JSON.stringify(showObj)));
                    //         console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>6")
                    //
                    //         detevalUtil.verifyBp(ssy, szy);
                    //         detevalUtil.verifyXt(kfxt, ckxt);
                    //         detevalUtil.verifyXz(zdgc, gysz,dmdzdb);
                    //
                    //     }else{
                    //         checkResultData(jsondata,form);
                    //     }
                    // }else{
                    //     checkResultData(jsondata,form);
                    // }
                    **/
                    step.next('#stepForm');
                }else{
                    layer.msg(jsondata.msg,{time:2000});
                }
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
            });
        });
        return false;
    });

    $('#pre').click(function() {
        step.pre('#stepForm');
        return false;
    });

    form.verify({
        bindRequired: function(value, item){ //value：表单的值、item：表单的DOM对象
            // console.log(value,$(item).attr("bind"))
            let bindId = "#" + $(item).attr("bind");
            let bindValue = $(bindId).val();
            let bindLength = bindValue.length;
            if(value.length == 0 && bindLength == 0){
                return '请至少输入一个血糖检测值!';
            }
        }
    });

    form.on('submit(formStep2)', function(data) {

        loading.Load(1, "正在评估,请稍后...");

        let gender = $("#gendercode").val();

        detevalUtil.verifyBmi(data.field.weight, data.field.height);
        detevalUtil.verifyWaistline(data.field.waistline, gender);
        detevalUtil.verifyBp(data.field.ssy, data.field.szy);
        detevalUtil.verifyXt(data.field.kfxt, data.field.ckxt);
        detevalUtil.verifyXz(data.field.zdgc, data.field.gysz,data.field.dmdzdb);
        detevalUtil.verifyYsSy(data.field.yssysr);
        detevalUtil.verifyYsTang(data.field.ystsr);
        detevalUtil.verifyYsYou(data.field.ysysr);
        detevalUtil.verifyYj(gender);

        detevalUtil.genBpGrade(data.field);
        detevalUtil.genDbGrade(data.field);
        detevalUtil.genLpGrade(data.field);
        detevalUtil.genAscvd(form.val("evalForm"));
        detevalUtil.genRisk();

        var bfzDic = {0: "无", 1: "冠心病", 2: "脑卒中", 3: "肾病综合征", 4: "眼底病变", 5: "周围神经病变", 6: "周围血管病变"};
        var bfzval = $("#bfz-val").val();
        var bfzname = $("#bfz-name").val();
        if (bfzval.indexOf("0") > -1 || bfzname.indexOf("无,") > -1) {
            if(bfzval == "0"){
                bfzval = "0";
                bfzname = "无";
            }else{
                let realBfz = bfzval.split(",").filter(item => item != '');
                var bfzArr = [];
                var nameArr = [];
                for(var i = 0; i <realBfz.length; i++){
                    var key = realBfz[i];
                    if(key != 0 ){
                        bfzArr.push(key);
                        nameArr.push(bfzDic[key]);
                    }
                }
                bfzval = bfzArr.join((bfzArr.length > 1 ? "," : ""));
                bfzname = nameArr.join((nameArr.length > 1 ? "," : ""));
                $("#bfz-val").val(bfzArr.join((bfzArr.length > 1 ? "," : "")));
                $("#bfz-name").val(nameArr.join((nameArr.length > 1 ? "," : "")));
            }
        }

        var postData = form.val("evalForm");
        postData["bfz"] = bfzval;
        postData["bfzname"] = bfzname;
        //console.log(postData)
        //console.log(JSON.stringify(postData));

        $("#zbpost").attr("disabled","disabled").addClass("layui-btn-disabled");
        $("#pre").attr("disabled","disabled").addClass("layui-btn-disabled");

        var postUrl = ctxPath+"/v/deteval/save";
        $.ajaxSetup({
            error:function(x,e){
                loading.loadRemove(0 ,function (){
                    layer.msg('连接服务器失败,请稍后再试...',{time:3000});
                    $("#zbpost").attr("disabled",null).removeClass("layui-btn-disabled");
                    $("#pre").attr("disabled",null).removeClass("layui-btn-disabled");
                });
                return false;
            }
        });
        $.getJSON(postUrl,postData,function(jsondata){
            loading.loadRemove(500 ,function (){
                if(jsondata.code=='200'){

                    postData["evalno"] = jsondata.data.evalno;
                    postData["createtime"] = jsondata.data.createtime;
                    detevalUtil.setEvalPage(postData);//生成评估页面

                    step.next('#stepForm');

                    setTimeout(function (){
                        detevalUtil.resetAscvdChart(postData);
                    },500)

                }else{
                    layer.msg(jsondata.msg,{time:2000});
                    $("#zbpost").attr("disabled",null).removeClass("layui-btn-disabled");
                    $("#pre").attr("disabled",null).removeClass("layui-btn-disabled");
                }
            });
        });
        return false;
    });

    $('#replay').click(function() {
        var url = ctxPath + window.location.pathname;
        window.location.href = url;
        return false;
    });

    //触发事件
    var active = {
        selectPatients: function() {
            layIndex1 = layer.open({
                title:['选择患者']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioPatients', 'no']
                ,end:function(){
                    //console.log(detPatientData)
                    if(detPatientData){
                        form.val('patientForm', JSON.parse(JSON.stringify(detPatientData)));
                        form.val('evalForm', JSON.parse(JSON.stringify(detPatientData)));
                        $("#patientid").val(detPatientData.id);
                        $("#lxdh").removeClass("layui-disabled");
                        $("#jtzz").removeClass("layui-disabled");
                        $("#lxdh").prop("disabled",null);
                        $("#jtzz").prop("disabled",null);
                        detevalUtil.setHBPRiskFactor(detPatientData);
                        detevalUtil.setDBRiskFactor(detPatientData);
                        detevalUtil.setLPRiskFactor(detPatientData);
                    }
                }
            });
        }
        ,printBg: function() {
            window.jsPDF = window.jspdf.jsPDF;
            html2canvas(document.querySelector("#bgForm"),{
                dpi: window.devicePixelRatio * 4, //将分辨率提高到特定的DPI 提高四倍
                scale: 4
            }).then(canvas =>{
                var pdf = new jsPDF('p', 'mm', 'a4');    //A4纸，纵向
                var ctx = canvas.getContext('2d'),
                    a4w = 190, a4h = 277,    //A4大小，210mm x 297mm，四边各保留10mm的边距，显示区域190x277
                    imgHeight = Math.floor(a4h * canvas.width / a4w),    //按A4显示比例换算一页图像的像素高度
                    renderedHeight = 0;

                while(renderedHeight < canvas.height) {
                    var page = document.createElement("canvas");
                    page.width = canvas.width;
                    page.height = Math.min(imgHeight, canvas.height - renderedHeight);//可能内容不足一页

                    //用getImageData剪裁指定区域，并画到前面建立的canvas对象中
                    page.getContext('2d').putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0);
                    pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(a4h, a4w * page.height / page.width));    //添加图像到页面，保留10mm边距

                    renderedHeight += imgHeight;
                    if(renderedHeight < canvas.height){
                        pdf.addPage();//若是后面还有内容，添加一个空页
                    }
                    delete page;
                }
                var filename = 'report_pdf_' + new Date().getTime() + '.pdf';
                // 导出pdf文件命名
                pdf.save(filename);
                var link = window.URL.createObjectURL(toBlob(pdf.output('datauristring')));
                var myWindow = window.open(link);
                myWindow.print();
            });
        }
    };


    function toBlob(base64Data) {
        let byteString = base64Data
        if (base64Data.split(',')[0].indexOf('base64') >= 0) {
            byteString = atob(base64Data.split(',')[1]); // base64 解码
        } else {
            byteString = unescape(base64Data.split(',')[1]);
        }
        // 获取文件类型
        var mimeString = base64Data.split(';')[0].split(":")[1]; // mime类型

        // ArrayBuffer 对象用来表示通用的、固定长度的原始二进制数据缓冲区
        // let arrayBuffer = new ArrayBuffer(byteString.length) // 创建缓冲数组
        // let uintArr = new Uint8Array(arrayBuffer) // 创建视图

        var uintArr = new Uint8Array(byteString.length); // 创建视图

        for (let i = 0; i < byteString.length; i += 1) {
            uintArr[i] = byteString.charCodeAt(i);
        }
        // 生成blob
        var blob = new Blob([uintArr], {
            type: mimeString
        })
        // 使用 Blob 创建一个指向类型化数组的URL, URL.createObjectURL是new Blob文件的方法,可以生成一个普通的url,可以直接使用,比如用在img.src上
        return blob;
    };

});