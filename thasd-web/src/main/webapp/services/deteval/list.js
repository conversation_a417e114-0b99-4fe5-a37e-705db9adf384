var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;

		var ctxPath = layui.getContextPath("ctxPath","listjs");
		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/deteval/list'
			,where: {
				name: $("#name").val()
				,islower:$("input[name='islower']:checked").val()
				,idcard:$("#idcard").val()
				,orgid: $("#scbmHide").val()
				,glorgid: $("#glorgidHide").val()
			}
			,cols: [[
				{type:'checkbox',fixed:'left'}
				,{field:'name',  title: '患者姓名',width: 86}
				,{field:'idcard',  title: '身份证号码',width: 171}
				,{field:'gender',  title: '性别',width:58}
				,{field:'age',  title: '年龄',width:58}
				,{field:'glorgname',  title: '当前管理单位',minWidth: 174}
				,{field:'gljyusername',  title: '当前管理医生',width: 119}
				,{align:'center', templet: '#bpgrade', width:72, title: '高血压'}
				,{align:'center', templet: '#dpgrade', width:72, title: '糖尿病'}
				,{align:'center', templet: '#lpgrade', width:72, title: '高血脂'}
				,{align:'center', templet: '#ascvd', width:108, title: 'ASCVD风险'}
				,{field:'createtime',  title: '评估时间',width: 171}
				,{field:'porgname',  title: '所属单位',minWidth: 174}
				,{field:'jyusername',  title: '所属医生',width: 86}
				,{fixed:'right', width:170, align:'center', toolbar:'#listtable-opt',title: '操作'}
			]]
			,page: true
			,height:'full-155'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var zFun =layui.treeselectTable;
	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		//console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/deteval/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'del'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				_delForm("id="+data.id);
			});
		} else if(obj.event === 'view'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['查看评估报告']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/deteval/viewIndex?id='+data.id, 'no']
				,end:function(){
				}
			});
		}else if(obj.event === 'edit'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['修改三高患者基本信息']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/patients/editIndex?id='+data.patientid, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		readcard:function (){
			//传需要赋值input的ID
			var shensi = new ShenSi("idcard");
		},
		reload: function(){
			var that = this;
			loadListFunction();
		}
		,export: function(){
			var qParams = {
				name: $("#name").val()
				,islower:$("input[name='islower']:checked").val()
				,idcard:$("#idcard").val()
				,orgid: $("#scbmHide").val()
				,glorgid: $("#glorgidHide").val()
			}
			window.open(ctxPath +"/v/deteval/export?" + $.param(qParams),'top');
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	var _loadDic = function(){
		$("#scbm").attr("ival","");
		$("#scbm").attr("nval","");
		$("#glorgid").attr("ival",authUser.orgid);
		$("#glorgid").attr("nval",authUser.orgname);
		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","dept",false);
		zFun.initSelectTree(zNodes, "请选择管理单位", "glorgid", false, true, "只能选择三级单位", "listQuery", false);
		zFun.selectTreeId("glorgid",authUser.orgid);
	}
	_loadDic();
	loadListFunction(true);

});