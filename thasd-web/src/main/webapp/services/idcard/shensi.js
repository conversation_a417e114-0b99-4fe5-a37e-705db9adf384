var websocket;
var isShensiOpen = false;
var elementId;
function ShenSi(eleId) {
    elementId = eleId;
    function StartWebSocket() {
        if(!window.WebSocket) {
            alert("该版本浏览器不支持WebSocket,无法使用身份证读卡器");
            return;
        }
        websocket = new WebSocket("ws://127.0.0.1:9000/");
        websocket.onopen = function(evt){
            //websocket.send("{"Method":"SdtReadCard", "CameraName":"1-uni"}");
            isShensiOpen = true;
            console.log("建立WebSocket连接成功");
            ReadIdCard();
        };
        websocket.onclose = function(evt){
            isShensiOpen = false;
            console.log("WebSocket已关闭")
        };
        websocket.onmessage = function(evt){
            console.log(evt)
            var datas = JSON.parse(evt.data);
            if (datas.Method == 'IdReadCard'){
                if (datas.RetCode == 0 && datas.IdCardInfo && datas.IdCardInfo != 'undefined') {
                    var arrInfo = datas.IdCardInfo.split(":");
                    var idcard = arrInfo[9];
                    document.getElementById(elementId).value = idcard;//设置身份证卡号
                    console.log("获取身份证卡号：" + idcard);
                }else{
                    alert('读身份证信息失败,错误代码:' + datas.RetCode.toString());
                }
                CloseDevice();
                setTimeout(function (){
                    websocket.close();
                },300);
            }else{
                if (datas.RetCode > 0){
                    //coding
                }else{
                    if(datas.Method == 'CloseDevice')return;
                    alert('连接服务失败,错误代码:' + datas.RetCode.toString());
                }
            }
        };
        websocket.onerror = function(evt){
            isShensiOpen = false;
            console.log("建立WebSocket连接失败",evt)
            alert("连接服务失败,无法使用身份证读卡器");
        };
    }
    StartWebSocket();
}

function ReadIdCard(){
    OpenDevice();
    IdReadCard();

}
function OpenDevice() {
    websocket.send('{"Method":"OpenDevice","PortType":"AUTO","PortPara":"","ExtendPara":""}');
}

function IdReadCard() {
    websocket.send('{"Method":"IdReadCard","CardType":"16","InfoEncoding":"1","TimeOutMs":"0"}');
}

function CloseDevice() {
    websocket.send('{"Method":"CloseDevice"}');
}	
