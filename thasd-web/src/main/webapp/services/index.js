 var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','accordion'], function(){//独立版的layer无需执行这一句
  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
  var jQuery = layui.jquery;

  function getUParam(name,id) {
	    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
	    if (r != null) return unescape(r[2]); return ""; //返回参数值
  }
  var ctxPath = getUParam("ctxPath","indexjs");
  $('.right___3kEl5 .action___119rd').on('mouseenter', function(){
	  $(".user-menu").addClass("user-menu-show");
  });
  var isMenuOut =function(){
	  var isHover = $('.right___3kEl5 .action___119rd').is(':hover');
	  if(!isHover){
		  $(".user-menu").removeClass("user-menu-show");
	  }
  }
  $('.right___3kEl5 .action___119rd').on('mouseleave', function(){
	  $(".ant-dropdown-menu-item").css({"min-width":$(".right___3kEl5").width()});
      setTimeout(isMenuOut,200);  
  });
  
  
  $('#loginOut').on('click', function(){
	  window.location.href = ctxPath + "/loginout"
  });
  
  $('#editPass').on('click', function(){
	  layer.open({
  		title:['修改密码']
  	    ,type: 2
  	    ,area: ['480px','336px']
  	    ,shade: [0.7, '#d0d7f6']
  	    ,scrollbar: true
  	    ,maxmin: false
  	    ,fixed:true
  	    ,move: false
  	    ,content: [ctxPath+'/exrule/editPasswordIndex', 'no']
  	    ,end:function(){
  		}
  	 });
  });
  
  $('#accordion').Accordion({
	   speed: 'fast',
	   header:'h3',
	   activeItem:0
 });
 $(".ant-dropdown-menu-item").css({"min-width":$(".right___3kEl5").width()});
});