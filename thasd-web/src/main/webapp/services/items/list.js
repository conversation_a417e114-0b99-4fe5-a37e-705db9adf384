layui.use(['layer','element','form','treeTable', 'util'], function(){//独立版的layer无需执行这一句

	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var element = layui.element;
	var util = layui.util;
	var table = layui.treeTable;
	var windowWidth = '1110px';
	var windowHeight = '96%';
	var frist = true;

	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '数据请求中...';
		if(frist){
			layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		}else{
			//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
	}
	_loadMkInfo();

	let optionConfig = {
		done: function(res, curr, count){
			frist = false;
			setTimeout(function(){
				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
				$(".initbox").remove();
				layer.closeAll("dialog");
			},300);
		}
		,elem: '#listtable'
		,url:ctxPath+'/v/items/treeList'
		,where: {
			code: $("#code").val()
			,name: $("#name").val()
		},
		tree: {
			iconIndex: 2,// 折叠图标显示在第几列
			isPidData: false,
			idName: 'code',  // id字段名称
			pidName: 'parentcode' // pid字段名称
		},
		cols: [
			[
				{type:'radio',rowspan: 2}
				,{width:135,field:'code', title: '编码',rowspan: 2}
				,{minWidth:280,field:'name', title: '名称',rowspan: 2}
				,{width:180,field:'xmnr', title: '项目内容',rowspan: 2}
				,{width:180,field:'cwnr', title: '除外内容',rowspan: 2}
				,{width:100,field:'jjdw', title: '计价单位',rowspan: 2}
				,{title: '价格（元）', colspan: 3,align:'center'}
				,{width:280,field:'remark', title: '说明',rowspan: 2}
			]
			,[{width:90,field:'sjdwjg', title: '三级医院'},{width:90,field:'ejdwjg', title: '二级医院'},{width:90,field:'yjdwjg', title: '一级医院'}]
		]
		,toolbar: ['add','edit']
		,defaultToolbar: ['filter']
		,page: false
		,height:'full-105'
		,cellMinWidth:100
		//,limit:20
	};
	var insTb = table.render(optionConfig);

	table.on('toolbar(listtable)', function(obj){
		switch(obj.event){
			case 'edit':
				var checkStatus = insTb.checkStatus(true);
				if(checkStatus.length > 0){
					layer.open({
						title:['修改收费项目信息']
						,type: 2
						,area: [windowWidth,windowHeight]
						,shade: [0.7, '#d0d7f6']
						,scrollbar: true
						,maxmin: false
						,fixed:true
						,move: false
						,content: [ctxPath+'/v/items/editIndex?id='+checkStatus[0].id+'&noparent=noparent', 'no']
						,end:function(){
						}
					});
				}else{
					layer.msg('请选择您要修改的记录',{time:2000});
				}
				break;
			case 'add':
				//执行重载
				layer.open({
					title:['新增收费项目']
					,type: 2
					,area: [windowWidth,windowHeight]
					,shade: [0.7, '#d0d7f6']
					,scrollbar: true
					,maxmin: false
					,fixed:true
					,move: false
					,content: [ctxPath+'/v/items/addIndex?noparent=noparent', 'no']
					,end:function(){
					}
				});
				break;
			case 'batchdel':
				var that = this;
				if($(that).attr("disabled")=="disabled")return;
				var data = table.checkStatus();
				var len = data.length;
				var idDatas = "";
				for(var i=0 , l = len; i < l; i++){
					if(i==0){
						idDatas += "id="+data[i].id
					}else{
						idDatas += "&id="+data[i].id
					}
				}
				if(len == 0){
					layer.msg('请选择您将要删除的记录',{time:2000});
					return false;
				} else{
					var info = '些';
					if(len==1)info='条';
					layer.confirm('你确认删除这'+info+'记录吗？', {
						btn: ['确认','取消'] //按钮
					}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
						$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
						_delForm(idDatas);
					}, function(){
					});
				}
				break;
		};
	});
	var loadListFunction = function(){
		var code = $("#code").val();
		var name = $("#name").val()
		optionConfig.where =  {
			code: code
			,name: name
		};
		insTb.reload(optionConfig);
	}

	window.resetSwClose = function(isClose){
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	}
	window.reloadList = function(){
		layer.closeAll();
		loadListFunction();
	}

	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/items/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	//触发事件
	var active = {
		reload: function(){
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
});




