var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var selectMenuNode = null;
var getSelectMenuNode = function(){
	return selectMenuNode;
}
var reloadMenuList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadMenuListFunction();
	});
}
var loadMenuListFunction = function(frist){
	layui.use(['layer','ztree','treeDic'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var treeDic = layui.treeDic;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function ajaxValForm(){
			$.ajaxSetup({
				error:function(x,e){
					_serverFail();
		       		return false;
		        }
		    });
		}
		function _getTreeList(){
			layer.msg('数据请求中...', {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	ajaxValForm();
			var orgid = authUser.grade == "3" ? authUser.porgid : (authUser.grade == "2" ? authUser.orgid : "")
		  	$.getJSON(ctxPath+"/exrule/jcjyTreeSingle","orgid=" + orgid,function(jsondata){
		  		if(jsondata.code=='200'){
		  			initTree(jsondata.data);
			  		layer.closeAll("dialog");
    				$("#refFl").attr("disabled",null).removeClass("layui-btn-disabled");
				}else{
					layer.msg(jsondata.msg,{time:2000});
				}
		  		
		  	});
		}
		var getMenuNodes = function(){
			  return zNodesJsonString;
	    }
		var zNodes = getMenuNodes();
		var initTree = function(zNodesString){
			treeDic.initSelectTree(zNodesString,"menuTable",true,"只能选择实际业务模块","menuTable",false);
		}
		if(frist){
			initTree(zNodes);
		}else{
			_getTreeList();
		}
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','form','table','bselect'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var form = layui.form;

		if(frist){
			loadMenuListFunction(true);
		}
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function (){
					$("div[name='dzpro-box']").each(function(index){
						$(this).mySelect({"dicData":dicData,"postUrl": (ctxPath + "/v/jcjy/save") });
					});
					bindSelect();
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},0);
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/v/jcjy/list'
		    ,where: {
		    	code: $("#code").val()
		       ,name: $("#name").val()
			   ,orgid: (authUser.grade == "3" ? authUser.porgid : (authUser.grade == "2" ? authUser.orgid : ""))
		       ,treecode: (selectMenuNode==null ? "" : (selectMenuNode.treeCode.length == 1 ? '0' + selectMenuNode.treeCode : selectMenuNode.treeCode))
		    }
		    ,cols: [[

		      {field:'name',  title: '项目名称'}
		      ,{field:'unit',  title: '单位',unresize:true,width:70}
		      ,{field:'price', title: '价格',unresize:true,width:70,templet: function(d){
					return parseFloat(d.price).toFixed(2);
		       }}
			  ,{field:'ordertype', title: '分类',unresize:true,width:70,templet: function(d){
			  		if(d.ordertype == 3){
			  			return "检验";
					}
					if(d.ordertype == 4){
						return "检查";
					}
			  }}
			,{field:'bind', title: '状态',unresize:true,width:70,templet: function(d){
					if(d.bind == 1){
						return "启用";
					}else{
						return "禁用";
					}
			}}
			  ,{fixed:'right', width:232, align:'center', templet:'#edit-box',title: '关联服务项目',unresize:true}
		    ]]
		    ,page: false
		    ,height:'full-155'
		    ,cellMinWidth:100
		    ,limit:20000
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var windowWidth = '500px';
	  var windowHeight = '420px';

	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  loadListFunction(true);
	//读取错误提示
    function _serverFail(){
    	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
		$("#synFl").attr("disabled",null).removeClass("layui-btn-disabled");
    }

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '加载中...';
		layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
	}


    /**
     * ajax预处理
     * @param id sumitid
    */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
	       		return false;
	        }
	    });
	}
	function _postForm(poststr){
		var postUrl = ctxPath+"/v/jcjy/save";
		ajaxValForm();
		$.getJSON(postUrl,poststr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('已更新',{time:1000},function(){
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	window.bindSelect = function (){
		$(document).unbind("click");
		$(document).bind("click", function (event) {
			var _con = $('.select-picker-options-wrp'); // 设置目标区域
			var _con2 = $('.select-picker-search-checked'); // 设置目标区域
			if (!_con2.is(event.target) && !_con.is(event.target) && _con.has(event.target).length === 0) { // Mark 1
				$('.select-picker-options-wrp').hide(); //淡出消失
			}
		})
	}

	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	});
	//触发事件
    var active = {
   		reload: function(){
	      	var that = this;
	      	loadListFunction();
	    }
		,synFl: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			$(that).attr("disabled","disabled").addClass("layui-btn-disabled");

			layer.msg('正在同步中...', {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			ajaxValForm();

			var orgid = authUser.grade == "3" ? authUser.porgid : (authUser.grade == "2" ? authUser.orgid : "")
			var orgname = authUser.grade == "3" ? authUser.porgname : (authUser.grade == "2" ? authUser.orgname : "")
			var orgcode = authUser.grade == "3" ? authUser.orgcode : (authUser.grade == "2" ? authUser.orgcode : "")
			$.getJSON(ctxPath+"/v/jcjy/sync","test=&orgid="+orgid+"&orgname="+orgname+"&orgcode="+orgcode,function(jsondata){
				if(jsondata.code=='200'){
					layer.msg("同步成功！",{time:2000},function(){
						$("#synFl").attr("disabled",null).removeClass("layui-btn-disabled");
						window.location.reload(true);
					});
				}else{
					layer.msg(jsondata.msg,{time:2000});
					$("#synFl").attr("disabled",null).removeClass("layui-btn-disabled");
				}

			});
		}
    	,refFl: function(){
	      	var that = this;
	      	if($(that).attr("disabled")=="disabled")return;	
			$(that).attr("disabled","disabled").addClass("layui-btn-disabled");
	      	loadMenuListFunction();
	    }
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
});