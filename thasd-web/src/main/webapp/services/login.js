function checkLoginFrame(){
	if(self.frameElement){
		if(self.frameElement.tagName=="IFRAME"){
			  var s = window.location.href;
			  window.parent.location.href = s;
		}
	}else{
		if(isdownload=="true"){
			window.location.href = downloadBrowserPath;
		}
	}
}
window.onload = function() {
	checkLoginFrame();
};
layui.use('layer', function(){//独立版的layer无需执行这一句
  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
  function des(a){return new b64().encode(a)};
  function getUParam(name,id) {
	    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
	    if (r != null) return unescape(r[2]); return ""; //返回参数值
  }
 
  //读取错误提示
  function _serverFail(){
	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
  }
  /**
   * load信息提示 带遮罩层
   * @param msg 提示信息
   * @code{default=加载中...}
   */
  function _loadMkInfo(msg){
  	if(msg==''||null==msg)msg = '加载中...';
  	layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); 
  }
  /**
   * ajax预处理
   * @param id sumitid
   */
  function ajaxValForm(id){
	$.ajaxSetup({
		error:function(x,e){
			_serverFail();
  	      	$(id).attr("disabled",null);
			return false;
		}
	});
  }
  /**
   * 提交表单
   * @param id 表单id
   * @code{.form、#form}
   */
  function _postForm(descode){
  	ajaxValForm("#loginBtn");
  	$.getJSON(getUParam("ctxPath","loginjs")+"/loginin",descode,function(jsondata){
		if(sessionStorage){
			// content-pear-tab-data
			// content-pear-tab-data-current
			sessionStorage.clear();
		}
  		if(jsondata.code=='200'){
			window.location.href= getUParam("ctxPath","loginjs")+"/index";
		}else{
			layer.msg(jsondata.msg,{time:2000});
  	      	$('#loginBtn').attr("disabled",null);
		}
  	});
  }
  //触发事件
  var active = {
  	loginBtn: function(){
      var that = this;
      $(that).attr("disabled","disabled");
      var flag = true; 
      layer.closeAll('tips');//关闭所有的tips层    
      $(".ant-input").each(function(){
  		var val = $.trim($(this).val());
  	    if(val==""){
  	    	flag = false;
  	    	$(this).val("");
  	    	layer.tips($(this).attr("msg"), '#'+$(this).attr("id"),{
  	    	  tipsMore: true
  	    	});
  	    	$(this).parent().parent().parent().parent().addClass("has-error");
  	      	$(that).attr("disabled",null);
  	    }
  	  });
      if(flag){
    	  _loadMkInfo("正在登陆...");
    	  var _jsession_des_id = des("username="+$('#username').val()+"&"+"password="+val.join(''));
    	  _postForm("_jsession_des_id="+_jsession_des_id);
      }
    }
  };
  var val = [];
  var lastCursorPos = 0;
  var lastInputLength = 0;
  var lastSelection = { start: 0, end: 0, length: 0 };
  var isComposing = false;

  // 获取光标位置
  function getCursorPosition(input) {
    if (input.selectionStart !== undefined) {
      return input.selectionStart;
    }
    return 0;
  }

  // 设置光标位置
  function setCursorPosition(input, pos) {
    if (input.setSelectionRange) {
      input.setSelectionRange(pos, pos);
    }
  }

  // 获取选择范围
  function getSelection(input) {
    if (input.selectionStart !== undefined && input.selectionEnd !== undefined) {
      return {
        start: input.selectionStart,
        end: input.selectionEnd,
        length: input.selectionEnd - input.selectionStart
      };
    }
    return { start: 0, end: 0, length: 0 };
  }

  // 处理输入中文等复合输入
  $('#password').on('compositionstart', function() {
    isComposing = true;
  });

  $('#password').on('compositionend', function() {
    isComposing = false;
  });

  // 处理粘贴事件
  $('#password').on('paste', function(e) {
    e.preventDefault();

    var input = this;
    var currentPos = getCursorPosition(input);
    var selection = getSelection(input);

    // 获取粘贴的文本
    var pastedText = '';
    if (e.originalEvent.clipboardData && e.originalEvent.clipboardData.getData) {
      pastedText = e.originalEvent.clipboardData.getData('text/plain');
    } else if (window.clipboardData && window.clipboardData.getData) {
      pastedText = window.clipboardData.getData('Text');
    }

    if (pastedText) {
      // 将粘贴的文本转换为字符数组
      var pastedChars = pastedText.split('');
      var insertPos;

      if (selection.length > 0) {
        // 有选择内容，替换选择的部分
        val.splice(selection.start, selection.length, ...pastedChars);
        insertPos = selection.start + pastedChars.length;
      } else {
        // 在光标位置插入粘贴的文本
        val.splice(currentPos, 0, ...pastedChars);
        insertPos = currentPos + pastedChars.length;
      }

      // 更新显示值
      var newDisplayValue = '●'.repeat(val.length);
      $(input).val(newDisplayValue);

      // 设置光标位置
      setCursorPosition(input, insertPos);
    }
  });

  // 处理键盘按下事件（在input事件之前）
  $('#password').on('keydown', function(e) {
    if (isComposing) return;

    var input = this;
    var currentPos = getCursorPosition(input);
    var selection = getSelection(input);
    var currentValue = $(input).val();

    // 记录按键前的状态
    lastCursorPos = currentPos;
    lastInputLength = currentValue.length;
    lastSelection = {
      start: selection.start,
      end: selection.end,
      length: selection.length
    };

    // 处理删除键
    if (e.keyCode === 8) { // Backspace
      e.preventDefault();

      if (selection.length > 0) {
        // 有选择内容，删除选择的部分
        val.splice(selection.start, selection.length);
        var newDisplayValue = '●'.repeat(val.length);
        $(input).val(newDisplayValue);
        setCursorPosition(input, selection.start);
      } else if (currentPos > 0) {
        // 删除光标前一个字符
        val.splice(currentPos - 1, 1);
        var newDisplayValue = '●'.repeat(val.length);
        $(input).val(newDisplayValue);
        setCursorPosition(input, currentPos - 1);
      }
      return false;
    }

    if (e.keyCode === 46) { // Delete
      e.preventDefault();

      if (selection.length > 0) {
        // 有选择内容，删除选择的部分
        val.splice(selection.start, selection.length);
        var newDisplayValue = '●'.repeat(val.length);
        $(input).val(newDisplayValue);
        setCursorPosition(input, selection.start);
      } else if (currentPos < currentValue.length) {
        // 删除光标后一个字符
        val.splice(currentPos, 1);
        var newDisplayValue = '●'.repeat(val.length);
        $(input).val(newDisplayValue);
        setCursorPosition(input, currentPos);
      }
      return false;
    }

    // 处理Ctrl+A全选
    if (e.ctrlKey && e.keyCode === 65) {
      // 允许全选，不阻止默认行为
      return true;
    }

    // 处理Ctrl+V粘贴
    if (e.ctrlKey && e.keyCode === 86) {
      // 允许粘贴，不阻止默认行为
      return true;
    }

    // 处理回车键
    if (e.keyCode === 13) {
      var pswstr = $.trim(val.join(''));
      if(pswstr.length == 0){
        layer.tips($("#password").attr("msg"), '#password',{
          tipsMore: true
        });
        $("#password").parent().parent().parent().parent().addClass("has-error");
        $("#loginBtn").attr("disabled",null);
      } else {
        $('#loginBtn').click();
      }
      return false;
    }
  });

  // 处理输入事件
  $(document).on('input propertychange', '#password', function(e){
    if (isComposing) return;

    var $input = $(this);
    var input = this;
    var currentValue = $input.val();
    var currentPos = getCursorPosition(input);

    // 如果输入框被清空（如Ctrl+A然后删除）
    if (currentValue === '') {
      val = [];
      lastSelection = { start: 0, end: 0, length: 0 };
      return;
    }

    // 查找新输入的字符（非●字符）
    var newChars = [];

    for (var i = 0; i < currentValue.length; i++) {
      if (currentValue[i] !== '●') {
        newChars.push(currentValue[i]);
      }
    }

    if (newChars.length > 0) {
      var insertPos;

      // 检查是否有之前记录的选择状态
      if (lastSelection.length > 0) {
        // 有选择内容，替换选择的部分
        val.splice(lastSelection.start, lastSelection.length, ...newChars);
        insertPos = lastSelection.start + newChars.length;

        // 清除选择状态
        lastSelection = { start: 0, end: 0, length: 0 };
      } else {
        // 在光标位置插入新字符
        val.splice(lastCursorPos, 0, ...newChars);
        insertPos = lastCursorPos + newChars.length;
      }

      // 更新显示值
      var newDisplayValue = '●'.repeat(val.length);
      $input.val(newDisplayValue);

      // 恢复光标位置
      setCursorPosition(input, insertPos);
    }
  });

  // 处理鼠标点击和其他操作，清除选择状态
  $('#password').on('mouseup keyup', function(e) {
    // 如果不是输入相关的按键，清除选择状态
    if (e.type === 'mouseup' || (e.type === 'keyup' &&
        e.keyCode !== 8 && e.keyCode !== 46 && e.keyCode !== 13 &&
        !(e.ctrlKey && (e.keyCode === 65 || e.keyCode === 86)) && !e.shiftKey)) {
      lastSelection = { start: 0, end: 0, length: 0 };
    }
  });
  $('.ant-btn').on('click', function(){
    var othis = $(this), method = othis.data('method');
    active[method] ? active[method].call(this, othis) : '';
  });
});