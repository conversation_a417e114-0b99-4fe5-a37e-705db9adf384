layui.use('layer', function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	function des(a){return new b64().encode(a)};
	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}

	//读取错误提示
	function _serverFail(){
		$("#msg").html('连接服务器失败,请稍后再试...');
		$("#code").html("500");
		$(".initbox").remove();
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postForm(descode){
		if(patientid  == "" || patientid  == null){
			$(".initbox").remove()
			return
		}
		ajaxValForm();
		$.getJSON(getUParam("ctxPath","loginjs")+"/fromxyy",descode,function(jsondata){
			if(sessionStorage){
				// content-pear-tab-data
				// content-pear-tab-data-current
				sessionStorage.clear();
			}
			if(jsondata.code=='200'){
				window.location.href= getUParam("ctxPath","loginjs")+"/v/pcenter?patientid="+patientid+"&form=his&referralid=";
			}else{
				$("#msg").html(jsondata.msg);
				$("#code").html(jsondata.code);
				$(".initbox").remove();
			}
		});
	}
	_postForm("username="+username+"&patientid="+patientid);
});