layui.use(['form', 'layedit', 'laydate','ztree','treeselect','menuIconPicker'], function(){
  var form = layui.form
  ,$ = layui.jquery
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate
  ,zFun =layui.treeselect;
  var iconPicker = layui.menuIconPicker;

  function initIconPicker() {
		iconPicker.render({
			elem: '#iconcode',
			type: 'fontClass',
			search: true,
			page: true,
			limit: 16
			,showHide: function (isShow){
				var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
				parent.layer.iframeAutoTop(index);//让层自适应iframe
			}
			,click: function(data) {
				var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
				parent.layer.iframeAutoTop(index);//让层自适应iframe
			}
			,success: function(d) {
			}

		});
  }
  function endes(a){return new b64().decode(a)};
  $(".initbox").remove();
  function getUParam(name,id) {
	    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
	    if (r != null) return unescape(r[2]); return ""; //返回参数值
  }
  var ctxPath = getUParam("ctxPath","adtjs");
  var id = getUParam("mainid","adtjs");
  /**
   * load信息提示 带遮罩层
   * @param msg 提示信息
   * @code{default=加载中...}
   */
  function _loadMkInfo(msg){
  	if(msg==''||null==msg)msg = '加载中...';
  	layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); 
  }
  //读取错误提示
  function _serverFail(){
	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
  }
  /**
   * ajax预处理
   * @param id sumitid
   */
  function ajaxValForm(){
	$.ajaxSetup({
		error:function(x,e){
			_serverFail();
			parent.resetSwClose(false);
			$("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
       		return false;
        }
    });
  }
  /**
   * 提交表单
   * @param id 表单id
   * @code{.form、#form}
   */
  function _postForm(poststr){
	var postUrl = ctxPath+"/v/menu/save";
	if(id != ""){
		postUrl = ctxPath+"/v/menu/edit";
	}
  	ajaxValForm();
  	$.getJSON(postUrl,poststr,function(jsondata){
  		if(jsondata.code=='200'){
  	    	layer.msg('保存数据成功',{time:1000},function(){
  	    		parent.layer.closeAll('iframe');
  	  			parent.reloadList();
  	    	});
		}else{
			layer.msg(jsondata.msg,{time:2000});
			$("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
			parent.resetSwClose(false);
		}
  	});
  }
  /**
   * 生成表单数据
   * @param id 表单id
   * @code{.form、#form}
   */
  function _getForm(){
  	ajaxValForm();
  	$.getJSON(ctxPath+"/v/menu/get","id="+id,function(jsondata){
  		if(jsondata.code=='200'){
  		  //表单初始赋值
  		  form.val('formtable', JSON.parse(JSON.stringify(jsondata.rd)));
  		  $("#sjcd").attr("ival",jsondata.rd.parentid);
  		  $("#sjcd").attr("nval",jsondata.rd.parentname);
  		  initTree();
  		  initIconPicker();
		}else{
			$("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
			layer.msg(jsondata.msg,{time:2000},function(){
				parent.layer.closeAll('iframe');
			});
		}
  	});
  }
  //触发事件
  var active = {
	cancel: function(){
		parent.layer.closeAll('iframe');
    }
  };
  $('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
	    active[method] ? active[method].call(this, othis) : '';
  });
  //自定义验证规则
  form.verify({
  });
  //监听提交
  form.on('submit(formsb)', function(data){
	parent.resetSwClose(true);
	_loadMkInfo("正在保存数据...");
	$("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
	var postData = JSON.stringify(data.field);
	_postForm(data.field);
    return false;
  });
  var getMenuNodes = function(){
	  return zNodesJsonString;
  }
  var zNodes = getMenuNodes();
  var initTree = function(){
	  zFun.initSelectTree(zNodes,"请选择上级模块","sjcd",true,false,"","menu",false);
  }
  if(id != ""){
	  _getForm();
  }else{
	  initIconPicker();
	  $("#sjcd").attr("ival",zNodes[0].id);
	  $("#sjcd").attr("nval",zNodes[0].name);
	  initTree();
  }
});