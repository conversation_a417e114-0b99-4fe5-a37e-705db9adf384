layui.use(['form','laytpl', 'layedit', 'laydate','ztree','treeselect'], function(){
    var form = layui.form
        ,$ = layui.jquery
        ,layer = layui.layer
        ,layedit = layui.layedit
        ,laydate = layui.laydate
        ,zFun =layui.treeselect;

    var laytpl = layui.laytpl;

    laydate.render({type: 'date',format:'yyyy-MM-dd',trigger: 'click',
        elem: '#birthday'
    });
    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }
    var ctxPath = getUParam("ctxPath","adtjs");
    var id = getUParam("mainid","adtjs");
    var optype = getUParam("optype","adtjs");

    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '加载中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    /**
     * 提交表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _addManage(idDatas){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/addManage",idDatas+"&pmtypecode=0&pmtype=管理中&ALIBABAKEY="+$("#alibabakey").val(),function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('纳入管理成功',{time:1000,shade: [0.001, '#ffffff']},function(){
                    window.location.reload(true);
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
        });
    }
    function checkPatient(idcard){
        var postUrl = ctxPath+"/v/patients/checkPatient";
        var poststr = "idcard="+idcard;
        ajaxValFormNull();
        $.getJSON(postUrl,poststr,function(jsondata){
            if(jsondata.code=='5001'){
                if(optype == "fast"){
                    layer.confirm('该患者已存在,是否直接纳入管理?', {
                        btn: ['是','否'] //按钮
                    }, function(index){
                        $("#idcard").val("");
                        layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                        layer.close(index)
                        _addManage("id="+jsondata.rd.id);
                    }, function(){
                       // $("#idcard").val("");
                    });
                }else{
                    // $("#idcard").val("");
                    layer.msg('该患者已存在',{time:1500},function(){});
                }
            }else{
                layer.msg(jsondata.msg,{time:1500},function(){});
            }
        });
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                parent.resetSwClose(false);
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                return false;
            }
        });
    }
    /**
     * 提交表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postForm(poststr){

        var postUrl = ctxPath+"/v/patients/save";
        if(id != ""){
            postUrl = ctxPath+"/v/patients/edit";
        }
        if(optype == "fast"){
            postUrl = ctxPath+"/v/patients/fast/save";
        }

        ajaxValForm();
        $.getJSON(postUrl,poststr,function(jsondata){
            if(jsondata.code=='5001'){
                if(optype == "fast"){
                    layer.confirm('该患者已存在,是否直接纳入管理?', {
                        btn: ['是','否'] //按钮
                    }, function(index){
                        layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                        layer.close(index)
                        _addManage("id="+jsondata.rd.id);
                    }, function(){
                        $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                    });
                }else{
                    layer.msg('保存数据成功',{time:1000},function(){
                        parent.layer.closeAll('iframe');
                        parent.reloadList();
                    });
                }
            }else if(jsondata.code=='200'){
                if(optype == "fast"){
                    $("#fastForm")[0].reset();
                    form.render(null,"formtable");
                    $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                    layer.msg('保存数据成功',{time:1500},function(){
                        window.location.reload(true);
                    });
                }else{
                    layer.msg('保存数据成功',{time:1000},function(){
                        parent.layer.closeAll('iframe');
                        parent.reloadList();
                    });
                }
            }else{
                layer.msg(jsondata.msg,{time:2000});
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                if(!optype == "fast"){
                    parent.resetSwClose(false);
                }
            }
        });
    }
    function setCheckLis(id,name,val,checked){
        if(val == 1){
            if(checked){
                $('input:checkbox[name="'+name+'"]').attr("disabled","");
                $("[name='"+name+"']").each(function(){
                    if($(this).val() == 1){
                        $(this)[0].checked = true;
                        $(this)[0].removeAttribute("disabled");
                    }else{
                        $(this)[0].checked = false;
                    }
                });
                form.render();
            }else{
                $('input:checkbox[name="'+name+'"]').removeAttr("disabled");
                form.render();
            }
        }
    }
    var setCheck = function (arr,name){
        $("[name='"+name+"']").each(function(){
            $(this)[0].checked = false;
            form.render();
        });
        $.each(arr, (i,v) => {
            let id = "#"+name.replace("-chk","");
            let node = $(`input[type="checkbox"][name^="${name}"][value="${v}"]`);
            if (node && node.length) {
                node[0].checked = true;
                form.render();
                setCheckLis(id,name,v,true);
            }
        });
        if(name == "guanli-chk"){
            $('input:checkbox[name="'+name+'"]').attr("disabled","");
           // form.render();
        }
    }

    /**
     * 生成表单数据
     * @param id 表单id
     * @code{.form、#form}
     */
    function _getForm(){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/get","id="+id,function(jsondata){
            if(jsondata.code=='200'){
                //表单初始赋值
                form.val('formtable', JSON.parse(JSON.stringify(jsondata.rd)));
                if(jsondata.rd.ywgms)setCheck(jsondata.rd.ywgms.split(","),"ywgms-chk");
                if(jsondata.rd.jwsjb)setCheck(jsondata.rd.jwsjb.split(","),"jwsjb-chk");
                if(jsondata.rd.jzsfq)setCheck(jsondata.rd.jzsfq.split(","),"jzsfq-chk");
                if(jsondata.rd.jzsmq)setCheck(jsondata.rd.jzsmq.split(","),"jzsmq-chk");
                if(jsondata.rd.jzsxdjm)setCheck(jsondata.rd.jzsxdjm.split(","),"jzsxdjm-chk");
                if(jsondata.rd.jzszn)setCheck(jsondata.rd.jzszn.split(","),"jzszn-chk");
                if(jsondata.rd.cjqk)setCheck(jsondata.rd.cjqk.split(","),"cjqk-chk");
                if(jsondata.rd.guanli){setCheck(jsondata.rd.guanli.split(","),"guanli-chk");}
                $("#sjarea").attr("ival",jsondata.rd.areaid);
                $("#sjarea").attr("nval",jsondata.rd.areaname);
                initTree();
            }else{
                $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
                layer.msg(jsondata.msg,{time:2000},function(){
                    parent.layer.closeAll('iframe');
                });
            }
            $(".initbox").remove();
            layer.closeAll("dialog");
        });
    }

    //触发事件
    var active = {
        cancel: function(){
            parent.layer.closeAll('iframe');
        }
    };

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    function IdCard(IdCard, type) {
        if (type === 1) {
            //获取出生日期
            let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
            return birthday;
        }
        if (type === 2) {
            //获取性别
            if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
                return "男";
            } else {
                return "女";
            }
        }
        if (type === 3) {
            //获取年龄
            var ageDate = new Date()
            var month = ageDate.getMonth() + 1
            var day = ageDate.getDate()
            var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
            if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
                age++
            }
            if (age <= 0) {
                age = 1
            }
            return age;
        }
    }
    $("#idcard").blur(function () {
        var idcard = $(this).val();
        if(idcard && idcard.length == 18){
            //当type=1时获取出生日期,type=2时获取性别,type=3时获取年龄
            var csrq = IdCard(idcard, 1);
            var sex = IdCard(idcard, 2);
            var age = IdCard(idcard, 3);
            $("#age").val(age);
            $("#birthday").val(csrq);
            $("#gender").val(sex);
            let node = $("#gendercode").find("option:contains('"+sex+"')");
            if(node){
                $("#gendercode").val(node.val());
                form.render('select');
            }
        }
        if($("#idcard").val().length>=15){
            // if(id == ""){
            //     _loadMkInfo("正在查验患者档案是否存在，请稍等...");
            //     checkPatient($("#idcard").val())
            // }
        }
    });
    //自定义验证规则
    form.verify({
        test: function(value){
            if(value.length <= 0){
                return 'test';
            }
        }
    });

    form.on('select(dicdata)', function(data){
        var oVid = "#"+data.elem.getAttribute("accept-name");
        $(oVid).val(data.othis.find("dd.layui-this").text());
    });

    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }
    form.on('checkbox(chkfilter)', function(data){
        let name = data.elem.name;
        let id = "#"+name.replace("-chk","");
        setCheckLis(id,name,data.value,data.elem.checked);
        var valId = "#" + name.replace("chk","val");
        if(data.elem.checked){
            if(data.value == 1){
                $(valId).val("1");
                $("#ywgmsqt").val("");
                $("#ywgmsqt").addClass("layui-disabled").attr("disabled");
                $("#ywgmsqt").prop("disabled",true);
            }else{
                let arr = $(valId).val().Split(",");
                arr.push(data.value);
                $(valId).val(arr.join((arr.length > 1 ? "," : "")));
            }
        }else{
            let arr = $(valId).val().Split(",");
            arr.removeArr(data.value);
            $(valId).val(arr.join((arr.length > 1 ? "," : "")));
        }
        if(data.value == 5 && name == "ywgms-chk"){
            if(data.elem.checked){
                $("#ywgmsqt").removeClass("layui-disabled").removeAttr("disabled");
            }else{
                $("#ywgmsqt").val("");
                $("#ywgmsqt").addClass("layui-disabled").attr("disabled");
                $("#ywgmsqt").prop("disabled",true);
            }
        }
    });

    var getAreaNodes = function(){
        return zNodesAreaJson;
    }
    var zAreaNodes = getAreaNodes();

    var initTree = function(){
        zFun.initSelectTree(zAreaNodes,"请选择行政区划","sjarea",false,true,"只能选择村、街道卫生机构","psarea",false);
    }
    //监听提交
    form.on('submit(formsb)', function(data){
        if(!optype == "fast"){
            parent.resetSwClose(false);
        }
        _loadMkInfo("正在保存数据...");
        $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
        var postData = JSON.stringify(data.field);
        _postForm(data.field);
        return false;
    });
    if(id != ""){
        _loadMkInfo();
        _getForm();
    }else{
        $("#sjarea").attr("ival","");
        $("#sjarea").attr("nval","");
        // $("#sjarea").attr("ival",zAreaNodes[0].id);
        // $("#sjarea").attr("nval",zAreaNodes[0].name);

        initTree();
        $(".initbox").remove();
    }
});