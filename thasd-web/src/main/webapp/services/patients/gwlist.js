var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){

	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var ctxPath = layui.getContextPath("ctxPath","listjs");
		var yglHeight = 128 + $(".search-box").height();

		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}
		//_loadMkInfo();
		var cols =  [[
			{type:'checkbox',fixed:'left'}
			,{field:'name',  title: '患者姓名',width: 86}
			,{field:'gender',  title: '性别',width:58}
			,{field:'age',  title: '年龄',width:58}
			,{field:'minzu',  title: '民族',width:65}
			,{field:'idcard',  title: '身份证号码',width: 171}
			,{field:'pmtype',  title: '状态',width:86}
			,{field:'orgname',  title: '所属单位',width: 174}
			,{field:'jyusername',  title: '所属医生',width: 86}
			,{align:'center', templet: '#bpgrade', width:72, title: '高血压'}
			,{align:'center', templet: '#dpgrade', width:72, title: '糖尿病'}
			,{align:'center', templet: '#lpgrade', width:72, title: '高血脂'}
			,{align:'center', templet: '#ascvd', width:108, title: 'ASCVD风险'}
			,{field:'prepgtime',  title: '上次评估时间',width: 159}
			,{field:'glorgname',  title: '当前管理单位',width: 174}
			,{field:'gljyusername',  title: '当前管理医生',width: 125}
		]];

		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/patients/pageLstByNot'
			,where: {
				name: $("#name").val()
				,islower:$("input[name='islower']:checked").val()
				,idcard:$("#idcard").val()
				,orgid:  authUser.orgid
				,bpgradecode: $("#bpgradecode").val()
				,dbgradecode: $("#dbgradecode").val()
				,lpgradecode: $("#lpgradecode").val()
				,jyuser: $("#jyuser").val()
				,gljyuser: $("#gljyuser").val()
				,jyusername: $("#jyusername").val()
				,gljyusername: $("#gljyusername").val()
			}
			,cols: cols
			,page: true
			,height: 'full-' + yglHeight
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['dropdown','layer','element','laydate','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var laydate = layui.laydate;
	var zFun =layui.treeselectTable;

	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}

	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
	});
	//触发事件
	var active = {
		readcard:function (){
			//传需要赋值input的ID
			var shensi = new ShenSi("idcard");
		},
		reload: function(){
			var that = this;
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	var _loadDic = function(){
		//console.log(JSON.stringify(zNodesJson[0].name));
		$("#scbm").attr("ival",authUser.orgid);
		$("#scbm").attr("nval",authUser.orgname);
		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","listQuery",false);
		zFun.selectTreeId("scbm",authUser.orgid);
		loadListFunction(true);

	}
	_loadDic();
});