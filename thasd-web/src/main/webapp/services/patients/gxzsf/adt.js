Array.prototype.of = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        return true;
    }
    return false;
};
var regexConfig = {
    positive: "^[0-9]\\d*$"//正整数 不带小数点
    , number: "^[0-9][0-9]*([\\.][0-9]{1,2})?$"//整数 包含小数点
    , number1: "^[1-9][0-9]*([\\.][0-9]{1,2})?$"//整数 包含小数点
}
 function buildRegex(regexPath) {
    var that = this;
    var op = "g";//全局搜索
    //op = op + "i";//忽略大小写
    return new RegExp(that.regexConfig[regexPath], op);
}
var detPatientData;

var tzyyCols = [[
    {unresize:true,type:'checkbox', width:70}
    ,{unresize:true, width:70, align:'center', toolbar:'#listtable-number',title:'序号'}
    ,{unresize:true,field: 'ywmc', title: '名称', }
    ,{unresize:true,field: 'ywyf', title: '用法', }
    ,{unresize:true,field: 'ywpl', title: '频率', }
    ,{unresize:true,field: 'ywyl', title: '用量', }
    ,{unresize:true,field: 'ywdw', title: '单位', }
    ,{hide:true,field: 'istz' }
    ,{hide:true,field: 'isvadd' }
    ,{unresize:true, width:129, align:'left', toolbar:'#listtable-opt',title: '操作'}
]];
var dqyycols = [[
    {unresize:true,type:'checkbox', width:70}
    ,{unresize:true, width:70, align:'center', toolbar:'#listtable-number',title:'序号'}
    ,{unresize:true,field: 'ywmc', title: '名称', }
    ,{unresize:true,field: 'ywyf', title: '用法', }
    ,{unresize:true,field: 'ywpl', title: '频率', }
    ,{unresize:true,field: 'ywyl', title: '用量', }
    ,{unresize:true,field: 'ywdw', title: '单位', }
    ,{hide:true,field: 'istz' }
    ,{hide:true,field: 'isvadd' }

    ,{unresize:true, width:129, align:'left', toolbar:'#listtable-opt',title: '操作'}
]];
layui.use(['form','table','laytpl', 'layedit', 'laydate','ztree','treeselect'], function(){
    var form = layui.form
        ,$ = layui.jquery
        ,layer = layui.layer
        ,layedit = layui.layedit
        ,laydate = layui.laydate
        ,zFun =layui.treeselect;
    var ptable = layui.table;
    var laytpl = layui.laytpl;

    laydate.render({type: 'date',format:'yyyy-MM-dd',trigger: 'click',
        elem: '#sfrq'
    });
    laydate.render({type: 'date',format:'yyyy-MM-dd',trigger: 'click',
        elem: '#xcsfrq'
    });

    $("#sfForm:input[type='number']").blur(function () {
        var id = $(this).attr("id");
        var value = $(this).val();
        var regex = $(this).attr("regex");
        if (value.length > 0 && regex && !that.buildRegex(regex).test(value)) {
            $(this).val("");
            return;
        }
    });

    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }
    var ctxPath = getUParam("ctxPath","adtjs");
    var id = getUParam("mainid","adtjs");
    var optype = getUParam("optype","adtjs");
    var lik = getUParam("lik","adtjs");
    var view = getUParam("view","adtjs");

    let layeroIndex,layeroId;
    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '加载中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    /**
     * 提交表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _addManage(idDatas){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/addManage",idDatas+"&pmtypecode=0&pmtype=管理中&ALIBABAKEY="+$("#alibabakey").val(),function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('纳入管理成功',{time:1000,shade: [0.001, '#ffffff']},function(){
                    window.location.reload(true);
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
        });
    }
    function checkPatient(idcard){
        var postUrl = ctxPath+"/v/patients/checkPatient";
        var poststr = "idcard="+idcard;
        ajaxValFormNull();
        $.getJSON(postUrl,poststr,function(jsondata){
            if(jsondata.code=='5001'){
                if(optype == "fast"){
                    layer.confirm('该患者已存在,是否直接纳入管理?', {
                        btn: ['是','否'] //按钮
                    }, function(index){
                        $("#idcard").val("");
                        layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                        layer.close(index)
                        _addManage("id="+jsondata.rd.id);
                    }, function(){
                       // $("#idcard").val("");
                    });
                }else{
                    // $("#idcard").val("");
                    layer.msg('该患者已存在',{time:1500},function(){});
                }
            }else{
                layer.msg(jsondata.msg,{time:1500},function(){});
            }
        });
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                parent.resetSwClose(false);
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                return false;
            }
        });
    }
    /**
     * 提交表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postForm(poststr){

        var postUrl = ctxPath+"/v/patients/gxzsf/save";
        if(id != ""){
            postUrl = ctxPath+"/v/patients/gxzsf/edit";
        }

        ajaxValForm();
        $.getJSON(postUrl,poststr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('保存数据成功',{time:1000},function(){
                    if(lik == "1"){
                        parent.layer.closeAll('iframe');
                    }else{
                        parent.layer.closeAll('iframe');
                        parent.reloadList();
                    }
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
            }
        });
    }
    function setCheckLis(id,name,val,checked){
        if(val == 1){
            if(checked){
                $('input:checkbox[name="'+name+'"]').attr("disabled","");
                $("[name='"+name+"']").each(function(){
                    if($(this).val() == 1){
                        $(this)[0].checked = true;
                        $(this)[0].removeAttribute("disabled");
                    }else{
                        $(this)[0].checked = false;
                    }
                });
                form.render();
            }else{
                $('input:checkbox[name="'+name+'"]').removeAttr("disabled");
                form.render();
            }
        }
    }
    var setCheck = function (arr,name){
        $("[name='"+name+"']").each(function(){
            $(this)[0].checked = false;
            form.render();
        });
        $.each(arr, (i,v) => {
            let id = "#"+name.replace("-chk","");
            let node = $(`input[type="checkbox"][name^="${name}"][value="${v}"]`);
            if (node && node.length) {
                node[0].checked = true;
                form.render();
                setCheckLis(id,name,v,true);
            }
        });
        if(name == "guanli-chk"){
            $('input:checkbox[name="'+name+'"]').attr("disabled","");
           // form.render();
        }
    }

    /**
     * 生成表单数据
     * @param id 表单id
     * @code{.form、#form}
     */
    function _getForm(){
        loadDqyyTable();
        loadTzyyTable();
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/gxzsf/get","id="+id,function(jsondata){
            if(jsondata.code=='200'){
                //表单初始赋值
                form.val('formtable', JSON.parse(JSON.stringify(jsondata.rd)));
                if(jsondata.rd.zhengzhuang)setCheck(jsondata.rd.zhengzhuang.split(","),"zhengzhuang-chk");
                $("#sjarea").attr("ival",jsondata.rd.areaid);
                $("#sjarea").attr("nval",jsondata.rd.areaname);
                initTree();
            }else{
                $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
                layer.msg(jsondata.msg,{time:2000},function(){
                    parent.layer.closeAll('iframe');
                });
            }
            $(".initbox").remove();
            layer.closeAll("dialog");
        });
    }

    ptable.on('tool(listtable1)', function(obj){
        listtable(obj);
    });
    ptable.on('tool(listtable2)', function(obj){
        listtable(obj);
    });
    function listtable(obj){
        var that = this;
        var data = obj.data;
        if(obj.event === 'editYY'){
            if($(that).attr("disabled")=="disabled")return;
            openAddFormBox("yyBox",data.istz == 0 ? "当前用药" : "调整用药",data.istz,data);
        } else if(obj.event === 'delYY'){
            if($(that).attr("disabled")=="disabled")return;
            if(id ==''){
                layer.confirm('你确认移除这条数据吗?', function(index){
                    layer.close(index)
                    $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
                    obj.del();
                    if(data.istz == 0){
                        var newData = ptable.cache["dqyyTableTbl"];
                        console.log(newData)
                        //loadDqyyTableData(newData);
                    }else{
                        var newData = ptable.cache["tzyyTableTbl"];
                        console.log(newData)
                        //loadTzyyTableData(newData);
                    }
                });
            }else{
                layer.confirm('你确认删除这条数据吗?', function(index){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    layer.close(index)
                    $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
                    _delYY("guid="+data.guid,data.istz);
                });
            }
        }
    }

    //触发事件
    var active = {
        cancel: function(){
            parent.layer.closeAll('iframe');
        }
        ,add1: function(){
            var that = this;
            openAddFormBox("yyBox","当前用药",0);
        }
        ,add2: function(){
            var that = this;
            openAddFormBox("yyBox","调整用药",1);
        }
        ,batchdel1: function(){
            var table = layui.table;
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            var checkCkbox = table.checkStatus('dqyyTableTbl'),data = checkCkbox.data;
            var len = checkCkbox.data.length;
            var idDatas = "";
            var idNum = [];
            for(var i=0 , l = len; i < l; i++){
                if(i==0){
                    idNum.push(i+1);
                    idDatas += "guid="+checkCkbox.data[i].guid
                }else{
                    idNum.push(i+1);
                    idDatas += "&guid="+checkCkbox.data[i].guid
                }
            }
            if(len == 0){
                layer.msg('请选择您将要删除的记录',{time:2000});
                return false;
            } else{

                var info = '些';
                if(len==1)info='条';

                if(id ==''){
                    layer.confirm('你确认移除这'+info+'记录吗？', {
                        btn: ['确认','取消'] //按钮
                    }, function(index){layer.close(index)
                        $("#batchdel1").attr("disabled","disabled").addClass("layui-btn-disabled");
                        var newData = ptable.cache["dqyyTableTbl"];
                        console.log(newData)
                        newData.map((item, index) => {
                            if (idNum.of(item.LAY_INDEX)) {
                                newData.splice(index, 1);
                            }
                        });
                        loadDqyyTableData(newData);
                    }, function(){
                    });
                }else{
                    layer.confirm('你确认删除这'+info+'记录吗？', {
                        btn: ['确认','取消'] //按钮
                    }, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
                        $("#batchdel1").attr("disabled","disabled").addClass("layui-btn-disabled");
                        _delYY(idDatas,0);
                    }, function(){
                    });
                }

            }
        }
        ,batchdel2: function(){
            var that = this;
            var table = layui.table;
            if($(that).attr("disabled")=="disabled")return;
            var checkCkbox = table.checkStatus('tzyyTableTbl'),data = checkCkbox.data;
            var len = checkCkbox.data.length;
            var idDatas = "";
            for(var i=0 , l = len; i < l; i++){
                if(i==0){
                    idDatas += "guid="+checkCkbox.data[i].guid
                }else{
                    idDatas += "&guid="+checkCkbox.data[i].guid
                }
            }
            if(len == 0){
                layer.msg('请选择您将要删除的记录',{time:2000});
                return false;
            } else{

                var info = '些';
                if(len==1)info='条';

                if(id ==''){

                    layer.confirm('你确认移除这'+info+'记录吗？', {
                        btn: ['确认','取消'] //按钮
                    }, function(index){layer.close(index)
                        $("#batchdel2").attr("disabled","disabled").addClass("layui-btn-disabled");
                        var newData = ptable.cache["tzyyTableTbl"];
                        console.log(newData)
                        newData.map((item, index) => {
                            if (idDatas.indexOf(item.guid) >= 0) {
                                newData.splice(index, 1);
                            }
                        });
                        loadTzyyTableData(newData);
                    }, function(){
                    });
                }else{

                    layer.confirm('你确认删除这'+info+'记录吗？', {
                        btn: ['确认','取消'] //按钮
                    }, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
                        $("#batchdel2").attr("disabled","disabled").addClass("layui-btn-disabled");
                        _delYY(idDatas,1);
                    }, function(){
                    });
                }

            }
        }
        ,selectDrugs: function() {
            layIndex1 = layer.open({
                title:['选择药品']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioDrugs', 'no']
                ,end:function(){
                    console.log(drugData)
                    if(drugData){
                        var formBoxId = "#yyBox";
                        $(formBoxId + " #ywmc").val(drugData.dname);
                        //$(formBoxId + " #ywyl").val(drugData.dguige);
                        //$(formBoxId + " #ywdw").val(drugData.dzxjldw);
                    }
                }
            });
        }
        ,selectPatients: function() {
            layIndex1 = layer.open({
                title:['选择患者']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioPatients', 'no']
                ,end:function(){
                    //console.log(detPatientData)
                    if(detPatientData){
                        $("#ryguid").val(detPatientData.id);
                        $("#name").val(detPatientData.name);
                    }
                }
            });
        }

    };

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    function IdCard(IdCard, type) {
        if (type === 1) {
            //获取出生日期
            let birthday = IdCard.substring(6, 10) + "-" + IdCard.substring(10, 12) + "-" + IdCard.substring(12, 14)
            return birthday;
        }
        if (type === 2) {
            //获取性别
            if (parseInt(IdCard.substr(16, 1)) % 2 === 1) {
                return "男";
            } else {
                return "女";
            }
        }
        if (type === 3) {
            //获取年龄
            var ageDate = new Date()
            var month = ageDate.getMonth() + 1
            var day = ageDate.getDate()
            var age = ageDate.getFullYear() - IdCard.substring(6, 10) - 1
            if (IdCard.substring(10, 12) < month || IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day) {
                age++
            }
            if (age <= 0) {
                age = 1
            }
            return age;
        }
    }
    $("#idcard").blur(function () {
        var idcard = $(this).val();
        if(idcard && idcard.length == 18){
            //当type=1时获取出生日期,type=2时获取性别,type=3时获取年龄
            var csrq = IdCard(idcard, 1);
            var sex = IdCard(idcard, 2);
            var age = IdCard(idcard, 3);
            $("#age").val(age);
            $("#birthday").val(csrq);
            $("#gender").val(sex);
            let node = $("#gendercode").find("option:contains('"+sex+"')");
            if(node){
                $("#gendercode").val(node.val());
                form.render('select');
            }
        }
        if($("#idcard").val().length>=15){
            if(id == ""){
                _loadMkInfo("正在查验患者档案是否存在，请稍等...");
                checkPatient($("#idcard").val())
            }
        }
    });
    //自定义验证规则
    form.verify({
        formChk: function(value, item){ //value：表单的值、item：表单的DOM对象
            //console.log(item)
            let name = item.name;
            let chkLength = $(":input[name='"+name+"']:checked").length;
            if(chkLength == 0){
                return '请至少选择一个选项!';
            }
        }
        ,formRadio: function(value, item){ //value：表单的值、item：表单的DOM对象
            //console.log(item)
            let name = item.name;
            let chkLength = $(":input[name='"+name+"']:checked").length;
            if(chkLength == 0){
                return '请至少选择一个选项!';
            }
        }
    });

    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }
    form.on('checkbox(chkfilter)', function(data){
        let name = data.elem.name;
        let id = "#"+name.replace("-chk","");
        setCheckLis(id,name,data.value,data.elem.checked);
        var valId = "#" + name.replace("chk","val");

        if(data.elem.checked){
            if(data.value == 1){
                $(valId).val("1");
                $("#zzqt").val("");
                $("#zzqt").addClass("layui-disabled").attr("disabled");
                $("#zzqt").prop("disabled",true);
            }else{
                let arr = $(valId).val().Split(",");
                arr.push(data.value);
                $(valId).val(arr.join((arr.length > 1 ? "," : "")));
            }
        }else{
            let arr = $(valId).val().Split(",");
            arr.removeArr(data.value);
            $(valId).val(arr.join((arr.length > 1 ? "," : "")));
        }

        if(data.value == 0 && name == "zhengzhuang-chk"){
            if(data.elem.checked){
                $("#zzqt").removeClass("layui-disabled").removeAttr("disabled");
            }else{
                $("#zzqt").val("");
                $("#zzqt").addClass("layui-disabled").attr("disabled");
                $("#zzqt").prop("disabled",true);
            }
        }
    });

    form.on('select(dicdata)', function(data){
        var oVid = "#"+data.elem.getAttribute("accept-name");
        $(oVid).val(data.othis.find("dd.layui-this").text());
    });

    form.on('select(ywblfy)', function(data){
        if(data.value == 2){
            $("#ywblfyy").removeClass("layui-disabled").removeAttr("disabled");
            $("#ywblfyy").prop("lay-verify","required");
        }else{
            $("#ywblfyy").val("");
            $("#ywblfyy").addClass("layui-disabled").attr("disabled");
            $("#ywblfyy").removeAttr("lay-verify");
            $("#ywblfyy").prop("disabled",true);
        }

    });

    form.on('select(sfzz)', function(data){
        console.log(data)
        if(data.value == 2){
            $("#zzyy").removeClass("layui-disabled").removeAttr("disabled");
            $("#zzjgjks").removeClass("layui-disabled").removeAttr("disabled");
            $("#zzlxr").removeClass("layui-disabled").removeAttr("disabled");
            $("#zzlxfs").removeClass("layui-disabled").removeAttr("disabled");
            $("#zzjg").removeClass("layui-disabled").removeAttr("disabled");

            $("#zzyy").attr("lay-verify","required");
            $("#zzjgjks").attr("lay-verify","required");
            $("#zzlxr").attr("lay-verify","required");
            $("#zzlxfs").attr("lay-verify","required");
            $("#zzjg").attr("lay-verify","required");
            form.render('select');
        }else{

            $("#zzyy").val("");
            $("#zzjgjks").val("");
            $("#zzlxr").val("");
            $("#zzlxfs").val("");
            $("#zzjg").val("");

            $("#zzyy").addClass("layui-disabled").attr("disabled");
            $("#zzjgjks").addClass("layui-disabled").attr("disabled");
            $("#zzlxr").addClass("layui-disabled").attr("disabled");
            $("#zzlxfs").addClass("layui-disabled").attr("disabled");
            $("#zzjg").addClass("layui-disabled").attr("disabled");

            $("#zzyy").attr("lay-verify","");
            $("#zzjgjks").attr("lay-verify","");
            $("#zzlxr").attr("lay-verify","");
            $("#zzlxfs").attr("lay-verify","");
            $("#zzjg").attr("lay-verify","");

            $("#zzyy").prop("disabled",true);
            $("#zzjgjks").prop("disabled",true);
            $("#zzlxr").prop("disabled",true);
            $("#zzlxfs").prop("disabled",true);
            $("#zzjg").prop("disabled",true);

            form.render('select');


        }

    });

    function loadDqyyTable(){
        layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
            var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
            var table = layui.table;
            var ctxPath = layui.getContextPath("ctxPath","adtjs");
            var mainid = getUParam("mainid","adtjs");
            var cols = dqyycols;
            var thisTable = table.render({
                done: function(res, curr, count){
                    bindClick();
                    setTimeout(function (){
                        // table.resize('dqyyTableTbl');
                    },500)
                },
                toolbar:  '#toolbar'
                ,defaultToolbar: []
                ,elem: '#dqyyTable'
                ,id: 'dqyyTableTbl'
                ,url:ctxPath+'/v/patients/gxzsf/drugs'
                ,where: {
                    sfguid: mainid
                    ,istz:0
                }
                ,cols: cols
                ,page: false
                ,height:315
                ,cellMinWidth:70
                ,limit:100000000000
            });
        });
    }

    function loadDqyyTableData(loadData){
        layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
            var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
            var table = layui.table;
            var cols = dqyycols;
            var thisTable = table.render({
                done: function(res, curr, count){
                    bindClick();
                    setTimeout(function (){
                        // table.resize('dqyyTableTbl');
                    },500)
                },
                toolbar:  '#toolbar'
                ,defaultToolbar: []
                ,elem: '#dqyyTable'
                ,id: 'dqyyTableTbl'
                ,data: loadData
                ,cols: cols
                ,page: false
                ,height:315
                ,cellMinWidth:70
                ,limit:100000000000
            });
        });
    }

    function loadTzyyTableData(loadData){
        layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
            var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
            var table = layui.table;
            var cols = tzyyCols;
            var thisTable = table.render({
                done: function(res, curr, count){
                    bindClick();
                    setTimeout(function (){
                        // table.resize('dqyyTableTbl');
                    },500)
                },
                toolbar:  '#toolbar2'
                ,defaultToolbar: []
                ,elem: '#tzyyTable'
                ,id: 'tzyyTableTbl'
                ,data: loadData
                ,cols: cols
                ,page: false
                ,height:315
                ,cellMinWidth:70
                ,limit:100000000000
            });
        });
    }

    function loadTzyyTable(){
        layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
            var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
            var table = layui.table;
            var ctxPath = layui.getContextPath("ctxPath","adtjs");
            var mainid = getUParam("mainid","adtjs");
            var cols = tzyyCols;
            var thisTable = table.render({
                done: function(res, curr, count){
                    bindClick();
                    setTimeout(function (){
                        // table.resize('dqyyTableTbl');
                    },500)
                },
                toolbar:  '#toolbar2'
                ,defaultToolbar: []
                ,elem: '#tzyyTable'
                ,id: 'tzyyTableTbl'
                ,url:ctxPath+'/v/patients/gxzsf/drugs'
                ,where: {
                    sfguid: mainid
                    ,istz:1
                }
                ,cols: cols
                ,page: false
                ,height:315
                ,cellMinWidth:70
                ,limit:100000000000
            });
        });
    }

    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postZdForm(postStr,index,formBoxId,istz){
        console.log(postStr,istz,id)
        if(id != ""){
            ajaxValForm();
            var postUrl = ctxPath+"/v/patients/gxzsf/drugsSaveOrUpdate";
            $.getJSON(postUrl,postStr,function(jsondata){
                if(jsondata.code=='200'){
                    layer.msg('保存成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                        if(istz == 0){
                            loadDqyyTable();
                        }else{
                            loadTzyyTable();
                        }
                        if(index){layer.close(index);}
                    });
                }else{
                    layer.msg(jsondata.msg,{time:2000});
                }
            });
        }else{
            if(index){layer.close(index);}
            if(istz == 0){
                var newData = ptable.cache["dqyyTableTbl"];
                newData.push(postStr);
                loadDqyyTableData(newData);
            }else{
                var newData = ptable.cache["tzyyTableTbl"];
                newData.push(postStr);
                loadTzyyTableData(newData);
            }

            layer.msg("数据已缓存",{time:1000},function () {
            });
        }
    }

    function openAddFormBox(formBoxId,title,istz,editData){
        $("#"+formBoxId).show();
        document.forms[formBoxId].reset();
        $("#" +formBoxId +" input.ipt-hidden").each(function(){
            $(this)[0].value = "";
        });
        if(editData){
            form.val(formBoxId,editData)
            form.render(null, formBoxId);
        }else{
            form.val(formBoxId,{guid:"",sfGuid:id,istz:istz,isvadd:0});
        }
        //执行重载
        layer.open({
            title:[title]
            ,type:1
            ,area: ["600px","500px"]
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){
                //监听提交
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    _postZdForm(postStr,index,formBoxId,istz);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }

    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _delYY(postStr,istz){
        ajaxValForm();
        var postUrl = ctxPath+"/v/patients/gxzsf/delDrugs";
        $.getJSON(postUrl,postStr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('删除成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    if(istz == 0){
                        loadDqyyTable();
                    }else{
                        loadTzyyTable();
                    }
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
            $("#batchdel1").attr("disabled",null).removeClass("layui-btn-disabled");
            $("#batchdel2").attr("disabled",null).removeClass("layui-btn-disabled");

        });
    }

    window.bindClick = function (){
        $('.layui-btn').unbind("click");
        $('.layui-input').unbind("click");

        $('.layui-btn').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-input').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }

    var getAreaNodes = function(){
        return zNodesAreaJson;
    }
    var zAreaNodes = getAreaNodes();

    var initTree = function(){
        zFun.initSelectTree(zAreaNodes,"请选择行政区划","sjarea",true,true,"只能选择村、街道卫生机构","psarea",false);
    }
    //监听提交
    form.on('submit(formsb)', function(data){
        console.log(data.field)
        console.log(form.val("formtable"))
        var postData = data.field;

        var newData = ptable.cache["dqyyTableTbl"];
        var newData2 = ptable.cache["tzyyTableTbl"];
        layui.each(newData2, function(index,item){
            newData.push(item);
        });
        postData["drugs"] = JSON.stringify(newData);
        console.log(postData);
        _loadMkInfo("正在保存数据...");
        $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
        _postForm(postData);
        return false;
    });
    if(id != ""){
        _loadMkInfo();
        _getForm();
    }else{
        loadDqyyTable();
        loadTzyyTable();
        $("#sjarea").attr("ival","");
        $("#sjarea").attr("nval","");
        // $("#sjarea").attr("ival",zAreaNodes[0].id);
        // $("#sjarea").attr("nval",zAreaNodes[0].name);

        initTree();
        $(".initbox").remove();
    }
});