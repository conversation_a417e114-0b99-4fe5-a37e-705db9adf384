layui.use(['layer','laytpl', 'loading', 'form', 'step','upload','element'], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        element = layui.element,
        form = layui.form,
        step = layui.step;
    var laytpl = layui.laytpl;
    var upload = layui.upload;
    var loading = layui.loading;
    var uploadResult;
    var ctxPath = layui.getContextPath("ctxPath","detevaljs");
    var source = layui.getContextPath("source","detevaljs");
    if(!source || source != "out"){
        $(".initbox").remove();
    }

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    form.verify({
        bindRequired: function(value, item){ //value：表单的值、item：表单的DOM对象
            // console.log(value,$(item).attr("bind"))
            let bindId = "#" + $(item).attr("bind");
            let bindValue = $(bindId).val();
            let bindLength = bindValue.length;
            if(value.length == 0 && bindLength == 0){
                return '请至少输入一个血糖检测值!';
            }
        }
    });

    step.render({
        elem: '#stepForm',
        filter: 'stepForm',
        width: '100%',
        stepWidth: '600px',
        height: '100%',
        isLayer: true,
        stepItems: [{
            title: '上传体检患者名单附件'
        }, {
            title: '解析与校验患者数据'
        }, {
            title: '导入结果'
        }]
    });

    //触发事件
    var active = {
        pre: function (){
            var url = ctxPath + window.location.pathname;
            window.location.href = url;
            return false;
        }
       ,replay: function (){
            var url = ctxPath + window.location.pathname;
            window.location.href = url;
            return false;
        }
    };

    var xhrOnProgress=function(fun) {
        xhrOnProgress.onprogress = fun; //绑定监听
        //使用闭包实现监听绑
        return function() {
            //通过$.ajaxSettings.xhr();获得XMLHttpRequest对象
            var xhr = $.ajaxSettings.xhr();
            //判断监听函数是否为函数
            if (typeof xhrOnProgress.onprogress !== 'function')
                return xhr;
            //如果有监听函数并且xhr对象支持绑定时就把监听函数绑定上去
            if (xhrOnProgress.onprogress && xhr.upload) {
                xhr.upload.onprogress = xhrOnProgress.onprogress;
            }
            return xhr;
        }
    }
    upload.render({
        elem : '#upload',
        url : ctxPath+"/exrule/uploadJktj"
        ,xhr:xhrOnProgress
        ,progress:function(value){
            console.log(value)
            element.progress('up-progress', value+'%')
        }
        ,before : function(obj) {
            $("#up-progress").css({"display":"block"});
            $("#upload").attr("disabled","disabled").addClass("layui-btn-disabled").removeClass("layui-btn-normal");
            $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
            obj.preview(function(index, file, result) {
                console.log(file.name);
                console.log(result);
                //$("#filenameshow").text(file.name);
                $("#attachmentname").val(file.name);
            });
        }
        ,accept: 'file'
        ,acceptMime:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel'
        ,exts:'xlsx|xls'
        ,done : function(res) {
            console.log(res);
            if (res.code == '200') {
                uploadResult = res;
                element.progress('up-progress', '上传成功');
                setTimeout(function () {
                    $("#up-progress").css({"display":"none"});
                    $("#filepath").val(res.url);
                    $("#upload").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-btn-normal");
                    $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                },1000);
            }else{
                $("#up-progress").css({"display":"none"});
                $("#upload").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-btn-normal");
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                layer.msg(res.msg);
            }
        },
        error : function() {
        }
    });

    function setResult(data){
        var getTpl = document.getElementById('result-card').innerHTML
            ,view = document.getElementById('result-card-box');
        laytpl(getTpl).render(data, function(html){
            view.innerHTML = html;
        });
        $('.layui-btn').on('click', function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }
    function readXls(){
        loading.Load(1, "正在解析数据,请稍后...");
        var postUrl = ctxPath+"/exrule/readJktj";
        var postData =  "filepath=" + uploadResult.url;

        $.ajaxSetup({
            error:function(x,e){
                let data = {"code":500,"msg":"连接服务器失败,请稍后再试..."};
                setResult(data);
                loading.loadRemove(500 ,function (){
                    step.next('#stepForm');
                });
                return false;
            }
        });
        $.getJSON(postUrl,postData,function(jsondata){
            if(jsondata.code=='200'){
                let data = {"code":200,"msg":jsondata.desc,"noList":jsondata.noList,"synclist":jsondata.synclist};
                setResult(data);
            }else{
                let data = {"code":500,"msg":jsondata.msg};
                setResult(data);
            }
            loading.loadRemove(500 ,function (){
                step.next('#stepForm');
            });
        });
    }

    form.on('submit(formStep)', function(data) {
        readXls();
        step.next('#stepForm');
        return false;
    });

    form.on('submit(formStep2)', function(data) {
        return false;
    });
});