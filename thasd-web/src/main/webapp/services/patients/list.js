String.prototype.Split = function (s) {
	return this.split(s).filter(item => item != '');
}
Array.prototype.removeArr = function(val) {
	var index = this.indexOf(val);
	if (index > -1) {
		this.splice(index, 1);
	}
};
var bindPatientData;
var bindDeviceTable;
var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table','form'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var form = layui.form;
		var ctxPath = layui.getContextPath("ctxPath","listjs");
		var gwServerUrl = layui.getContextPath("gwServerUrl","listjs");
		var jyServerUrl = layui.getContextPath("jyServerUrl","listjs");
		var pmtypecode = layui.getContextPath("pmtypecode","listjs");
		var model = layui.getContextPath("model","listjs");
		var isbfz = layui.getContextPath("isbfz","listjs");

		var yglHeight = 128 + $(".search-box").height();

		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}

		_loadMkInfo();
		var nomalCos =  [[
			{type:'checkbox',fixed:'left',checkField:'pmtypecode',checkDis:true}
			,{field:'name',  title: '患者姓名',width: 100}
			,{field:'gender',  title: '性别',width:65}
			,{field:'age',  title: '年龄',width:65}
			,{field:'minzu',  title: '民族',width:65}
			,{field:'idcard',  title: '身份证号码',width: 171}
			,{field:'pmtype',  title: '状态',width:100}
			,{field:'orgname',  title: '所属单位'}
			,{field:'lxdh',  title: '联系电话'}
			,{field:'jtzz',  title: '家庭住址'}
			,{field:'areaname',  title: '所属区域'}
			,{fixed:'right', width:210, align:'center', toolbar:'#listtable-opt',title: '操作'}
		]];
		var cols = nomalCos;
		if(model == "ygl"){

			cols =  [[
				{type:'checkbox',fixed:'left'}
				,{field:'name',  title: '患者姓名',width: 86}
				,{field:'gender',  title: '性别',width:58}
				,{field:'age',  title: '年龄',width:58}
				,{field:'minzu',  title: '民族',width:65}
				,{field:'idcard',  title: '身份证号码',width: 171}
				,{field:'pmtype',  title: '状态',width:86}
				,{field:'orgname',  title: '所属单位',width: 174}
				,{field:'jyusername',  title: '所属医生',width: 86}
				,{align:'center', templet: '#bpgrade', width:72, title: '高血压'}
				,{align:'center', templet: '#dpgrade', width:72, title: '糖尿病'}
				,{align:'center', templet: '#lpgrade', width:72, title: '高血脂'}
				,{align:'center', templet: '#ascvd', width:108, title: 'ASCVD风险'}
				,{field:'prepgtime',  title: '上次评估时间',width: 159}
				,{field:'glorgname',  title: '当前管理单位',width: 174}
				,{field:'gljyusername',  title: '当前管理医生',width: 125}
				,{fixed:'right', width:190, align:'left', toolbar:'#listtable-opt',title: '操作'}
			]];
			if(isbfz == "1"){
				cols =  [[
					{type:'checkbox',fixed:'left'}
					,{field:'name',  title: '患者姓名',width: 86}
					,{field:'gender',  title: '性别',width:58}
					,{field:'age',  title: '年龄',width:58}
					,{field:'minzu',  title: '民族',width:65}
					,{field:'idcard',  title: '身份证号码',width: 171}
					,{field:'pmtype',  title: '状态',width:86}
					,{field:'orgname',  title: '所属单位',width: 174}
					,{field:'jyusername',  title: '所属医生',width: 86}
					,{field:'bfzname',  title: '并发症',width: 174}
					,{align:'center', templet: '#bpgrade', width:72, title: '高血压'}
					,{align:'center', templet: '#dpgrade', width:72, title: '糖尿病'}
					,{align:'center', templet: '#lpgrade', width:72, title: '高血脂'}
					,{align:'center', templet: '#ascvd', width:108, title: 'ASCVD风险'}
					,{field:'prepgtime',  title: '上次评估时间',width: 159}
					,{field:'glorgname',  title: '当前管理单位',width: 174}
					,{field:'gljyusername',  title: '当前管理医生',width: 125}
					,{fixed:'right', width:190, align:'left', toolbar:'#listtable-opt',title: '操作'}
				]];
			}
		}

		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/patients/list'
			,where: {
				name: $("#name").val()
				,islower:$("input[name='islower']:checked").val()
				,pmtypecode:pmtypecode
				,model:model
				,idcard:$("#idcard").val()
				,orgid: $("#scbmHide").val()
				,glorgid: model == "ygl" ? (authUser.grade=="1"  ? $("#glorgidHide").val() : authUser.orgid) : ""
				,bpgradecode: $("#bpgradecode").val()
				,dbgradecode: $("#dbgradecode").val()
				,lpgradecode: $("#lpgradecode").val()
				,adgradecode: $("#adgradecode").val()
				,jyuser: $("#jyuser").val()
				,gljyuser: $("#gljyuser").val()
				,jyusername: $("#jyusername").val()
				,gljyusername: $("#gljyusername").val()
				,bfz: $("#bfz").val()
				,isbfz: isbfz == "1" ? "1" : ""
			}
			,cols: cols
			,page: true
			,height: model == "ygl" ? 'full-' + yglHeight : 'full-155'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['dropdown','layer','element','laydate','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var laydate = layui.laydate;
	var zFun =layui.treeselectTable;
	var dropdown = layui.dropdown;

	dropdown.render({
		elem: '.zxzBtn'
		,data: [{
			title: '向上协诊'
			,id: 101
		},{
			title: '向下转出'
			,id: 102
		}]
		,click: function(obj){
		}
		,style: 'width: 105px;'
	});

	var moreData = [{
		title: '导出已管理名单'
		,id: 101
	},{
		title: '导出花名册'
		,id: 102
	},{
		title: '变更患者伴并发症状态'
		,id: 104
	}];

	if(authUser.grade=="2"){
		moreData = [{
			title: '患者设备绑定'
			,id: 105
		},{
			title: '导出已管理名单'
			,id: 101
		},{
			title: '导出花名册'
			,id: 102
		},{
			title: '变更当前管理医生'
			,id: 103
		},{
			title: '变更患者伴并发症状态'
			,id: 104
		}];
	}
	dropdown.render({
		elem: '.more-btn'
		,data: moreData
		,click: function(obj){
			console.log(obj)
			if(obj.id == 101){
				var othis = $(this), method = 'exportYgl';
				active[method].call(this, othis)
			}
			if(obj.id == 102){
				// var othis = $(this), method = 'export';
				var othis = $(this), method = 'export2';
				active[method].call(this, othis)
			}
			if(obj.id == 103){
				var othis = $(this), method = 'changeDocter';
				active[method].call(this, othis)
			}
			if(obj.id == 104){
				var othis = $(this), method = 'changeBbfzInfo';
				active[method].call(this, othis)
			}
			if(obj.id == 105){
				var othis = $(this), method = 'lotpdvlist';
				active[method].call(this, othis)
			}
		}
		,style: 'width: 165px;'
	});
	// dropdown.render({
	// 	elem: '.more-btn-sf'
	// 	,data: [{
	// 		title: '公卫随访'
	// 		,id: 201
	// 	},{
	// 		title: '高血脂随访'
	// 		,id: 202
	// 	}]
	// 	,click: function(obj){
	// 		console.log(obj)
	// 		if(obj.id == 201){
	// 			var othis = $(this), method = 'sf';
	// 			active[method].call(this, othis)
	// 		}
	// 		if(obj.id == 202){
	// 			var othis = $(this), method = 'gxzsf';
	// 			active[method].call(this, othis)
	// 		}
	// 	}
	// 	,style: 'width: 165px;'
	// });
	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	var gwServerUrl = layui.getContextPath("gwServerUrl","listjs");
	var jyServerUrl = layui.getContextPath("jyServerUrl","listjs");
	var model = layui.getContextPath("model","listjs");
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	function ajaxValFormSf(guid){
		$.ajaxSetup({
			error:function(x,e){
				window.open("http://"+gwServerUrl+"/Login?hisid="+authUser.username+"&hispwd="+authUser.gwpwd+"&ryguid="+guid,"_blank");
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _addManage(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/addManage",idDatas+"&pmtypecode=0&pmtype=管理中&ALIBABAKEY="+ALIBABAKEY,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('纳入管理成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	/**
	 * 提交失访表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postGenPlanForm(postStr){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/plan/genPlan",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('生成报告成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	/**
	 * 提交失访表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postLostForm(method,postStr,index){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/lost/"+method,postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('变更数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	/**
	 * 向上协诊
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postUpReferralForm(postStr,index,formBoxId){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/referral/up",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('提交成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	function _postDeviceForm(postStr,index,formBoxId){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/bindDevice",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('绑定成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					table.reloadData("lotpdvlist")
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	function unBindDevice(postStr){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/unBindDevice",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('解除绑定成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					table.reloadData("lotpdvlist")
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	let layeroIndex,layeroId;
	let layeroIndex2,layeroId2;

	function openUpReferral(patientData){
		console.log(patientData)
		var formBoxId = "upReferralForm",formBoxName = "向上协诊";
		let defaultArea = ['500px'];
		/**
		 * reset
		 */
		$("#zcsj").val("");
		laydate.render({
			elem: '#zcsj'
			,value: new Date()
			,format:"yyyy-MM-dd HH:mm:ss"
			,trigger:"dddddddd"
		});
		$("#xzyy").val("");
		$("#patientid").val(patientData.id);
		$("#patientname").val(patientData.name);


		$("#ojyuserid").val(patientData.gljyuserid);
		$("#ojyusername").val(patientData.gljyusername);
		$("#ojyuser").val(patientData.gljyuser);

		// $("#evalid").val(patientData.evalid);
		// $("#bpgradecode").val(patientData.bpgradecode);
		// $("#dbgradecode").val(patientData.dbgradecode);
		// $("#lpgradecode").val(patientData.lpgradecode);
		$("#"+formBoxId).show();
		//执行重载
		layer.open({
			title:[formBoxName]
			,type:1
			,area: defaultArea
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $("#"+formBoxId)
			,btn: ['确定', '取消']
			,success: function(layero, index){
				layeroIndex = index;
				layeroId = layero.attr("id");
				layero.find(".layui-layer-content").css({height:"auto"});
			}
			,yes: function(index, layero){
				layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
				var postStr = form.val(formBoxId);
				_postUpReferralForm(postStr,index,formBoxId);
			}
			,btn2: function(index, layero){
			}
			,end:function(){
				layeroIndex = null;
				layeroId = null;
				$("#"+formBoxId).hide();
			}
		});
	}

	function addBindDevice(patientData){
		var formBoxId = "bindDeviceForm",formBoxName = "添加设备";
		let defaultArea = ['500px'];
		$("#serialnumber").val("");
		$("#lotidcard").val(patientData.idcard);
		$("#lotname").val(patientData.name);
		$("#lotglorgname").val(patientData.glorgname);
		$("#lotglorgid").val(patientData.glorgid);

		$("#"+formBoxId).show();
		//执行重载
		layer.open({
			title:[formBoxName]
			,type:1
			,area: defaultArea
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $("#"+formBoxId)
			,btn: ['确定', '取消']
			,success: function(layero, index){
				layeroIndex2 = index;
				layeroId2 = layero.attr("id");
				layero.find(".layui-layer-content").css({height:"auto"});
			}
			,yes: function(index, layero){
				layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
				var postStr = form.val(formBoxId);
				_postDeviceForm(postStr,index,formBoxId);
			}
			,btn2: function(index, layero){
			}
			,end:function(){
				layeroIndex2 = null;
				layeroId2 = null;
				$("#"+formBoxId).hide();
			}
		});
	}

	function removePmtype(patientData){
		var postStr = "id=" +patientData.id;
		// console.log(patientData)
		console.log(postStr)
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/removePmtype",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('操作成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
					// if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	function setCheckLis(id,name,val,checked){
		if(val == "0"){
			if(checked){
				$('input:checkbox[name="'+name+'"]').attr("disabled","");
				$("[name='"+name+"']").each(function(){
					console.log($(this).val(),$(this).val() == 0,id)
					if($(this).val() == 0){
						$(this)[0].checked = true;
						$(this)[0].removeAttribute("disabled");
					}else{
						$(this)[0].checked = false;
					}
				});
				form.render(null, 'changeBbfzInfo');
			}else{
				$('input:checkbox[name="'+name+'"]').removeAttr("disabled");
				form.render(null, 'changeBbfzInfo');
			}
		}
	}
	var setCheck = function (arr,name){
		$("[name='"+name+"']").each(function(){
			$(this)[0].checked = false;
			form.render(null, 'changeBbfzInfo');
		});
		$.each(arr, (i,v) => {
			let id = "#"+name.replace("-chk","");
			let node = $(`input[type="checkbox"][name^="${name}"][value="${v}"]`);
			if (node && node.length) {
				node[0].checked = true;
				form.render(null, 'changeBbfzInfo');
				setCheckLis(id,name,v,true);
			}
		});
	}

	function changeBbfzInfoForm(postStr,index){
		console.log(postStr)
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/changeBbfzInfo",postStr,function(jsondata){
			if(jsondata.code=='200'){
				if(index){layer.close(index);}
				layer.msg('操作成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	function changeBbfzInfo(patientData){
		let defaultArea = ['500px'];
		var formBoxId = "changeBbfzInfo";
		layer.open({
			title:["变更患者伴并发症状态"]
			,type:1
			,area: defaultArea
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $("#"+formBoxId)
			,btn: ['确定', '取消']
			,success: function(layero, index){
				layeroIndex = index;
				layeroId = layero.attr("id");
				layero.find(".layui-layer-content").css({height:"auto"});
				/**
				 * 清空状态
				 */
				$("#bfz-val").val("");
				$("#bfz-name").val("");
				$("[name='bfz-chk']").each(function(){
					$(this)[0].checked = false;
					$(this)[0].removeAttribute("disabled");
				});
				form.render(null, 'changeBbfzInfo');
				form.on('checkbox(chk-special-filter)', function(data){
					let name = data.elem.name;
					let title = data.elem.title;
					let value = data.value;
					let id = "#"+name.replace("-chk","");
					var valId = "#" + name.replace("chk","val");
					var valName = "#" + name.replace("chk","name");
					setCheckLis(id,name,data.value,data.elem.checked);
					if(data.elem.checked){
						data.elem.checked = true;
						data.elem.removeAttribute("disabled");
						form.render(null, 'changeBbfzInfo');
						if(data.value == "0"){
							$(valId).val("0");
							$(valName).val(title);
						}else{
							let arr = $(valId).val().Split(",");
							arr.push(data.value);
							$(valId).val(arr.join((arr.length > 1 ? "," : "")));
							let arr2 = $(valName).val().Split(",");
							arr2.push(data.elem.title);
							$(valName).val(arr2.join((arr2.length > 1 ? "," : "")));
						}
					}else{
						let arr = $(valId).val().Split(",");
						arr.removeArr(data.value);
						$(valId).val(arr.join((arr.length > 1 ? "," : "")));

						let arr2 = $(valName).val().Split(",");
						arr2.removeArr(data.elem.title);
						$(valName).val(arr2.join((arr2.length > 1 ? "," : "")));
					}

				});

				if(patientData.bfz){
					setCheck(patientData.bfz.split(","),"bfz-chk");
					$("#bfz-val").val(patientData.bfz);
					$("#bfz-name").val(patientData.bfzname);
				}

			}
			,yes: function(index, layero){
				layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
				var postData = form.val(formBoxId);
				var bfzDic = {0: "无", 1: "冠心病", 2: "脑卒中", 3: "肾病综合征", 4: "眼底病变", 5: "周围神经病变", 6: "周围血管病变"};
				var bfzval = postData.bfz;
				var bfzname = postData.bfzname;
				if (bfzval.indexOf("0") > -1 || bfzname.indexOf("无,") > -1) {
					if(bfzval == "0"){
						bfzval = "0";
						bfzname = "无";
					}else{
						let realBfz = bfzval.split(",").filter(item => item != '');
						var bfzArr = [];
						var nameArr = [];
						for(var i = 0; i <realBfz.length; i++){
							var key = realBfz[i];
							if(key != 0 ){
								bfzArr.push(key);
								nameArr.push(bfzDic[key]);
							}
						}
						bfzval = bfzArr.join((bfzArr.length > 1 ? "," : ""));
						bfzname = nameArr.join((nameArr.length > 1 ? "," : ""));
					}
				}
				var postStr = "evalid=" +patientData.evalid + "&bfz="+bfzval + "&bfzname="+bfzname;
				changeBbfzInfoForm(postStr,index);
			}
			,btn2: function(index, layero){
			}
			,end:function(){
				layeroIndex = null;
				layeroId = null;
				$("#"+formBoxId).hide();
			}
		});
	}
	function renderDeviceTable(formBoxId,patientData){
		layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
			var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
			var table = layui.table;
			var ctxPath = layui.getContextPath("ctxPath","listjs");
			var isFristLoad = true;
			var deviceTb  = table.render({
				done: function(res, curr, count){
					$("#"+formBoxId + ' .layui-btn').on('click', function(){
						var othis = $(this), method = othis.data('type');
						active[method] ? active[method].call(this, othis) : '';
					});
				}
				,elem: '#lotpdvlist'
				,id: 'lotpdvlist'
				,data:[]
				,where: {
					idcard: patientData.idcard
				}
				,toolbar:  '#lottoolbar'
				,defaultToolbar: []
				,cols: [[
					,{unresize:true,field:'serialnumber', width:280, title: '机器码'}
					,{unresize:true,field:'bindtime', width:200, title: '绑定时间'}
					,{unresize:true,field:'createname',  width:171,title: '创建人'}
					,{unresize:true,width:129, toolbar:'#lottoolbar-opt2',title: '操作'}
				]]
				,page: false
				,height:586
				,width: 1024
				,limit:10000000000000000
			});
			if(isFristLoad){
				deviceTb.config.url = ctxPath+"/v/patients/deviceList";
				table.reload('lotpdvlist');
			}
			isFristLoad = false

			table.on('tool(lotpdvlist)', function(obj){
				var that = this;
				var data = obj.data;
				if(obj.event === 'unbind'){
					if($(that).attr("disabled")=="disabled")return;
					layer.confirm('你确认解除绑定吗?', function(index_1){
						layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
						layer.close(index_1)
						$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
						unBindDevice("id="+data.id)
					});

				}
			});
		});
	}
	function lotpdvlist(patientData){
		bindPatientData = patientData;
		let defaultArea = ['1024px','646px'];
		var formBoxId = "lotpdv-box";
		layer.open({
			title:["设备绑定列表"]
			,type:1
			,area: defaultArea
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $("#"+formBoxId)
			,success: function(layero, index){
				layeroIndex = index;
				layeroId = layero.attr("id");
				layero.find(".layui-layer-content").css({height:"auto"});
				$("#"+formBoxId).html('<div id="lotpdvlist" class="lotpdvlist" lay-filter="lotpdvlist"></div>');
				setTimeout(function(){
					renderDeviceTable(formBoxId,patientData)
				},100)
			}
			,end:function(){
				bindPatientData = null;
				layeroIndex = null;
				layeroId = null;
				$("#"+formBoxId).html('<div id="lotpdvlist" class="lotpdvlist" lay-filter="lotpdvlist"></div>');
				$("#"+formBoxId).hide();
			}
		});
	}
	function changeDocter(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/changeDocter",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('操作成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	function applyMove(patientData){
		var postStr = "patientid=" +patientData.id;
		console.log(patientData)
		ajaxValForm();
		$.getJSON(ctxPath+"/v/patients/apply/apply",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('申请成功，请等待审批！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	function openLost(){
		$("#sfdjBox").show();
		//执行重载
		layer.open({
			title:['失访登记']
			,type:1
			,area: '500px'
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $('#sfdjBox')
			,btn: ['确定', '取消']
			,yes: function(index, layero){
				layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
				var postStr = form.val("formtable");
				_postLostForm("sfdj",postStr,index);
			}
			,btn2: function(index, layero){
			}
			,end:function(){
				$("#sf-ids").val("");
				$("#sf-names").html("");
				$("#sfdjBox").hide();
			}
		});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'viewPlan'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			parent.layer.open({
				title:['个性化管理方案']
				,type: 2
				,area: ["99.5%","99%"]
				,shade: [0.7, '#000000']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/plan/viewPlan?id='+data.planid, 'no']
				,end:function(){
				}
			});
		} else if(obj.event === 'del'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				_delForm("id="+data.id);
			});
		} else if(obj.event === 'applyMove'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认申请迁入该患者吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				applyMove(data);
			});
		} else if(obj.event === 'addManage'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认将该患者纳入三高管理吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				_addManage("id="+data.id);
			});
		}  else if(obj.event === 'edit'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['修改三高患者基本信息']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/patients/editIndex?id='+data.id, 'no']
				,end:function(){
				}
			});
		} else if(obj.event === 'lost'){
			if($(that).attr("disabled")=="disabled")return;
			$("#sf-ids").val(data.id);
			$("#sf-names").html(data.name);
			openLost();
		}else if(obj.event === 'lostCx'){
			if($(that).attr("disabled")=="disabled")return;
			$("#sf-ids-cx").val(data.id);
			layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			var postStr = form.val("formtableCx");
			_postLostForm("sfcx",postStr);
		}else if(obj.event === 'genPlan'){
			if($(that).attr("disabled")=="disabled")return;
			layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			var postData = form.val("formtablePlan");
			postData["patientid"] = data.id;
			postData["detevalid"] = data.evalid;
			_postGenPlanForm(postData);
		} else if(obj.event === 'addDeteval'){
			if($(that).attr("disabled")=="disabled")return;
			parent.clkDeteval(data);
		}
	});
	//触发事件
	var active = {
		readcard:function (){
			//传需要赋值input的ID
			var shensi = new ShenSi("idcard");
		},
		reload: function(){
			var that = this;
			loadListFunction();
		}
		,exportYgl: function(){
			window.open(ctxPath +"/v/patients/exportYgl",'top');
		}
		,export: function(){
			window.open(ctxPath +"/v/patients/export",'top');
		}
		,export2: function(){
			window.open(ctxPath +"/v/patients/export2",'top');
		}
		,changeBbfzInfo:function (){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				if(data[0].evalid){
					changeBbfzInfo(data[0]);
				}else{
					layer.msg('当前患者无评估报告',{time:2000});
					return false;
				}
			}
		}
		,lotpdvlist:function (){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				lotpdvlist(data[0]);
			}
		}
		,addDevice: function(){
			addBindDevice(bindPatientData)
		}
		,reloadDevice: function(){
			table.reload("lotpdvlist")
		}
		,changeDocter: function(){

			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择患者',{time:2000});
				return false;
			} else{
				var info = '这些';
				if(len==1)info='该';
				layer.confirm('你确认将'+info+'患者的当前管理医生变更为'+authUser.name+'['+authUser.username+']吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					changeDocter(idDatas);
				}, function(){
				});
			}

		}

		,lostCx: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			var nameDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += checkCkbox.data[i].id;
					nameDatas += checkCkbox.data[i].name;
				}else{
					idDatas += ","+checkCkbox.data[i].id;
					nameDatas += ","+checkCkbox.data[i].name;
				}
			}
			if(len == 0){
				layer.msg('请至少选择一位患者',{time:2000});
				return false;
			}
			$("#sf-ids-cx").val(idDatas);
			layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			var postStr = form.val("formtableCx");
			_postLostForm("sfcx",postStr);
		}
		,lost: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			var nameDatas = "";

			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += checkCkbox.data[i].id;
					nameDatas += checkCkbox.data[i].name;
				}else{
					idDatas += ","+checkCkbox.data[i].id;
					nameDatas += ","+checkCkbox.data[i].name;
				}
			}
			if(len == 0){
				layer.msg('请至少选择一位患者',{time:2000});
				return false;
			}
			$("#sf-ids").val(idDatas);
			$("#sf-names").html(nameDatas)
			openLost();
		}
		,qy:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				window.open("http://"+jyServerUrl+"/fdn-web/exrule/qyeditIndex?yszh="+authUser.username+"&ysmm="+authUser.gwpwd+"&jmsfzh="+data[0].idcard,"_blank");
			}
		}
		,sf:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				var guid = data[0].id;
				var postStr = "idcard=" +data[0].idcard;
				ajaxValFormSf(guid);
				$.getJSON(ctxPath+"/v/patients/getEhrPatient",postStr,function(jsondata){
					console.log(jsondata)
					if(jsondata.code=='200'){
						if(jsondata.data && jsondata.data.guid){
							guid = jsondata.data.guid;
						}
					}
					window.open("http://"+gwServerUrl+"/Login?hisid="+authUser.username+"&hispwd="+authUser.gwpwd+"&ryguid="+guid,"_blank");
				});
			}

		}
		,gxzsf:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				var guid = data[0].id;
				var name = data[0].name;

				var postStr = "idcard=" +data[0].idcard + "&guid=" + data[0].id;
				layer.open({
					title:['新增高血脂随访']
					,type: 2
					,area: [windowWidth(),windowHeight()]
					,shade: [0.7, '#d0d7f6']
					,scrollbar: true
					,maxmin: false
					,fixed:true
					,move: false
					,content: [ctxPath+'/v/patients/gxzsf/addIndex?lik=1&ryGuid='+guid+"&name="+name, 'no']
					,end:function(){
					}
				});
				// ajaxValForm();
				// $.getJSON(ctxPath+"/v/patients/getEhrPatient2",postStr,function(jsondata){
				//
				// 	console.log(jsondata)
				// 	if(jsondata.code=='200'){
				//
				// 		guid = jsondata.data.guid;
				//
				// 		layer.open({
				// 			title:['新增高血脂随访']
				// 			,type: 2
				// 			,area: [windowWidth(),windowHeight()]
				// 			,shade: [0.7, '#d0d7f6']
				// 			,scrollbar: true
				// 			,maxmin: false
				// 			,fixed:true
				// 			,move: false
				// 			,content: [ctxPath+'/v/patients/gxzsf/addIndex', 'no']
				// 			,end:function(){
				// 			}
				// 		});
				// 	}else{
				// 		layer.msg(jsondata.msg,{time:2000});
				// 	}
				// });
			}

		}
		,upReferral:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				openUpReferral(data[0]);
			}
		}
		,upReferral2:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				openUpReferral(data[0]);
			}
		}
		,removePmtype:function () {
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('只能选择一个患者',{time:2000});
				return false;
			} else{
				layer.confirm('你确认将该患者移除管理吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					removePmtype(data[0]);
				}, function(){
				});
			}
		}
		,addDeteval: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else if(len > 1){
				layer.msg('请选择一个患者',{time:2000});
				return false;
			} else{
				parent.clkDeteval(data[0]);
			}
		}
		,addManage: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			var idLen = 0;
			for(var i=0 , l = len; i < l; i++){
				if(checkCkbox.data[i].pmtypecode != "0"){
					idDatas += "id="+checkCkbox.data[i].id + "&";
					idLen++;
				}
			}
			console.log(idDatas,len,idLen)
			if(idLen == 0){
				layer.msg('请选择至少一个患者,已管理患者、辖区外患者需要进行迁入申请!',{time:3000});
				return false;
			} else{
				var info = '这些';
				if(idLen==1)info='该';
				layer.confirm('你确认将'+info+'患者纳入三高管理吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
					_addManage(idDatas);
				}, function(){
				});
			}
		}
		,batchdel: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择您将要删除的记录',{time:2000});
				return false;
			} else{
				var info = '些';
				if(len==1)info='条';
				layer.confirm('你确认删除这'+info+'记录吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
					_delForm(idDatas);
				}, function(){
				});
			}
		}
		,add: function(){
			var that = this;
			//执行重载
			layer.open({
				title:['新增三高患者']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/patients/addIndex', 'no']
				,end:function(){
				}
			});
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	var _loadDic = function(){
		//console.log(JSON.stringify(zNodesJson[0].name));
		if(model == "ygl"){
			$("#scbm").attr("ival","");
			$("#scbm").attr("nval","");

			if(authUser.grade=="1"){
				$("#glorgid").attr("ival",authUser.orgid);
				$("#glorgid").attr("nval",authUser.orgname);
			}

		}else{
			$("#scbm").attr("ival",authUser.orgid);
			$("#scbm").attr("nval",authUser.orgname);
		}

		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){

		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","listQuery",false);
		zFun.selectTreeId("scbm",authUser.orgid);

		if(model == "ygl" && authUser.grade=="1" ){
			zFun.initSelectTree(zNodes, "请选择管理单位", "glorgid", false, true, "只能选择三级单位", "listQuery", false);
			zFun.selectTreeId("glorgid",authUser.orgid);
		}

	}
	_loadDic();
	loadListFunction(true);
});