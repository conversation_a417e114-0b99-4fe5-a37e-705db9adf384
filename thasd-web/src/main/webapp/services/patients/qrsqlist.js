function loadTable(tempLetId,queryParam){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","listjs");
        var cols = [[
            {field:'name',  title: '患者姓名',width: 86}
            ,{field:'gender',  title: '性别',width:58}
            ,{field:'age',  title: '年龄',width:58}
            ,{field:'minzu',  title: '民族',width:65}
            ,{field:'idcard',  title: '身份证号码',width: 171}
            ,{field:'jtzz',  title: '家庭住址',width: 171}
            ,{field:'porgname',  title: '所属单位',width: 174}
            ,{field:'createtime',  title: '申请时间',width: 164}
            ,{field:'orgname',  title: '申请迁入单位'}
            ,{field:'applystatue',  title: '审批状态',width: 86,templet: function(d){
                var s = '<span class="c-dsp">待审批</span>';
                if(d.applystatue == 1){
                    s = '<span class="c-ysp">已审批</span>';
                }
                if(d.applystatue == 2){
                    s = '<span class="c-cx">已撤销</span>';
                }
                return s;
            }}
            ,{fixed:'right', width:110, align:'left', toolbar:'#sp-opt',title: '操作'}
        ]];
        if(tempLetId=="yspTable"){
            cols = [[
                {field:'name',  title: '患者姓名',width: 86}
                ,{field:'gender',  title: '性别',width:58}
                ,{field:'age',  title: '年龄',width:58}
                ,{field:'minzu',  title: '民族',width:65}
                ,{field:'idcard',  title: '身份证号码',width: 171}
                ,{field:'jtzz',  title: '家庭住址',width: 171}
                ,{field:'porgname',  title: '所属单位',width: 174}
                ,{field:'createtime',  title: '申请时间',width: 164}
                ,{field:'orgname',  title: '申请迁入单位',width: 174}
                ,{field:'applystatue',  title: '审批状态',width: 86,templet: function(d){
                    var s = '<span class="c-dsp">待审批</span>';
                    if(d.applystatue == 1){
                        s = '<span class="c-ysp">已审批</span>';
                    }
                    if(d.applystatue == 2){
                        s = '<span class="c-cx">已撤销</span>';
                    }
                    return s;
                }}
                ,{field:'applyusername',  title: '审批人',width: 86}
                ,{field:'applytime',  title: '审批时间',width: 164}
                ,{field:'applyresult',  title: '审批结果',width: 86,templet: function(d){
                    var s = "";
                    if(d.applyresult == 1){
                        s = '<span class="c-yes">同意</span>';
                    }
                    if(d.applyresult == 2){
                        s = '<span class="c-no">不同意</span>';
                    }
                    return s;
                }}
                ,{field:'applyremark',  title: '审批意见',width: 174}
            ]];
        }
        if(tempLetId=="sqTable"){
            cols = [[
                {field:'name',  title: '患者姓名',width: 86}
                ,{field:'gender',  title: '性别',width:58}
                ,{field:'age',  title: '年龄',width:58}
                ,{field:'minzu',  title: '民族',width:65}
                ,{field:'idcard',  title: '身份证号码',width: 171}
                ,{field:'jtzz',  title: '家庭住址',width: 171}
                ,{field:'porgname',  title: '所属单位',width: 174}
                ,{field:'createtime',  title: '申请时间',width: 164}
                ,{field:'applystatue',  title: '审批状态',width: 86,templet: function(d){
                    var s = '<span class="c-dsp">待审批</span>';
                    if(d.applystatue == 1){
                        s = '<span class="c-ysp">已审批</span>';
                    }
                    if(d.applystatue == 2){
                        s = '<span class="c-cx">已撤销</span>';
                    }
                    return s;
                }}
                ,{field:'applyorgname',  title: '审批单位',width: 174}
                ,{field:'applyusername',  title: '审批(撤销)人',width: 164,templet: function(d){
                        if(d.applystatue == 2){
                            return d.revokeusername;
                        }else{
                            return d.applyusername;
                        }
                 }}
                ,{field:'applytime',  title: '审批(撤销)时间',width: 164,templet: function(d){
                        if(d.applystatue == 2){
                            return d.revoketime;
                        }else{
                            return d.applytime;
                        }
                }}
                ,{field:'applyresult',  title: '审批结果',width: 86,templet: function(d){
                    var s = "";
                    if(d.applyresult == 1){
                        s = '<span class="c-yes">同意</span>';
                    }
                    if(d.applyresult == 2){
                        s = '<span class="c-no">不同意</span>';
                    }
                    return s;
                    }}
                ,{field:'applyremark',  title: '审批意见',width: 174}
                ,{fixed:'right', width:110, align:'left', toolbar:'#cx-opt',title: '操作'}
            ]];
        }
        var thisTable = table.render({
            done: function(res, curr, count){
                $("li."+tempLetId).attr("load-stetup",1);
                res.data.forEach(function (item, index) {
                    if (item.applystatue == 2) {
                        var tr = $(".layui-table").find("tbody tr[data-index='" + index + "']");
                        tr.css("text-decoration", "line-through");
                    }
                });
                bindClick();
                setTimeout(function (){
                },500)
            },
            toolbar:  tempLetId=="sqTable" ? '' : ''
            ,defaultToolbar: tempLetId=="sqTable" ? [] : []
            ,elem: "#"+tempLetId
            ,id: tempLetId+'Tbl'
            ,url:ctxPath+'/v/patients/apply/pageLst'
            ,where: queryParam
            ,cols: cols
            ,page: true
            ,height:'full-116'
            ,limit:30
        });
    });
}

layui.use(['layer', 'form','element', 'laydate',"table"], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        laydate = layui.laydate,
        element = layui.element,
        form = layui.form;
    var table = layui.table;
    var ctxPath = layui.getContextPath("ctxPath","listjs");

    window.bindClick = function (){
        // $('.layui-btn').unbind("click");
        // $('.layui-input').unbind("click");
        //
        // $('.layui-btn').bind("click",function(){
        //     var othis = $(this), method = othis.data('type');
        //     active[method] ? active[method].call(this, othis) : '';
        // });
        // $('.layui-input').bind("click",function(){
        //     var othis = $(this), method = othis.data('type');
        //     active[method] ? active[method].call(this, othis) : '';
        // });
    }


    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }
    form.verify({
        formRadio: function(value, item){ //value：表单的值、item：表单的DOM对象
            //console.log(item)
            let name = item.name;
            let chkLength = $(":input[name='"+name+"']:checked").length;
            if(chkLength == 0){
                return '请至少选择一个选项!';
            }
        }
    });
    let layeroIndex,layeroId;
    function openSp(data){
        console.log(data)
        var formBoxId = "spForm",formBoxName = "审批";
        let defaultArea = ['500px'];
        /**
         * reset
         */
        $("#id").val(data.id);
        $("#applyresult").val("");
        $("#applyremark").val("");
        $("#"+formBoxId).show();
        //执行重载
        layer.open({
            title:[formBoxName]
            ,type:1
            ,area: defaultArea
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.find(".layui-layer-content").css({height:"auto"});
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render();
            }
            ,yes: function(index, layero){
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    applyAccept(postStr,index);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }
    function applyAccept(postStr,index){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/apply/accept",postStr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('审批成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    loadTable("wspTable",{
                        applystatue: 0
                        ,applyorgid: authUser.orgid
                    })
                    if(index){layer.close(index);}
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
            $(".layui-table-cell a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
        });
    }
    function applyRevoke(data){
        var postStr = "id=" + data.id;
        console.log(data)
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/apply/revoke",postStr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('撤销成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    loadTable("sqTable",{
                        orgid: authUser.orgid
                    })
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
            $(".layui-table-cell a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
        });
    }
    //监听工具条
    table.on('tool(listtable)', function(obj){
        var that = this;
        var data = obj.data;
        if(obj.event === 'sp'){
            openSp(data);
        }
    });
    table.on('tool(listtable3)', function(obj){
        var that = this;
        var data = obj.data;
        if(obj.event === 'cx'){
            if($(that).attr("disabled")=="disabled")return;
            layer.confirm('你确认撤销申请吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
                $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
                applyRevoke(data);
            });
        }
    });
    element.on('tab(qrsqTab)', function(data){
        var tid = $(".qrsqTab li.layui-this").attr("tid");
        var step = $(".qrsqTab li.layui-this").attr("load-stetup");
        var applystatue = $(".qrsqTab li.layui-this").attr("applystatue");
        /**
         * 需要审批及已审批
         */
        var queryParam = {
            applystatue: applystatue
            ,applyorgid: authUser.orgid
        }
        /**
         * 我的迁入申请
         */
        var queryParam2 = {
            orgid: authUser.orgid
        }
        if(step){// == "0"
            if(tid == "sq"){
                $('.ultoolbar button#exportMe').css("display",'inline-block')
                $('.ultoolbar button#exportMe').bind("click",function(){
                    window.open(ctxPath+"/v/patients/apply/exportMe?orgid="+authUser.orgid,'top');
                });
                loadTable(tid +"Table",queryParam2)
            }else {
                $('.ultoolbar button#exportMe').unbind("click");
                $('.ultoolbar button#exportMe').css("display",'none')
                loadTable(tid +"Table",queryParam)
            }
        }
    });

    $(document).ready(function(){
        if(authUser.grade == "1" && authUser.orgcode == "000001"){
            var queryParam2 = {
                orgid: authUser.orgid
            }
            loadTable("sqTable",queryParam2)
        }else{
            loadTable("wspTable",{
                applystatue: 0
                ,applyorgid: authUser.orgid
            })
        }
        bindClick();
        $(".initbox").remove();
    });

});