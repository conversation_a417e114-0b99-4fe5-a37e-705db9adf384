var dataIndex = null;
var dataId = null;
var dataArray = null;

var reloadList = function(){
    layui.use(['layer'], function(){
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        layer.closeAll();
        loadListFunction();
    });
}
var loadListFunction = function(frist){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;

        var ctxPath = layui.getContextPath("ctxPath","listjs");

        function _loadMkInfo(msg){
            if(!frist)return;
            if(msg==''||null==msg)msg = '数据请求中...';
            if(frist){
                layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
            }else{
                layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
            }
        }
        _loadMkInfo();
        table.render({
            done: function(res, curr, count){
                if(frist){
                    $(".layui-none").html("请输入或选择检索条件后查询");
                }
                setTimeout(function(){
                    dataArray = res.data;
                    $("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
                    $(".initbox").remove();
                    layer.closeAll("dialog");
                },300);
            }
            ,elem: '#listtable'
            ,url:ctxPath+'/v/patients/list'
            ,where: {
                 name: $("#name").val()
                ,idcard:$("#idcard").val()
                ,islower:$("input[name='islower']:checked").val()
                ,orgid: frist ? "init-empty-abababababababababab" : $("#scbmHide").val()
            }
            ,cols: [[
                {unresize:true,width:80,title: '',align:'center',templet:'#dx'}
                ,{field:'name',  title: '患者姓名',width: 100}
                ,{field:'gender',  title: '性别',width:65}
                ,{field:'age',  title: '年龄',width:65}
                ,{field:'minzu',  title: '民族',width:65}
                ,{field:'idcard',  title: '身份证号码',width: 171}
                ,{field:'pmtype',  title: '状态',width:100}
                ,{field:'orgname',  title: '管理单位'}
                ,{field:'lxdh',  title: '联系电话'}
                ,{field:'jtzz',  title: '家庭住址'}
                ,{field:'areaname',  title: '所属区域'}
            ]]
            ,page: true
            ,height:'full-152'
            ,cellMinWidth:100
            ,limit:20
        });
    });
}

var resetSwClose = function(isClose){
    layui.use(['layer'], function(){
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        if(isClose){
            $('.layui-layer-setwin a.layui-layer-close1').hide();
        }else{
            $('.layui-layer-setwin a.layui-layer-close1').show();
        }
    });
}

layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
    var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
    var jQuery = layui.jquery;
    var element = layui.element;
    var table = layui.table;
    var form = layui.form;
    var zFun =layui.treeselectTable;

    $(".menuTabel").css({"max-width":$(".fulllistbox").width()-$(".leftMenu").width()});
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    //监听工具条
    table.on('tool(listtable)', function(obj){
        var that = this;
        var data = obj.data;
    });
    form.on('radio(selid)', function(data){
        dataIndex = $(this).parent().parent().parent().attr("data-index");
        dataId = data.value;
        //console.log(index); //得到radio原始DOM对象
        //console.log(data); //得到radio原始DOM对象
        //console.log(data.elem); //得到radio原始DOM对象
        //console.log(data.value); //被点击的radio的value值
    });
    //触发事件
    var active = {
        readcard:function (){
            //传需要赋值input的ID
            var shensi = new ShenSi("idcard");
        },
        reload: function(){
            var that = this;
            loadListFunction();
        }
        ,cancel: function(){
            parent.layer.close(parent.layIndex1);
        }
        ,confirm: function(){
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            $(that).attr("disabled","disabled").addClass("layui-btn-disabled");
            if(null != dataIndex){
                var selData = dataArray[dataIndex];
                var body = parent.layer.getChildFrame('body',parent.layIndex1);
                parent.detPatientData = selData;
                parent.layer.close(parent.layIndex1)
            }else{
                $(that).attr("disabled",null).removeClass("layui-btn-disabled");
                layer.msg('请选择一位患者',{time:2000});
                return false;
            }
        }
    };
    $('.layui-btn.user-search').on('click', function(){
        var othis = $(this), method = othis.data('method');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });

    var _loadDic = function(){
        //console.log(JSON.stringify(zNodesJson[0].name));
        // $("#scbm").attr("ival",authUser.orgid);
        // $("#scbm").attr("nval",authUser.orgname);
        $("#scbm").attr("ival","");
        $("#scbm").attr("nval","");
        initTree();
        loadListFunction(true);
    }
    var getMenuNodes = function(){
        return zNodesJson;
    }
    var zNodes = getMenuNodes();
    var initTree = function(){
        zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","dept",false);
        // zFun.selectTreeId("scbm",authUser.orgid);
    }
    _loadDic();
});