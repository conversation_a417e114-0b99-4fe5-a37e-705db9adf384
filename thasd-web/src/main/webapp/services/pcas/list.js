layui.use(['layer','element','form','treeTable'], function(){//独立版的layer无需执行这一句

	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var element = layui.element;
	var table = layui.treeTable;
	var windowWidth = '500px';
	var windowHeight = '460px';
	var frist = true;

	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '数据请求中...';
		if(frist){
			layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		}else{
			//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
	}
	_loadMkInfo();
	var insTb = table.render({
		done: function(res, curr, count){
			frist = false;
			setTimeout(function(){
				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
				$(".initbox").remove();
				layer.closeAll("dialog");
			},300);
		}
		,elem: '#listtable'
		,url:ctxPath+'/v/pcas/treeList'
		,where: {
			code: $("#code").val()
			,name: $("#name").val()
		},
		tree: {
			iconIndex: 1,// 折叠图标显示在第几列
			isPidData: false,// 是否是id、pid形式数据
			idName: 'id',  // id字段名称
			pidName: 'pid' // pid字段名称
		},
		cols: [[
			 {width:100,field:'id', title: '编码'}
			,{minWidth:280,field:'name', title: '名称'}
			,{width:180,field:'provincecode', title: '所属省(直辖市)编码'}
			,{width:140,field:'citycode', title: '所属市(区)编码'}
		]]
		,page: false
		,height:'full-54'
		,cellMinWidth:100
		//,limit:20
	});

	var loadListFunction = function(){
		insTb.refresh();
	}

	var reloadList = function(){
		layer.closeAll();
		loadListFunction();
	}
	var resetSwClose = function(isClose){
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	}

	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/pcas/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'del'){
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
				_delForm("id="+data.id);
			});
		} else if(obj.event === 'edit'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['修改中国行政区划信息']
				,type: 2
				,area: [windowWidth,windowHeight]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/pcas/editIndex?id='+data.id+'&noparent=noparent'+'&orgid='+data.orgid, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		reload: function(){
			insTb.clearFilter();
			var keywords = $('#name').val();
			if (keywords) {
				insTb.filterData(keywords);
			} else {
				insTb.clearFilter();
			}
		}
		,batchdel: function(){
			var that = this;
			if($(that).attr("disabled")=="disabled")return;
			var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
			var len = checkCkbox.data.length;
			var idDatas = "";
			for(var i=0 , l = len; i < l; i++){
				if(i==0){
					idDatas += "id="+checkCkbox.data[i].id
				}else{
					idDatas += "&id="+checkCkbox.data[i].id
				}
			}
			if(len == 0){
				layer.msg('请选择您将要删除的记录',{time:2000});
				return false;
			} else{
				var info = '些';
				if(len==1)info='条';
				layer.confirm('你确认删除这'+info+'记录吗？', {
					btn: ['确认','取消'] //按钮
				}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
					$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
					_delForm(idDatas);
				}, function(){
				});
			}
		}
		,add: function(){
			var that = this;
			//执行重载
			layer.open({
				title:['新增中国行政区划']
				,type: 2
				,area: [windowWidth,windowHeight]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/pcas/addIndex?noparent=noparent', 'no']
				,end:function(){
				}
			});
		}

	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});
});




