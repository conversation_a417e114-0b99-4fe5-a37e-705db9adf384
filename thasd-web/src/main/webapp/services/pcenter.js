var tzyyIndex = -1;

layui.use(['admin', 'loading', 'jquery', 'convert', 'popup','hotkey','drawer'], function () {
	var admin = layui.admin;
	var $ = layui.jquery;
	var drawer = layui.drawer;
	var loading = layui.loading;
	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}

	var ctxPath = getUParam("ctxPath","indexjs");

	// 初始化顶部用户信息
	admin.setConfigType("yml");
	admin.setConfigPath(ctxPath + "/layui/pear/pear-centent.config.yml");
	admin.render();
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}


})