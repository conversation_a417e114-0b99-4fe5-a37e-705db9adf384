layui.use(['layer'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");
	window.location.href = ctxPath + "/v/plan/viewPlan?id="+window.parent.patient.planid;
});