var layeroIndex,layeroId;
var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table','element'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var element = layui.element;
		var ctxPath = layui.getContextPath("ctxPath","listjs");
		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				element.render('progress');
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/plansfjl/list'
			,where: {
				patientid: window.parent.patientid
			}
			,cols: [[
				{type:'checkbox',fixed:'left'}
				,{align:'center',field:'name',  title: '患者姓名',width: 100}
				,{align:'center',field:'gender',  title: '性别',width:65}
				,{align:'center',field:'age',  title: '年龄',width:65}
				,{align:'center',field:'proname',title: '服务项目'}
				,{field:'fwsj', width:180, title: '服务时间'}
				,{field:'createname',  title: '服务医生',width: 100}
				,{field:'result',  title: '结果',templet: function(d){
						if(d.hisdoc && d.hisdoc.length > 0){
							if(d.gson){
								var gson = d.gson;
								var lpData = gson ? JSON.parse(gson) : {};
								if(lpData.lab){
									return '<a class="layui-btn layui-btn-xs" lay-event="viewHis">查看</a>';
								}
								return '';
							}else{
								return d.result || "";
							}
						}else{
							return d.result || "";
						}
				}}
			]]
			,page: true
			,height:'full-155'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','laytpl','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var element = layui.element;
	var zFun =layui.treeselectTable;
	var laytpl = layui.laytpl;
	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	var gwServerUrl = layui.getContextPath("gwServerUrl","listjs");
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'viewHis'){
			var boxId = "zymxbox";
			var seaHeight = $("body").height();
			let  height = (seaHeight-100) + "px";
			let defaultArea = ['872px',height];
			var gson = data.gson;
			var lpData = gson ? JSON.parse(gson) : {};
			var applycode = lpData.lab ? lpData.lab.labapplycode : "";

			layer.msg("数据请求中...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});

			var emptyHtml = '<div class="emptyBox">暂无数据</div>'
			$.ajaxSetup({
				error:function(x,e){
					layer.msg('连接服务器失败,请稍后再试...',{time:2000});
					return false;
				}
			});
			$.getJSON(ctxPath+'/v/plansfjl/jybg',"applycode="+applycode + "&orgcode="+data.orgcode,function(jsondata){
				if(jsondata.code=='200'){
					let tplId = "jybg-templet";
					var getTpl = document.getElementById(tplId).innerHTML
						,view = document.getElementById('zymxbox');
					laytpl(getTpl).render(jsondata.data, function(html){
						view.innerHTML = html;
					});
					layer.closeAll("dialog");
					layer.open({
						title:["查看结果"]
						,type:1
						,area: defaultArea
						,shade: [0.7, '#d0d7f6']
						,scrollbar: true
						,maxmin: true
						,fixed:true
						,move: false
						,content: $("#"+boxId)
						,btn: []
						,success: function(layero, index){
							layeroIndex = index;
							layeroId = layero.attr("id");
							$("#"+boxId).show();
						}
						,end:function(){
							layeroIndex = null;
							layeroId = null;
							$("#"+boxId).hide();
						}
					});
				}else{
					var view = document.getElementById('jybg');
					view.innerHTML = emptyHtml;
					layer.msg(jsondata.msg,{time:2000},function(){
					});
				}
			});
		}
		if(obj.event === 'view'){
			var spMethod = "Gxysf";
			if(data.gtp2 =="tnb"){
				spMethod = "Tnbsf";
			}
			//执行重载
			layer.open({
				title:['查看随访详情']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: ['http://'+gwServerUrl+'/Ehr/'+spMethod+'/ViewNonauth?id='+data.guid, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		reload: function(){
			var that = this;
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	loadListFunction(true);

});