var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table','element'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var element = layui.element;
		var ctxPath = layui.getContextPath("ctxPath","listjs");
		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				element.render('progress');
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/pcenter/jzjlData'
			,where: {
				// patientid: window.parent.patientid
				idcard: window.parent.patient.idcard
			}
			,cols: [[
				{type:'checkbox',fixed:'left'}
				,{align:'center',field:'hismzid',  title: '门诊号',width: 150}
				,{align:'center',field:'jzrq',  title: '就诊日期',width:179}
				,{align:'center', templet: '#xy', title: '血压',width:110}
				,{align:'center', field: 'xt',title: '血糖',width:100}
				,{field:'zgzl',  title: '主观资料'}
				,{field:'kgzl',  title: '客观资料'}
				,{field:'pg',  title: '评估'}
				,{field:'czjh',  title: '处置计划'}
				,{align:'center', templet: '#sjly', width:100, title: '数据来源'}
				,{align:'center', field: 'orgname', width:179, title: '接诊机构'}
				,{field:'addUserName',  title: '接诊医生',width:110}
			]]
			,page: true
			,height:'full-155'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var element = layui.element;
	var zFun =layui.treeselectTable;
	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	var gwServerUrl = layui.getContextPath("gwServerUrl","listjs");
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'view'){
			//执行重载
			layer.open({
				title:['接诊记录']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: ['http://'+gwServerUrl+'/Ehr/'+spMethod+'/ViewNonauth?id='+data.guid, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		reload: function(){
			var that = this;
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	loadListFunction(true);

});