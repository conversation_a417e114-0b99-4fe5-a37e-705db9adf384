function loadLotTable(tabId,tabContentId){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var ctxPath = layui.getContextPath("ctxPath","lotjs");
		// var queryParams = "patientid="+window.parent.patient.id;
		var queryParams = "idcard="+window.parent.patient.idcard;
		function getBpLevel(sbp, dbp) {
			if ((sbp >= 140 && sbp < 160)) {
				return ["1级高血压","layui-bg-orange"]; //1级高血压
			}else if ((sbp >= 160 && sbp < 180) ) {
				return ["2级高血压","layui-bg-orange"]; //2级高血压
			}else if (sbp >= 180) {
				return ["3级高血压","layui-bg-red2"]; //3级高血压
			}else if ( (dbp >= 90 && dbp < 100) ) {
				return ["1级高血压","layui-bg-orange"]; //1级高血压
			}else if ((dbp >= 100 && dbp < 110)) {
				return ["2级高血压","layui-bg-orange"]; //2级高血压
			}else if (dbp >= 110) {
				return ["3级高血压","layui-bg-red2"]; //3级高血压
			}
			return ["正常","layui-bg-green"]; //正常
		}
		function getXtEval(kfXt) {
			if ((kfXt < 6.1)) { //3.9~6.1 - 3.9~7.8
				return ["正常","layui-bg-green"]; //正常
			}
			if (kfXt >= 6.1 && kfXt < 7) {
				return ["空腹血糖受损","layui-bg-orange"]; //空腹血糖受损
			}
			if (kfXt >= 7) {
				return ["血糖高","layui-bg-red2"]; //糖尿病
			}
			return ["正常","layui-bg-green"]; //正常
		}
		var cols = [[
			{unresize:true,type:'numbers',title: '序号', width: 50}
			,{field: 'serialnumber', title: '设备编码', width: 121}
			,{title: '血压值', width: 196,templet: function(d){
					return d.ssy + " / " + d.szy + "mmHg";
			}}
			,{title: '评估', width: 196,templet: function(d){
					var bpPG = getBpLevel(d.ssy,d.szy)
					return '<span class="layui-badge '+bpPG[1]+'">'+bpPG[0]+'</span>';
				}}
			,{field: 'jcsj', title: '监测时间'}

		]];
		var whereQurl = {idcard: window.parent.patient.idcard};
		var queryUrl = ctxPath + "/v/sf/lotbplist";
		if(tabId == "xt"){
			queryUrl = ctxPath + "/v/sf/lotdblist";
			cols = [[
				{unresize:true,type:'numbers',title: '序号', width: 50}
				,{field: 'serialnumber', title: '设备编码', width: 121}
				,{title: '血糖值', width: 196,templet: function(d){
						return d.val + "mmol/L";
				}}
				,{title: '评估', width: 196,templet: function(d){
						var dbPG = getXtEval(d.val)
						return '<span class="layui-badge '+dbPG[1]+'">'+dbPG[0]+'</span>';
				}}
				,{field: 'jcsj', title: '监测时间'}
			]];
		}
		var thisTable = table.render({
			done: function(res, curr, count){
				setTimeout(function (){
				},500)
			},
			toolbar:  ''
			,defaultToolbar: []
			,elem: "#"+tabContentId
			,id: tabContentId+'Tbl'
			,url: queryUrl
			,where: whereQurl
			,cols: cols
			,page: true
			,height:400
			,limit:20
		});
	});
}

layui.use(['layer', 'loading', 'echarts', 'element', 'count','laytpl','table'], function() {
	var $ = layui.jquery,
		layer = layui.layer,
		element = layui.element,
		count = layui.count,
		laytpl = layui.laytpl,
		echarts = layui.echarts;
	var loading = layui.loading;
	var table = layui.table;
	var ctxPath = layui.getContextPath("ctxPath","lotjs");

	loading.block({
		type: 3,
		elem: '.loading-chart1',
		msg: ''
	})
	loading.block({
		type: 3,
		elem: '.loading-chart2',
		msg: ''
	})

	element.on('tab(zwjcTab)', function(data){
		// console.log(data);
		// console.log(this); //当前Tab标题所在的原始DOM元素
		// console.log(data.index); //得到当前Tab的所在下标
		// console.log(data.elem); //得到当前的Tab大容器
		var tabId = $(".zwjcTab li.layui-this").attr("val");
		var loadStetup = $(".zwjcTab li.layui-this").attr("load-stetup");
		var tabContentId = tabId + "Table";
		if(loadStetup == "0"){
			loadLotTable(tabId,tabContentId);
		}
	});
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	function getChart(){
		ajaxValForm();
		var queryUrl = ctxPath+"/v/sf/lotchart";
		// var queryParams = "patientid="+window.parent.patient.id;
		var queryParams = "idcard="+window.parent.patient.idcard;
		$.getJSON(queryUrl,queryParams,function(jsondata){
			if(jsondata.code=='200'){
				initChart1(jsondata)
				initChart2(jsondata)
			}else{
				layer.msg(jsondata.msg,{time:2000},function(){});
			}
		});
	}

	var column1 = echarts.init(document.getElementById('echarts-gxy'), 'walden');
	var column2 = echarts.init(document.getElementById('echarts-tnb'), 'walden');

	getChart();
	loadLotTable("xy","xyTable")

	function initChart1(result){
		let bgColor = "#fff";
		let color = [
			"#0090FF",
			"#36CE9E",
			"#FFC005",
			"#FF515A",
			"#8B5CFF",
			"#00CA69"
		];

		let xAxisData = result.rd.gxy.map(function(v){return v.jcsj});
		let yAxisData1 = result.rd.gxy.map(function(v){return v.ssy});
		let yAxisData2 = result.rd.gxy.map(function(v){return v.szy});

		var hexToRgba = function(hex, opacity) {
			let rgbaColor = "";
			let reg = /^#[\da-f]{6}$/i;
			if (reg.test(hex)) {
				rgbaColor =
					`rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
						"0x" + hex.slice(3, 5)
					)},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
			}
			return rgbaColor;
		}
		option = {
			backgroundColor: bgColor,
			color: color,
			legend: {
				right: 0,
				top: 0
			},
			tooltip: {
				trigger: "axis",
				formatter: function(params) {
					let html = '<b>'+params[0].name+'</b>';
					params.forEach(function(v) {
						html +=
							`<div style="color: #666;font-size: 14px;line-height: 24px">
					                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
					                ${v.seriesName}
					                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
					                mmhg`;
					})



					return html
				},
				extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
				axisPointer: {
					type: 'shadow',
					shadowStyle: {
						color: '#ffffff',
						shadowColor: 'rgba(225,225,225,1)',
						shadowBlur: 5
					}
				}
			},
			grid: {
				top: 50,
				containLabel: true
			},
			xAxis: [{
				type: "category",
				boundaryGap: false,
				axisLabel: {
					// formatter: '{value}月',
					formatter: '{value}',
					textStyle: {
						color: "#333"
					}
				},
				axisLine: {
					lineStyle: {
						color: "#D9D9D9"
					}
				},
				data: xAxisData
			}],
			yAxis: [{
				type: "value",
				name: '单位：mmhg',
				axisLabel: {
					textStyle: {
						color: "#666"
					}
				},
				nameTextStyle: {
					color: "#666",
					fontSize: 12,
					lineHeight: 40
				},
				splitLine: {
					lineStyle: {
						type: "dashed",
						color: "#E9E9E9"
					}
				},
				axisLine: {
					show: false
				},
				axisTick: {
					show: false
				}
			}],
			series: [{
				name: "收缩压",
				type: "line",
				smooth: true,
				// showSymbol: false,/
				symbolSize: 8,
				zlevel: 3,
				lineStyle: {
					normal: {
						color: color[0],
						shadowBlur: 3,
						shadowColor: hexToRgba(color[0], 0.5),
						shadowOffsetY: 8
					}
				},
				areaStyle: {
					normal: {
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[{
								offset: 0,
								color: hexToRgba(color[0], 0.3)
							},
								{
									offset: 1,
									color: hexToRgba(color[0], 0.1)
								}
							],
							false
						),
						shadowColor: hexToRgba(color[0], 0.1),
						shadowBlur: 10
					}
				},
				data: yAxisData1
			}, {
				name: "舒张压",
				type: "line",
				smooth: true,
				// showSymbol: false,
				symbolSize: 8,
				zlevel: 3,
				lineStyle: {
					normal: {
						color: color[1],
						shadowBlur: 3,
						shadowColor: hexToRgba(color[1], 0.5),
						shadowOffsetY: 8
					}
				},
				areaStyle: {
					normal: {
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[{
								offset: 0,
								color: hexToRgba(color[1], 0.3)
							},
								{
									offset: 1,
									color: hexToRgba(color[1], 0.1)
								}
							],
							false
						),
						shadowColor: hexToRgba(color[1], 0.1),
						shadowBlur: 10
					}
				},
				data: yAxisData2
			}]
		};
		column1.setOption(option);
		loading.blockRemove('.loading-chart1', 0);
	}

	function initChart2(result){
		let bgColor = "#fff";
		let color = [
			"#0090FF",
			"#36CE9E",
			"#FFC005",
			"#FF515A",
			"#8B5CFF",
			"#00CA69"
		];

		let xAxisData = result.rd.tnb.map(function(v) {return v.jcsj});
		let yAxisData1 = result.rd.tnb.map(function(v) {return v.val});

		var hexToRgba = function (hex, opacity) {
			let rgbaColor = "";
			let reg = /^#[\da-f]{6}$/i;
			if (reg.test(hex)) {
				rgbaColor =
					`rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
						"0x" + hex.slice(3, 5)
					)},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
			}
			return rgbaColor;
		}
		option = {
			backgroundColor: bgColor,
			color: color,
			legend: {
				right: 0,
				top: 0
			},
			tooltip: {
				trigger: "axis",
				formatter: function(params) {
					let html = '<b>'+params[0].name+'</b>';
					params.forEach(function(v)  {
						html +=
							`<div style="color: #666;font-size: 14px;line-height: 24px">
					                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
					                ${v.seriesName}
					                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
					                mmol/l`;
					})
					return html
				},
				extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
				axisPointer: {
					type: 'shadow',
					shadowStyle: {
						color: '#ffffff',
						shadowColor: 'rgba(225,225,225,1)',
						shadowBlur: 5
					}
				}
			},
			grid: {
				top: 50,
				containLabel: true
			},
			xAxis: [{
				type: "category",
				boundaryGap: false,
				axisLabel: {
					// formatter: '{value}月',
					formatter: '{value}',
					textStyle: {
						color: "#333"
					}
				},
				axisLine: {
					lineStyle: {
						color: "#D9D9D9"
					}
				},
				data: xAxisData
			}],
			yAxis: [{
				type: "value",
				name: '单位：mmol/l',
				axisLabel: {
					textStyle: {
						color: "#666"
					}
				},
				nameTextStyle: {
					color: "#666",
					fontSize: 12,
					lineHeight: 40
				},
				splitLine: {
					lineStyle: {
						type: "dashed",
						color: "#E9E9E9"
					}
				},
				axisLine: {
					show: false
				},
				axisTick: {
					show: false
				}
			}],
			series: [{
				name: "血糖",
				type: "line",
				smooth: true,
				// showSymbol: false,/
				symbolSize: 8,
				zlevel: 3,
				lineStyle: {
					normal: {
						color: color[0],
						shadowBlur: 3,
						shadowColor: hexToRgba(color[0], 0.5),
						shadowOffsetY: 8
					}
				},
				areaStyle: {
					normal: {
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[{
								offset: 0,
								color: hexToRgba(color[0], 0.3)
							},
								{
									offset: 1,
									color: hexToRgba(color[0], 0.1)
								}
							],
							false
						),
						shadowColor: hexToRgba(color[0], 0.1),
						shadowBlur: 10
					}
				},
				data: yAxisData1
			}]
		};
		column2.setOption(option);
		loading.blockRemove('.loading-chart2', 0);
	}
	window.onresize = function() {
		column1.resize();
		column2.resize();
	}
});