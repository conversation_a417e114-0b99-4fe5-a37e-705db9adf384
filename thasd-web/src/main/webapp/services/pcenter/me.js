layui.use(['form','laytpl', 'layedit', 'laydate','ztree','treeselect'], function(){
    var form = layui.form
        ,$ = layui.jquery
        ,layer = layui.layer
        ,layedit = layui.layedit
        ,laydate = layui.laydate
        ,zFun =layui.treeselect;

    var laytpl = layui.laytpl;

    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }
    var ctxPath = getUParam("ctxPath","adtjs");

    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '加载中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                parent.resetSwClose(false);
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                return false;
            }
        });
    }
    function setCheckLis(id,name,val,checked){
        if(val == 1){
            if(checked){
                $('input:checkbox[name="'+name+'"]').attr("disabled","");
                $("[name='"+name+"']").each(function(){
                    if($(this).val() == 1){
                        $(this)[0].checked = true;
                        $(this)[0].removeAttribute("disabled");
                    }else{
                        $(this)[0].checked = false;
                    }
                });
                form.render();
            }else{
                $('input:checkbox[name="'+name+'"]').removeAttr("disabled");
                form.render();
            }
        }
    }
    var setCheck = function (arr,name){
        $("[name='"+name+"']").each(function(){
            $(this)[0].checked = false;
            form.render();
        });
        $.each(arr, function(i, v) {
            let id = "#"+name.replace("-chk","");
            let node = $(`input[type="checkbox"][name^="${name}"][value="${v}"]`);
            if (node && node.length) {
                node[0].checked = true;
                form.render();
                setCheckLis(id,name,v,true);
            }
        });
        if(name == "guanli-chk"){
            $('input:checkbox[name="'+name+'"]').attr("disabled","");
            // form.render();
        }
    }

    /**
     * 生成表单数据
     * @param id 表单id
     * @code{.form、#form}
     */
    function _getForm(){
        var jsondata = window.parent.patient;
        //表单初始赋值
        form.val('formtable', JSON.parse(JSON.stringify(jsondata)));
        if(jsondata.ywgms)setCheck(jsondata.ywgms.split(","),"ywgms-chk");
        if(jsondata.jwsjb)setCheck(jsondata.jwsjb.split(","),"jwsjb-chk");
        if(jsondata.jzsfq)setCheck(jsondata.jzsfq.split(","),"jzsfq-chk");
        if(jsondata.jzsmq)setCheck(jsondata.jzsmq.split(","),"jzsmq-chk");
        if(jsondata.jzsxdjm)setCheck(jsondata.jzsxdjm.split(","),"jzsxdjm-chk");
        if(jsondata.jzszn)setCheck(jsondata.jzszn.split(","),"jzszn-chk");

        if(jsondata.cjqk)setCheck(jsondata.cjqk.split(","),"cjqk-chk");
        if(jsondata.guanli){setCheck(jsondata.guanli.split(","),"guanli-chk");}

        // $("#sjarea").attr("ival",jsondata.areaid);
        // $("#sjarea").attr("nval",jsondata.areaname);

        $('input:checkbox').each(function(){
            $(this).prop("disabled",true);
        });

        // initTree();
        $(".initbox").remove();
        layer.closeAll("dialog");
    }

    //触发事件
    var active = {
    };

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });

    Array.prototype.removeArr = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    String.prototype.Split = function (s) {
        return this.split(s).filter(item => item != '');
    }
    form.on('checkbox(chkfilter)', function(data){
        let name = data.elem.name;
        let id = "#"+name.replace("-chk","");
        setCheckLis(id,name,data.value,data.elem.checked);
        var valId = "#" + name.replace("chk","val");
        if(data.elem.checked){
            if(data.value == 1){
                $(valId).val("1");
                $("#ywgmsqt").val("");
                $("#ywgmsqt").addClass("layui-disabled").attr("disabled");
                $("#ywgmsqt").prop("disabled",true);
            }else{
                let arr = $(valId).val().Split(",");
                arr.push(data.value);
                $(valId).val(arr.join((arr.length > 1 ? "," : "")));
            }
        }else{
            let arr = $(valId).val().Split(",");
            arr.removeArr(data.value);
            $(valId).val(arr.join((arr.length > 1 ? "," : "")));
        }
        if(data.value == 5 && name == "ywgms-chk"){
            if(data.elem.checked){
                $("#ywgmsqt").removeClass("layui-disabled").removeAttr("disabled");
            }else{
                $("#ywgmsqt").val("");
                $("#ywgmsqt").addClass("layui-disabled").attr("disabled");
                $("#ywgmsqt").prop("disabled",true);
            }
        }
    });

    var getAreaNodes = function(){
        return zNodesAreaJson;
    }
    var zAreaNodes = getAreaNodes();

    var initTree = function(){
        zFun.initSelectTree(zAreaNodes,"请选择行政区划","sjarea",true,true,"只能选择村、街道卫生机构","psarea",false);
    }

    _getForm();
});