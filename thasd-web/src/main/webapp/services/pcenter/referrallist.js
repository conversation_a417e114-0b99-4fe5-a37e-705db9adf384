layui.use(['layer','element','form','table', 'util'], function(){//独立版的layer无需执行这一句

	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var element = layui.element;
	var util = layui.util;
	var table = layui.table;
	var windowWidth = '1110px';
	var windowHeight = '96%';
	var frist = true;

	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '数据请求中...';
		if(frist){
			layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		}else{
			//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
	}
	_loadMkInfo();

	let optionConfig = {
		done: function(res, curr, count){
			frist = false;
			setTimeout(function(){
				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
				$(".initbox").remove();
				layer.closeAll("dialog");
			},300);
		}
		,elem: '#listtable'
		,url:ctxPath+'/v/referral/list'
		,where: {
			patientid: window.parent.patientid
		},
		cols: [[
			{width:90,field:'name', title: '姓名'}
			,{width:90,field:'gender', title: '性别'}
			,{width:90,field:'age', title: '年龄'}
			,{align:'center', templet: '#bpgrade', width:100, title: '高血压'}
			,{align:'center', templet: '#dpgrade', width:100, title: '糖尿病'}
			,{align:'center', templet: '#lpgrade', width:100, title: '高血脂'}
			,{width:174,field:'zcorgname', title: '转出单位'}
			,{width:170,field:'zcsj', title: '转出时间'}
			,{width:90,field:'zcys', title: '转出医生'}
			,{width:90,field:'zrys', title: '接诊医生'}
			,{width:170,field:'jzsj', title: '接诊时间'}
			,{width:174,field:'zrorgname', title: '接诊单位'}
			,{field:'xzyy', title: '协诊原因'}
		]]
		,page: true
		,height:'full-155'
		,cellMinWidth:100
		,limit:20
	};

	var insTb = table.render(optionConfig);

	table.on('toolbar(listtable)', function(obj){
		switch(obj.event){
			case 'edit':
				break;
			case 'add':
				break;
			case 'batchdel':
				break;
		};
	});
	var loadListFunction = function(){
		var code = $("#code").val();
		var name = $("#name").val()
		optionConfig.where =  {
			code: code
			,name: name
		};
		insTb.reload(optionConfig);
	}

	window.resetSwClose = function(isClose){
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	}
	window.reloadList = function(){
		layer.closeAll();
		loadListFunction();
	}

	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/referral/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	//触发事件
	var active = {
		reload: function(){
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
});




