function formatDateTime(datestr, format) {
    if(null == datestr || '' == datestr)return '';
    var date = new Date(datestr);
    var o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
        'H+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        S: date.getMilliseconds(), // 毫秒
        a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
        A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
    };
    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
            );
        }
    }
    return format;
}

Date.prototype.Format = function(fmt)
{ //author: meizz
    var o = {
        "M+" : this.getMonth()+1,                 //月份
        "d+" : this.getDate(),                    //日
        "h+" : this.getHours(),                   //小时
        "m+" : this.getMinutes(),                 //分
        "s+" : this.getSeconds(),                 //秒
        "q+" : Math.floor((this.getMonth()+3)/3), //季度
        "S"  : this.getMilliseconds()             //毫秒
    };
    if(/(y+)/.test(fmt))
        fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substring(4 - RegExp.$1.length));
    for(var k in o)
        if(new RegExp("("+ k +")").test(fmt))
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substring((""+ o[k]).length)));
    return fmt;
}
function splitStr(str,index){
    return str.split(",")[index] || '-';
}
function FormatMinute(StatusMinute){
    if(StatusMinute == '0' || StatusMinute == ''){
        return "-";
    }
    var day=parseInt(StatusMinute/60/24);
    var hour=parseInt(StatusMinute/60%24);
    var min= parseInt(StatusMinute % 60);
    StatusMinute="";
    if (day > 0)
    {
        StatusMinute= day + "天";
    }
    if (hour>0)
    {
        StatusMinute += hour + "小时";
    }
    if (min>0)
    {
        StatusMinute += parseFloat(min) + "分钟";
    }
    return StatusMinute;
}

layui.use(['layer','laytpl','element','form','table','ztree','laydate','treeselectTable'], function(){//独立版的layer无需执行这一句
    var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
    var jQuery = layui.jquery;
    var laytpl = layui.laytpl;
    var element = layui.element;
    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }
    var ctxPath = getUParam("ctxPath","listjs");
    var id = getUParam("id","listjs");
    var zyh = getUParam("zyh","listjs");
    var bah = getUParam("bah","listjs");
    var orgcode = getUParam("orgcode","listjs");
    function getUrl(){
        var url = "";
        url += ("id="+id);
        return url;
    }

    //触发事件
    var active = {

    };

    element.on('tab(mainTab)', function(data){
        var val = $(".mainTab li.layui-this").attr("val");
        var step = $(".mainTab li.layui-this").attr("load-stetup");
        if(step == "0"){
            $(".mainTab li.layui-this").attr("load-stetup","1");

            if(val == "ryjl"){
                _getRyjlData();
            }
            if(val == "cyjl"){
                _getCyjlData();
            }
            if(val == "scbcjl"){
                _getScbcjlData();
            }
            if(val == "rcbcjl"){
                _getRcbcjlData();
            }
            if(val == "yzd"){
                _getYzdData();
            }
            if(val == "lsyzd"){
                _getLsyzdData();
            }
            _getLsyzdData
            if(val == "jcbg"){
                _getJcbgData();
            }
            if(val == "jybg"){
                _getJybgData();
            }
        }
    });

    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '数据请求中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }
    var emptyHtml = '<div class="emptyBox">暂无数据</div>'
    function _getData(paramUrl){
        //_loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/get',paramUrl,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "basy-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('basy');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                $(".initbox").remove();
            }else{
                var view = document.getElementById('basy');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                    $(".initbox").remove();
                });
            }
        });
    }

    function _getRyjlData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/rylst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "ryjl-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('ryjl');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('ryjl');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    function _getCyjlData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/cylst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "cyjl-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('cyjl');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('cyjl');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    function _getScbcjlData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/scbclst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "scbcjl-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('scbcjl');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('scbcjl');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }


    function _getRcbcjlData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/rcbclst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "rcbcjl-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('rcbcjl');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('rcbcjl');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    function _getYzdData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/yzdlst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "yzd-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('yzd');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('yzd');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    function _getLsyzdData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/lsyzdlst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "lsyzd-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('lsyzd');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('lsyzd');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    function _getXyData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/xycflst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "xycf-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('xycf');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('xycf');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }
    function _getZyData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/zycflst',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "zycf-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('zycf');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('zycf');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }
    function _getJcbgData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/jcbg',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "jcbg-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('jcbg');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('jcbg');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }
    function _getJybgData(){
        _loadMkInfo();
        ajaxValForm();
        $.getJSON(ctxPath+'/v/pcenter/zybl/jybg',"zyh="+zyh + "&orgcode="+orgcode,function(jsondata){
            if(jsondata.code=='200'){
                let tplId = "jybg-templet";
                var getTpl = document.getElementById(tplId).innerHTML
                    ,view = document.getElementById('jybg');
                laytpl(getTpl).render(jsondata.data, function(html){
                    view.innerHTML = html;
                });
                layer.closeAll("dialog");
            }else{
                var view = document.getElementById('jybg');
                view.innerHTML = emptyHtml;
                layer.msg(jsondata.msg,{time:2000},function(){
                });
            }
        });
    }

    _getData(getUrl());

});