var isTopIndex = true,isPcenterHome = false;
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
var tzyyIndex = -1;

function clkDeteval (data){
	layui.use(['jquery'], function () {
		var $ = layui.jquery;
		let pgEle = $("a.site-demo-active[menu-url='/v/deteval']");
		if(pgEle){
			let frameId = pgEle.attr("menu-id");
			pgEle.trigger("click");
			let iframe = $(document).find("#"+frameId)[0];
			let src = iframe.src + "?source=out";
			iframe.src = src;
			console.log(src)
			if (iframe.attachEvent){
				iframe.attachEvent("onload", function(){ // IE
					postMessage();
				});
			} else {
				iframe.onload = function(){ // 非IE
					postMessage();
				};
			}
			function postMessage(){
				setTimeout(function (){
					$(document).find("#"+frameId)[0].contentWindow.postMessage({
						id:"clkDeteval"
						,data:data
					},"*");
				},500);
			}
		}
	});
}

layui.use(['admin','table', 'loading', 'jquery', 'convert', 'popup','hotkey','drawer'], function () {
	var admin = layui.admin;
	var $ = layui.jquery;
	var drawer = layui.drawer;
	var loading = layui.loading;
	var table = layui.table;
	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","indexjs");

	// 初始化顶部用户信息
	admin.setAvatar(ctxPath + "/css/rs/tx.png", userName);
	admin.setConfigType("yml");
	admin.setConfigPath(ctxPath + "/layui/pear/pear.config.yml");
	admin.render();
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}

	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postForm(postUrl,postStr){
		ajaxValForm();
		$.getJSON(postUrl,postStr,function(jsondata){
			if(jsondata.code=='200'){
				loading.loadChangeRemove("同步数据成功!",1500);
			}else{
				loading.loadChangeRemove(jsondata.msg || jsondata.message,2500);
			}
		});
	}

	// 定义 ctrl+a、ctrl+b、r、f 四组快捷键
	// hotkeys('ctrl+alt+d,ctrl+d', function(event,handler){
	// 	switch(handler.key){
	// 		case "ctrl+alt+d":
	// 			drawer.open({
	// 				direction: "right",
	// 				dom: ".admin-menu",
	// 				distance: "240px",
	// 				success:function(){
	// 					$(".admin-menu").hide();
	// 				}
	// 			});
	// 			$(".admin-menu").show();
	// 			break;
	// 	}
	// 	//handler.scope 范围
	// });

	$('#clear-basic').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/clearData",postStr);
	});
	$('#import-area').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/syncArea",postStr);
	});
	$('#import-org').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/syncOrg",postStr);
	});
	$('#import-user').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/syncUser",postStr);
	});
	$('#import-data').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/completData",postStr);
	});

	$('#import-patients').on('click', function(){
		 loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/syncPatients",postStr);
	});


	$('#reGenJh').on('click', function(){
		loading.Load(2, "正在同步数据，请稍后......");
		postStr = "ALIBABAKEY="+alibabaKey;
		_postForm(ctxPath+"/sys/reGenSfjhDetail",postStr);
	});

	// 自定义消息点击回调
	admin.message(function(id, title, context, form) {

	});

	$('#loginOut').on('click', function(){
		window.location.href = ctxPath + "/loginout"
	});
	$('#editPass').on('click', function(){
		layer.open({
			title:['修改密码']
			,type: 2
			,area: ['480px','336px']
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: [ctxPath+'/exrule/editPasswordIndex', 'no']
			,end:function(){
			}
		});
	});
	$('#downFwjh').on('click', function(){
		window.open(ctxPath +"/exrule/exportNotices",'top');
	});

	$('#readcard').on('click', function(){
		var shensi = new ShenSi("idcard");
	});
	$('#reloadMessage').on('click', function(){
		if(isTopIndex){ table.reload("noticeResult",{
			where: {
				page:1
				,idcard: $("#idcard").val()
				,name: $("#pname").val()
				,gljyusername: $("#gljyusername").val()
			}
		});}
	});
})