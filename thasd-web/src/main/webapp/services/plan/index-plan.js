var zdData,yyData;
var layIndex1;

class SessionStorageAPI {
    // 验证浏览器支持性
    static isSupported() {
        return typeof window !== 'undefined' && 'sessionStorage' in window;
    }

    // 存储数据（支持对象、数组等复杂类型）
    static setItem(key, value) {
        if (!this.isSupported())  throw new Error('sessionStorage not supported');

        try {
            const data = typeof value === 'string' ? value : JSON.stringify(value);
            window.sessionStorage.setItem(key,  data);
        } catch (e) {
            if (e.name  === 'QuotaExceededError') {
                console.error(' 存储空间已满，无法保存数据');
            }
            throw e;
        }
    }

    // 读取数据（自动解析JSON格式）
    static getItem(key) {
        if (!this.isSupported())  return null;

        const data = window.sessionStorage.getItem(key);
        try {
            return data ? JSON.parse(data)  : data;
        } catch {
            return data; // 返回原始字符串
        }
    }

    // 删除指定数据
    static removeItem(key) {
        if (!this.isSupported())  return;
        window.sessionStorage.removeItem(key);
    }

    // 清空所有存储
    static clear() {
        if (!this.isSupported())  return;
        window.sessionStorage.clear();
    }

    // 获取键名
    static key(index) {
        if (!this.isSupported())  return null;
        return window.sessionStorage.key(index);
    }

    // 获取存储数量（只读属性）
    static get length() {
        if (!this.isSupported())  return 0;
        return window.sessionStorage.length;
    }
}
function loadJkzdFormTable(itemcode,tempLetId){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","planjs");
        var mainid = layui.getContextPath("mainid","planjs");
        var cols = [[
            {unresize:true,type:'checkbox', width: 70}
            ,{unresize:true, width:70, align:'center', toolbar:'#listtable-number',title:'序号'}
            ,{unresize:true,field:'content', title: '内容',minWidth:800}
            ,{unresize:true,  width:129,align:'left', toolbar:'#listtable-opt2',title: '操作'}
        ]];
        var thisTable = table.render({
            done: function(res, curr, count){
                bindClick();
                let cntCls = "td.jkzd"+itemcode+"td";
                if(res.data){
                    if(res.data.length > 0){
                        let renderHtml = '';
                        for(var i = 0; i<res.data.length; i++)
                        {
                            console.log(res.data[i])
                            renderHtml += '<p class="jkzd-content">' + res.data[i].content + '</p>';
                        }
                        $(cntCls).html(renderHtml);
                    }else{
                        $(cntCls).html("暂无");
                    }
                }
                setTimeout(function (){
                },500)
            },
            toolbar:  '#toolbar'
            ,defaultToolbar: []
            ,elem: "#"+tempLetId
            ,id: tempLetId+'Tbl'
            ,url:ctxPath+'/v/plan/jkzdList'
            ,where: {
                planid: mainid
                ,itemcode: itemcode
            }
            ,cols: cols
            ,page: false
            ,height:315
            ,limit:100000000000
        });
    });
}

function loadYyzdFormTable(){

    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","planjs");
        var mainid = layui.getContextPath("mainid","planjs");

        function StrToDate(datestr) {
            return new Date(datestr);
        }

        var datesAreOnSameDay = (first, second) =>
            first.getFullYear() === second.getFullYear() &&
            first.getMonth() === second.getMonth() &&
            first.getDate() === second.getDate();
        var toolbarTempId = "#toolbarYyzd2";
        var toolbarColTempId = "#listtable-opt3";
        if(datesAreOnSameDay(StrToDate(plantime), new Date())){
            toolbarTempId = "#toolbarYyzd";
            toolbarColTempId = "#listtable-opt4";
        }
        var cols = [[
            {unresize:true,type:'numbers',align:'center',width:70,title:'序号'}
            ,{unresize:true,field: 'yytype', title: '用药类型', }
            ,{unresize:true,field: 'ywmc', title: '药品名称', }
            ,{unresize:true,field: 'yypl',title: '频率', }
            ,{unresize:true,field: 'yyjl',title: '用量', }
            ,{unresize:true,field: 'yyff', title: '用发', }
            ,{unresize:true, width:129, align:'left', toolbar:toolbarColTempId,title: '操作'}
        ]];
        var thisTable = table.render({
            done: function(res, curr, count){
                bindClick();
                if(res.data){
                    if(res.data.length > 0){
                        yyzdRd = res.data;
                    }else{
                        yyzdRd = [];
                    }
                    setYyzdTable();
                }
                setTimeout(function (){
                },500)
            },
            toolbar:  toolbarTempId
            ,defaultToolbar: []
            ,elem: '#yyzdFormTable'
            ,id: 'yyzdFormTableTbl'
            ,url:ctxPath+'/v/plan/yyzdList'
            ,where: {// planid: mainid
                patientid:patientid
            }
            ,cols: cols
            ,page: false
            ,height:315
            ,limit:100000000000
        });
    });
}

function loadSfjhFormTable(){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","planjs");
        var mainid = layui.getContextPath("mainid","planjs");
        var cols = [[
            {unresize:true,type:'checkbox', width:70}
            ,{unresize:true, width:70, align:'center', toolbar:'#listtable-number',title:'序号'}
            ,{unresize:true,field: 'proname', title: '服务项目', }
            ,{unresize:true,field: 'prorate', title: '服务频率', }
            ,{unresize:true,field: 'executetime', title: '服务时间', }
            ,{unresize:true, width:129, align:'left', toolbar:'#listtable-opt',title: '操作'}
        ]];
        var thisTable = table.render({
            done: function(res, curr, count){
                getSfjhAll();
                bindClick();
                setTimeout(function (){
                    // table.resize('fzjhFormTableTbl');
                },500)
            },
            toolbar:  '#toolbar'
            ,defaultToolbar: []
            ,elem: '#fzjhFormTable'
            ,id: 'fzjhFormTableTbl'
            ,url:ctxPath+'/v/plan/fzjhList'
            ,where: {
                planid: mainid
            }
            ,cols: cols
            ,page: false
            ,height:315
            ,cellMinWidth:70
            ,limit:100000000000
        });
    });
}

layui.use(['layer', 'loading', 'form','element', 'laydate',"table","planUtil","detevalUtil"], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        laydate = layui.laydate,
        planUtil = layui.planUtil,
        detevalUtil = layui.detevalUtil,
        element = layui.element,
        form = layui.form,
        loading = layui.loading;
    var table = layui.table;
    var ctxPath = layui.getContextPath("ctxPath","planjs");
    var mainid = layui.getContextPath("mainid","planjs");

    let layeroIndex,layeroId;
    function setDeteval(){
        if(detevalRd){
            detevalUtil.setEvalPage(detevalRd);//生成评估页面
        }
    }
    var resetAscvdChart = function (){
        if(detevalRd){
            detevalUtil.resetAscvdChart(detevalRd);
        }
    }
    laydate.render({type: 'date',format:'yyyy-MM-dd',trigger: 'click',
        elem: '#executetime'
        ,min: 1//7天前
    });
    window.bindClick = function (){
        $('.layui-btn').unbind("click");
        $('.layui-input').unbind("click");

        $('.layui-btn').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-input').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }

    window.getSfjhAll = function (){
        ajaxValFormNull();
        var postUrl = ctxPath+"/v/plan/fzjhAll";
        var postStr = "planid="+planid;
        $.getJSON(postUrl,postStr,function(jsondata){
            console.log(jsondata)
            if(jsondata.code=='200'){
                sfjhDetailRd = jsondata.rd.sfjhDetail;
                sfjhRd = jsondata.rd.sfjh;
                setSfjhTable();
                setSfjhMxTable();
            }
        });
    }
    window.setYyzdTable = function (){
        table.render({
            elem: '#yyzdResult'
            ,cols: [[ //标题栏
                {type:'numbers',title: '序号', width: 70}
                ,{field: 'yytype', title: '用药类型', width: 100}
                ,{field: 'ywmc', title: '药品名称', minWidth: 145}
                ,{field: 'yypl', title: '频率', width: 136}
                ,{field: 'yyjl', title: '用量', width: 100}
                ,{field: 'yyff', title: '用发', width: 100}
            ]]
            ,data: yyzdRd ? yyzdRd : []
            ,page: false
            ,height:315
            ,cellMinWidth:70
            ,limit:100000000000
        });
    }
    window.setSfjhTable = function (){
        table.render({
            elem: '#sfjhResult'
            ,cols: [[ //标题栏
                {type:'numbers',title: '序号', width: 70}
                ,{field: 'proname', title: '服务项目', minWidth: 145}
                ,{field: 'prorate', title: '服务频率', width: 100}
                ,{field: 'len', title: '服务次数', width: 100}
                ,{field: 'remark', title: '备注', width: 136}
            ]]
            ,data: sfjhRd ? sfjhRd : []
            ,page: false
            ,height:315
            ,limit:100000000000
            ,cellMinWidth:70
        });
    }
    window.setSfjhMxTable = function (){
        if(sfjhDetailRd) {
            table.render({
                elem: '#sfjhMxResult'
                ,cols: [[ //标题栏
                    {type:'numbers',title: '序号', width: 70}
                    ,{field: 'proname', title: '服务项目', minWidth: 145}
                    ,{field: 'executetime', title: '提醒时间', width: 174}
                ]]
                ,data: sfjhDetailRd ?sfjhDetailRd : []
                ,page: false
                ,height:315
                ,limit:100000000000
                ,cellMinWidth:70
            });
        }
    }

    var windowWidth = function (){

        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        console.log(seaWidth,seaHeight)

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowWidth2;
    }

    var windowHeight = function (){
        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowHeight2;
    }


    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }

    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _delZd(postStr,itemcode){
        ajaxValForm();
        var postUrl = ctxPath+"/v/plan/";
        if(itemcode == "04"){
            postUrl += "delYyzd";
        }else if (itemcode == "9999"){
            postUrl += "delSfjh";
        }else{
            postUrl += "delJkzd";
        }
        $.getJSON(postUrl,postStr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('删除成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    if(itemcode == "8888"){
                        loadJkzdFormTable(itemcode,'aiFormTable');
                    }
                    if(itemcode == "01"){
                        loadJkzdFormTable(itemcode,'yszdFormTable');
                    }
                    if(itemcode == "02"){
                        loadJkzdFormTable(itemcode,'ydzdFormTable');
                    }
                    if(itemcode == "03"){
                        loadJkzdFormTable(itemcode,'jyzdFormTable');
                    }
                    if(itemcode == "05"){
                        loadJkzdFormTable(itemcode,'jczdFormTable');
                    }
                    if(itemcode == "04"){
                        loadYyzdFormTable();
                    }
                    if(itemcode == "9999"){
                        loadSfjhFormTable();
                    }
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
            $("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
        });
    }
    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postZdForm(postStr,index,formBoxId,itemcode){
        ajaxValForm();
        var postUrl = ctxPath+"/v/plan/";
        if(itemcode == "04"){
            postUrl += "saveYyzd";
        }else if (itemcode == "9999"){
            postUrl += "saveSfjh";
        }else{
            postUrl += "saveJkzd";
        }
        $.getJSON(postUrl,postStr,function(jsondata){
            if(jsondata.code=='200'){
                layer.msg('保存成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    if(itemcode == "8888"){
                        loadJkzdFormTable(itemcode,'aiFormTable');
                    }
                    if(itemcode == "01"){
                        loadJkzdFormTable(itemcode,'yszdFormTable');
                    }
                    if(itemcode == "02"){
                        loadJkzdFormTable(itemcode,'ydzdFormTable');
                    }
                    if(itemcode == "03"){
                        loadJkzdFormTable(itemcode,'jyzdFormTable');
                    }
                    if(itemcode == "05"){
                        loadJkzdFormTable(itemcode,'jczdFormTable');
                    }
                    if(itemcode == "04"){
                        loadYyzdFormTable();
                    }
                    if(itemcode == "9999"){
                        loadSfjhFormTable();
                    }
                    if(index){layer.close(index);}
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
        });
    }
    function openAddFormBox(formBoxId,formBoxName,itemcode,editData){
        $("#"+formBoxId).show();

        document.forms[formBoxId].reset();
        $("#" +formBoxId +" input.ipt-hidden").each(function(){
            $(this)[0].value = "";
        });

        if(itemcode == "9999"){
            form.val(formBoxId,{planid:planid,patientid:patientid,id:"",ALIBABAKEY:alibabakey,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
        }else if(itemcode == "04"){
            if(editData){
                form.val(formBoxId,editData)
                form.render(null, formBoxId);
            }else{
                form.val(formBoxId,{planid:planid,patientid:patientid,id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            }
        }else{
            if(editData){
                form.val(formBoxId,editData)
                form.render(null, formBoxId);
            }else{
                form.val(formBoxId,{planid:planid,patientid:patientid,id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            }
        }
        //执行重载
        layer.open({
            title:[formBoxName]
            ,type:1
            ,area: [windowWidth(),windowHeight()]
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){

                //监听提交
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    _postZdForm(postStr,index,formBoxId,itemcode);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }

    //监听工具条
    table.on('tool(listtable)', function(obj){
        var that = this;
        var data = obj.data;
        if(obj.event === 'edit'){
            if($(that).attr("disabled")=="disabled")return;
            var itemcode = data.itemcode;
            var formBoxName = data.item;
            var formBoxId = "zdBox" + itemcode;
            data["ALIBABAKEY"] = alibabakey;
            openAddFormBox(formBoxId,formBoxName,itemcode,data);
        } else if(obj.event === 'del'){
            if($(that).attr("disabled")=="disabled")return;
            var itemcode = data.itemcode == null ? "9999" :data.itemcode ;
            layer.confirm('你确认删除这条数据吗?', function(index){
                layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                layer.close(index)
                $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
                _delZd("id="+data.id,itemcode);
            });

        }
    });

    //触发事件
    var active = {
        reload: function(){
            var that = this;
            var itemcode = $(that).parents(".layui-colla-item").find(".layui-colla-title").attr("itemcode");
            if(itemcode == "8888"){
                loadJkzdFormTable(itemcode,'aiFormTable');
            }
            if(itemcode == "01"){
                loadJkzdFormTable(itemcode,'yszdFormTable');
            }
            if(itemcode == "02"){
                loadJkzdFormTable(itemcode,'ydzdFormTable');
            }
            if(itemcode == "03"){
                loadJkzdFormTable(itemcode,'jyzdFormTable');
            }
            if(itemcode == "05"){
                loadJkzdFormTable(itemcode,'jczdFormTable');
            }
            if(itemcode == "04"){
                loadYyzdFormTable();
            }
            if(itemcode == "9999"){
                loadSfjhFormTable();
            }
        }
        ,editYyzd: function (){
            var that = this;
            //执行重载
            parent.tzyyIndex = parent.layer.open({
                title:['调整用药']
                ,type: 2
                ,area: ['100%','100%']
                ,shade: [0.7, '#000000']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/v/plan/tzyy?planid='+mainid + '&patientid='+patientid, 'no']
                ,end:function(){
                    loadYyzdFormTable();
                }
            });
        }
        ,add: function(){
            var that = this;
            var itemcode = $(that).parents(".layui-colla-item").find(".layui-colla-title").attr("itemcode");
            console.log(itemcode)
            var formBoxName = $(that).parents(".layui-colla-item").find(".layui-colla-title").attr("item");
            var formBoxId = "zdBox" + itemcode;
            if(itemcode == "9999"){
                openAddFormBox(formBoxId,formBoxName,itemcode);
            }else if(itemcode == "04"){
                openAddFormBox(formBoxId,formBoxName,itemcode);
            }else{
                openAddFormBox(formBoxId,formBoxName,itemcode);
            }
        }
        ,batchdel: function(){
            var that = this;
            var itemcode = $(that).parents(".layui-colla-item").find(".layui-colla-title").attr("itemcode");
            var itemTbId = $(that).parents(".layui-colla-item").find(".layui-colla-title").attr("itemtbid");
            console.log(itemcode)
            var checkCkbox = table.checkStatus(itemTbId),data = checkCkbox.data;
            var len = checkCkbox.data.length;
            var idDatas = "";
            for(var i=0 , l = len; i < l; i++){
                if(i==0){
                    idDatas += "id="+checkCkbox.data[i].id
                }else{
                    idDatas += "&id="+checkCkbox.data[i].id
                }
            }
            if(len == 0){
                layer.msg('请选择您将要删除的记录',{time:2000});
                return false;
            } else{
                var info = '些';
                if(len==1)info='条';
                layer.confirm('你确认删除这'+info+'记录吗？', {
                    btn: ['确认','取消'] //按钮
                }, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
                    $("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
                    _delZd(idDatas,itemcode);
                }, function(){
                });
            }
        }
        ,addAI: function(){
            var that = this;
            var itemcode = $(that).attr("itemcode");

            if(detevalRd){
                if(!SessionStorageAPI.isSupported()){
                    layer.msg('浏览器不支持本地存储，无法使用AI辅助生成',{time:2000});
                    return
                }
                SessionStorageAPI.removeItem("detevalRd");
                SessionStorageAPI.setItem("detevalRd",detevalRd);
                layIndex1 = layer.open({
                    title:['AI辅助']
                    ,type: 2
                    ,area: [windowWidth(),windowHeight()]
                    ,shade: [0.7, '#d0d7f6']
                    ,scrollbar: true
                    ,maxmin: false
                    ,fixed:true
                    ,move: false
                    ,content: [ctxPath+'/exrule/ai?itemcode=' +itemcode, 'no']
                    ,end:function(){
                        // console.log(zdData)
                        if(zdData){
                            var formBoxId = "#zdBox"+itemcode;
                            $(formBoxId + " #content").val(zdData)
                        }
                    }
                });
            }else{
                layer.msg('数据不完整无法使用AI辅助生成',{time:2000});
            }

        }
        ,addZd: function(){
            var that = this;
            var itemcode = $(that).attr("itemcode");
            layIndex1 = layer.open({
                title:['引入模板内容']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getItemPlan?itemcode=' +itemcode, 'no']
                ,end:function(){
                    // console.log(zdData)
                    if(zdData){
                        var formBoxId = "#zdBox"+itemcode;
                        var oldContent = $(formBoxId + " #content").val();
                        if(oldContent.length > 0){
                            $(formBoxId + " #content").val(oldContent + '\r\n' + zdData)
                        }else{
                            $(formBoxId + " #content").val(zdData)
                        }
                    }
                }
            });
        }
        ,addYy: function(){
            var that = this;
            var itemcode = $(that).attr("itemcode");
            layIndex1 = layer.open({
                title:['引入用药模板']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getYyPlan?itemcode=' +itemcode, 'no']
                ,end:function(){
                    console.log(yyData)
                    if(yyData){
                        var formBoxId = "#zdBox"+itemcode;
                        $(formBoxId + " #ywmc").val(yyData.ywmc);
                        $(formBoxId + " #ywmcpy").val(yyData.ywmcpy);
                        $(formBoxId + " #yytype").val(yyData.yytype);
                        $(formBoxId + " #yytypecode").val(yyData.yytypecode);

                        $(formBoxId + " #yyff").val(yyData.yyff);
                        $(formBoxId + " #yyffcode").val(yyData.yyffcode);

                        $(formBoxId + " #yyjl").val(yyData.yyjl);
                        $(formBoxId + " #yypl").val(yyData.yypl);
                        $(formBoxId + " #yyplcode").val(yyData.yyplcode);

                        form.render('select');

                    }
                }
            });
        }
        ,selectDrugs: function() {
            layIndex1 = layer.open({
                title:['选择药品']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioDrugs', 'no']
                ,end:function(){
                    console.log(drugData)
                    if(drugData){
                        var formBoxId = "#zdBox04";
                        $(formBoxId + " #ywmc").val(drugData.dname);
                        $(formBoxId + " #ywmcpy").val(drugData.dpym);
                        $(formBoxId + " #yytype").val(drugData.yylx);
                        $(formBoxId + " #yytypecode").val(drugData.yylxcode);
                        $(formBoxId + " #yyjl").val(drugData.dguige);

                    }
                }
            });
        }
    };

    form.on('select(dicdata)', function(data){
        // lay-verify="required"
        var IdInput = $(data.elem).parent().parent().parent().find("input[name='"+data.elem.getAttribute("accept-name")+"']");
        var realText = $("#"+data.elem.id).find("option:selected").attr("realText");
        if(realText){
            IdInput.val($("#"+data.elem.id).find("option:selected").attr("realText"));
        }else{
            IdInput.val(data.othis.find("dd.layui-this").text());
        }

        if(data.elem.id == "pronamecode"){
            var idv = $("#"+data.elem.id).find("option:selected").attr("idv");
            $("#proid").val(idv);
        }
        if(data.elem.id == "proratecode"){

            var ratelevel = $("#"+data.elem.id).find("option:selected").attr("ratelevel");
            $("#ratelevel").val(ratelevel);

            if(data.elem.value == "#"){
                $("#executetime").removeClass("layui-disabled");
                $("#executetime").prop("disabled",null);
                $("#executetime").attr("lay-verify","required");
            }else{
                $("#executetime").addClass("layui-disabled");
                $("#executetime").prop("disabled",true);
                $("#executetime").attr("lay-verify",null);
                $("#executetime").val("");
            }
        }
        console.log(data,IdInput,data.elem.value,$("#"+data.elem.id).find("option:selected").attr("realText"),data.othis.find("dd.layui-this").text())
    });

    element.on('tab(planTab)', function(data){
        var tid = $(".planTab li.layui-this").attr("tid");
        var step = $(".planTab li.layui-this").attr("load-stetup");
        if(tid == "pg" && step == "0"){
            $(".planTab li.layui-this").attr("load-stetup","1");
            setTimeout(resetAscvdChart,100)
            setYyzdTable();
            setSfjhTable();
            setSfjhMxTable();
        }
    });

    $(document).ready(function(){
        setDeteval();
        setYyzdTable();
        setSfjhTable();
        setSfjhMxTable();
        loadJkzdFormTable("8888",'aiFormTable');
        loadJkzdFormTable("01",'yszdFormTable');
        loadJkzdFormTable("02",'ydzdFormTable');
        loadJkzdFormTable("03",'jyzdFormTable');
        loadJkzdFormTable("05",'jczdFormTable');
        loadYyzdFormTable();
        loadSfjhFormTable();


        bindClick();

        $(".initbox").remove();
    });

});