var zdData,yyData;
var layIndex1;

class SessionStorageAPI {
    // 验证浏览器支持性
    static isSupported() {
        return typeof window !== 'undefined' && 'sessionStorage' in window;
    }

    // 存储数据（支持对象、数组等复杂类型）
    static setItem(key, value) {
        if (!this.isSupported())  throw new Error('sessionStorage not supported');

        try {
            const data = typeof value === 'string' ? value : JSON.stringify(value);
            window.sessionStorage.setItem(key,  data);
        } catch (e) {
            if (e.name  === 'QuotaExceededError') {
                console.error(' 存储空间已满，无法保存数据');
            }
            throw e;
        }
    }

    // 读取数据（自动解析JSON格式）
    static getItem(key) {
        if (!this.isSupported())  return null;

        const data = window.sessionStorage.getItem(key);
        try {
            return data ? JSON.parse(data)  : data;
        } catch {
            return data; // 返回原始字符串
        }
    }

    // 删除指定数据
    static removeItem(key) {
        if (!this.isSupported())  return;
        window.sessionStorage.removeItem(key);
    }

    // 清空所有存储
    static clear() {
        if (!this.isSupported())  return;
        window.sessionStorage.clear();
    }

    // 获取键名
    static key(index) {
        if (!this.isSupported())  return null;
        return window.sessionStorage.key(index);
    }

    // 获取存储数量（只读属性）
    static get length() {
        if (!this.isSupported())  return 0;
        return window.sessionStorage.length;
    }
}

layui.use(['layer', 'loading', 'form','element', 'laydate',"table","planUtil","detevalUtil"], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        laydate = layui.laydate,
        planUtil = layui.planUtil,
        detevalUtil = layui.detevalUtil,
        element = layui.element,
        form = layui.form,
        loading = layui.loading;
    var table = layui.table;
    var ctxPath = layui.getContextPath("ctxPath","planjs");
    var mainid = layui.getContextPath("mainid","planjs");

    let layeroIndex,layeroId;
    function setDeteval(){
        if(detevalRd){
            detevalUtil.setEvalPage(detevalRd);//生成评估页面
        }
    }
    var resetAscvdChart = function (){
        if(detevalRd){
            detevalUtil.resetAscvdChart(detevalRd);
        }
    }

    window.getSfjhAll = function (){
        ajaxValFormNull();
        var postUrl = ctxPath+"/v/plan/fzjhAll";
        var postStr = "planid="+planid;
        $.getJSON(postUrl,postStr,function(jsondata){
            console.log(jsondata)
            if(jsondata.code=='200'){
                sfjhDetailRd = jsondata.rd.sfjhDetail;
                sfjhRd = jsondata.rd.sfjh;
                setSfjhTable();
                setSfjhMxTable();
            }
        });
    }
    window.setYyzdTable = function (){
        table.render({
            elem: '#yyzdResult'
            ,cols: [[ //标题栏
                {type:'numbers',title: '序号', width: 70}
                ,{field: 'yytype', title: '用药类型', width: 100}
                ,{field: 'ywmc', title: '药品名称', minWidth: 145}
                ,{field: 'yypl', title: '频率', width: 136}
                ,{field: 'yyjl', title: '用量', width: 100}
                ,{field: 'yyff', title: '用发', width: 100}
            ]]
            ,data: yyzdRd ? yyzdRd : []
            ,page: false
            ,height:315
            ,cellMinWidth:70
            ,limit:100000000000
        });
    }
    window.setSfjhTable = function (){
        table.render({
            elem: '#sfjhResult'
            ,cols: [[ //标题栏
                {type:'numbers',title: '序号', width: 70}
                ,{field: 'proname', title: '服务项目', minWidth: 145}
                ,{field: 'prorate', title: '服务频率', width: 100}
                ,{field: 'len', title: '服务次数', width: 100}
                ,{field: 'remark', title: '备注', width: 136}
            ]]
            ,data: sfjhRd ? sfjhRd : []
            ,page: false
            ,height:315
            ,limit:100000000000
            ,cellMinWidth:70
        });
    }
    window.setSfjhMxTable = function (){
        if(sfjhDetailRd) {
            table.render({
                elem: '#sfjhMxResult'
                ,cols: [[ //标题栏
                    {type:'numbers',title: '序号', width: 70}
                    ,{field: 'proname', title: '服务项目', minWidth: 145}
                    ,{field: 'executetime', title: '提醒时间', width: 174}
                ]]
                ,data: sfjhDetailRd ?sfjhDetailRd : []
                ,page: false
                ,height:315
                ,limit:100000000000
                ,cellMinWidth:70
            });
        }
    }
    $(document).ready(function(){
        setDeteval();
        setYyzdTable();
        setSfjhTable();
        setSfjhMxTable();
        $(".initbox").remove();
    });

});