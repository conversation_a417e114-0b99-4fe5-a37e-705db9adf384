var dataIndex = null;
var dataId = null;
var dataArray = null;

var reloadList = function(){
    layui.use(['layer'], function(){
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        layer.closeAll();
        loadListFunction();
    });
}
var loadListFunction = function(frist){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;

        var ctxPath = layui.getContextPath("ctxPath","listjs");

        function _loadMkInfo(msg){
            if(!frist)return;
            if(msg==''||null==msg)msg = '数据请求中...';
            if(frist){
                layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
            }else{
                layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
            }
        }
        _loadMkInfo();
        table.render({
            done: function(res, curr, count){
                setTimeout(function(){
                    dataArray = res.data;
                    $("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
                    $(".initbox").remove();
                    layer.closeAll("dialog");
                },300);
            }
            ,elem: '#listtable'
            ,url:ctxPath+"/v/plan/tpl/list"
            ,where: {
                itemcode: itemcode
                ,orgid: authUser.grade == "3" ? authUser.porgid : authUser.orgid
            }
            ,cols: [[
                {unresize:true,width:80,title: '',align:'center',templet:'#dx'}
                ,{field:'plantype', width:170, title: '心血管疾病风险分层'}
                ,{field:'yytype', width:100, title: '用药类型'}
                ,{field:'bbfz',  title: '伴并发症'}
                ,{field:'ywmc',  title: '药品名称'}
                ,{field:'yypl',  title: '频率'}
                ,{field:'yyjl',  title: '用量'}
                ,{field:'yyff',  title: '用发'}
            ]]
            ,page: false
            ,height:'full-152'
            ,cellMinWidth:100
            ,limit:10000000000000000
        });
    });
}

var resetSwClose = function(isClose){
    layui.use(['layer'], function(){
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        if(isClose){
            $('.layui-layer-setwin a.layui-layer-close1').hide();
        }else{
            $('.layui-layer-setwin a.layui-layer-close1').show();
        }
    });
}

layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
    var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
    var jQuery = layui.jquery;
    var element = layui.element;
    var table = layui.table;
    var form = layui.form;
    var zFun =layui.treeselectTable;

    $(".menuTabel").css({"max-width":$(".fulllistbox").width()-$(".leftMenu").width()});
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    //监听工具条
    table.on('tool(listtable)', function(obj){
        var that = this;
        var data = obj.data;
    });
    form.on('radio(selid)', function(data){
        dataIndex = $(this).parent().parent().parent().attr("data-index");
        dataId = data.value;
        //console.log(index); //得到radio原始DOM对象
        //console.log(data); //得到radio原始DOM对象
        //console.log(data.elem); //得到radio原始DOM对象
        //console.log(data.value); //被点击的radio的value值
    });
    //触发事件
    var active = {
        reload: function(){
            var that = this;
            loadListFunction();
        }
        ,cancel: function(){
            parent.layer.close(parent.layIndex1);
        }
        ,confirm: function(){
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            $(that).attr("disabled","disabled").addClass("layui-btn-disabled");
            if(null != dataIndex){
                var selData = dataArray[dataIndex];
                var body = parent.layer.getChildFrame('body',parent.layIndex1);
                parent.yyData = selData;
                parent.layer.close(parent.layIndex1)
            }else{
                $(that).attr("disabled",null).removeClass("layui-btn-disabled");
                layer.msg('请选择一个模板',{time:2000});
                return false;
            }
        }
    };
    $('.layui-btn.user-search').on('click', function(){
        var othis = $(this), method = othis.data('method');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });

    loadListFunction(true);
});