var drugData;
var layIndex1;
layui.use(['layer', 'loading', 'form','element','laytpl','resize'], function() {
    var $ = layui.jquery,
        jQuery = layui.jquery,
        layer = layui.layer,
        element = layui.element,
        form = layui.form,
        laytpl = layui.laytpl,
        loading = layui.loading;

    var ctxPath = layui.getContextPath("ctxPath","plantpljs");
    let layeroIndex,layeroId;
    var tplTempletConfig = {"8888" : "aiTemplet" ,"01" : "ysTemplet" ,"02" : "ydTemplet" ,"03" : "jyTemplet" ,"04" : "yyTemplet" ,"05" : "jcTemplet","06" : "fwqdTemplet","07" : "fwqdTemplet","08" : "fwqdTemplet"};
    var yyFormArea = ['520px','auto'];
    var fwFormArea = ['640px','auto'];

    function bindClick(){

        $('.layui-form-mid.content .content-copy').unbind("click");
        $('.layui-btn').unbind("click");
        $('.layui-input').unbind("click");

        $('.layui-btn').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-input').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-form-mid.content  .content-copy').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }
    bindClick();
    form.on('select(dicdata-auto)', function(data){
        if(data.type){
            if(layeroId){
                var layerContent = $("#"+layeroId).find(".layui-layer-content");
                if(data.type == "show"){
                    layerContent.css({height:layerContent[0].scrollHeight})
                }else{
                    layerContent.css({height:"auto"})
                }
                if(layeroIndex){
                    layer.AutoTop(layeroIndex)
                }
            }
        }
    });
    form.on('select(dicdata)', function(data){

        var IdInput = $(data.elem).parent().parent().parent().find("input[name='"+data.elem.getAttribute("accept-name")+"']");
        var realText = $("#"+data.elem.id).find("option:selected").attr("realText");
        if(realText){
            IdInput.val($("#"+data.elem.id).find("option:selected").attr("realText"));
        }else{
            IdInput.val(data.othis.find("dd.layui-this").text());
        }

        console.log(data,IdInput,$("#"+data.elem.id).find("option:selected").attr("realText"),data.othis.find("dd.layui-this").text())
    });

    element.on('tab(planTab)', function(data){
        // console.log(data);
        // console.log(this); //当前Tab标题所在的原始DOM元素
        // console.log(data.index); //得到当前Tab的所在下标
        // console.log(data.elem); //得到当前的Tab大容器
        var itemcode = $(this).attr("val");
        var tabContentId = ".tab-" + itemcode + "Content";
        var loadStetup = $(tabContentId).attr("load-stetup");
        if(loadStetup == "0"){
            getTplByItem(itemcode);
        }
    });

    form.on('radio(chk-radio-filter)', function(data){
        console.log($(data.elem).parent().parent().parent().html())
        let title = data.elem.title;
        let acceptName = data.elem.getAttribute("accept-name");
        var IdInput = $(data.elem).parent().parent().parent().find("input[name='"+acceptName+"']");
        IdInput.val(title);
    });

    //触发事件
    var active = {
        addYs: function(){
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            var formBoxId = "zdBox"+$(that).attr("itemcode");
            if($(that).attr("itemcode") == "06" || $(that).attr("itemcode") == "07" || $(that).attr("itemcode") == "08"){
                openAddFormBox($(that).attr("formBoxId"),$(that).attr("formBoxName"),$(that).attr("itemcode"),fwFormArea);
            }else if($(that).attr("itemcode") == "04"){
                openAddFormBox($(that).attr("formBoxId"),$(that).attr("formBoxName"),$(that).attr("itemcode"),yyFormArea);
            }else{
                openAddFormBox($(that).attr("formBoxId"),$(that).attr("formBoxName"),$(that).attr("itemcode"));
            }
        }
        ,edit: function(){
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            var formBoxId = "zdBox"+$(that).attr("itemcode");
            document.forms[formBoxId].reset();
            $("#" +formBoxId +" input.ipt-hidden").attr("type","hidden");
            if($(that).attr("itemcode") == "06" || $(that).attr("itemcode") == "07" || $(that).attr("itemcode") == "08"){
                form.val(formBoxId,{ALIBABAKEY:alibabakey,item:$(that).attr("formBoxName"),itemcode:$(that).attr("itemcode"),orgid:'000000000000000000000000000000000000',orgcode:'0',orgname:'根节点'});
            }else if($(that).attr("itemcode") == "04"){
                form.val(formBoxId,{ALIBABAKEY:alibabakey,item:$(that).attr("formBoxName"),itemcode:$(that).attr("itemcode"),orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            }else{
                form.val(formBoxId,{ALIBABAKEY:alibabakey,item:$(that).attr("formBoxName"),itemcode:$(that).attr("itemcode"),orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            }
            var id = $(that).attr("did");
            getTplById(id);
        }
        ,del: function(){
            var that = this;
            if($(that).attr("disabled")=="disabled")return;
            var id = $(that).attr("did");
            var itemcode = $(that).attr("itemcode");
            delTplById(id,itemcode);
        }
        ,refresh: function (){
            var that = this;
            var itemcode = $(that).attr("itemcode");
            getTplByItem(itemcode);
        }
        ,content: function (){
            var that = this;
            let box = $(this);
            if (box[0].scrollWidth > box[0].offsetWidth) {
                box.parent().addClass("show");
            }
        }
        ,close: function (){
            let box = $(this);
            box.parent().removeClass("show");
        }
        ,selectDrugs: function() {
            layIndex1 = layer.open({
                title:['选择药品']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioDrugs', 'no']
                ,end:function(){
                    console.log(drugData)
                    if(drugData){
                        var formBoxId = "#zdBox04";
                        $(formBoxId + " #ywmc").val(drugData.dname);
                        $(formBoxId + " #ywmcpy").val(drugData.dpym);
                        $(formBoxId + " #yytype").val(drugData.yylx);
                        $(formBoxId + " #yytypecode").val(drugData.yylxcode);
                        $(formBoxId + " #yyjl").val(drugData.dguige);


                    }
                }
            });
        }
    };

    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }

    function delTplById(id,itemcode){
        loading.block({
            type: 3,
            elem: '.layui-show',
            msg: ''
        });
        var queryUrl = ctxPath+"/v/plan/tpl/del";
        var queryParams = "id="+id;
        ajaxValForm();
        $.getJSON(queryUrl,queryParams,function(jsondata){
            if(jsondata.code=='200'){
                getTplByItem(itemcode);
                layer.msg('删除数据成功',{time:1500},function(){
                });
            }else{
                layer.msg(jsondata.msg,{time:2000},function(){});
            }
        });
    }

    function getTplById(id){
        loading.block({
            type: 3,
            elem: '.layui-show',
            msg: ''
        });
        var queryUrl = ctxPath+"/v/plan/tpl/get";
        var queryParams = "id="+id;
        ajaxValForm();
        $.getJSON(queryUrl,queryParams,function(jsondata){
            if(jsondata.code=='200'){
                var formBoxId = "zdBox"+jsondata.rd.itemcode;
                //console.log(formBoxId)
                //console.log(JSON.parse(JSON.stringify(jsondata.rd)))
                form.val(formBoxId, JSON.parse(JSON.stringify(jsondata.rd)));
                if(jsondata.rd.itemcode == "06" || jsondata.rd.itemcode == "07" || jsondata.rd.itemcode == "08"){
                    openFormBox(formBoxId,jsondata.rd.item,jsondata.rd.itemcode,fwFormArea);
                }else if(jsondata.rd.itemcode == "04"){
                    openFormBox(formBoxId,jsondata.rd.item,jsondata.rd.itemcode,yyFormArea);
                }else{
                    openFormBox(formBoxId,jsondata.rd.item,jsondata.rd.itemcode);
                }
            }else{
                layer.msg(jsondata.msg,{time:2000},function(){});
            }
            loading.blockRemove(".layui-show", 0);
        });
    }
    function getTplByItem(_itemcode){

        var itemcode = $(".layui-tab .layui-tab-title li.layui-this").attr("val");
        if(_itemcode) itemcode = _itemcode;

        loading.block({
            type: 3,
            elem: '.tab-'+itemcode+'Content',
            msg: ''
        })

        var orgid = authUser.orgid;
        var queryUrl = ctxPath+"/v/plan/tpl/list";
        var queryParams = "itemcode="+itemcode + "&orgid="+orgid;

        var getTitleTpl = document.getElementById("tleTemplet").innerHTML
            ,titleView  = $(".tab-"+itemcode+"Content .layui-row-title")[0];

        var getTpl = document.getElementById(tplTempletConfig[itemcode]).innerHTML
            ,view  = $(".tab-"+itemcode+"Content .layui-row-cus .tplbox")[0];

        let itemWidth = 567;

        //药品指导
        if(itemcode == "04"){
            getTitleTpl = document.getElementById("tle2Templet").innerHTML;
            itemWidth = 1018;
        }
        //服务清单
        if(itemcode == "06" || itemcode == "07" || itemcode == "08"){
            queryParams = "itemcode="+itemcode;
            getTitleTpl = document.getElementById("tle3Templet").innerHTML;
        }

        ajaxValForm();
        $.getJSON(queryUrl,queryParams,function(jsondata){
            if(jsondata.code=='200'){

                if(jsondata.data && jsondata.data.length > 0){

                    if(itemcode == "06" || itemcode == "07" || itemcode == "08"){

                        laytpl(getTpl).render(jsondata, function(html){
                            view.innerHTML = html;
                        });
                        laytpl(getTitleTpl).render({"len": 1}, function(html){
                            titleView.innerHTML = html;
                        });

                    } else {

                        laytpl(getTpl).render(jsondata, function(html){
                            view.innerHTML = html;
                        });

                        let outWidth = $(".tab-"+itemcode+"Content")[0].getBoundingClientRect().width;

                        let aarlength = parseFloat(outWidth).toFixed(1) / parseFloat(itemWidth).toFixed(1);

                        laytpl(getTitleTpl).render({"len": parseInt(aarlength)}, function(html){
                            titleView.innerHTML = html;
                        });

                        console.log(outWidth,itemWidth,aarlength,parseInt(aarlength),Math.round(aarlength))
                    }

                    var tabContentId = ".tab-" + itemcode + "Content";
                    $(tabContentId).attr("load-stetup",1);

                }else{

                    laytpl(getTpl).render(jsondata, function(html){
                        view.innerHTML = "";
                    });
                    laytpl(getTitleTpl).render({"len": 0}, function(html){
                        titleView.innerHTML = "";
                    });
                    layer.msg("暂无数据",{time:1000},function(){});
                }

            }else{
                layer.msg(jsondata.msg,{time:2000},function(){});
            }
            bindClick();
            loading.blockRemove('.tab-'+itemcode+'Content', 0);
            $(".initbox").remove();
        });
    }

    /**
     * 提交饮食指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postZdForm(postStr,index,formBoxId,itemcode){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/plan/tpl/save",postStr,function(jsondata){
            if(jsondata.code=='200'){
                getTplByItem(itemcode)
                layer.msg('保存成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    if(index){layer.close(index);}
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }
        });
    }
    function openAddFormBox(formBoxId,formBoxName,itemcode,area){
        let defaultArea = ['500px'];
        if(area) defaultArea = area;
        $("#"+formBoxId).show();
        document.forms[formBoxId].reset();
        $("#" +formBoxId +" input.ipt-hidden").each(function(){
            $(this)[0].value = "";
            //console.log(formBoxId,$(this),$(this).html(),$(this).val())
        });
        if(itemcode == "06" || itemcode == "07" || itemcode == "08"){
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","hidden");
            form.val(formBoxId,{id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:'000000000000000000000000000000000000',orgcode:'0',orgname:'根节点'});
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","text");
        }else if(itemcode == "04"){
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","hidden");
            form.val(formBoxId,{id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","text");
        }else{
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","hidden");
            form.val(formBoxId,{id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
            // $("#" +formBoxId +" input.ipt-hidden").attr("type","text");
        }
        //执行重载
        layer.open({
            title:[formBoxName]
            ,type:1
            ,area: defaultArea
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.find(".layui-layer-content").css({height:"auto"});
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){
                //监听提交
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    _postZdForm(postStr,index,formBoxId,itemcode);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }

    function openFormBox(formBoxId,formBoxName,itemcode,area){
        let defaultArea = ['500px'];
        if(area) defaultArea = area;
        $("#"+formBoxId).show();
        //执行重载
        layer.open({
            title:[formBoxName]
            ,type:1
            ,area: defaultArea
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.find(".layui-layer-content").css({height:"auto"});
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){

                //监听提交
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    _postZdForm(postStr,index,formBoxId,itemcode);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }

    $(document).ready(function(){
        getTplByItem();
        $(".initbox").remove();
    })

    window.onresize = function() {

        var itemcode = $(".layui-tab .layui-tab-title li.layui-this").attr("val");
        if(itemcode == "06" || itemcode == "07" || itemcode == "08"){
            return;
        }
        var getTitleTpl = document.getElementById("tleTemplet").innerHTML
            ,titleView  = $(".layui-show .layui-row-title")[0];

        let itemWidth = 567;

        //药品指导
        if(itemcode == "04"){
            getTitleTpl = document.getElementById("tle2Templet").innerHTML;
            itemWidth = 878;
        }

        let outWidth = $(".layui-show")[0].getBoundingClientRect().width;
        let aarlength = parseFloat(outWidth).toFixed(1) / parseFloat(itemWidth).toFixed(1);
        // Math.round(aarlength)
        laytpl(getTitleTpl).render({"len": parseInt(aarlength)}, function(html){
            titleView.innerHTML = html;
        });

        //console.log(outWidth,itemWidth,aarlength,parseInt(aarlength),Math.round(aarlength))
    }
});