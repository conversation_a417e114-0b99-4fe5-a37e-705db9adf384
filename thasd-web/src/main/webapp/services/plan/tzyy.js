Array.prototype.removeById = function(val) {
    var array = this;
    for (var i = 0;i < array.length; i++){
        if (array[i].id == val){

            array.splice(i,1);

        }
    }
};

var zdData,yyData;
var layIndex1;
// {'id':'','yytype':'','yytypecode':'','ywmc':'','ywmcpy':'','yypl':'','yyplcode':'','yyjl':'','yyff':'','yyffcode':'','isadd':'1'}
var yyzdRd = [];
function loadYyzdFormTable(){
    layui.use(['layer','table', 'form','element'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery,element = layui.element,
            form = layui.form, layer = layui.layer;//独立版的layer无需执行这一句

        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","planjs");
        var cols = [[
            {unresize:true, width:90, align:'center', toolbar:'#listtable-check',title:'是否保留'}
            ,{unresize:true,type:'numbers',width:60,title:'序号'}
            ,{unresize:true,field: 'yytype', title: '用药类型',width:90 }
            ,{unresize:true,field: 'ywmc', title: '药品名称',width:100 }
            ,{unresize:true,field: 'yypl',title: '频率',width:100 }
            ,{unresize:true,field: 'yyjl',title: '用量', width:70}
            ,{unresize:true,field: 'yyff', title: '用发',width:70 }
        ]];
        var thisTable = table.render({
            done: function(res, curr, count){
                bindClick();
            },
            toolbar: '<div class="dqyyBox">当前用药</div>'
            ,defaultToolbar: []
            ,elem: '#yyzdFormTable'
            ,id: 'yyzdFormTableTbl'
            ,url:ctxPath+'/v/plan/yyzdList'
            ,where: { // planid: planid
                patientid:patientid
            }
            ,cols: cols
            ,page: false
            ,height:"full-0"
            ,limit:100000000000
        });
        form.on('checkbox(chk-dqyy-filter)', function(data){
            console.log(data); //当前行的一些常用操作集合
            if(data.elem.checked){
                data.elem.checked = false;
            }else{
                data.elem.checked = true;
            }
        });

        //触发行单击事件
        table.on('row(listtable)', function(obj){

            var checked = $(obj.tr.selector).find('input:checkbox[name="dqyy-chk"]').is(':checked');

            console.log(obj) //得到当前行元素对象
            console.log(obj.tr,obj.tr.selector,obj.tr[0].selector) //得到当前行元素对象
            console.log(obj.data) //得到当前行数据
            console.log(checked)

            //{'id':'','yytype':'','yytypecode':'','ywmc':'','ywmcpy':'','yypl':'','yyplcode':'','yyjl':'','yyff':'','yyffcode':'','isadd':'1'}
            if (checked){
                $(obj.tr.selector).find('input:checkbox[name="dqyy-chk"]').prop('checked', false);
                yyzdRd.removeById(obj.data.id);
            }else{
                $(obj.tr.selector).find('input:checkbox[name="dqyy-chk"]').prop('checked', true);
                let addRd = {id:obj.data.id,yytype:obj.data.yytype,yytypecode:obj.data.yytypecode,ywmc:obj.data.ywmc,ywmcpy:obj.data.ywmcpy,yypl:obj.data.yypl,yyplcode:obj.data.yyplcode,yyjl:obj.data.yyjl,yyff:obj.data.yyff,yyffcode:obj.data.yyffcode,isadd:1};
                yyzdRd.unshift(addRd);
            }
            form.render('checkbox');
            loadYyzdFormTable2();
        });
    });
}

function loadYyzdFormTable2(){
    layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
        var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
        var table = layui.table;
        var ctxPath = layui.getContextPath("ctxPath","planjs");
        var cols = [[
            {unresize:true,type:'numbers',width:60,title:'序号'}
            ,{unresize:true,field: 'yytype', title: '用药类型',width:90 }
            ,{unresize:true,field: 'ywmc', title: '药品名称' }
            ,{unresize:true,field: 'yypl',title: '频率' }
            ,{unresize:true,field: 'yyjl',title: '用量'}
            ,{unresize:true,field: 'yyff', title: '用发' }
            ,{unresize:true,  width:129,align:'left', toolbar:'#listtable-opt2',title: '操作'}
        ]];
        var thisTable = table.render({
            text: {
                none: '<b style="color:#ff2222">当前用药需要保留请点击左侧复选框勾选或行选,不勾选的当前用药会记入历史用药;</br>点击添加用药，添加新的用药，完成后检查是否有误，最后点击保存调整用药指导</b>' //默认：无数据。
            },
            done: function(res, curr, count){
                bindClick();
            },
            toolbar:  '#toolbarYyzd'
            ,defaultToolbar: []
            ,elem: '#yyzdFormTable2'
            ,id: 'yyzdFormTableTbl2'
            ,cols: cols
            ,data:yyzdRd ? yyzdRd : []
            ,page: false
            ,height:"full-0"
            ,limit:100000000000
        });
    });
}

layui.use(['layer', 'loading', 'form','element', 'laydate',"table"], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        laydate = layui.laydate,
        element = layui.element,
        form = layui.form,
        loading = layui.loading;
    var table = layui.table;
    var ctxPath = layui.getContextPath("ctxPath","planjs");

    let layeroIndex,layeroId;



    var windowWidth = function (){

        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        //console.log(seaWidth,seaHeight)

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowWidth2;
    }

    var windowHeight = function (){
        var seaWidth = $("body").width();
        var seaHeight = $("body").height();

        let windowWidth2 = (seaWidth-20) + "px";
        let  windowHeight2 = (seaHeight-20) + "px";
        return windowHeight2;
    }

    window.bindClick = function (){
        $('.layui-btn').unbind("click");
        $('.layui-input').unbind("click");

        $('.layui-btn').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-input').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                return false;
            }
        });
    }

    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postZdForm(postData){
        ajaxValForm();
        var postUrl = ctxPath+"/v/plan/saveTzyy";
        $.ajax({
            url: postUrl,
            data: JSON.stringify(postData),
            method: "post",
            dataType: "json",
            contentType: 'application/json',
            success: function (jsondata) {
                console.log(jsondata)
                if(jsondata.code=='200'){
                    layer.msg('保存成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                        parent.layer.close(parent.tzyyIndex);
                    });
                }else{
                    layer.msg(jsondata.msg,{time:2000});
                }
            }
        });
    }

    //监听工具条
    table.on('tool(listtable2)', function(obj){
        var event = obj.event;
        var that = this;
        var data = obj.data;
        if(event === 'edit'){
            if($(that).attr("disabled")=="disabled")return;
            var itemcode = "04";
            var formBoxName = "用药指导";
            var formBoxId = "zdBox" + itemcode;
            data["ALIBABAKEY"] = alibabakey;
            openAddFormBox(formBoxId,formBoxName,itemcode,obj);
        } else if(event === 'del'){
            if($(that).attr("disabled")=="disabled")return;
            layer.confirm('你确认移除这条数据吗?', function(index){
                yyzdRd.removeById(obj.data.id);
                obj.del();
                layer.close(index);

            });
        }
        obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
    });

    function openAddFormBox(formBoxId,formBoxName,itemcode,obj){
        $("#"+formBoxId).show();
        document.forms[formBoxId].reset();
        $("#" +formBoxId +" input.ipt-hidden").each(function(){
            $(this)[0].value = "";
        });
        if(obj){
            form.val(formBoxId,obj.data)
            form.render(null, formBoxId);
        }else{
            form.val(formBoxId,{planid:planid,id:"",ALIBABAKEY:alibabakey,item:formBoxName,itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
        }
        //执行重载
        layer.open({
            title:[formBoxName]
            ,type:1
            ,area: ["600px","500px"]
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){
                //监听提交
                form.on('submit(tpVer)', function(data){
                    var postStr = form.val(formBoxId);
                    //console.log(data)
                    //console.log(postStr)
                    let addRd = {id:data.field.id,yytype:data.field.yytype,yytypecode:data.field.yytypecode,ywmc:data.field.ywmc,ywmcpy:data.field.ywmcpy,yypl:data.field.yypl,yyplcode:data.field.yyplcode,yyjl:data.field.yyjl,yyff:data.field.yyff,yyffcode:data.field.yyffcode,isadd:0};
                    if(obj){
                        obj.update(addRd, true);
                        yyzdRd = table.cache["yyzdFormTableTbl2"];
                    }else{
                        yyzdRd.push(addRd);
                    }
                    loadYyzdFormTable2();
                    if(index){layer.close(index);}
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }
    //触发事件
    var active = {
        save: function(){
            layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
            var that = this;

            console.log(table.cache["yyzdFormTableTbl2"])
            console.log(yyzdRd)

            var postData = {patientid:patientid,planid:planid,alibabakey:alibabakey,item:"用药指导",itemcode:"04",orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname};
            var addList = [];
            var notCheck = [];
            for(var i = 0; i < yyzdRd.length; i++){
                if(yyzdRd[i].isadd == 0){
                    addList.push(yyzdRd[i]);
                }
            }
            $("input[name='dqyy-chk']:not(:checked)").each(function() {
                notCheck.push($(this).val());
            });
            postData["notcheckids"] = notCheck;
            postData["addlist"] = addList;
            _postZdForm(postData);
            //console.log(postData,JSON.stringify(postData));

        },
        add: function(){
            var that = this;
            var itemcode = "04";
            var formBoxName = "用药指导";
            var formBoxId = "zdBox" + itemcode;
            openAddFormBox(formBoxId,formBoxName,itemcode);
        }
        ,addYy: function(){
            var that = this;
            var itemcode = $(that).attr("itemcode");
            layIndex1 = layer.open({
                title:['引入用药模板']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getYyPlan?itemcode=' +itemcode, 'no']
                ,end:function(){
                    console.log(yyData)
                    if(yyData){
                        var formBoxId = "#zdBox"+itemcode;
                        $(formBoxId + " #ywmc").val(yyData.ywmc);
                        $(formBoxId + " #ywmcpy").val(yyData.ywmcpy);
                        $(formBoxId + " #yytype").val(yyData.yytype);
                        $(formBoxId + " #yytypecode").val(yyData.yytypecode);

                        $(formBoxId + " #yyff").val(yyData.yyff);
                        $(formBoxId + " #yyffcode").val(yyData.yyffcode);

                        $(formBoxId + " #yyjl").val(yyData.yyjl);
                        $(formBoxId + " #yypl").val(yyData.yypl);
                        $(formBoxId + " #yyplcode").val(yyData.yyplcode);

                        form.render('select');

                    }
                }
            });
        }
        ,selectDrugs: function() {
            layIndex1 = layer.open({
                title:['选择药品']
                ,type: 2
                ,area: ['96%','96%']
                ,shade: [0.7, '#d0d7f6']
                ,scrollbar: true
                ,maxmin: false
                ,fixed:true
                ,move: false
                ,content: [ctxPath+'/exrule/getRadioDrugs', 'no']
                ,end:function(){
                    console.log(drugData)
                    if(drugData){

                        var formBoxId = "#zdBox04";
                        document.forms["zdBox04"].reset();
                        $(formBoxId +" input.ipt-hidden").each(function(){
                            $(this)[0].value = "";
                        });

                        form.val("zdBox04",{planid:planid,id:"",ALIBABAKEY:alibabakey,item:"用药指导",itemcode:itemcode,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname});
                        form.render();
                        $(formBoxId + " #ywmc").val(drugData.dname);
                        $(formBoxId + " #ywmcpy").val(drugData.dpym);
                        $(formBoxId + " #yytype").val(drugData.yylx);
                        $(formBoxId + " #yytypecode").val(drugData.yylxcode);
                        $(formBoxId + " #yyjl").val(drugData.dguige);
                        form.render('select');

                    }
                }
            });
        }
    };
    form.on('select(dicdata-auto)', function(data){
        if(data.type){
            if(layeroId){
                var layerContent = $("#"+layeroId).find(".layui-layer-content");
                if(data.type == "show"){
                    layerContent.css({height:layerContent[0].scrollHeight})
                }else{
                    layerContent.css({height:"auto"})
                }
                if(layeroIndex){
                    layer.AutoTop(layeroIndex)
                }
            }
        }
    });
    form.on('select(dicdata)', function(data){
        // lay-verify="required"
        var IdInput = $(data.elem).parent().parent().parent().find("input[name='"+data.elem.getAttribute("accept-name")+"']");
        var realText = $("#"+data.elem.id).find("option:selected").attr("realText");
        if(realText){
            IdInput.val($("#"+data.elem.id).find("option:selected").attr("realText"));
        }else{
            IdInput.val(data.othis.find("dd.layui-this").text());
        }
        //console.log(data,IdInput,data.elem.value,$("#"+data.elem.id).find("option:selected").attr("realText"),data.othis.find("dd.layui-this").text())
    });



    $(document).ready(function(){
        loadYyzdFormTable();
        loadYyzdFormTable2();
        bindClick();
        $(".initbox").remove();
    });

});