layui.use(['form','table','laytpl', 'layedit', 'laydate'], function(){
    var form = layui.form
        ,$ = layui.jquery
        ,layer = layui.layer
        ,laydate = layui.laydate
    var table = layui.table;
    var laytpl = layui.laytpl;

    Date.prototype.FormatNow = function (fmt) { //author: meizz
        var o = {
            "M+": this.getMonth(), //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }

    function getUParam(name,id) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
        var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
        if (r != null) return unescape(r[2]); return ""; //返回参数值
    }

    var ctxPath = getUParam("ctxPath","plansfjl");
    var id = getUParam("mainid","plansfjl");
    var view = getUParam("view","plansfjl");

    let layeroIndex,layeroId;
    /**
     * load信息提示 带遮罩层
     * @param msg 提示信息
     * @code{default=加载中...}
     */
    function _loadMkInfo(msg){
        if(msg==''||null==msg)msg = '加载中...';
        layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
    }
    //读取错误提示
    function _serverFail(){
        layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }

    function ajaxValFormNull(){
        $.ajaxSetup({
            error:function(x,e){
                return false;
            }
        });
    }

    /**
     * ajax预处理
     * @param id sumitid
     */
    function ajaxValForm(){
        $.ajaxSetup({
            error:function(x,e){
                _serverFail();
                parent.resetSwClose(false);
                $("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
                return false;
            }
        });
    }

    /**
     * 生成表单数据
     * @param id 表单id
     * @code{.form、#form}
     */
    function _getForm(){
        ajaxValForm();
        $.getJSON(ctxPath+"/v/patients/gxzsf/get","id="+id,function(jsondata){
            if(jsondata.code=='200'){
                //表单初始赋值
                form.val('formtable', JSON.parse(JSON.stringify(jsondata.rd)));
            }else{
                $("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
                layer.msg(jsondata.msg,{time:2000},function(){
                    parent.layer.closeAll('iframe');
                });
            }
            $(".initbox").remove();
            layer.closeAll("dialog");
        });
    }

    /**
     * 提交指导表单
     * @param id 表单id
     * @code{.form、#form}
     */
    function _postZdForm(postStr,index,formBoxId){
        ajaxValForm();
        var postUrl = ctxPath+"/v/plansfjl/saveOrUpdate";
        $.getJSON(postUrl,postStr,function(jsondata){
            if(isTopIndex){ table.reloadData("noticeResult");}
            if(isPcenterHome){ table.reloadData("dfzTableTbl");}

            if(jsondata.code=='200'){
                layer.msg('保存成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
                    if(index){layer.close(index);}
                });
            }else{
                layer.msg(jsondata.msg,{time:2000});
            }

        });
    }

    function openAddFormBox(formBoxId,title,addData,editData){
        console.log(formBoxId,title,addData,editData)
        $("#"+formBoxId).show();
        document.forms[formBoxId].reset();
        $("#" +formBoxId +" input.ipt-hidden").each(function(){
            $(this)[0].value = "";
        });
        if(addData){

            let dateNow = new Date();
            let dateTime = dateNow.FormatNow("yyyy-MM-dd");
            form.val(formBoxId,{ALIBABAKEY:alibabaKey,orgid:authUser.orgid,orgcode:authUser.orgcode,orgname:authUser.orgname ,fwsj: dateTime,id:"",detailid:addData.id,proname:addData.proname,name:addData.name});
            form.render(null, formBoxId);
        }
        if(editData){
            form.val(formBoxId,editData)
            form.render(null, formBoxId);
        }
        //执行重载
        layer.open({
            title:[title]
            ,type:1
            ,area: ["600px","500px"]
            ,shade: [0.7, '#d0d7f6']
            ,scrollbar: true
            ,maxmin: false
            ,fixed:true
            ,move: false
            ,content: $("#"+formBoxId)
            ,btn: ['确定', '取消']
            ,success: function(layero, index){
                layeroIndex = index;
                layeroId = layero.attr("id");
                layero.addClass('layui-form');
                layero.find('.layui-layer-btn0').attr({
                    'lay-filter': 'tpVer',
                    'lay-submit': ''
                });
                form.render()
            }
            ,yes: function(index, layero){
                //监听提交
                form.on('submit(tpVer)', function(data){
                    layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
                    var postStr = form.val(formBoxId);
                    _postZdForm(postStr,index,formBoxId);
                    return false;
                });
            }
            ,btn2: function(index, layero){
            }
            ,end:function(){
                layeroIndex = null;
                layeroId = null;
                $("#"+formBoxId).hide();
            }
        });
    }

    var dateNow = new Date();
    var dateTime = dateNow.FormatNow("yyyy-MM-dd");
    laydate.render({type: 'date',format:'yyyy-MM-dd',trigger: 'click',
        elem: '#fwsj',value: dateTime
    });

    //触发事件
    var active = {
        // reloadMessage: function(){
        //     if(isTopIndex){ table.reloadData("noticeResult");}
        //     if(isPcenterHome){ table.reloadData("dfzTableTbl");}
        // },
        cancel: function(){
            parent.layer.closeAll('iframe');
        }
        ,addPlanSfjl: function(){
            var that = this;
            openAddFormBox("planSfjlBox","新增服务记录");
        }
    };
    function listtable(obj){
        var that = this;
        var data = obj.data;
        if(obj.event === 'addPlanSfjl'){
            if($(that).attr("disabled")=="disabled")return;
            openAddFormBox("planSfjlBox","新增服务记录",data);
        } else if(obj.event === 'editPlanSfjl'){
            if($(that).attr("disabled")=="disabled")return;
            openAddFormBox("planSfjlBox","修改服务记录",null,data);
        }
    }
    table.on('tool(dfzlisttable)', function(obj){
        listtable(obj);
    });
    table.on('tool(noticeResult)', function(obj){
        listtable(obj);
    });
    table.on('tool(plansfjlTbl)', function(obj){
        listtable(obj);
    });

    $('.layui-input').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    //自定义验证规则
    form.verify({
        formChk: function(value, item){ //value：表单的值、item：表单的DOM对象
            //console.log(item)
            let name = item.name;
            let chkLength = $(":input[name='"+name+"']:checked").length;
            if(chkLength == 0){
                return '请至少选择一个选项!';
            }
        }
        ,formRadio: function(value, item){ //value：表单的值、item：表单的DOM对象
            //console.log(item)
            let name = item.name;
            let chkLength = $(":input[name='"+name+"']:checked").length;
            if(chkLength == 0){
                return '请至少选择一个选项!';
            }
        }
    });

    window.bindClickBySfjl = function (){
        $('.layui-btn').unbind("click");
        $('.layui-input').unbind("click");

        $('.layui-btn').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
        $('.layui-input').bind("click",function(){
            var othis = $(this), method = othis.data('type');
            active[method] ? active[method].call(this, othis) : '';
        });
    }

    if(id != ""){
        _loadMkInfo();
        _getForm();
    }else{
        $(".initbox").remove();
    }
});