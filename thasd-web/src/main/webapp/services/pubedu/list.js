layui.use(['layer','element','form','table', 'util'], function(){//独立版的layer无需执行这一句

	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var element = layui.element;
	var util = layui.util;
	var table = layui.table;
	var windowWidth = '1110px';
	var windowHeight = '96%';
	var frist = true;

	function getUParam(name,id) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);//匹配目标参数
		if (r != null) return unescape(r[2]); return ""; //返回参数值
	}
	var ctxPath = getUParam("ctxPath","listjs");

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '数据请求中...';
		if(frist){
			layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		}else{
			//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
	}
	_loadMkInfo();

	let optionConfig = {
		done: function(res, curr, count){
			frist = false;
			setTimeout(function(){
				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
				$(".initbox").remove();
				layer.closeAll("dialog");
			},300);
		}
		,elem: '#listtable'
		,url:ctxPath+'/v/pubedu/list'
		,where: {
			code: $("#code").val()
			,name: $("#name").val()
		},
		cols: [[
			{align:'center', templet: '#img', width:130,height:72,title: '缩略图'}
			,{minWidth:200,field:'title', title: '标题'}
			,{minWidth:240,field:'content', title: '内容'}
			,{width:174,field:'fbsj', title: '发布时间'}
			,{width:180,field:'fbdw', title: '发布单位'}
			,{width:94,field:'fbr', title: '发布人'}
		]]
		,page: true
		,height:'full-135'
		,cellMinWidth:100
		,limit:20
	};

	var insTb = table.render(optionConfig);

	table.on('toolbar(listtable)', function(obj){
		switch(obj.event){
			case 'edit':
				break;
			case 'add':
				break;
			case 'batchdel':
				break;
		};
	});
	var loadListFunction = function(){
		var code = $("#code").val();
		var name = $("#name").val()
		optionConfig.where =  {
			code: code
			,name: name
		};
		insTb.reload(optionConfig);
	}

	window.resetSwClose = function(isClose){
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	}
	window.reloadList = function(){
		layer.closeAll();
		loadListFunction();
	}

	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delForm(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/pubedu/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}
	//触发事件
	var active = {
		reload: function(){
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
});




