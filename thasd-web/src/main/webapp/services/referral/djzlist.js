var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;

		var ctxPath = layui.getContextPath("ctxPath","listjs");
		var pmtypecode = layui.getContextPath("pmtypecode","listjs");
		var model = layui.getContextPath("model","listjs");

		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}

		_loadMkInfo();

		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					$("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/referral/accept/list'
			,where: {
				name: $("#name").val()
				,idcard:$("#idcard").val()
				,orgid: authUser.orgid
			}
			,cols:  [[
				{fixed:'left',field:'name',  title: '患者姓名',width: 100}
				,{field:'gender',  title: '性别',width:65}
				,{field:'age',  title: '年龄',width:65}
				,{align:'center', templet: '#bpgrade', width:100, title: '高血压'}
				,{align:'center', templet: '#dpgrade', width:100, title: '糖尿病'}
				,{align:'center', templet: '#lpgrade', width:100, title: '高血脂'}
				,{align:'center',width:174,field:'zcorgname', title: '转出单位'}
				,{align:'center',width:170,field:'zcsj', title: '转出时间'}
				,{align:'center',width:90,field:'zcys', title: '转出医生'}
				,{field:'xzyy', title: '协诊原因'}
				,{fixed:'right', width:190, align:'left', toolbar:'#listtable-opt',title: '操作'}
			]]
			,page: true
			,height:'full-155'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','laydate','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var laydate = layui.laydate;
	var zFun =layui.treeselectTable;

	var windowWidth = function () {

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	var model = layui.getContextPath("model","listjs");
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
		$("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}

	/**
	 * 接诊
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postAcceptReferralForm(postStr){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/referral/accept/accept",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('提交成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}


	/**
	 * 向下协诊
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _postDownReferralForm(postStr,index,formBoxId){
		ajaxValForm();
		$.getJSON(ctxPath+"/v/referral/accept/down",postStr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('提交成功！',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
					if(index){layer.close(index);}
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
			$("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
		});
	}

	let layeroIndex,layeroId;
	function openDownReferral(patientData){
		console.log(patientData)
		var formBoxId = "upReferralForm",formBoxName = "向下转出";
		let defaultArea = ['500px'];
		$("#clyj").val("");
		$("#referralid").val(patientData.referralid);
		$("#"+formBoxId).show();
		//执行重载
		layer.open({
			title:[formBoxName]
			,type:1
			,area: defaultArea
			,shade: [0.7, '#d0d7f6']
			,scrollbar: true
			,maxmin: false
			,fixed:true
			,move: false
			,content: $("#"+formBoxId)
			,btn: ['确定', '取消']
			,success: function(layero, index){
				layeroIndex = index;
				layeroId = layero.attr("id");
				layero.find(".layui-layer-content").css({height:"auto"});
			}
			,yes: function(index, layero){
				layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
				var postStr = form.val(formBoxId);
				_postDownReferralForm(postStr,index,formBoxId);
			}
			,btn2: function(index, layero){
			}
			,end:function(){
				layeroIndex = null;
				layeroId = null;
				$("#"+formBoxId).hide();
				$("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled");
			}
		});
	}

	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'viewPlan'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			parent.layer.open({
				title:['个性化管理方案']
				,type: 2
				,area: ["99%","99%"]
				,shade: [0.7, '#000000']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/plan/viewPlan?id='+data.planid, 'no']
				,end:function(){
				}
			});
		}else if(obj.event === 'accept'){
			if($(that).attr("disabled")=="disabled")return;
			$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
			layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			var postData = form.val("formtablePlan");
			postData["referralid"] = data.referralid;
			_postAcceptReferralForm(postData);
		} else if(obj.event === 'down'){
			if($(that).attr("disabled")=="disabled")return;
			$(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
			layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			openDownReferral(data);
		}
	});
	//触发事件
	var active = {
		readcard:function (){
			//传需要赋值input的ID
			var shensi = new ShenSi("idcard");
		},
		reload: function(){
			var that = this;
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	var _loadDic = function(){
		//console.log(JSON.stringify(zNodesJson[0].name));
		if(model == "ygl"){
			$("#scbm").attr("ival","");
			$("#scbm").attr("nval","");
			$("#glorgid").attr("ival",authUser.orgid);
			$("#glorgid").attr("nval",authUser.orgname);
		}else{
			$("#scbm").attr("ival",authUser.orgid);
			$("#scbm").attr("nval",authUser.orgname);
		}

		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","listQuery",false);
		zFun.selectTreeId("scbm",authUser.orgid);
		if(model == "ygl") {
			zFun.initSelectTree(zNodes, "请选择管理单位", "glorgid", false, true, "只能选择三级单位", "listQuery", false);
			zFun.selectTreeId("glorgid",authUser.orgid);
		}
	}
	//_loadDic();
	loadListFunction(true);
});