var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var selectMenuNode = null;
var getSelectMenuNode = function(){
	return selectMenuNode;
}
var reloadMenuList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadMenuListFunction();
	});
}
var loadMenuListFunction = function(frist){
	layui.use(['layer','ztree','treeDic'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var treeDic = layui.treeDic;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function ajaxValForm(){
			$.ajaxSetup({
				error:function(x,e){
					_serverFail();
		       		return false;
		        }
		    });
		}
		function _getTreeList(){
			layer.msg('数据请求中...', {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	ajaxValForm();
		  	$.getJSON(ctxPath+"/exrule/menuTreeSingle","",function(jsondata){
		  		if(jsondata.code=='200'){
		  			initTree(jsondata.list);
			  		layer.closeAll("dialog");
    				$("#refFl").attr("disabled",null).removeClass("layui-btn-disabled");
				}else{
					layer.msg(jsondata.msg,{time:2000});
				}
		  		
		  	});
		}
		var getMenuNodes = function(){
			  return zNodesJsonString;
	    }
		var zNodes = getMenuNodes();
		var initTree = function(zNodesString){
			treeDic.initSelectTree(zNodesString,"menuTable",true,"只能选择实际业务模块","menuTable",false);
		}
		if(frist){
			initTree(zNodes);
		}else{
			_getTreeList();
		}
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		if(frist){
			loadMenuListFunction(true);
		}
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/v/resource/list'
		    ,where: {
		    	code: $("#code").val()
		       ,name: $("#name").val()
		       ,url: $("#url").val()
		       ,menuid: (selectMenuNode==null ? "" : selectMenuNode.id)
		    }
		    ,cols: [[
			  {type:'checkbox',fixed:'left',unresize:true}
		      ,{field:'name',  title: '资源名称',unresize:true}
		      ,{field:'url', title: '访问地址',unresize:true}
		      ,{field:'menuname',  title: '所属模块',unresize:true}
		      ,{field:'createtime', title: '创建时间',unresize:true}
		      ,{fixed:'right', width:170, align:'center', toolbar:'#listtable-opt',title: '操作',unresize:true}
		    ]]
		    ,page: true
		    ,height:'full-155'
		    ,cellMinWidth:100
		    ,limit:20
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var windowWidth = '500px';
	  var windowHeight = '420px';

	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  loadListFunction(true);
	//读取错误提示
    function _serverFail(){
    	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
    */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
	       		return false;
	        }
	    });
	}
	/**
	   * 提交表单
	   * @param id 表单id
	   * @code{.form、#form}
	*/
	function _delForm(idDatas){
	  	ajaxValForm();
	  	$.getJSON(ctxPath+"/v/resource/del",idDatas,function(jsondata){
	  		if(jsondata.code=='200'){
	  			layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
	  				loadListFunction();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
	  	});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	    if(obj.event === 'del'){
	      if($(that).attr("disabled")=="disabled")return;	
	      layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
			  $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
	    	  _delForm("id="+data.id);
	      });
	    } else if(obj.event === 'edit'){
	  	    windowHeight = '440px';
	        if($(that).attr("disabled")=="disabled")return;	
	    	//执行重载
	      	layer.open({
	    		title:['修改资源信息']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/resource/editIndex?id='+data.id, 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
	});
	//触发事件
    var active = {
   		reload: function(){
	      	var that = this;
	      	loadListFunction();
	    }
    	,batchdel: function(){
    		var that = this;
	        if($(that).attr("disabled")=="disabled")return;	
    		var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
    		var len = checkCkbox.data.length;
    		var idDatas = "";
    		for(var i=0 , l = len; i < l; i++){
        		if(i==0){
        			idDatas += "id="+checkCkbox.data[i].id
        		}else{
        			idDatas += "&id="+checkCkbox.data[i].id
        		}
    		}
    		if(len == 0){
    			layer.msg('请选择您将要删除的记录',{time:2000});
    			return false;
    		} else{
    			var info = '些';
    			if(len==1)info='条';
    			layer.confirm('你确认删除这'+info+'记录吗？', {
    				btn: ['确认','取消'] //按钮
    			}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
    				$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
    				_delForm(idDatas);
    			}, function(){
    			}); 
    		}
    	}
    	,add: function(){
	      	var that = this;
	    	var treeObj = $.fn.zTree.getZTreeObj("menuTableTree");
	      	var nodes = treeObj.getSelectedNodes();
	      	if(nodes.length==0){
    			layer.msg('请先选择模块分类',{time:2000});
	      		return;
	      	}
	      	selectMenuNode = nodes[0];
	    	//执行重载
	      	layer.open({
	    		title:['新增资源']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/resource/addIndex', 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
    	,refFl: function(){
	      	var that = this;
	      	if($(that).attr("disabled")=="disabled")return;	
			$(that).attr("disabled","disabled").addClass("layui-btn-disabled");
	      	loadMenuListFunction();
	    }
	
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
});