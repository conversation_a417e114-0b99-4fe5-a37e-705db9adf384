var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery
		, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery
		, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/v/role/list'
		    ,where: {
		    	code: $("#code").val()
		       ,name: $("#name").val()
		       ,orgid: $("#scbmHide").val()
		    }
		    ,cols: [[
			  {type:'checkbox',fixed:'left'}
		      ,{field:'name',  title: '名称'}
		      ,{field:'orgname', title: '所属单位'}
		      ,{field:'remark', title: '描述'}
		      ,{width:120,title: '单位权限', align:'center', templet: '#listtable-org'}
		      ,{width:140,title: '模块与资源权限', align:'center', templet: '#listtable-menurs'}
		      ,{fixed:'right', width:170, align:'center', toolbar:'#listtable-opt',title: '操作'}
		    ]]
		    ,page: true
		    ,height:'full-155'
		    ,cellMinWidth:100
		    ,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var zFun =layui.treeselectTable;
	  var windowWidth = '560px';
	  var windowHeight = '535px';

	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  loadListFunction(true);
	//读取错误提示
    function _serverFail(){
    	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
    */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
	       		return false;
	        }
	    });
	}
	/**
	   * 提交表单
	   * @param id 表单id
	   * @code{.form、#form}
	*/
	function _delForm(idDatas){
	  	ajaxValForm();
	  	$.getJSON(ctxPath+"/v/role/del",idDatas,function(jsondata){
	  		if(jsondata.code=='200'){
	  			layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
	  				loadListFunction();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
	  	});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	    if(obj.event === 'deptRole'){
		      if($(that).attr("disabled")=="disabled")return;	
		      $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled").removeClass("layui-bg-orange");
		      layer.open({
		    		title:['设置部门权限']
		    	    ,type: 2
		    	    ,area: ['334px','560px']
		    	    ,shade: [0.7, '#d0d7f6']
		    	    ,scrollbar: true
		    	    ,maxmin: false
		    	    ,fixed:true
		    	    ,move: false
		    	    ,content: [ctxPath+'/v/role/roleDeptIndex?id='+data.id, 'no']
		    	    ,end:function(){
		  		      $(that).parent().find("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-bg-orange");
		    		}
		     });
		}else if(obj.event === 'orgRole'){
		      if($(that).attr("disabled")=="disabled")return;	
		      $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled").removeClass("layui-bg-engreen");
		      layer.open({
		    		title:['设置单位权限']
		    	    ,type: 2
		    	    ,area: ['334px','560px']
		    	    ,shade: [0.7, '#d0d7f6']
		    	    ,scrollbar: true
		    	    ,maxmin: false
		    	    ,fixed:true
		    	    ,move: false
		    	    ,content: [ctxPath+'/v/role/roleOrgIndex?id='+data.id, 'no']
		    	    ,end:function(){
		  		      $(that).parent().find("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-bg-engreen");
		    		}
		     });
		}else if(obj.event === 'menursRole'){
		      if($(that).attr("disabled")=="disabled")return;	
		      $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled").removeClass("layui-bg-cyan");
		      layer.open({
		    		title:['设置模块与资源权限']
		    	    ,type: 2
		    	    ,area: ['334px','560px']
		    	    ,shade: [0.7, '#d0d7f6']
		    	    ,scrollbar: true
		    	    ,maxmin: false
		    	    ,fixed:true
		    	    ,move: false
		    	    ,content: [ctxPath+'/v/role/roleMenuRsIndex?id='+data.id, 'no']
		    	    ,end:function(){
		  		      $(that).parent().find("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-bg-cyan");
		    		}
		     });
		}else if(obj.event === 'del'){
	      if($(that).attr("disabled")=="disabled")return;	
	      layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
			  $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
	    	  _delForm("id="+data.id);
	      });
	    } else if(obj.event === 'edit'){
	        if($(that).attr("disabled")=="disabled")return;	
	    	//执行重载
	      	layer.open({
	    		title:['修改角色基本信息']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/role/editIndex?id='+data.id, 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
	});
	//触发事件
    var active = {
   		reload: function(){
	      	var that = this;
	      	loadListFunction();
	    }
    	,batchdel: function(){
    		var that = this;
	        if($(that).attr("disabled")=="disabled")return;	
    		var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
    		var len = checkCkbox.data.length;
    		var idDatas = "";
    		for(var i=0 , l = len; i < l; i++){
        		if(i==0){
        			idDatas += "id="+checkCkbox.data[i].id
        		}else{
        			idDatas += "&id="+checkCkbox.data[i].id
        		}
    		}
    		if(len == 0){
    			layer.msg('请选择您将要删除的记录',{time:2000});
    			return false;
    		} else{
    			var info = '些';
    			if(len==1)info='条';
    			layer.confirm('你确认删除这'+info+'记录吗？', {
    				btn: ['确认','取消'] //按钮
    			}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
    				$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
    				_delForm(idDatas);
    			}, function(){
    			}); 
    		}
    	}
    	,add: function(){
	      	var that = this;
	    	//执行重载
	      	layer.open({
	    		title:['新增角色']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/role/addIndex', 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
	
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	
  	var _loadDic = function(){
		//console.log(JSON.stringify(zNodesJson[0].name));
		  $("#scbm").attr("ival","");
		  $("#scbm").attr("nval","");
		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","dept",false);
	}
	_loadDic();
});