var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var selectMenuNode = null;
var getSelectMenuNode = function(){
	return selectMenuNode;
}
var reloadMenuList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadMenuListFunction();
	});
}
var loadMenuListFunction = function(frist){
	layui.use(['layer','ztree','treeDic'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var treeDic = layui.treeDic;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function ajaxValForm(){
			$.ajaxSetup({
				error:function(x,e){
					_serverFail();
		       		return false;
		        }
		    });
		}
		function _getTreeList(){
			layer.msg('数据请求中...', {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	ajaxValForm();
		  	$.getJSON(ctxPath+"/synchis/list","",function(jsondata){
		  		if(jsondata.code=='200'){
		  			initTree(jsondata.data);
			  		layer.closeAll("dialog");
    				$("#refFl").attr("disabled",null).removeClass("layui-btn-disabled");
				}else{
					layer.msg(jsondata.msg,{time:2000});
				}
		  		
		  	});
		}
		var getMenuNodes = function(){
			  return zNodesJsonString;
	    }
		var zNodes = getMenuNodes();
		var initTree = function(zNodesString){
			treeDic.initSelectTree(zNodesString,"menuTable",true,"只能选择实际业务模块","menuTable",false);
		}
		if(frist){
			initTree(zNodes);
		}else{
			_getTreeList();
		}
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','form','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		var form = layui.form;

		if(frist){
			loadMenuListFunction(true);
		}
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();

		var cid = (selectMenuNode==null ? "-1" : selectMenuNode.id);
		$.ajaxSetup({
			error:function(x,e){
				$.ajaxSettings.async = true;
				return false;
			}
		});
		$.ajaxSettings.async = false;
		$.getJSON(ctxPath+'/synchis/listFromItem',"cid="+cid,function(jsondata){
			$.ajaxSettings.async = true;
			if(jsondata.code=='0'){
				dicData = jsondata.data;
			}else{
				dicData = [];
			}
		});
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					setTimeout(function (){
						$("select[name='dzpro']").removeClass("dis");
						bindSelect();
					},1000);
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/synchis/listCollate'
		    ,where: {
		       cid: cid
		    }
		    ,cols: [[

		      {field:'source',  title: '别名',width:200}
			 ,{fixed:'right', width:200, align:'center', templet:'#edit-box',title: '来源字段',unresize:true}
		     ,{field:'prefix', title: 'Table Alias', edit: 'textarea',unresize:true,width:170, style: '-moz-box-align: start;'}
			 ,{field:'note', title: '注释', edit: 'textarea',unresize:true, style: '-moz-box-align: start;'}
				,{field:'bind', title: '状态',unresize:true,width:70,templet: function(d){
				if(d.bind == 0){
					return "启用";
				}else{
					return "禁用";
				}
			  }}
		    ]]
		    ,page: false
		    ,height:'full-55'
		    ,cellMinWidth:100
		    ,limit:20000
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var windowWidth = '500px';
	  var windowHeight = '420px';

	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  loadListFunction(true);
	//读取错误提示
    function _serverFail(){
    	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
		$("#synFl").attr("disabled",null).removeClass("layui-btn-disabled");
    }

	function _loadMkInfo(msg){
		if(msg==''||null==msg)msg = '加载中...';
		layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
	}


    /**
     * ajax预处理
     * @param id sumitid
    */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
	       		return false;
	        }
	    });
	}
	function _postForm(poststr){
		var postUrl = ctxPath+"/synchis/saveOrUpdateCollate";
		ajaxValForm();
		$.getJSON(postUrl,poststr,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('已更新',{time:1000},function(){
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	function genitem(){
		var postUrl = ctxPath+"/synchis/genTranSql";
		var cid = (selectMenuNode==null ? "" : selectMenuNode.id);
		ajaxValForm();
		$.getJSON(postUrl,"id="+cid + "&cid=" + cid,function(jsondata){
			if(jsondata.code=='200'){
				layer.msg('已更新',{time:1000},function(){
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	window.bindSelect = function (){
		$("select[name='dzpro']").unbind("change");
		$("select[name='dzpro']").bind("change", function () {
			_loadMkInfo("正在保存数据...");
			var source = $(this).find("option:selected").attr("dataid");
			var from = $(this).find("option:selected").attr("val");
			_postForm("source="+source + "&from="+from);

		})
	}

	/**
	 * 提交表单
	 * @param id 表单id
	 * @code{.form、#form}
	 */
	function _delFormFl(idDatas){
		ajaxValForm();
		$.getJSON(ctxPath+"/synchis/del",idDatas,function(jsondata){
			if(jsondata.code=='200'){
				loadTreeListFunction();
				selectDicNode = null;
				layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
					loadListFunction();
				});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
		});
	}

	table.on('edit(listtable)', function(obj){
		var field = obj.field //得到字段
			,value = obj.value //得到修改后的值
			,data = obj.data; //得到所在行所有键值

		var source = data.source;
		_postForm("source="+source + "&"+field+"="+value);
		console.log(field,value,data)
	});

	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	});
	var windowWidth2 = '100%';
	var windowHeight2 = '100%';
	//触发事件
    var active = {
   		reload: function(){
	      	var that = this;
	      	loadListFunction();
	    }
		,genitem: function(){
			var that = this;
			genitem();
		}
		,addFl: function(){
			var that = this;
			//执行重载
			layer.open({
				title:['新增配置']
				,type: 2
				,area: [windowWidth2,windowHeight2]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/synchis/addIndex', 'no']
				,end:function(){
				}
			});
		}
		,editFl: function(){
			var that = this;
			var treeObj = $.fn.zTree.getZTreeObj("menuTableTree");
			var nodes = treeObj.getSelectedNodes();
			if(nodes.length==0){
				layer.msg('请选择您要修改的配置',{time:2000});
				return;
			}
			//执行重载
			layer.open({
				title:['修改配置']
				,type: 2
				,area: [windowWidth2,windowHeight2]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/synchis/editIndex?id='+nodes[0].id, 'no']
				,end:function(){
				}
			});
		}
		,delFl: function(){
			var that = this;
			var treeObj = $.fn.zTree.getZTreeObj("menuTableTree");
			var nodes = treeObj.getSelectedNodes();
			if(nodes.length==0){
				layer.msg('请选择您要删除的字典分类',{time:2000});
				return;
			}
			if($(that).attr("disabled")=="disabled")return;
			layer.confirm('字典分类与字典码表级联,级联的字典数据也会被删除，你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				$(that).attr("disabled","disabled").addClass("layui-btn-disabled");
				_delFormFl("id="+nodes[0].id);
			});
		}
    	,refFl: function(){
	      	var that = this;
	      	if($(that).attr("disabled")=="disabled")return;	
			$(that).attr("disabled","disabled").addClass("layui-btn-disabled");
	      	loadMenuListFunction();
	    }
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
});