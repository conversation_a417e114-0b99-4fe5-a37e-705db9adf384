layui.use(['layer', 'loading', 'echarts', 'element', 'count','laydate'], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        element = layui.element,
        count = layui.count,
        echarts = layui.echarts;
    var loading = layui.loading;
    var laydate = layui.laydate;

    Date.prototype.FormatNow = function (fmt) { //author: meizz
        var o = {
            "M+": this.getMonth(), //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    function convertDateFromString(dateString) {
        if (dateString) {
            var date = new Date(dateString.replace(/-/,"/"))
            return date;
        }
    }
    function  DateAdd(interval,number,date)
    {
        /*
         *---------------  DateAdd(interval,number,date)  -----------------
         *  DateAdd(interval,number,date)
         *  功能:实现VBScript的DateAdd功能.
         *  参数:interval,字符串表达式，表示要添加的时间间隔.
         *  参数:number,数值表达式，表示要添加的时间间隔的个数.
         *  参数:date,时间对象.
         *  返回:新的时间对象.
         *  var  now  =  new  Date();
         *  var  newDate  =  DateAdd( "d ",5,now);
         *  author:wanghr100(灰豆宝宝.net)
         *  update:2004-5-28  11:46
         *---------------  DateAdd(interval,number,date)  -----------------
         */
        switch(interval)
        {
            case  "y "  :  {
                date.setFullYear(date.getFullYear()+number);
                return  date;
                break;
            }
            case  "q "  :  {
                date.setMonth(date.getMonth()+number*3);
                return  date;
                break;
            }
            case  "m "  :  {
                date.setMonth(date.getMonth()+number);
                return  date;
                break;
            }
            case  "w "  :  {
                date.setDate(date.getDate()+number*7);
                return  date;
                break;
            }
            case  "d "  :  {
                date.setDate(date.getDate()+number);
                return  date;
                break;
            }
            case  "h "  :  {
                date.setHours(date.getHours()+number);
                return  date;
                break;
            }
            case  "m "  :  {
                date.setMinutes(date.getMinutes()+number);
                return  date;
                break;
            }
            case  "s "  :  {
                date.setSeconds(date.getSeconds()+number);
                return  date;
                break;
            }
            default  :  {
                date.setDate(d.getDate()+number);
                return  date;
                break;
            }
        }
    }
    var dateNow = new Date();
    var maxDate = dateNow.FormatNow("yyyy");
    //年月范围
    var end;
    var start =  laydate.render({
        elem: '#sttime'
        ,type: 'date'
        ,done:function(value, date, endDate){
            console.log(value)
            console.log(date)
            console.log(endDate)

            if(value.length>0){
                end.config.min = {
                    year:date.year,
                    month:date.month-1,//关键
                    date:date.date,
                    hours:date.hours,
                    minutes:date.minutes,
                    seconds:date.seconds
                };
                $("#ettime").val("");
                $("#ettime").attr("disabled",null);
                $("#ettime").removeClass("layui-input-disabled");
                $("#ettime").removeClass("layui-disabled");
            }else{
                $("#ettime").val("");
                $("#ettime").attr("disabled","disabled");
                $("#ettime").addClass("layui-input-disabled");
                $("#ettime").addClass("layui-disabled");
            }
        }
    });
    end =  laydate.render({
        elem: '#ettime'
        ,type: 'date'
        ,done:function(value, date, endDate){
            if(value==""){
                if($("#sttime").val().length==0){
                    $("#ettime").attr("disabled","disabled");
                    $("#ettime").addClass("layui-input-disabled");
                    $("#ettime").addClass("layui-disabled");
                }
            }
        }
    });


    var ctxPath = layui.getContextPath("ctxPath","consolejs");
    var tjfxType = layui.getContextPath("tjfxType","consolejs");



    loading.block({
        type: 3,
        elem: '.loading-chart',
        msg: ''
    })

    var column1 = echarts.init(document.getElementById('echarts-dwfb'));
    var column2 = echarts.init(document.getElementById('echarts-sgbl'));

    function initChart1(result){

        var colorList = ['#07B0FF', '#F5DD0C', '#FF8D02', '#FF2222', '#2CBD32'];
        var color2List = ['#07B0FF6F', '#F5DD0C6F', '#FF8D026F','#FF22226F', '#2CBD326F'];

        var glen = result.groupLabel.length;
        var barWidth = 30;
        var dataRoom = {};

        var seriesData = new Array();
        for (let i = 0; i < result.series.length; i++) {
            seriesData[i] = {
                name: result.series[i].name,
                type: 'bar',
                data: result.series[i].data.map(item => (item === 0 ? null : item)) ,
                stack: 'vD',
                emphasis: {
                    label:{
                        show:true
                    },
                    focus: 'series'
                },
                barWidth:barWidth, //柱子宽度
                barMinHeight: 12,
                barGap: 1, //柱子之间间距
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0.8, color: colorList[i] // 0%
                            }, {
                                offset: 1, color: color2List[i] // 100%
                            }]
                        },
                        // color: colorList[0],
                        opacity: 1,
                    }
                }
            };
        }
        option = {
            backgroundColor: '#fff',
            tooltip: {
                trigger: "axis",
                padding: [8, 10],
                backgroundColor: 'rgba(0,0,0,0.5)',
                textStyle: {
                    color: "rgba(255,255,255,1)"
                },
                axisPointer: {
                    type: "shadow",
                    textStyle: {
                        color: "#fff"
                    }
                }
                ,formatter:function (params){
                    // console.log(params)
                    let relVal = params[0].name;
                    for (let i = 0;i < params.length; i++) {
                        // console.log(params[i].seriesName ,isNaN(params[i].value) ? 0 : params[i].value,params[i].marker)
                        relVal += '<br/>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+params[i].color.colorStops[0].color+';"></span>'+ params[i].seriesName + ' : '+ (isNaN(params[i].value) ? 0 : params[i].value)
                    }
                    return relVal;
                }
            },
            legend: {
                data: ['易患', '低危', '中危', '高危'],
                align: 'left',
                right: 0,
                textStyle: {
                    color: "#333",
                    fontSize: 14,
                    fontWeight: 400
                },
                itemWidth: 14,
                itemHeight: 14,
                itemGap: 26
            },
            grid: {
                left: '0',
                right: '0',
                bottom: '8%',
                top: '15%',
                containLabel: true
            },
            label: {
                show: true,
                color: '#FFFFFF',
                fontSize: 12,
                fontWeight: 700,
                formatter: function (params) {
                    if(params.data && params.data > 0){
                        return params.data;
                    }
                    return "";
                },
            },
            xAxis: [{
                type: 'category',
                offset: 10,
                data: result.groupLabel,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                nameLocation: 'end',//坐标轴名称显示位置。
                axisLabel: {
                    textStyle: {
                        color: "#000",
                        fontSize: 13,
                        fontWeight: 400
                    },
                    show: true,
                    interval: 0,
                    formatter: function (params) {
                        params = params.replace("卫生院","").replace("卫生服务中心","").replace("卫生室","");
                        if (params.length > 8) {
                            return `${params.slice(0, 4)}...`.split("").join("\n");
                        }
                        return params.split("").join("\n");
                    },
                }
            }],
            yAxis: [{
                type: 'value',
                axisLabel: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            }],
            series: seriesData
        };

        if(glen >= 20){
            option["dataZoom"] = {
                realtime:true, //拖动滚动条时是否动态的更新图表数据
                height:25,//滚动条高度
                start:0,//滚动条开始位置（共100等份）
                end:50//结束位置（共100等份）
            };
        }

        column1.setOption(option);
    }

    function initChart2(result){

        var colorList = ["#07B0FF", '#F5DD0C', '#FF8D02', '#FF2222', '#2CBD32'];
        option = {
            title: {
                text: '已评估人数：' + result.ypgrs,
                left: 'center',
                top: 20,
                textStyle: {
                    color: '#000000'
                }
            },
            color: colorList,
            legend: {
                top: '60',
                left: 'center'
            },
            label: {
                alignTo: 'edge',
                formatter: '{name|{b}}\n{time|{c} 人}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 15,
                rich: {
                    time: {
                        fontSize: 10,
                        color: '#999'
                    }
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: [50, 60],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    data: result.zb
                }
            ]
        };
        column2.setOption(option);
    }


    function getPatientsCount(){

        var queryCountUrl = ctxPath+"/v/tjfx/xxgjbFc";
        var queryCountParams = "tjfxType="+tjfxType;
        queryCountParams += ("&sttime="+$("#sttime").val());
        queryCountParams += ("&ettime="+$("#ettime").val());
        $.getJSON(queryCountUrl,queryCountParams,function(jsondata){

            if(jsondata.code=='200'){

                initChart1(jsondata.rd);
                initChart2(jsondata.rd);

                loading.blockRemove(".loading-chart", 0);

            }else{

            }
        });
    }

    getPatientsCount();

    $('.layui-btn').on('click', function(){
        var othis = $(this), method = othis.data('type');
        active[method] ? active[method].call(this, othis) : '';
    });
    var active = {
        reload: function(){
            var that = this;
            loading.block({
                type: 3,
                elem: '.loading-chart',
                msg: ''
            })
            getPatientsCount();
        }
        ,downXxgJbfc: function (){
            var queryCountParams = "tjfxType=" + tjfxType;
            queryCountParams += ("&sttime="+$("#sttime").val());
            queryCountParams += ("&ettime="+$("#ettime").val());
            window.open(ctxPath +"/v/tjfx/downXxgJbfc?"+queryCountParams,'top');
        }
    }
    window.onresize = function() {
        column1.resize();
        column2.resize();
    }
});