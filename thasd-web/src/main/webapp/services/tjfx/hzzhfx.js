layui.use(['layer', 'loading', 'echarts', 'element', 'count'], function() {
    var $ = layui.jquery,
        layer = layui.layer,
        element = layui.element,
        count = layui.count,
        echarts = layui.echarts;
    var loading = layui.loading;
    var ctxPath = layui.getContextPath("ctxPath","consolejs");

    loading.block({
        type: 3,
        elem: '.loading-chart',
        msg: ''
    })

    loading.block({
        type: 3,
        elem: '.loading-chart2',
        msg: ''
    })

    var column1 = echarts.init(document.getElementById('echarts-dwfb'));
    var column2 = echarts.init(document.getElementById('echarts-sgbl'));
    var column3 = echarts.init(document.getElementById('echarts-dwfb-g'));
    var column4 = echarts.init(document.getElementById('echarts-sgbl-g'));

    var column5 = echarts.init(document.getElementById('echarts-bfz'));
    var column6 = echarts.init(document.getElementById('echarts-bfzpie'));

    function initChart1(result){

        var colorList = ["#face1d", '#37c461', '#2194ff', '#F56948', '#9E87FF'];
        var color2List = ["#f5e7af", '#9eecb5', '#96c5f1','#ecb1a4', '#ded8fc'];

        var glen = result.groupLabel.length;
        var barWidth = 30;
        var dataRoom = {};
        option = {
            backgroundColor: '#fff',
            tooltip: {
                trigger: "axis",
                padding: [8, 10],
                backgroundColor: 'rgba(0,0,0,0.5)',
                textStyle: {
                    color: "rgba(255,255,255,1)"
                },
                axisPointer: {
                    type: "shadow",
                    textStyle: {
                        color: "#fff"
                    }
                }
                ,formatter:function (params){
                    // console.log(params)
                    let relVal = params[0].name;
                    for (let i = 0;i < params.length; i++) {
                        // console.log(params[i].seriesName ,isNaN(params[i].value) ? 0 : params[i].value,params[i].marker)
                        relVal += '<br/>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+params[i].color.colorStops[0].color+';"></span>'+ params[i].seriesName + ' : '+ (isNaN(params[i].value) ? 0 : params[i].value)
                    }
                    return relVal;
                }
            },
            legend: {
                data: ['高血压', '糖尿病', '高血脂'],
                align: 'left',
                right: 0,
                textStyle: {
                    color: "#333",
                    fontSize: 14,
                    fontWeight: 400
                },
                itemWidth: 14,
                itemHeight: 14,
                itemGap: 26
            },
            grid: {
                left: '0',
                right: '0',
                bottom: '8%',
                top: '15%',
                containLabel: true
            },
            label: {
                show: true,
                color: '#FFFFFF',
                fontSize: 12,
                fontWeight: 700,
                formatter: function (params) {
                    if(params.data && params.data > 0){
                        return params.data;
                    }
                    return "";
                },
            },
            xAxis: [{
                type: 'category',
                offset: 10,
                data: result.groupLabel,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                nameLocation: 'end',//坐标轴名称显示位置。
                axisLabel: {
                    textStyle: {
                        color: "#000",
                        fontSize: 13,
                        fontWeight: 400
                    },
                    show: true,
                    interval: 0,
                    formatter: function (params) {
                        params = params.replace("卫生院","").replace("社区卫生服务中心","").replace("卫生室","").replace("卫生服务站","");
                        if (params.length > 8) {
                            return `${params.slice(0, 4)}...`.split("").join("\n");
                        }
                        return params.split("").join("\n");
                    },
                }
            }],
            yAxis: [{
                type: 'value',
                axisLabel: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            }],
            series: [{
                name: '高血压',
                type: 'bar',
                data: result.groupBarGxy.map(item => (item === 0 ? null : item)) ,
                stack: 'Ad',
                emphasis: {
                    focus: 'series'
                },
                barWidth:barWidth, //柱子宽度
                barMinHeight: 12,
                barGap: 1, //柱子之间间距
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: colorList[0] // 0%
                            }, {
                                offset: 1, color: color2List[0] // 100%
                            }]
                        },
                        // color: colorList[0],
                        opacity: 1,
                    }
                }
            }, {
                name: '糖尿病',
                type: 'bar',
                data: result.groupBarTnb.map(item => (item === 0 ? null : item)) ,
                stack: 'Ad',
                emphasis: {
                    focus: 'series'
                },
                barWidth:barWidth,
                barMinHeight: 12,
                barGap: 1,
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: colorList[1] // 0%
                            }, {
                                offset: 1, color: color2List[1] // 100%
                            }]
                        },
                        // color: colorList[1],
                        opacity: 1,
                    }
                }
            }, {
                name: '高血脂',
                type: 'bar',
                data: result.groupBarGxz.map(item => (item === 0 ? null : item)) ,
                stack: 'Ad',
                emphasis: {
                    focus: 'series'
                },
                barWidth:barWidth,
                barMinHeight: 12,
                barGap: 1,
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: colorList[2] // 0%
                            }, {
                                offset: 1, color: color2List[2] // 100%
                            }]
                        },
                        //color: colorList[2],
                        opacity: 1,

                    }
                }
            }]
        };

        if(glen >= 20){
            option["dataZoom"] = {
                realtime:true, //拖动滚动条时是否动态的更新图表数据
                height:25,//滚动条高度
                start:0,//滚动条开始位置（共100等份）
                end:50//结束位置（共100等份）
            };
        }

        column1.setOption(option);
    }

    function initChart2(result){

        var colorList = ["#ee6666", '#9E87FF', '#91cc75', '#5470c6'];
        option = {
            color: colorList,
            legend: {
                top: '5%',
                left: 'center'
            },
            label: {
                alignTo: 'edge',
                formatter: '{name|{b}}\n{time|{c} 人}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 15,
                rich: {
                    time: {
                        fontSize: 10,
                        color: '#999'
                    }
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: [50, 60],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    data: result.pieSgPatients
                }
            ]
        };
        column2.setOption(option);
    }

    function initChart3(result){

        var colorList = ["#face1d", '#37c461', '#2194ff', '#F56948', '#9E87FF'];
        var color2List = ["#f5e7af", '#9eecb5', '#96c5f1','#ecb1a4', '#ded8fc'];

        var glen = result.lgroupLabel.length;
        var barWidth = 30;
        var dataRoom = {};
        option = {
            backgroundColor: '#fff',
            tooltip: {
                trigger: "axis",
                padding: [8, 10],
                backgroundColor: 'rgba(0,0,0,0.5)',
                textStyle: {
                    color: "rgba(255,255,255,1)"
                },
                axisPointer: {
                    type: "shadow",
                    textStyle: {
                        color: "#fff"
                    }
                }
                ,formatter:function (params){
                    // console.log(params)
                    let relVal = params[0].name;
                    for (let i = 0;i < params.length; i++) {
                        // console.log(params[i].seriesName ,isNaN(params[i].value) ? 0 : params[i].value,params[i].marker)
                        relVal += '<br/>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+params[i].color.colorStops[0].color+';"></span>'+ params[i].seriesName + ' : '+ (isNaN(params[i].value) ? 0 : params[i].value)
                    }
                    return relVal;
                }
            },
            legend: {
                data: ['两高', '三高'],
                align: 'left',
                right: 0,
                textStyle: {
                    color: "#333",
                    fontSize: 14,
                    fontWeight: 400
                },
                itemWidth: 14,
                itemHeight: 14,
                itemGap: 26
            },
            grid: {
                left: '0',
                right: '0',
                bottom: '8%',
                top: '15%',
                containLabel: true
            },
            label: {
                show: true,
                color: '#FFFFFF',
                fontSize: 12,
                fontWeight: 700,
                formatter: function (params) {
                    if(params.data && params.data > 0){
                        return params.data;
                    }
                    return "";
                },
            },
            xAxis: [{
                type: 'category',
                offset: 10,
                data: result.lgroupLabel,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                nameLocation: 'end',//坐标轴名称显示位置。
                axisLabel: {
                    textStyle: {
                        color: "#000",
                        fontSize: 13,
                        fontWeight: 400
                    },
                    show: true,
                    interval: 0,
                    formatter: function (params) {
                        params = params.replace("卫生院","").replace("卫生服务中心","").replace("卫生室","");
                        if (params.length > 8) {
                            return `${params.slice(0, 4)}...`.split("").join("\n");
                        }
                        return params.split("").join("\n");
                    },
                }
            }],
            yAxis: [{
                type: 'value',
                axisLabel: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            }],
            series: [{
                name: '两高',
                type: 'bar',
                data: result.groupBarL2.map(item => (item === 0 ? null : item)) ,
                stack: 'Ad',
                emphasis: {
                    focus: 'series'
                },
                barWidth:barWidth, //柱子宽度
                barMinHeight: 12,
                barGap: 1, //柱子之间间距
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0.8, color: colorList[4] // 0%
                            }, {
                                offset: 1, color: color2List[4] // 100%
                            }]
                        },
                        // color: colorList[0],
                        opacity: 1,
                    }
                }
            }, {
                name: '三高',
                type: 'bar',
                data: result.groupBarL3.map(item => (item === 0 ? null : item)) ,
                stack: 'Ad',
                emphasis: {
                    focus: 'series'
                },
                barWidth:barWidth,
                barMinHeight: 12,
                barGap: 1,
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0.8, color: colorList[3] // 0%
                            }, {
                                offset: 1, color: color2List[3] // 100%
                            }]
                        },
                        // color: colorList[1],
                        opacity: 1,
                    }
                }
            }]
        };
        if(glen >= 28){
            option["dataZoom"] = {
                realtime:true, //拖动滚动条时是否动态的更新图表数据
                height:25,//滚动条高度
                start:0,//滚动条开始位置（共100等份）
                end:60//结束位置（共100等份）
            };
        }

        column3.setOption(option);
    }

    function initChart4(result){

        var colorList = ["#9E87FF", '#ee6666', '#91cc75', '#5470c6'];
        option = {
            color: colorList,
            legend: {
                top: '5%',
                left: 'center'
            },
            label: {
                alignTo: 'edge',
                formatter: '{name|{b}}\n{time|{c} 人}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 15,
                rich: {
                    time: {
                        fontSize: 10,
                        color: '#999'
                    }
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: [50, 60],
                    center: ['50%', '50%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    data: result.lPie
                }
            ]
        };
        column4.setOption(option);
    }

    function initChart5(result){

        var colorList = ["#00A8E1", '#99CC00', '#E30039', '#FCD300', '#800080', '#FF6600', '#808000', '#DB00C2', '#008080', '#0000FF'];
        var color2List = ["#00A8E16F", '#99CC006F', '#E300396F', '#FCD3006F', '#8000806F', '#FF66006F', '#8080006F', '#DB00C26F', '#0080806F', '#0000FF6F'];

        var glen = result.groupLabel.length;
        var barWidth = 30;
        var dataRoom = {};

        var seriesData = new Array();
        for (let i = 0; i < result.series.length; i++) {
            seriesData[i] = {
                name: result.series[i].name,
                type: 'bar',
                data: result.series[i].data.map(item => (item === 0 ? null : item)) ,
                stack: 'vD',
                emphasis: {
                    label:{
                        show:true
                    },
                    focus: 'series'
                },
                barWidth:barWidth, //柱子宽度
                barMinHeight: 12,
                barGap: 1, //柱子之间间距
                itemStyle: {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0.8, color: colorList[i] // 0%
                            }, {
                                offset: 1, color: color2List[i] // 100%
                            }]
                        },
                        // color: colorList[0],
                        opacity: 1,
                    }
                }
            };
        }
        option = {
            backgroundColor: '#fff',
            tooltip: {
                trigger: "axis",
                padding: [8, 10],
                backgroundColor: 'rgba(0,0,0,0.5)',
                textStyle: {
                    color: "rgba(255,255,255,1)"
                },
                axisPointer: {
                    type: "shadow",
                    textStyle: {
                        color: "#fff"
                    }
                }
                ,formatter:function (params){
                    // console.log(params)
                    let relVal = params[0].name;
                    for (let i = 0;i < params.length; i++) {
                        // console.log(params[i].seriesName ,isNaN(params[i].value) ? 0 : params[i].value,params[i].marker)
                        relVal += '<br/>' + '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+params[i].color.colorStops[0].color+';"></span>'+ params[i].seriesName + ' : '+ (isNaN(params[i].value) ? 0 : params[i].value)
                    }
                    return relVal;
                }
            },
            legend: {
                data: ['冠心病', '脑卒中', '肾病综合征', '眼底病变', '周围神经病变', '周围血管病变'],
                align: 'left',
                right: 0,
                textStyle: {
                    color: "#333",
                    fontSize: 14,
                    fontWeight: 400
                },
                itemWidth: 14,
                itemHeight: 14,
                itemGap: 26
            },
            grid: {
                left: '0',
                right: '0',
                bottom: '8%',
                top: '15%',
                containLabel: true
            },
            label: {
                show: true,
                color: '#FFFFFF',
                fontSize: 12,
                fontWeight: 700,
                formatter: function (params) {
                    if(params.data && params.data > 0){
                        return params.data;
                    }
                    return "";
                },
            },
            xAxis: [{
                type: 'category',
                offset: 10,
                data: result.groupLabel,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                nameLocation: 'end',//坐标轴名称显示位置。
                axisLabel: {
                    textStyle: {
                        color: "#000",
                        fontSize: 13,
                        fontWeight: 400
                    },
                    show: true,
                    interval: 0,
                    formatter: function (params) {
                        params = params.replace("卫生院","").replace("卫生服务中心","").replace("卫生室","");
                        if (params.length > 8) {
                            return `${params.slice(0, 4)}...`.split("").join("\n");
                        }
                        return params.split("").join("\n");
                    },
                }
            }],
            yAxis: [{
                type: 'value',
                axisLabel: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            }],
            series: seriesData
        };

        if(glen >= 20){
            option["dataZoom"] = {
                realtime:true, //拖动滚动条时是否动态的更新图表数据
                height:25,//滚动条高度
                start:0,//滚动条开始位置（共100等份）
                end:50//结束位置（共100等份）
            };
        }

        column5.setOption(option);
    }

    function initChart6(result){
        var colorList = ["#00a8e1", '#99cc00', '#e30039', '#fcd300', '#800080', '#ff6600', '#808000', '#db00c2', '#008080', '#0000ff'];
        option = {
            title: {
                text: '已评估人数：' + result.ypgrs,
                left: 'center',
                top: 20,
                textStyle: {
                    color: '#000000'
                }
            },
            color: colorList,
            legend: {
                top: '60',
                left: 'center'
            },
            label: {
                alignTo: 'edge',
                formatter: '{name|{b}}\n{time|{c} 人}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 15,
                rich: {
                    time: {
                        fontSize: 10,
                        color: '#999'
                    }
                }
            },
            series: [
                {
                    type: 'pie',
                    radius: [50, 60],
                    center: ['50%', '280'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    data: result.zb
                }
            ]
        };
        column6.setOption(option);
    }

    function getPatientsCount(){
        var queryCountUrl = ctxPath+"/v/tjfx/countMainPatients";
        var queryCountParams = "tjxfType=hzzhfx";
        $.getJSON(queryCountUrl,queryCountParams,function(jsondata){

            if(jsondata.code=='200'){

                count.up("yglrs", {
                    time: 4000,
                    num: jsondata.rd.yglrs,
                    regulator: 100
                });

                count.up("djrs", {
                    time: 4000,
                    num: jsondata.rd.djrs,
                    regulator: 100
                });

                count.up("sfrs", {
                    time: 4000,
                    num: jsondata.rd.sfrs,
                    regulator: 100
                });

                count.up("gxy", {
                    time: 4000,
                    num: jsondata.rd.gxyrs,
                    regulator: 100
                });

                count.up("tnb", {
                    time: 4000,
                    num: jsondata.rd.tnbrs,
                    regulator: 100
                });

                count.up("gxz", {
                    time: 4000,
                    num: jsondata.rd.gxzrs,
                    regulator: 100
                });

                initChart1(jsondata.rd);
                initChart2(jsondata.rd);
                initChart3(jsondata.rd);
                initChart4(jsondata.rd);
                loading.blockRemove(".loading-chart", 0);

            }else{
                layer.msg('连接服务器失败,请稍后再试...',{time:2000});
            }
        });
    }

    function getBfzCount(){
        var queryCountUrl = ctxPath+"/v/tjfx/bfzTjfx";
        var queryCountParams = "";
        $.getJSON(queryCountUrl,queryCountParams,function(jsondata){

            if(jsondata.code=='200'){

                initChart5(jsondata.rd);
                initChart6(jsondata.rd);
                loading.blockRemove(".loading-chart2", 0);

            }else{
                layer.msg('连接服务器失败,请稍后再试...',{time:2000});
            }
        });
    }

    getPatientsCount();
    getBfzCount();
    window.onresize = function() {
        column1.resize();
        column2.resize();
        column3.resize();
        column4.resize();
        column5.resize();
        column6.resize();
    }
});