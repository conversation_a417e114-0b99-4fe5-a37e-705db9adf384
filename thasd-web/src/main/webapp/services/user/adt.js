var _getCascDept = function(orgid,dData){}//空放 无用
layui.use(['form', 'layedit', 'laydate','ztree','treeselect'], function(){
  var form = layui.form
  ,$ = layui.jquery
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate
  ,zFun =layui.treeselect;
  $(".initbox").remove();
  function endes(a){return new b64().decode(a)};
  function getUParam(name,id) {
	    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
	    if (r != null) return unescape(r[2]); return ""; //返回参数值
  }
  var ctxPath = getUParam("ctxPath","adtjs");
  var id = getUParam("mainid","adtjs");
  /**
   * load信息提示 带遮罩层
   * @param msg 提示信息
   * @code{default=加载中...}
   */
  function _loadMkInfo(msg){
  	if(msg==''||null==msg)msg = '加载中...';
  	layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); 
  }
  //读取错误提示
  function _serverFail(){
	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
  }
  /**
   * ajax预处理
   * @param id sumitid
   */
  function ajaxValForm(){
	$.ajaxSetup({
		error:function(x,e){
			_serverFail();
			parent.resetSwClose(false);
			$("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
       		return false;
        }
    });
  }
  /**
   * 提交表单
   * @param id 表单id
   * @code{.form、#form}
   */
  function _postForm(poststr){
	var postUrl = ctxPath+"/v/user/save";
	if(id != ""){
		postUrl = ctxPath+"/v/user/edit";
	}
  	ajaxValForm();
  	$.getJSON(postUrl,poststr,function(jsondata){
  		if(jsondata.code=='200'){
  	    	layer.msg('保存数据成功',{time:1000},function(){
  	    		parent.layer.closeAll('iframe');
  	  			parent.reloadList();
  	    	});
		}else{
			layer.msg(jsondata.msg,{time:2000});
			$("#subpost").attr("disabled",null).removeClass("layui-btn-disabled");
			parent.resetSwClose(false);
		}
  	});
  }
  /**
   * 生成表单数据
   * @param id 表单id
   * @code{.form、#form}
   */
  function _getForm(){
  	ajaxValForm();
  	$.getJSON(ctxPath+"/v/user/get","id="+id,function(jsondata){
  		if(jsondata.code=='200'){
  		   //console.log(JSON.stringify(jsondata.rd));
  		  //表单初始赋值
           $("input:radio[name='category'][value="+jsondata.rd.category+"]").attr("checked","checked");  
  		  form.val('formtable', JSON.parse(JSON.stringify(jsondata.rd)));
			$("#scbm").attr("ival",jsondata.rd.orgid);
			$("#scbm").attr("nval",jsondata.rd.orgname);
		  initTree();
		}else{
			$("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
			layer.msg(jsondata.msg,{time:2000},function(){
				parent.layer.closeAll('iframe');
			});
		}
  	});
  }
  //触发事件
  var active = {
	cancel: function(){
		parent.layer.closeAll('iframe');
    }
  };
  
  $('#parentid').change(function(){
		var othis = $(this), method = othis.data('type');
	    active[method] ? active[method].call(this, othis) : '';
  });
  $('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
	    active[method] ? active[method].call(this, othis) : '';
  });
  //自定义验证规则
  form.verify({
  });
  //监听提交
  form.on('submit(formsb)', function(data){
	parent.resetSwClose(true);
	_loadMkInfo("正在保存数据...");
	$("#subpost").attr("disabled","disabled").addClass("layui-btn-disabled");
	var postData = JSON.stringify(data.field);
	_postForm(data.field);
    return false;
  });
  var getMenuNodes = function(){
	  return zNodesJson;
  }
  var zNodes = getMenuNodes();
  var initTree = function(){
	  zFun.initSelectTree(zNodes,"请选择所属单位","scbm",true,true,"","dept",false);
  }
  if(id != ""){
	  _getForm();
  }else{
	  $("#orgcode").val(authUser.orgcode);
	  $("#scbm").attr("ival",authUser.orgid);
	  $("#scbm").attr("nval",authUser.orgname);
	  initTree();
  }
});