var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var layIndex1;
var layIndex2;
var closeLayer = function(layIndex){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(layIndex==1){
			layer.close(layIndex1);
		}
		if(layIndex==2){
			layer.close(layIndex2);
		}
	});
}
var reloadListChild = function(){
	resetSwClose(false);
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var iframeWin = window[$("body").find('iframe')[0]['name']];
		iframeWin.reloadList();
	});
}
/**
 * 赋予用户角色
 */
var selectRoleList = function(ctxPath,id,category){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		//执行重载
		layIndex2 = layer.open({
			title:['赋予用户新的角色']
		    ,type: 2
		    ,area: ['80%','80%']
		    ,shade: [0.7, '#d0d7f6']
		    ,scrollbar: true
		    ,maxmin: false
		    ,fixed:true
		    ,move: false
		    ,content: [ctxPath+'/exrule/setRoleSelectIndex?id='+id+'&category='+category, 'no']
		    ,end:function(){
			}
		});
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/v/user/list'
		    ,where: {
				code: $("#code").val()
				,name: $("#name").val()
				,username: $("#username").val()
				,orgid: $("#scbmHide").val()
		    }
		    ,cols: [[
			  {type:'checkbox',fixed:'left'}
		      ,{field:'username',  title: '用户名'}
		      ,{field:'name', title: '名称'}
		      ,{field:'orgname', title: '所属单位'}
		      ,{field:'createtime', title: '创建时间'}
		      ,{fixed:'right',width:100,title: '是否启用', align:'center', templet: '#listtable-isval'}
		      ,{fixed:'right',width:100,title: '所属角色', align:'center', templet: '#listtable-roles'}
		      ,{fixed:'right', minWidth:180, align:'left', templet:'#listtable-opt',title: '操作'}
		    ]]
		    ,page: true
		    ,height:'full-155'
		    ,cellMinWidth:100
		    ,limit:20
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var form = layui.form;
	  var table = layui.table;
	  var windowWidth = '600px';
	  var windowHeight = '540px';
	  var zFun =layui.treeselectTable;
	    
	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  loadListFunction(true);
	//读取错误提示
    function _serverFail(){
    	layer.msg('连接服务器失败,请稍后再试...',{time:2000});
    }
    /**
     * ajax预处理
     * @param id sumitid
    */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
	       		return false;
	        }
	    });
	}
	/**
	   * 提交表单
	   * @param id 表单id
	   * @code{.form、#form}
	*/
	function _delForm(idDatas){
	  	ajaxValForm();
	  	$.getJSON(ctxPath+"/v/user/del",idDatas,function(jsondata){
	  		if(jsondata.code=='200'){
	  			layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
	  				loadListFunction();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
	  	});
	}
	function _setIsvalForm(idDatas){
	  	ajaxValForm();
	  	$.getJSON(ctxPath+"/v/user/setValid",idDatas,function(jsondata){
	  		if(jsondata.code=='200'){
	  			layer.msg('操作成功',{time:1000,shade: [0.001, '#ffffff']},function(){
	  				loadListFunction();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
	  	});
	}
	
	/**
	   * 重置密码
	   * @param id 表单id
	   * @code{.form、#form}
	*/
	function _resetPasswordForm(idDatas){
	  	ajaxValForm();
	  	$.getJSON(ctxPath+"/v/user/resetPassword",idDatas,function(jsondata){
	  		if(jsondata.code=='200'){
	  			layer.msg('重置密码成功',{time:1000,shade: [0.001, '#ffffff']},function(){
	  				loadListFunction();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
			}
	  	});
	}
	
	table.on('checkbox(listtable)', function(obj){
	});
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	    if(obj.event === 'userRole'){
		      if($(that).attr("disabled")=="disabled")return;	
		      $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled").removeClass("layui-bg-cyan");
		      layIndex1 = layer.open({
		    		title:['用户已分配角色列表']
		    	    ,type: 2
		    	    ,area: ['80%','80%']
		    	    ,shade: [0.7, '#d0d7f6']
		    	    ,scrollbar: true
		    	    ,maxmin: false
		    	    ,fixed:true
		    	    ,move: false
		    	    ,content: [ctxPath+'/v/user/setRoleIndex?id='+data.id+'&category='+data.category, 'no']
		    	    ,end:function(){
		  		      $(that).parent().find("a.layui-btn").attr("disabled",null).removeClass("layui-btn-disabled").addClass("layui-bg-cyan");
		    		}
		     });
		}else if(obj.event === 'resetPassword'){
		      if($(that).attr("disabled")=="disabled")return;	
		      layer.confirm('你确认用户名为<font color="red">['+data.name+']</font>的用户重置密码吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				  $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
		    	  _resetPasswordForm("id="+data.id);
		      });
		}else if(obj.event === 'del'){
	      if($(that).attr("disabled")=="disabled")return;	
	      layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
			  $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
	    	  _delForm("id="+data.id);
	      });
	    } else if(obj.event === 'edit'){
	        if($(that).attr("disabled")=="disabled")return;	
	    	//执行重载
	      	layer.open({
	    		title:['修改用户信息']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/user/editIndex?id='+data.id, 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
	});
	//监听指定开关
	form.on('switch(switchIsVal)', function(data){
		var tf = this.checked;
		var tid = this.value;
		var that =this;
		var isval = "1";
		var username = $(this).attr("username");
		var tftxt = "启用";
		if(!tf){
			tftxt="停用";
			isval = "0";
		}
		layer.confirm('你确认'+tftxt+'<font color="red">['+username+']</font>此账户吗？',
			{
				btn: ['确认','取消'] //按钮
			}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
				_setIsvalForm("id="+tid+"&isval="+isval);
			}, function(){
				if(tf){
					$(that).removeAttr("checked");
					form.render(); 
				}else{
					$(that).prop("checked",true);
					form.render(); 
				}
			}); 
		});
	//触发事件
    var active = {
   		reload: function(){
	      	var that = this;
	      	loadListFunction();
	    }
    	,batchdel: function(){
    		var that = this;
	        if($(that).attr("disabled")=="disabled")return;	
    		var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
    		var len = checkCkbox.data.length;
    		var idDatas = "";
    		for(var i=0 , l = len; i < l; i++){
				if(checkCkbox.data[i].id != "000000000000000000000000000000000000"){
	    			idDatas += "id="+checkCkbox.data[i].id+"&"
				}
    		}
    		idDatas = idDatas.substring(0,idDatas.length-1);
    		if(len == 0){
    			layer.msg('请选择您将要删除的记录',{time:2000});
    			return false;
    		} else{
    			var info = '些';
    			if(len==1)info='条';
    			layer.confirm('你确认删除这'+info+'记录吗？', {
    				btn: ['确认','取消'] //按钮
    			}, function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
    				$("#batchdel").attr("disabled","disabled").addClass("layui-btn-disabled");
    				_delForm(idDatas);
    			}, function(){
    			}); 
    		}
    	}
    	,add: function(){
	      	var that = this;
	    	//执行重载
	      	layer.open({
	    		title:['新增用户']
	    	    ,type: 2
	    	    ,area: [windowWidth,windowHeight]
	    	    ,shade: [0.7, '#d0d7f6']
	    	    ,scrollbar: true
	    	    ,maxmin: false
	    	    ,fixed:true
	    	    ,move: false
	    	    ,content: [ctxPath+'/v/user/addIndex', 'no']
	    	    ,end:function(){
	    		}
	    	});
	    }
	
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	
  	var _loadDic = function(){
		//console.log(JSON.stringify(zNodesJson[0].name));
		  $("#scbm").attr("ival","");
		  $("#scbm").attr("nval","");
		initTree();
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","dept",false);
	}
	_loadDic();
});