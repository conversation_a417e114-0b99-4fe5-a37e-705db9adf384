var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		var id = getUParam("id","listjs");
		var category = getUParam("category","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/exrule/getRoleExistingList'
		    ,where: {
		    	userid: id
		    }
		    ,cols: [[
		       {field:'orgname', title: '所属单位'}
		      ,{field:'name',  title: '名称'}
		      ,{field:'remark', title: '描述'}
		      ,{minWidth:100, align:'center', templet:'#listtable-opt',title: '操作'}
		    ]]
		    ,page: false
		    ,height:'full-120'
		    ,cellMinWidth:100
		    ,limit:20
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var windowWidth = '500px';
	  var windowHeight = '420px';
	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  var id = getUParam("id","listjs");
	  var category = getUParam("category","listjs");
	  loadListFunction(true);
	  
	  /**
	     * ajax预处理
	     * @param id sumitid
	    */
		function ajaxValForm(){
			$.ajaxSetup({
				error:function(x,e){
					_serverFail();
		       		return false;
		        }
		    });
		}
		/**
		   * 提交表单
		   * @param id 表单id
		   * @code{.form、#form}
		*/
		function _delForm(idDatas){
		  	ajaxValForm();
		  	$.getJSON(ctxPath+"/exrule/delUserRole",idDatas,function(jsondata){
		  		if(jsondata.code=='200'){
		  			layer.msg('删除数据成功',{time:1000,shade: [0.001, '#ffffff']},function(){
		  				loadListFunction();
		  	    	});
				}else{
					layer.msg(jsondata.msg,{time:2000});
				}
		  	});
		}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	    if(obj.event === 'del'){
	      if($(that).attr("disabled")=="disabled")return;	
	      layer.confirm('你确认删除这条数据吗?', function(index){layer.msg("业务数据请求中,请稍后...", {icon: 16,time: 0,shade: [0.001, '#000000']},function(){}); layer.close(index)
			  $(that).parent().find("a.layui-btn").attr("disabled","disabled").addClass("layui-btn-disabled");
	    	  _delForm("userid="+id+"&id="+data.id);
	      });
		}
	});
	//触发事件
    var active = {
    	ref: function(){
	      	var that = this;
	      	loadListFunction();
	    }
    	,add: function(){//赋予用户角色
	      	var that = this;
	      	parent.selectRoleList(ctxPath,id,category);
	    }
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
});