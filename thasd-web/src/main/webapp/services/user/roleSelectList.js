var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;
		function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
		}
		var ctxPath = getUParam("ctxPath","listjs");
		var id = getUParam("id","listjs");
		var category = getUParam("category","listjs");
		function _loadMkInfo(msg){
		  	if(msg==''||null==msg)msg = '数据请求中...';
		  	if(frist){
		  		layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
		  	}else{
		  		//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		  	}
	    }
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
    				$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);  
			}
		    ,elem: '#listtable'
		    ,url:ctxPath+'/exrule/getRoleList'
		    ,where: {
		    	userid: id
		       ,category: category
		    }
		    ,cols: [[
			   {type:'checkbox',fixed:'left'}
			  ,{field:'orgname', title: '所属单位'}
		      ,{field:'name',  title: '名称'}
		      ,{field:'remark', title: '描述'}
		    ]]
		    ,page: false
		    ,height:'full-100'
		    ,cellMinWidth:100
		    ,limit:20
		});
	});
}
var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table'], function(){//独立版的layer无需执行这一句
	  var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	  var jQuery = layui.jquery;
	  var element = layui.element;
	  var table = layui.table;
	  var windowWidth = '500px';
	  var windowHeight = '420px';

	  function getUParam(name,id) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
		    var r = decodeURIComponent($("#"+id).attr("src").substr($("#"+id).attr("src").indexOf("?")).substr(1)).match(reg);  //匹配目标参数
		    if (r != null) return unescape(r[2]); return ""; //返回参数值
	  }
	  var ctxPath = getUParam("ctxPath","listjs");
	  
	  var ctxPath = getUParam("ctxPath","listjs");
	  var id = getUParam("id","listjs");
      var category = getUParam("category","listjs");
		
	  loadListFunction(true);
	  /**
	   * load信息提示 带遮罩层
	   * @param msg 提示信息
	   * @code{default=加载中...}
	   */
	  function _loadMkInfo(msg){
	  	if(msg==''||null==msg)msg = '加载中...';
	  	//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
	  }
	  //读取错误提示
	  function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	  }
	  /**
	   * ajax预处理
	   * @param id sumitid
	   */
	  function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				parent.resetSwClose(false);
				$("#confirm").attr("disabled",null).removeClass("layui-btn-disabled");
	       		return false;
	        }
	    });
	  }
	  /**
	   * 提交表单
	   * @param id 表单id
	   * @code{.form、#form}
	   */
	  function _postForm(poststr){
		var postUrl = ctxPath+"/exrule/addRoles";
	  	ajaxValForm();
	  	$.getJSON(postUrl,poststr,function(jsondata){
	  		if(jsondata.code=='200'){
	  	    	layer.msg('保存数据成功',{time:1000},function(){
	  	    		parent.closeLayer(2);
	  	  			parent.reloadListChild();
	  	    	});
			}else{
				layer.msg(jsondata.msg,{time:2000});
				$("#confirm").attr("disabled",null).removeClass("layui-btn-disabled");
				parent.resetSwClose(false);
			}
	  	});
	  }
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
	    var data = obj.data;
	});
	//触发事件
    var active = {
    	cancel: function(){
    		parent.closeLayer(2);
	    }
    	,confirm: function(){
	      	var that = this;
	        if($(that).attr("disabled")=="disabled")return;	
	        $(that).attr("disabled","disabled").addClass("layui-btn-disabled");
    		var checkCkbox = table.checkStatus('listtable'),data = checkCkbox.data;
    		var len = checkCkbox.data.length;
    		var idDatas = "";
    		for(var i=0 , l = len; i < l; i++){
    			idDatas += checkCkbox.data[i].id+","
    		}
    		idDatas = idDatas.substring(0,idDatas.length-1);
    		if(len == 0){
    	        $(that).attr("disabled",null).removeClass("layui-btn-disabled");
    			layer.msg('请选择角色',{time:2000});
    			return false;
    		}
    		parent.resetSwClose(true);
    		_loadMkInfo("正在保存数据...");
    		_postForm("roleids="+idDatas + "&userid="+id);
	    }
    }; 
  	$('.layui-btn.user-search').on('click', function(){
  		var othis = $(this), method = othis.data('method');
  	    active[method] ? active[method].call(this, othis) : '';
    });
  	$('.layui-btn').on('click', function(){
  		var othis = $(this), method = othis.data('type');
  	    active[method] ? active[method].call(this, othis) : '';
    });
});