function formatDateTime(datestr, format) {
	if(null == datestr || '' == datestr)return '';
	var date = new Date(datestr);
	var o = {
		'M+': date.getMonth() + 1, // 月份
		'd+': date.getDate(), // 日
		'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
		'H+': date.getHours(), // 小时
		'm+': date.getMinutes(), // 分
		's+': date.getSeconds(), // 秒
		'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
		S: date.getMilliseconds(), // 毫秒
		a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
		A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
	};
	if (/(y+)/.test(format)) {
		format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	for (let k in o) {
		if (new RegExp('(' + k + ')').test(format)) {
			format = format.replace(
				RegExp.$1,
				RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
			);
		}
	}
	return format;
}

var reloadList = function(){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;
		layer.closeAll();
		loadListFunction();
	});
}
var loadListFunction = function(frist){
	layui.use(['layer','table'], function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		var table = layui.table;

		var ctxPath = layui.getContextPath("ctxPath","listjs");
		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '数据请求中...';
			if(frist){
				layer.msg(msg, {icon: 16,time: 0,shade: [1, '#ffffff']},function(){});
			}else{
				//layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
			}
		}
		_loadMkInfo();
		table.render({
			done: function(res, curr, count){
				setTimeout(function(){
					$("#batchdel").attr("disabled",null).removeClass("layui-btn-disabled");
					$(".initbox").remove();
					layer.closeAll("dialog");
				},300);
			}
			,elem: '#listtable'
			,url:ctxPath+'/v/ylsj/mzlist'
			,where: {
				idcard:$("#idcard").val()
				// ,orgcode: ($("#orgcode").val() == "" ? "" : $("#orgcode").val())
				,orgcode : ((authUser.username == "admin" || authUser.grade == "1") ? ($("#orgcode").val() == "" ? null : $("#orgcode").val()) : (authUser.orgcode ? authUser.orgcode : "isEmpty"))
				,deptid: ($("#deptid").val() == "" ? null : $("#deptid").val())
				,name: $("#name").val()
				,mzh: $("#mzh").val()
			}
			,cols: [[
				{type:'numbers',title: '序号', width: 70, align:'center'}
				,{field:'mzh',  title: '门诊号',width: 160, align:'center'}
				,{field:'name',  title: '患者姓名',width: 100, align:'center'}
				,{field:'idcardno',  title: '证件号',width: 175, align:'center'}
				,{field:'sex',  title: '性别',width:65, align:'center'}
				,{field:'minzu',  title: '民族',width:65, align:'center'}
				,{title: '年龄',width:75,templet: function(d){
						return d.age + d.ageunit;
				}, align:'center'}
				// ,{ title: '门诊类型',templet: function(d){
				// 		return d.mztype == 1 ? "普通门诊" : d.mztype == 2 ? "急诊" : "";
				// }}
				,{field:'zdmc',  title: '诊断',width:200, align:'left'}
				,{field:'zs',  title: '主诉',width:200, align:'left'}
				,{field:'orgname',  title: '就诊机构'}
				,{field:'deptname',  title: '就诊科室'}
				,{field:'createusername',  title: '接诊医生',width: 88}
				,{field:'ghdate',  title: '就诊日期',width: 180,templet: function (d){
					return formatDateTime(d.ghdate || '' ,'yyyy年MM月dd日 HH:mm');
				}}
				,{fixed:'right', width:100, align:'center', toolbar:'#listtable-opt',title: '操作'}
			]]
			,page: true
			,height:'full-128'
			,cellMinWidth:100
			,limit:20
		});
	});
}

var resetSwClose = function(isClose){
	layui.use(['layer'], function(){
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		if(isClose){
			$('.layui-layer-setwin a.layui-layer-close1').hide();
		}else{
			$('.layui-layer-setwin a.layui-layer-close1').show();
		}
	});
}
layui.use(['layer','element','form','table','ztree','treeselectTable'], function(){//独立版的layer无需执行这一句
	var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
	var table = layui.table;
	var form = layui.form;
	var zFun =layui.treeselectTable;
	var windowWidth = function (){

		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		console.log(seaWidth,seaHeight)

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowWidth2;
	}

	var windowHeight = function (){
		var seaWidth = $("body").width();
		var seaHeight = $("body").height();

		let windowWidth2 = (seaWidth-20) + "px";
		let  windowHeight2 = (seaHeight-20) + "px";
		return windowHeight2;
	}

	var ctxPath = layui.getContextPath("ctxPath","listjs");
	//读取错误提示
	function _serverFail(){
		layer.msg('连接服务器失败,请稍后再试...',{time:2000});
	}
	/**
	 * ajax预处理
	 * @param id sumitid
	 */
	function ajaxValForm(){
		$.ajaxSetup({
			error:function(x,e){
				_serverFail();
				return false;
			}
		});
	}
	//监听工具条
	table.on('tool(listtable)', function(obj){
		var that = this;
		var data = obj.data;
		if(obj.event === 'view'){
			if($(that).attr("disabled")=="disabled")return;
			//执行重载
			layer.open({
				title:['门急诊病历详情']
				,type: 2
				,area: [windowWidth(),windowHeight()]
				,shade: [0.7, '#d0d7f6']
				,scrollbar: true
				,maxmin: false
				,fixed:true
				,move: false
				,content: [ctxPath+'/v/pcenter/mjzbl/tabIndex?id='+data.id+"&mzh="+data.mzh+"&orgcode="+data.orgcode, 'no']
				,end:function(){
				}
			});
		}
	});
	//触发事件
	var active = {
		reload: function(){
			var that = this;
			loadListFunction();
		}
	};
	$('.layui-btn.user-search').on('click', function(){
		var othis = $(this), method = othis.data('method');
		active[method] ? active[method].call(this, othis) : '';
	});
	$('.layui-btn').on('click', function(){
		var othis = $(this), method = othis.data('type');
		active[method] ? active[method].call(this, othis) : '';
	});

	var _loadDic = function(){
		// console.log(JSON.stringify(zNodesJson[0]));

		if(authUser.username == "admin"){
			$("#scbm").attr("ival",'');
			$("#scbm").attr("nval",'');
			$("#orgcode").val('');
			initTree();
		} else if (authUser.grade == "1" || authUser.grade == "2") {
			$("#scbm").attr("ival",authUser.orgid);
			$("#scbm").attr("nval",authUser.orgname);
			$("#orgcode").val(authUser.orgcode);
			initTree();
		}
	}
	var getMenuNodes = function(){
		return zNodesJson;
	}
	var zNodes = getMenuNodes();
	var initTree = function(){
		zFun.initSelectTree(zNodes,"请选择所属单位","scbm",false,true,"只能选择三级单位","ylsjOrg",false);
		if(authUser.orgid != '000000000000000000000000000000000000'){
			zFun.selectTreeId("scbm",authUser.orgid);
		}

	}

	_loadDic();

	loadListFunction(true);

});