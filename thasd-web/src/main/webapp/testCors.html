<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
<title>testCors</title>
<link href="favicon.ico" rel="shortcut icon" type="image/x-icon"/>
<script type="text/javascript" src="./layui/layui.js?ver=${ctl.randomstr()}"></script>
</head>
<body>
<button class="btn1">正常请求</button>
<button class="btn2">跨域请求</button>
</body>
<script type="text/javascript">
	layui.use('layer', function(){//独立版的layer无需执行这一句
		var $ = layui.jquery, layer = layui.layer;//独立版的layer无需执行这一句
		//读取错误提示
		function _serverFail(){
			layer.msg('连接服务器失败,请稍后再试...',{time:2000});
		}
		/**
		 * load信息提示 带遮罩层
		 * @param msg 提示信息
		 * @code{default=加载中...}
		 */
		function _loadMkInfo(msg){
			if(msg==''||null==msg)msg = '加载中...';
			layer.msg(msg, {icon: 16,time: 0,shade: [0.001, '#000000']},function(){});
		}
		/**
		 * ajax预处理
		 * @param id sumitid
		 */
		function ajaxValForm(){
			$.ajaxSetup({
				error:function(x,e){
					_serverFail();
					return false;
				}
			});
		}
		/**
		 * 提交表单
		 * @param id 表单id
		 * @code{.form、#form}
		 */
		function _postForm(){
			ajaxValForm();
			$.getJSON("http://localhost:9010/testCors",function(jsondata){
				console.log("localhost:\n",jsondata);
			});
		}
		function _postForm2(){
			ajaxValForm();
			$.getJSON("http://*************:9010/testCors",function(jsondata){
				console.log("*************:\n",jsondata);
			});
		}

		$('.btn1').on('click', function(){
			_postForm();
		});
		$('.btn2').on('click', function(){
			_postForm2();
		});

	});
</script>
</html>