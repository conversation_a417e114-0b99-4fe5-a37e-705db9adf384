package cn.wingcloud.tools;

import Test.bean.BizBasicDicData;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.common.UniqueIdUtils;
import cn.wingcloud.pojo.PlanTpl;
import cn.wingcloud.util.DateConvert;
import com.alibaba.fastjson.JSON;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import org.beetl.sql.core.SQLManager;

import java.util.List;

public class GenFwxmList {

    static Prop p;

    /**
     * PropKit.useFirstFound(...) 使用参数中从左到右最先被找到的配置文件
     * 从左到右依次去找配置，找到则立即加载并立即返回，后续配置将被忽略
     */
    static void loadConfig() {
        if (p == null) {
            Prop profile = PropKit.useFirstFound( "env.properties");
            p = PropKit.use("config-"+profile.get("activeProject","prod")+".txt");
        }
    }

    public static String[] getSp(String s){
        return s.split("@");
    }

    public static void main(String[] args) {

        loadConfig();
        JFinalBeetlSql.init(p);


        ExcelReader reader = ExcelUtil.getReader(FileUtil.file("C:\\Users\\<USER>\\Downloads\\服务清单.xlsx"), 0);
        List<PlanTpl> readAll = reader.readAll(PlanTpl.class);
        final int[] code = {1};
        readAll.stream().forEach(bizItemDictionary -> {

            bizItemDictionary.setId(UniqueIdUtils.getUniqueId());

            String prorate = bizItemDictionary.getProrate();
            String prorate2 = bizItemDictionary.getProrate2();
            String prorate3 = bizItemDictionary.getProrate3();

            bizItemDictionary.setProrate(getSp(prorate)[0]);
            bizItemDictionary.setProratecode(getSp(prorate)[1]);

            bizItemDictionary.setProrate2(getSp(prorate2)[0]);
            bizItemDictionary.setProrate2code(getSp(prorate2)[1]);

            bizItemDictionary.setProrate3(getSp(prorate3)[0]);
            bizItemDictionary.setProrate3code(getSp(prorate3)[1]);

            String prooffer = bizItemDictionary.getProoffer();
            String prooffer2 = bizItemDictionary.getProoffer2();
            String prooffer3 = bizItemDictionary.getProoffer3();


            bizItemDictionary.setProoffer(getSp(prooffer)[1]);
            bizItemDictionary.setProoffercode(getSp(prooffer)[0]);

            bizItemDictionary.setProoffer2(getSp(prooffer2)[1]);
            bizItemDictionary.setProoffer2code(getSp(prooffer2)[0]);

            bizItemDictionary.setProoffer3(getSp(prooffer3)[1]);
            bizItemDictionary.setProoffer3code(getSp(prooffer3)[0]);


            bizItemDictionary.setIsdel(0L);
            bizItemDictionary.setSortcode(Double.valueOf(code[0]));
            bizItemDictionary.setCreateid("000000000000000000000000000000000000");
            bizItemDictionary.setCreatename("超级管理员");
            bizItemDictionary.setCreatetime(DateConvert.getDateString(DateConvert.YMDHMS));
            bizItemDictionary.setOrgcode("0");
            bizItemDictionary.setOrgname("根节点");
            bizItemDictionary.setOrgid("000000000000000000000000000000000000");


            code[0]++;
        });


        SQLManager sqlManager = JFinalBeetlSql.dao();
        sqlManager.insertBatch(PlanTpl.class,readAll);
        System.out.println(JSON.toJSONString(readAll));

    }
}
