package cn.wingcloud.tools;

import Test.bean.BasicMenu;
import Test.bean.BasicResource;
import cn.hutool.core.util.IdUtil;
import cn.wingcloud.annotation.Menu;
import cn.wingcloud.annotation.MethodMenu;
import cn.wingcloud.annotation.Resource;
import cn.wingcloud.annotation.SingleMenu;
import cn.wingcloud.annotation.constant.MenuEnum;
import cn.wingcloud.annotation.constant.MenuExEnum;
import cn.wingcloud.beetlsql.JFinalBeetlSql;
import cn.wingcloud.util.DateConvert;
import com.alibaba.fastjson.JSON;
import com.jfinal.kit.Prop;
import com.jfinal.kit.PropKit;
import org.beetl.sql.core.SQLManager;
import org.reflections.Reflections;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

public class GenMenuAndResourceTest {

    public static String path = "E:\\git\\thasd\\thasd-web\\src\\main\\java\\cn\\wingcloud\\jfinal\\action";

    static Prop p;

    /**
     * PropKit.useFirstFound(...) 使用参数中从左到右最先被找到的配置文件
     * 从左到右依次去找配置，找到则立即加载并立即返回，后续配置将被忽略
     */
    static void loadConfig() {
        if (p == null) {
            Prop profile = PropKit.useFirstFound( "env.properties");
            p = PropKit.use("config-"+profile.get("activeProject","prod")+".txt");
        }
    }

    public static void main(String[] args) {

//        File dir = new File(path);
//        File[] files = dir.listFiles();
//        Arrays.stream(files).forEach(file -> System.out.println(file.getAbsolutePath()));

        loadConfig();
        JFinalBeetlSql.init(p);

        List<BasicMenu> menuList = new ArrayList<>();
        List<BasicResource> resourceList = new ArrayList<>();
        ConfigurationBuilder config = new ConfigurationBuilder();

        config.filterInputsBy(new FilterBuilder().includePackage("cn.wingcloud.jfinal.action"));
        config.addUrls(ClasspathHelper.forPackage("cn.wingcloud.jfinal.action"));
        config.setScanners(new SubTypesScanner(),new TypeAnnotationsScanner(),new MethodAnnotationsScanner());

        MenuEnum[] menuEnums = MenuEnum.values();
        LinkedHashMap<MenuEnum, BasicMenu> menuMap = new LinkedHashMap<>();
        Arrays.stream(menuEnums).filter(menuEnum -> !menuEnum.equals(MenuEnum.CLASS)).forEach(menuEnum -> {

            BasicMenu menu = BasicMenu.builder()
                    .id(menuEnum.getCode())
                    .name(menuEnum.getName()).menuid(menuEnum.getCode())
                    .parentid(MenuEnum.ROOT.getCode()).parentname(MenuEnum.ROOT.getName())
                    .visible0(menuEnum.getVisible0()).visible2(menuEnum.getVisible2())
                    .url(menuEnum.getUrl())
                    .createid("000000000000000000000000000000000000").createname("超级管理员").createtime(DateConvert.getDateString(DateConvert.YMDHMS))
                    .isdel(0L).isvalid(1L)
                    .sortcode(Double.valueOf(menuEnum.getCode()))
                    .build();

            menuMap.put(menuEnum,menu);
            if(menu.getName().equals(MenuExEnum.HZGL_QRSQ.getName()))menuList.add(menu);
            //menuList.add(menu);

        });

        Reflections reflections = new Reflections(config);
        /**
         * 带有Menu注解 类
         */
        Set<Class<?>> set = reflections.getTypesAnnotatedWith(Menu.class);
        set.forEach(aClass -> {
            Menu anno = aClass.getAnnotation(Menu.class);
            if(!anno.menu().equals(MenuExEnum.HZGL_QRSQ))return;

            String menuid = Integer.valueOf(anno.parent().getCode()) + anno.menu().getCode();
            /**
             * method解析
             */
            Method[] methods = aClass.getDeclaredMethods();
            Arrays.stream(methods).filter(method -> method.isAnnotationPresent(Resource.class)).forEach(method -> {
                Resource resourceAnno = method.getAnnotation(Resource.class);
                String selMenuid = Integer.valueOf(anno.parent().getCode()) + resourceAnno.parentMenu().getCode();
                BasicResource.BasicResourceBuilder menuBuilder = BasicResource.builder();
                menuBuilder.id(getUniqueId())
                .name(resourceAnno.value().getName()).menuid(resourceAnno.parentMenu().equals(MenuExEnum.NULL) ? menuid : selMenuid)
                .createid("000000000000000000000000000000000000").createname("超级管理员").createtime(DateConvert.getDateString(DateConvert.YMDHMS))
                .sortcode(Double.valueOf(resourceAnno.value().getCode())).isdel(0L).isvalid(1L);

                /**
                 * 相当于Resource表中的menuid,menuname  parentid parentname
                 */
                if(resourceAnno.classes().equals(MenuEnum.class)){
                    menuBuilder.menuname(anno.menu().getName()).url(method.getName());
                }else{
                    Menu rsAnno = resourceAnno.classes().getAnnotation(Menu.class);
                    String menuid2 = Integer.valueOf(rsAnno.parent().getCode()) + rsAnno.menu().getCode();
                    StringBuilder subUrl = new StringBuilder(anno.url().replace(anno.subf(),"")).append("/").append(method.getName());
                    menuBuilder.menuid(menuid2).menuname(rsAnno.menu().getName()).url(subUrl.toString());
                }

                resourceList.add(menuBuilder.build());

            });

            //action 解析
            if(!anno.subf().equals("")){
                return;
            }

            BasicMenu menu = BasicMenu.builder()
            .id(menuid)
            .name(anno.menu().getName()).menuid(menuid).url(anno.url())
            .parentid(anno.parent().getCode()).parentname(anno.parent().getName()).visible0(anno.visible0()).visible2(anno.visible2())
            .createid("000000000000000000000000000000000000").createname("超级管理员").createtime(DateConvert.getDateString(DateConvert.YMDHMS))
            .sortcode(Double.valueOf(menuid)).isdel(0L).isvalid(1L)
            .build();

            if(menuMap.containsKey(anno.parent())){
                BasicMenu parentMenu = menuMap.get(anno.parent());
                List<BasicMenu> tmpMenuList = Optional.ofNullable(parentMenu.getMenuList()).orElseGet(()-> new ArrayList<BasicMenu>());
                tmpMenuList.add(menu);
                parentMenu.setMenuList(tmpMenuList);
                menuMap.replace(anno.parent(),parentMenu);
            }

            if(menu.getName().equals(MenuExEnum.HZGL_QRSQ.getName()))menuList.add(menu);
            //menuList.add(menu);

        });

        /**
         * 带有SingleMenu注解 方法
         */
        Set<Method> methodSet = reflections.getMethodsAnnotatedWith(MethodMenu.class);
        methodSet.forEach(method -> {

            MethodMenu anno = method.getAnnotation(MethodMenu.class);

            if(!anno.subf().equals("")){
                return;
            }
            String menuid = Integer.valueOf(anno.parent().getCode()) + anno.menu().getCode();
            BasicMenu menu = BasicMenu.builder()
                    .id(menuid)
                    .name(anno.menu().getName()).menuid(menuid).url(anno.url())
                    .parentid(anno.parent().getCode()).parentname(anno.parent().getName()).visible0(anno.visible0()).visible2(anno.visible2())
                    .createid("000000000000000000000000000000000000").createname("超级管理员").createtime(DateConvert.getDateString(DateConvert.YMDHMS))
                    .sortcode(Double.valueOf(menuid)).isdel(0L).isvalid(1L)
                    .build();

            if(menuMap.containsKey(anno.parent())){
                BasicMenu parentMenu = menuMap.get(anno.parent());
                List<BasicMenu> tmpMenuList = Optional.ofNullable(parentMenu.getMenuList()).orElseGet(()-> new ArrayList<BasicMenu>());
                tmpMenuList.add(menu);
                parentMenu.setMenuList(tmpMenuList);
                menuMap.replace(anno.parent(),parentMenu);
            }
            if(menu.getName().equals(MenuExEnum.HZGL_QRSQ.getName()))menuList.add(menu);
            //menuList.add(menu);

        });


        /**
         * 带有SingleMenu注解 方法
         */
        Set<Method> singleSet = reflections.getMethodsAnnotatedWith(SingleMenu.class);
        singleSet.forEach(method -> {

            SingleMenu anno = method.getAnnotation(SingleMenu.class);

            if(!anno.subf().equals("")){
                return;
            }

            BasicMenu menu = BasicMenu.builder()
                    .id(anno.code())
                    .name(anno.name()).menuid(anno.code()).url("/" + method.getName())
                    .parentid(anno.parent().getCode()).parentname(anno.parent().getName()).visible0(anno.visible0()).visible2(anno.visible2())
                    .createid("000000000000000000000000000000000000").createname("超级管理员").createtime(DateConvert.getDateString(DateConvert.YMDHMS))
                    .sortcode(Double.valueOf(anno.code())).isdel(0L).isvalid(1L)
                    .build();

            if(menuMap.containsKey(anno.parent())){
                BasicMenu parentMenu = menuMap.get(anno.parent());
                List<BasicMenu> tmpMenuList = Optional.ofNullable(parentMenu.getMenuList()).orElseGet(()-> new ArrayList<BasicMenu>());
                tmpMenuList.add(menu);
                parentMenu.setMenuList(tmpMenuList);
                menuMap.replace(anno.parent(),parentMenu);
            }
            if(menu.getName().equals(MenuExEnum.HZGL_QRSQ.getName()))menuList.add(menu);
            //menuList.add(menu);

        });

//        List<BasicMenu> menuTree = new ArrayList<>();
//        menuMap.forEach((menuEnum, menu) -> {
//            menuTree.add(menu);
//        });

        System.out.println(JSON.toJSONString(menuList));
        System.out.println(JSON.toJSONString(resourceList));

        SQLManager sqlManager = JFinalBeetlSql.dao();
        sqlManager.insertBatch(BasicMenu.class,menuList.stream().filter(basicMenu -> !basicMenu.getMenuid().equals(MenuEnum.ROOT.getCode())).collect(Collectors.toList()));
        //sqlManager.insertBatch(BasicResource.class,resourceList);
    }

    /**
     * 采用Snowflake算法生成唯一ID
     *
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2020/06/08 12:32
     **/
    public static String getUniqueId() {
        return IdUtil.fastSimpleUUID().toUpperCase();
    }
}
