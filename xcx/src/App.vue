<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
let config = {
	//白名单
	whiteList: [
    "/pages/login/index"
	] as string[],
	//登录页
	loginPage: "/pages/login/index"
}
onLaunch(() => {
	console.log("App Launch");
});
onShow(() => {
	console.log("App Show");
});
onHide(() => {
	console.log("App Hide");
});
// let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
// list.forEach(item => {
//   uni.addInterceptor(item, {
//     invoke(e) { // 调用前拦截
//       console.log('Interceptor:',item,e);
//       //获取用户
//       const loginUser = uni.getStorageSync('loginUser')
//       //获取要跳转的页面路径（url去掉"?"和"?"后的参数）
//       const url: string = e.url.split('?')[0];
//       let notNeed = config.whiteList.includes(url)
//       // 如果在whiteList里面就不需要登录
//       if (notNeed) {
//         return e
//       } else {
//         //需要登录
//         if (!loginUser) {
//           uni.navigateTo({
//             url: config.loginPage
//           })
//           return false
//         } else {
//           return e
//         }
//       }
//     },
//     fail(err) { // 失败回调拦截 
//       console.log(err);
//     }
//   })
// })
</script>
<style lang="scss">
scroll-view::-webkit-scrollbar,scroll-view.uv-tabs__wrapper__scroll-view::-webkit-scrollbar,.uv-tabs .uv-tabs__wrapper .uv-tabs__wrapper__scroll-view-wrapper scroll-view.uv-tabs__wrapper__scroll-view::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
</style>