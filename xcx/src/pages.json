{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^uv-(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue",
			"^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
		}
	},
	"pages": [ 
		{
			"path": "pages/login/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}

			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/glfa/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/glfa/pgbg",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/pgjl/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/jzjl/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/jzjl/bglist",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/me/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/jcjl/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
		,
		{
			"path": "pages/me/editpassword",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false
				}
			}
		}
	],
	"tabBar": {
		"color": "#333333",
		"selectedColor": "#13a387",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/home/<USER>",
				"iconPath": "static/image/home.png",
				"selectedIconPath": "static/image/home2.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/glfa/index",
				"iconPath": "static/image/fa.png",
				"selectedIconPath": "static/image/fa2.png",
				"text": "健康指导"
			},
			// {
			// 	"pagePath": "pages/pgjl/index",
			// 	"iconPath": "static/image/pg.png",
			// 	"selectedIconPath": "static/image/pg2.png",
			// 	"text": "评估记录"
			// },
			{
				"pagePath": "pages/jzjl/index",
				"iconPath": "static/image/jz.png",
				"selectedIconPath": "static/image/jz2.png",
				"text": "就诊记录"
			},
			{
				"pagePath": "pages/jcjl/index",
				"iconPath": "static/image/jc.png",
				"selectedIconPath": "static/image/jc2.png",
				"text": "智能监测"
			},
			{
				"pagePath": "pages/me/index",
				"iconPath": "static/image/me.png",
				"selectedIconPath": "static/image/me2.png",
				"text": "我的"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "white",
		"navigationBarTitleText": "三高患者客户端",
		"navigationBarBackgroundColor": "#13a387",
		"backgroundColor": "#13a387"
	}
}
