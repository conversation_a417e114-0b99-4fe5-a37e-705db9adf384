.cus-page{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100vh;
}
.statusBarBox{
    width: 100%;
}
.contentBox{
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    flex: 1;
}
.nav-bar-title{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
}
.statusBarNav.home .uni-navbar__header-btns.uni-navbar__header-btns-left{
    width: auto !important;
}
.statusBarNav.home .uni-navbar__header-btns.uni-navbar__header-btns-left wx-image{
    margin-top: 3px;
}
.statusBarNav.home .uni-navbar__header .nav-bar-title .title{
    font-size: 18px;
    font-family: "webfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 2px;
}