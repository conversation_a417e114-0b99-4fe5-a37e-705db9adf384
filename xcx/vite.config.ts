import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import path, { resolve } from 'node:path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  // optimizeDeps: {
  //   include: ["@dcloudio/uni-ui"], // 添加对 @dcloudio/uni-ui 的依赖优化
  // },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '#': path.resolve(__dirname, 'src/types'),
    },
  },
  optimizeDeps: {
    include: ["@dcloudio/uni-ui"],
  },
});
